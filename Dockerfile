FROM harbor-test.ebonex.io/base/ebonex-base-sky:v8.8
RUN /usr/bin/sudo -u ebangapp mkdir -p /ebang/{app,log,tmp,data,etc}/fms-server && \
    /usr/bin/sudo -u ebangapp ln -s /ebang/log/fms-server /ebang/app/fms-server/logs

USER ebangapp
WORKDIR /ebang/app/fms-server
COPY target/fms-server.jar /ebang/app/fms-server

ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-*********-0.el7_8.x86_64
ENV DEFAULT_JAVA_HEAP_OPTS="-XX:+AlwaysPreTouch"
ENV JAVA_HEAP_OPTS=""
ENV DEFAULT_JAVA_OPTS="-server -Dfile.encoding=UTF8 -Djava.net.preferIPv4Stack=true -verbosegc -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -Xlog:gc*,safepoint:./logs/gc.log:time,uptime:filecount=10,filesize=100M -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintFlagsFinal -XX:+AlwaysPreTouch"
ENV JAVA_OPTS=""

ENV JAVA_AGENT=""

ENTRYPOINT exec java ${JAVA_AGENT} ${DEFAULT_JAVA_OPTS} ${DEFAULT_JAVA_HEAP_OPTS} ${JAVA_HEAP_OPTS} ${JAVA_OPTS} -jar /ebang/app/fms-server/fms-server.jar