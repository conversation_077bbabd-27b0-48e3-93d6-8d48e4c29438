import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.FmsServerApplication;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.vo.DataWithPermalinkVO;
import com.ebon.energy.fms.domain.vo.EnergyFlowExtendedVO;
import com.ebon.energy.fms.domain.vo.telemetry.DataWithLinks;
import com.ebon.energy.fms.domain.vo.telemetry.InverterInfo;
import com.ebon.energy.fms.mapper.second.InvertersMapper;
import com.ebon.energy.fms.service.InverterService;
import com.ebon.energy.fms.service.TelemetryService;
import com.ebon.energy.fms.util.HttpClientUtil;
import com.ebon.energy.fms.util.JsonComparison;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest(classes = FmsServerApplication.class)
public class InverterTest {

    @Resource
    private InvertersMapper invertersMapper;

    @Resource
    private InverterService inverterService;

    @Resource
    private TelemetryService telemetryService;

    @Test
    public void testInverter() {
        List<InvertersDO> list = invertersMapper.selectList(new QueryWrapper<>());
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            if (i >= 1000) {
                break;
            }

            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                System.out.println(inverterService.getInverterInfo(invertersDO.getSerialNumber()));
            } catch (BizException e) {
                System.out.println("biz errorMsg:" + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testInverter error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testLastTelemetry() {
        Page<InvertersDO> page = new Page<>(1, 300);
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvertersDO::getSerialNumber,"RB21120704110049");
        List<InvertersDO> list = invertersMapper.selectPage(page, queryWrapper).getRecords();
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                DataWithLinks<InverterInfo> lastTelemetry = telemetryService.getLastTelemetry(invertersDO.getSerialNumber());

                String portalRes = getPortalTelemetryRes(invertersDO.getSerialNumber());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(lastTelemetry), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg:" + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testLastTelemetry error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testLastStatus() {
        Page<InvertersDO> page = new Page<>(1, 500);
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        List<InvertersDO> list = invertersMapper.selectPage(page, queryWrapper).getRecords();
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                DataWithLinks<InverterInfo> lastStatus = telemetryService.getLastStatus(invertersDO.getSerialNumber(), false);

                String portalRes = getPortalSystemStatusRes(invertersDO.getSerialNumber());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(lastStatus), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg:" + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testLastStatus error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    @Test
    public void testLastEnergyflow() {
        Page<InvertersDO> page = new Page<>(1, 1000);
        LambdaQueryWrapper<InvertersDO> queryWrapper = new LambdaQueryWrapper<>();
        List<InvertersDO> list = invertersMapper.selectPage(page, queryWrapper).getRecords();
        int i = 0;
        List<String> errors = new ArrayList<>();
        for (InvertersDO invertersDO : list) {
            try {
                System.out.println((i++) + " sn:" + invertersDO.getSerialNumber());
                DataWithPermalinkVO<EnergyFlowExtendedVO> lastEnergyflow = telemetryService.getLastEnergyflow(invertersDO.getSerialNumber());

                String portalRes = getPortalEnergyflowRes(invertersDO.getSerialNumber());
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                JsonComparison.compareJsonStrings(objectMapper.writeValueAsString(lastEnergyflow), portalRes);
            } catch (BizException e) {
                System.out.println("biz errorMsg: " + invertersDO.getSerialNumber() + " " + e.getErrorMsg());
            } catch (Exception e) {
                errors.add(invertersDO.getSerialNumber());
                System.out.println("testLastEnergyflow error: " + invertersDO.getSerialNumber());
                e.printStackTrace();
            }
        }
        log.error("errors:{}", errors);
    }

    private String getPortalEnergyflowRes(String sn) {
        String url = "https://rbtestwebui-leia.azurewebsites.net/api/v2/energyflowd2/";
        String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=29vjFjaQuycGIrQxPmvMepgNN6rSP9B9TLS9ZnYrl__b6_1FQ0u3zQA6S5W9lE1Saj6NGQZpBd94SNxpMJYALeLGrQC6LTy1VylJstyN2xwwEOZpwlh7p-YU9yhkC2NpKZIrfWwEmeZVbuGnvrgnhfilAFdQhzsyYmbZiVfTUxfqaLGE78cz3qVgrFSBvexcYZ4Ezk9qsVltrZbG73fn_kpvOK_0eVVxNmQsaHZKxS8b0m6hOfQmAY4TCYewIxKS7BD0h0qHieCf4lCplfqO2OQwqnf-O9QmYneQTp0RqlPWyCVI82rI_MiLkJzhkVR4Z7iqAWtm86iXTvOljxf1Ncyfr4duutZOq_DBdkyWKjJ0w2lBFyccXbE8azEWehTpU-ZTSrblK75wBPHviG6knBV8jeFe8QVV2J88vYvk3fyP9Zxc46oTXEbU6Klje7TBVW8x6DgD1UbzTZsfbNo_PtVd9iMeGSQ2SI_ikhRRrOmUmNwvmkQLglPORivpuehP6ijqKt_QZpB7CMH82CfGGOG7jNI9-f-6Q9XWYO3dyJC9NPS5MgNbheEhdet4LbP_KwGDANyrsWm4K3neh-sWew; ARRAffinity=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; ARRAffinitySameSite=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; __RequestVerificationToken=8h1zNX8UzF8_57w17y-GWAtodaU55_v7Sx_bjyoCAdppbsyhyDu0QuADxtqZ7XPCnp76G7XGFP7XAhniXF-VN8flWYfNicwWTgaD-HI2PYU1; ai_session=7qtwf5bwazk/dwGmQ7OdO+|1745911398249|1745927488925";
        //String url = "https://portal.redbacktech.com/api/v2/energyflowd2/";
        //String cookie = "xxx";
        return HttpClientUtil.get(url + sn, cookie);
    }

    private String getPortalSystemStatusRes(String sn) {
        //String url = "https://rbtestwebui-leia.azurewebsites.net/product/"+sn+"/data/status/latest";
        //String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=29vjFjaQuycGIrQxPmvMepgNN6rSP9B9TLS9ZnYrl__b6_1FQ0u3zQA6S5W9lE1Saj6NGQZpBd94SNxpMJYALeLGrQC6LTy1VylJstyN2xwwEOZpwlh7p-YU9yhkC2NpKZIrfWwEmeZVbuGnvrgnhfilAFdQhzsyYmbZiVfTUxfqaLGE78cz3qVgrFSBvexcYZ4Ezk9qsVltrZbG73fn_kpvOK_0eVVxNmQsaHZKxS8b0m6hOfQmAY4TCYewIxKS7BD0h0qHieCf4lCplfqO2OQwqnf-O9QmYneQTp0RqlPWyCVI82rI_MiLkJzhkVR4Z7iqAWtm86iXTvOljxf1Ncyfr4duutZOq_DBdkyWKjJ0w2lBFyccXbE8azEWehTpU-ZTSrblK75wBPHviG6knBV8jeFe8QVV2J88vYvk3fyP9Zxc46oTXEbU6Klje7TBVW8x6DgD1UbzTZsfbNo_PtVd9iMeGSQ2SI_ikhRRrOmUmNwvmkQLglPORivpuehP6ijqKt_QZpB7CMH82CfGGOG7jNI9-f-6Q9XWYO3dyJC9NPS5MgNbheEhdet4LbP_KwGDANyrsWm4K3neh-sWew; ARRAffinity=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; ARRAffinitySameSite=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; __RequestVerificationToken=8h1zNX8UzF8_57w17y-GWAtodaU55_v7Sx_bjyoCAdppbsyhyDu0QuADxtqZ7XPCnp76G7XGFP7XAhniXF-VN8flWYfNicwWTgaD-HI2PYU1; ai_session=7qtwf5bwazk/dwGmQ7OdO+|1745911398249|1745927488925";
        String url = "https://portal.redbacktech.com/product/" + sn + "/data/status/latest";
        String cookie = "xxx";
        return HttpClientUtil.get(url, cookie);
    }

    private String getPortalTelemetryRes(String sn) {
        //String url = "https://rbtestwebui-leia.azurewebsites.net/product/"+sn+"/data/telemetry/latest";
        //String cookie = "ai_user=EVnFe|2025-03-17T01:21:22.872Z; .AspNet.ApplicationCookie=29vjFjaQuycGIrQxPmvMepgNN6rSP9B9TLS9ZnYrl__b6_1FQ0u3zQA6S5W9lE1Saj6NGQZpBd94SNxpMJYALeLGrQC6LTy1VylJstyN2xwwEOZpwlh7p-YU9yhkC2NpKZIrfWwEmeZVbuGnvrgnhfilAFdQhzsyYmbZiVfTUxfqaLGE78cz3qVgrFSBvexcYZ4Ezk9qsVltrZbG73fn_kpvOK_0eVVxNmQsaHZKxS8b0m6hOfQmAY4TCYewIxKS7BD0h0qHieCf4lCplfqO2OQwqnf-O9QmYneQTp0RqlPWyCVI82rI_MiLkJzhkVR4Z7iqAWtm86iXTvOljxf1Ncyfr4duutZOq_DBdkyWKjJ0w2lBFyccXbE8azEWehTpU-ZTSrblK75wBPHviG6knBV8jeFe8QVV2J88vYvk3fyP9Zxc46oTXEbU6Klje7TBVW8x6DgD1UbzTZsfbNo_PtVd9iMeGSQ2SI_ikhRRrOmUmNwvmkQLglPORivpuehP6ijqKt_QZpB7CMH82CfGGOG7jNI9-f-6Q9XWYO3dyJC9NPS5MgNbheEhdet4LbP_KwGDANyrsWm4K3neh-sWew; ARRAffinity=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; ARRAffinitySameSite=6d3d50ebcd0e89ffba781af0478dd8f0a095e9e6d896b3eb9a6ff4d70015a1fd; __RequestVerificationToken=8h1zNX8UzF8_57w17y-GWAtodaU55_v7Sx_bjyoCAdppbsyhyDu0QuADxtqZ7XPCnp76G7XGFP7XAhniXF-VN8flWYfNicwWTgaD-HI2PYU1; ai_session=7qtwf5bwazk/dwGmQ7OdO+|1745911398249|1745927488925";
        String url = "https://portal.redbacktech.com/product/" + sn + "/data/telemetry/latest";
        String cookie = "xxx";
        return HttpClientUtil.get(url, cookie);
    }

}
