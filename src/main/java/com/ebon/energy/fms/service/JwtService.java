package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.constants.AuthorizeConstants;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class JwtService {

    @Value("${authorize.jwt.secret}")
    private String secret;

    @Value("${authorize.jwt.expire-seconds}")
    private Long tokenExpireSeconds;

    public String generateToken(String subject) {
        Date expiration = new Date(System.currentTimeMillis() + this.tokenExpireSeconds * 1000);
        String compact = Jwts.builder()
                .setSubject(subject)
                .claim(AuthorizeConstants.AUTHORIZE_KEY, subject)
                .signWith(SignatureAlgorithm.HS256, secret)
                .setExpiration(expiration)
                .compact();

        return compact;
    }

    public String parseSubject(String jwtToken) {
        if (StringUtils.isEmpty(jwtToken)) {
            return null;
        }
        try {
            String subject = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(jwtToken)
                    .getBody()
                    .getSubject();

            return subject;
        } catch (Exception e) {
            log.error("parseSubject error", e);
        }
        return null;
    }

}


