package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import com.ebon.energy.fms.domain.entity.RedbackUserDetailsDO;
import com.ebon.energy.fms.domain.entity.SiteDO;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.ebon.energy.fms.mapper.primary.UserMapper;
import com.ebon.energy.fms.mapper.third.RedbackUserDetailsMapper;
import com.ebon.energy.fms.util.CookieUtil;
import com.ebon.energy.fms.util.MD5Util;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.ebon.energy.fms.common.constants.AuthorizeConstants.BASE_SALT;
import static com.ebon.energy.fms.common.constants.AuthorizeConstants.COOKIE_TOKEN_KEY;

@Slf4j
@Service
public class UserDetailService {

    @Resource
    private RedbackUserDetailsMapper userDetailsMapper;

    public List<RedbackUserDetailsDO> getUserDetails(List<String> sns, String name) {
        if (StringUtils.isBlank(RequestUtil.getPortolUserId())) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<RedbackUserDetailsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RedbackUserDetailsDO::getRedbackProductSn, sns);
        queryWrapper.eq(RedbackUserDetailsDO::getName, name);
        queryWrapper.eq(RedbackUserDetailsDO::getRedbackUserId, RequestUtil.getPortolUserId());
        return userDetailsMapper.selectList(queryWrapper);
    }

}