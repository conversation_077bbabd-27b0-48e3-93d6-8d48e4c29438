package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.AuditsDO;
import com.ebon.energy.fms.domain.po.AuditsListPO;
import com.ebon.energy.fms.domain.vo.AuditsVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.mapper.second.AuditsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuditService {

    @Resource
    private AuditsMapper auditsMapper;

    public PageResult<AuditsVO> getAuditList(AuditsListPO po) {
        Page<AuditsDO> page = new Page<>(po.getCurrent(), po.getPageSize());

        LambdaQueryWrapper<AuditsDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNoneBlank(po.getDateStr())) {
            try {
                Date startOfDay = DateUtils.parseDate(po.getDateStr(), "yyyy-MM-dd");
                Date endOfDay = new Date(startOfDay.getTime() + DateUtils.MILLIS_PER_DAY);
                queryWrapper.ge(AuditsDO::getTimeAccessed, startOfDay).lt(AuditsDO::getTimeAccessed, endOfDay);
            } catch (ParseException e) {
                throw new BizException(CommonErrorCodeEnum.REQUEST_ERROR);
            }
        }

        if (StringUtils.isNoneBlank(po.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(AuditsDO::getAuditID, po.getKeyword())
                    .or().like(AuditsDO::getContent, po.getKeyword())
                    .or().like(AuditsDO::getIpAddress, po.getKeyword())
                    .or().like(AuditsDO::getMethod, po.getKeyword())
                    .or().like(AuditsDO::getTag, po.getKeyword())
                    .or().like(AuditsDO::getUrlAccessed, po.getKeyword())
                    .or().like(AuditsDO::getUserName, po.getKeyword()));
        }

        if (po.getOrder() != null) {
            for (AuditsListPO.Order col : po.getOrder()) {
                switch (col.getColumn()) {
                    case 0:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(AuditsDO::getTimeAccessed);
                        } else {
                            queryWrapper.orderByAsc(AuditsDO::getTimeAccessed);
                        }
                        break;
                    case 1:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(AuditsDO::getTag);
                        } else {
                            queryWrapper.orderByAsc(AuditsDO::getTag);
                        }
                        break;
                    case 2:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(AuditsDO::getUrlAccessed);
                        } else {
                            queryWrapper.orderByAsc(AuditsDO::getUrlAccessed);
                        }
                        break;
                    case 3:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(AuditsDO::getUserName);
                        } else {
                            queryWrapper.orderByAsc(AuditsDO::getUserName);
                        }
                        break;
                }
            }
        }

        Page<AuditsDO> auditPage = auditsMapper.selectPage(page, queryWrapper);
        List<AuditsVO> list = auditPage.getRecords().stream().map(e -> {
            AuditsVO vo = new AuditsVO();
            BeanUtils.copyProperties(e, vo);
            vo.setTimeAccessed(e.getTimeAccessed().toString());
            return vo;
        }).collect(Collectors.toList());
        return PageResult.toResponse(list, page.getTotal(), po.getCurrent(), po.getPageSize());
    }

}
