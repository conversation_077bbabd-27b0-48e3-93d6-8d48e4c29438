package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.config.AzureStorageProperties;
import com.ebon.energy.fms.domain.vo.RedbackTableStorageEntity;
import com.ebon.energy.fms.domain.vo.TelemetryDataVO;
import com.ebon.energy.fms.service.TableStoreService;
import com.microsoft.azure.storage.*;
import com.microsoft.azure.storage.table.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.security.InvalidKeyException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

@Service
public class AzureTableStorageService implements TableStoreService {
    private static final Logger logger = LoggerFactory.getLogger(AzureTableStorageService.class);
    private static final String OLD_STORAGE_TABLE_NAME = "inverterdata";
    private static final String TELEMETRY_TABLE_NAME = "telemetrydata";
    private static final String EB_TELEMETRY_TABLE_NAME = "ebtelemetrydata";
    private static final int SIXTY_DAYS_IN_SECONDS = (int) Duration.ofDays(60).getSeconds();

    private final AzureStorageProperties properties;
    private long useOnlyNewTelemetryFromEpoch;
    private long useOnlyOldStoragePriorToEpoch;

    private CloudTable inverterTable;
    private CloudTable telemetryTable;
    private CloudTable ebTelemetryTable;

    @Autowired
    public AzureTableStorageService(AzureStorageProperties properties) throws StorageException, URISyntaxException, InvalidKeyException {
        this.properties = properties;
        initializeStorageRepositoryDates();
        initializeTables();
    }

    private void initializeTables() throws StorageException, URISyntaxException, InvalidKeyException {
        if (properties.getOldAccountName() != null && properties.getOldAccountKey() != null) {
            String oldConnectionString = String.format(
                    "DefaultEndpointsProtocol=https;AccountName=%s;AccountKey=%s",
                    properties.getOldAccountName(), properties.getOldAccountKey());

            CloudStorageAccount oldAccount = CloudStorageAccount.parse(oldConnectionString);
            CloudTableClient oldClient = oldAccount.createCloudTableClient();
            this.inverterTable = oldClient.getTableReference(OLD_STORAGE_TABLE_NAME);
            this.inverterTable.createIfNotExists();
        }

        if (properties.getTelemetryAccountName() != null && properties.getTelemetryAccountKey() != null) {
            String telemetryConnectionString = String.format(
                    "DefaultEndpointsProtocol=https;AccountName=%s;AccountKey=%s",
                    properties.getTelemetryAccountName(), properties.getTelemetryAccountKey());

            CloudStorageAccount telemetryAccount = CloudStorageAccount.parse(telemetryConnectionString);
            CloudTableClient telemetryClient = telemetryAccount.createCloudTableClient();
            this.telemetryTable = telemetryClient.getTableReference(TELEMETRY_TABLE_NAME);
            this.telemetryTable.createIfNotExists();

            this.ebTelemetryTable = telemetryClient.getTableReference(EB_TELEMETRY_TABLE_NAME);
            this.ebTelemetryTable.createIfNotExists();
        }
    }

    private void initializeStorageRepositoryDates() {
        LocalDate farFutureDate = parseDate("********");
        LocalDate useOnlyNewTelemetryFromDate = parseDateOrDefault(
                properties.getUseOnlyNewTelemetryFromDate(), farFutureDate);
        LocalDate useOnlyOldStoragePriorToDate = parseDateOrDefault(
                properties.getUseOnlyOldStoragePriorToDate(), farFutureDate);

        // 验证日期配置
        if (useOnlyNewTelemetryFromDate.isBefore(useOnlyOldStoragePriorToDate)) {
            logger.error("新遥测存储日期{}早于旧存储日期{}，设置无效",
                    useOnlyNewTelemetryFromDate, useOnlyOldStoragePriorToDate);
            useOnlyNewTelemetryFromDate = farFutureDate;
        }

        this.useOnlyOldStoragePriorToEpoch = convertToEpoch(useOnlyOldStoragePriorToDate);
        this.useOnlyNewTelemetryFromEpoch = convertToEpoch(useOnlyNewTelemetryFromDate);

        logger.info("存储仓库初始化完成: 旧存储 < {} < 两者 < {} < 新存储, 启用新存储保存={}",
                useOnlyOldStoragePriorToDate, useOnlyNewTelemetryFromDate,
                properties.getEnableNewTelemetryStorage());
    }

    /**
     * 异步获取单个系统状态
     * @param serialNumber 设备序列号
     * @param epoch 时间戳(秒或毫秒)
     * @return CompletableFuture包装的系统状态
     */
    @Async
    public CompletableFuture<String> getSystemStatusAsync(String serialNumber, long epoch) {
        try {
            // 自动处理毫秒/秒时间戳
            epoch = normalizeEpoch(epoch);

            String result = querySingleEntity(
                    serialNumber,
                    epoch,
                    entity -> entity.getDocument(),
                    "Document");

            return CompletableFuture.completedFuture(result);
        } catch (Exception ex) {
            CompletableFuture<String> future = new CompletableFuture<>();
            future.completeExceptionally(ex);
            return future;
        }
    }

    /**
     * 异步获取单个系统状态
     * @param serialNumber 设备序列号
     * @param epoch 时间戳(秒或毫秒)
     * @return CompletableFuture包装的系统状态
     */
    @Async
    public CompletableFuture<String> getRossTelemetryAsync(String serialNumber, long epoch) {
        try {
            // 自动处理毫秒/秒时间戳
            epoch = normalizeEpoch(epoch);

            String result = querySingleEntity(
                    serialNumber,
                    epoch,
                    entity -> entity.getRossTelemetry(),
                    "RossTelemetry");

            return CompletableFuture.completedFuture(result);
        } catch (Exception ex) {
            CompletableFuture<String> future = new CompletableFuture<>();
            future.completeExceptionally(ex);
            return future;
        }
    }

    /**
     * 异步获取原始遥测数据
     * @param serialNumber 设备序列号
     * @param epoch 时间戳(秒或毫秒)
     * @return CompletableFuture包装的遥测数据
     */
    @Async
    public CompletableFuture<String> getDeviceTelemetryAsync(String serialNumber, long epoch) {
        try {
            epoch = normalizeEpoch(epoch);

            String result = querySingleEntity(
                    serialNumber,
                    epoch,
                    entity -> entity.getDeviceTelemetry() + (entity.getDeviceTelemetry2() != null ? entity.getDeviceTelemetry2() : ""),
                    "DeviceTelemetry", "DeviceTelemetry2");

            return CompletableFuture.completedFuture(result);
        } catch (Exception ex) {
            CompletableFuture<String> future = new CompletableFuture<>();
            future.completeExceptionally(ex);
            return future;
        }
    }


    /**
     * 通用异步查询方法
     * @param serialNumber 设备序列号
     * @param epoch 标准化后的时间戳(秒)
     * @param mapper 结果映射函数
     * @param columns 要查询的列
     * @return 查询结果
     * @throws StorageException 存储异常
     */
    private <T> T querySingleEntity(
            String serialNumber,
            long epoch,
            Function<RedbackTableStorageEntity, T> mapper,
            String... columns) throws StorageException {

        // 确定使用哪个表
        boolean useNewTable = epoch >= useOnlyOldStoragePriorToEpoch;
        boolean useOldTable = epoch <= useOnlyNewTelemetryFromEpoch;

        if (!useNewTable && !useOldTable) {
            throw new IllegalArgumentException("时间戳 " + epoch + " 不在任何存储表的有效范围内");
        }

        // 尝试从新表查询
        if (useNewTable && ebTelemetryTable != null) {
            try {
                T result = queryTable(ebTelemetryTable, serialNumber, epoch, true, mapper, columns);
                if (result != null) {
                    return result;
                } else {
                    result = queryTable(telemetryTable, serialNumber, epoch, true, mapper, columns);
                    if (result != null) {
                        return result;
                    }
                }
            } catch (Exception ex) {
                logger.warn("查询新表失败，尝试旧表", ex);
            }
        }

        // 尝试从旧表查询
        if (useOldTable && inverterTable != null) {
            return queryTable(inverterTable, serialNumber, epoch, false, mapper, columns);
        }

        return null;
    }

    /**
     * 实际表查询逻辑
     */
    private <T> T queryTable(
            CloudTable table,
            String serialNumber,
            long epoch,
            boolean newStorageFormat,
            Function<RedbackTableStorageEntity, T> mapper,
            String... columns) throws StorageException {

        String rowKey = newStorageFormat
                ? getRowKey(Long.MAX_VALUE - epoch)
                : getRowKey(epoch);

//        TableOperation operation = TableOperation.retrieve(
//                serialNumber, rowKey, RedbackTableStorageEntity.class);
        String filter1 = TableQuery.generateFilterCondition(
                "PartitionKey",
                TableQuery.QueryComparisons.EQUAL,
                serialNumber
        );

        String filter2 = TableQuery.generateFilterCondition(
                "RowKey",
                TableQuery.QueryComparisons.EQUAL,
                rowKey
        );

        // 组合条件 (AND)
        String combinedFilter = TableQuery.combineFilters(filter1, TableQuery.Operators.AND, filter2);
        TableQuery<RedbackTableStorageEntity> query = TableQuery.from(RedbackTableStorageEntity.class)
                .where(combinedFilter)
                .select(columns).take(1);

        Iterable<RedbackTableStorageEntity> result = table.execute(query);
        AtomicReference<RedbackTableStorageEntity> entity = new AtomicReference<>();
        result.forEach(redbackTableStorageEntity -> {
            entity.set(redbackTableStorageEntity);
        });
//        TableResult result = table.execute(operation);
        if (entity.get() != null) {
            return mapper.apply(entity.get());
        }
        return null;
    }

    /**
     * 标准化时间戳(处理毫秒/秒)
     */
    private long normalizeEpoch(long epoch) {
        // 处理毫秒时间戳(大于946684800000，即2000年左右)
        if (epoch > 946684800000L) {
            if (epoch % 1000 == 0) {
                return epoch / 1000;
            }
            throw new IllegalArgumentException("无效的时间戳格式: " + epoch);
        }
        return epoch;
    }


    private LocalDate parseDate(String dateStr) {
        return LocalDate.parse(dateStr, DateTimeFormatter.BASIC_ISO_DATE);
    }

    private LocalDate parseDateOrDefault(String dateStr, LocalDate defaultDate) {
        try {
            return parseDate(dateStr);
        } catch (Exception e) {
            return defaultDate;
        }
    }

    private long convertToEpoch(LocalDate date) {
        return date.atStartOfDay(ZoneOffset.UTC).toInstant().getEpochSecond();
    }

    private static String getRowKey(long epoch) {
        return String.format("%010d", epoch);
    }


    @Override
    public CloudPlatformName getCloudPlatformName() {
        return CloudPlatformName.Azure;
    }

    @Override
    public TelemetryDataVO getTelemetryData(String sn, long epoch) {
        CompletableFuture<String> statusAsync = getSystemStatusAsync(sn, epoch);
        CompletableFuture<String> rossTelemetryAsync = getRossTelemetryAsync(sn, epoch);
        TelemetryDataVO dataVO = new TelemetryDataVO();
        dataVO.setSerialNumber(sn);

        statusAsync.whenComplete((status, throwable) -> {
            dataVO.setSystemStatus(status);
        });

        rossTelemetryAsync.whenComplete((status, throwable) -> {
            dataVO.setTelemetry(status);
        });

        CompletableFuture.allOf(statusAsync, rossTelemetryAsync);
        return dataVO;
    }


    public static void main(String[] args) {
        String rowKey = getRowKey(Long.MAX_VALUE - 1745975695L);
        System.out.println(rowKey);
    }
}