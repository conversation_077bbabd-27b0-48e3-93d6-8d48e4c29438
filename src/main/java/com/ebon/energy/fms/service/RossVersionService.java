package com.ebon.energy.fms.service;

import com.ebon.energy.fms.domain.po.DirectMethodSendPO;
import com.ebon.energy.fms.domain.po.UpdateRossVersionPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.redback.DeviceInfoDTO;
import com.ebon.energy.fms.domain.vo.redback.DeviceVersionInfoDTO;
import com.ebon.energy.fms.util.RedbackWebApiUtil;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.BindException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

@Service
@Slf4j
public class RossVersionService {

    @Resource
    private RedbackWebApiUtil redbackWebApiUtil;

    @Resource
    private InverterService inverterService;

    @Resource
    private ProductService productService;


    public List<CheckRoss2ReadyVO> checkRoss2Ready(List<String> serialNumbers) throws IOException, InterruptedException {
        String email = RequestUtil.getLoginUserEmail();

        if (CollectionUtils.isEmpty(serialNumbers)) {
            throw new BindException("no serial number");
        }

        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(serialNumbers.size(), 20)); // Limit to 10 threads max

        List<Future<CheckRoss2ReadyVO>> futures = new ArrayList<>();

        for (String serialNumber : serialNumbers) {
            futures.add(executorService.submit(() -> processSerialNumber(serialNumber)));
        }

        List<CheckRoss2ReadyVO> vos = new ArrayList<>();
        for (Future<CheckRoss2ReadyVO> future : futures) {
            try {
                vos.add(future.get());
            } catch (ExecutionException e) {
                throw new RuntimeException("Error processing serial number", e);
            }
        }

        // Shutdown the executor
        executorService.shutdown();

        return vos;
    }

    private CheckRoss2ReadyVO processSerialNumber(String serialNumber) {
        CheckRoss2ReadyVO ross2ReadyVO = new CheckRoss2ReadyVO();
        ross2ReadyVO.setSerialNumber(serialNumber);

        try {
            InverterInfoVO inverterInfo = productService.getWithInstallation(serialNumber);
            DeviceVersionInfoDTO reported = new DeviceVersionInfoDTO();
            reported.setRossVersion(inverterInfo.getRossVersion());
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            deviceInfoDTO.setSerialNumber(inverterInfo.getSerialNumber());
            deviceInfoDTO.setReported(reported);
            ross2ReadyVO.setDeviceInfo(deviceInfoDTO);
            ross2ReadyVO.setInverterModelName(Objects.nonNull(inverterInfo) ? inverterInfo.getModelName() : "");
            ross2ReadyVO.setRoss2Ready(inverterInfo.getIsOnline());
        } catch (Exception e) {
            ross2ReadyVO.setRoss2Ready(Boolean.FALSE);
        }

        return ross2ReadyVO;
    }

    public List<String> getVersions() throws IOException, InterruptedException {
        String email = RequestUtil.getLoginUserEmail();

        return RedbackWebApiUtil.getAvailableRossVersionsAsync(email);
    }


    public List<RossVersionsUpdateErrorVO> upgrade(UpdateRossVersionPO po) throws IOException, InterruptedException {
        String email = RequestUtil.getLoginUserEmail();
        List<String> serialNumbers = po.getSerialNumbers();

        if (CollectionUtils.isEmpty(serialNumbers)) {
            return Collections.emptyList();
        }

        // 创建线程池，限制最大线程数避免资源耗尽
        ExecutorService executor = Executors.newFixedThreadPool(Math.min(serialNumbers.size(), 20));

        // 存储异步任务结果
        List<CompletableFuture<RossVersionsUpdateErrorVO>> futures = new ArrayList<>();
        List<RossVersionsUpdateErrorVO> errors = Collections.synchronizedList(new ArrayList<>());

        for (String serialNumber : serialNumbers) {
            CompletableFuture<RossVersionsUpdateErrorVO> future = CompletableFuture.supplyAsync(() -> {
                try {
                    RedbackWebApiUtil.requestRoss2InstallAsync(
                            serialNumber,
                            po.getRossVersion(),
                            po.getWatchdogVersion(),
                            po.getForce(),
                            email
                    );
                    return null; // 成功返回null
                } catch (Exception e) {
                    return RossVersionsUpdateErrorVO.builder()
                            .serialNumber(serialNumber)
                            .error(e.getMessage())
                            .build();
                }
            }, executor);

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 过滤出非null的错误结果
        for (CompletableFuture<RossVersionsUpdateErrorVO> future : futures) {
            RossVersionsUpdateErrorVO error = future.join();
            if (error != null) {
                errors.add(error);
            }
        }

        executor.shutdown();
        return errors;
    }
}
