package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.domain.entity.DeviceDO;
import com.ebon.energy.fms.domain.entity.InverterAttentionDO;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.vo.DeviceVO;
import com.ebon.energy.fms.mapper.primary.InverterAttentionMapper;
import com.ebon.energy.fms.mapper.third.DeviceMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InverterAttentionService {

    @Resource
    private InverterAttentionMapper inverterAttentionMapper;

    public InverterAttentionDO getBySn(String sn) {
        return inverterAttentionMapper.selectById(sn);
    }

    public List<InverterAttentionDO> getByIds(List<String> ids) {
        LambdaQueryWrapper<InverterAttentionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InverterAttentionDO::getRedbackProductSn, ids);
        return inverterAttentionMapper.selectList(queryWrapper);
    }

    public void save(InverterAttentionDO attentionDO) {
        inverterAttentionMapper.insert(attentionDO);
    }

    public void update(InverterAttentionDO attentionDO) {
        inverterAttentionMapper.updateById(attentionDO);
    }

    public void updateErrorProcessed(String sn) {
        InverterAttentionDO attentionDO = new InverterAttentionDO();
        attentionDO.setRedbackProductSn(sn);
        attentionDO.setStatus(1);
        inverterAttentionMapper.updateById(attentionDO);
    }
}
