package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.ModelTypeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FirmwareVersionsDO;
import com.ebon.energy.fms.domain.po.FirmwareUpdatePO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.mapper.second.FirmwareVersionsMapper;
import com.ebon.energy.fms.util.RedbackWebApiUtil;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FirmwareService {

    @Resource
    private FirmwareVersionsMapper firmwareVersionsMapper;

    @Resource
    private ModelVersionRelationService modelVersionRelationService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private RedbackWebApiUtil redbackWebApiUtil;

    /**
     * 获取型号关联的版本
     * @param modelId
     * @return
     */
    public List<String> getFirmwareVersionByModel(Integer modelId) {
        List<String> strings = modelVersionRelationService.listVersionsByModelId(modelId, ModelTypeEnum.Firmware);
        return null;
    }

    /**
     * 获取全部版本
     * @return
     */
    public List<FirmwareVersionDetailVO> getAllFirmwareVersion() {
        List<FirmwareVersionsDO> vos = firmwareVersionsMapper.selectList(new QueryWrapper<>());
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }
        return vos.stream().map(v -> {
            FirmwareVersionDetailVO firmwareVersionVO = new FirmwareVersionDetailVO();
            BeanUtils.copyProperties(v, firmwareVersionVO);
            return firmwareVersionVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取全部版本(格式化后)
     * @return
     */
    public List<FirmwareVersionSimpleVO> getFirmwareVersion() {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getIsOfficialVersion, Boolean.TRUE);
        List<FirmwareVersionsDO> vos = firmwareVersionsMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }
        return vos.stream().map(v -> {
            FirmwareVersionSimpleVO firmwareVersionVO = new FirmwareVersionSimpleVO();
            firmwareVersionVO.setId(v.getId());
            firmwareVersionVO.setVersion(v.getDspVersion() + v.getDspVersion() + v.getArmVersion());
            return firmwareVersionVO;
        }).collect(Collectors.toList());
    }


    /**
     * 获取版本详情
     * @return
     */
    public FirmwareVersionDetailVO getFirmwareVersionDetail(Integer id) {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getId, id);
        FirmwareVersionsDO vos = firmwareVersionsMapper.selectOne(wrapper);
        FirmwareVersionDetailVO vo = new FirmwareVersionDetailVO();
        if (Objects.isNull(vos)) {
            return vo;
        }

        BeanUtils.copyProperties(vos, vo);
        vo.setCreator(vos.getCreatedBy());
        vo.setLastModifiedUtc(String.valueOf(vos.getLastModifiedUtc().getTime()));
        vo.setIsOfficial(vos.getIsOfficialVersion());
        return vo;
    }

    /**
     * 更新固件
     * @param po
     * @return
     */
    public List<FirmwareUpdateErrorVO> upgrade(FirmwareUpdatePO po) {
        String email = RequestUtil.getLoginUserEmail();
        List<FirmwareUpdateErrorVO> errors = new ArrayList<>();

        for (int i = 0; i < po.getSerialNumbers().size(); i++) {
            String serialNumber = po.getSerialNumbers().get(i);

            try {
                DeviceVO device = deviceService.getLastDevice(serialNumber, ApplicationName.Ross.name());
                if (Objects.isNull(device)) {
                    throw new BizException("404", "Device not found");
                }

                redbackWebApiUtil.updateDeviceTwinAsync(serialNumber, ApplicationName.Ross.name(), po.getFullTwinJson(), email);
            } catch (Exception e) {
                FirmwareUpdateErrorVO vo =  FirmwareUpdateErrorVO.builder()
                        .serialNumber(serialNumber)
                        .error(e.getMessage())
                        .build();
                errors.add(vo);
            }
        }
        return errors;
    }

    ///  function createUUID() {
    ///     return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    ///         var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    ///         return v.toString(16);
    ///     });
    /// }
}
