package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.controller.request.ApllyBatterySettingRequest;
import com.ebon.energy.fms.controller.request.BatterySettingsChangeRequest;
import com.ebon.energy.fms.controller.request.SettingChangeRequestDto;
import com.ebon.energy.fms.domain.entity.ProductDeviceSettingsDO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.ICommonSettingsReader;
import com.ebon.energy.fms.domain.vo.setting.UniversalSettingValueDto;
import com.ebon.energy.fms.domain.vo.setting.provider.*;
import com.ebon.energy.fms.domain.vo.telemetry.OuijaBoard;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.repository.RedbackRepository;
import com.ebon.energy.fms.repository.impl.SettingsServiceImpl;
import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.VersionParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.ebon.energy.fms.util.SafeAccess.getOrElse;

@Slf4j
@Service
@RequiredArgsConstructor
public class SettingsService {

    private final ProductService productService;

    private final SettingsServiceImpl settingsService;

    private final ConfigurationService configurationService;

    private final DeviceSettingsService deviceSettingsService;

    private final RedbackRepository redbackRepository;


    private final BatteryModelInfo batteryModelInfo;

    public Object getSettings(String sn) {
        ProductVO productVO = productService.getProduct(sn);
        if (productVO == null) {
            throw new BizException("The serial number doesn't exist.");
        }

        SystemStatus systemStatus = null;
        if (StringUtils.isNotBlank(productVO.getLatestSystemStatus())) {
            systemStatus = JSONObject.parseObject(productVO.getLatestSystemStatus(), SystemStatus.class);
        }

        boolean isRoss1 = getOrElse(systemStatus, SystemStatus::getOuijaBoard, OuijaBoard::getSoftwareVersion, VersionParser::isRoss1, false);
        if (isRoss1) {
            ConfigurationsVO configurationVO = configurationService.getByConfigType(sn, ConfigurationType.Electrical);
            if (configurationVO == null || StringUtils.isBlank(configurationVO.getConfigurations())) {
                throw new BizException("electrical config not found for " + sn);
            }

            ElectricalConfigurationVO electricalCfgVO = JSONObject.parseObject(configurationVO.getConfigurations(), ElectricalConfigurationVO.class);
            electricalCfgVO.patchLegacyBatteryInfo();
            return electricalCfgVO;
        } else {
            ProductDeviceSettingsDO productDeviceSettings = deviceSettingsService.getProductDeviceSettings(sn);
            if (productDeviceSettings == null) {
                return null;
            }

            DeviceSettingsVO deviceSettingsVO = new DeviceSettingsVO();
            deviceSettingsVO.setReported(JSONObject.parseObject(productDeviceSettings.getReportedDeviceSettings(), RossReportedSettingsVO.class));
            deviceSettingsVO.setDesired(JSONObject.parseObject(productDeviceSettings.getDesiredDeviceSettings(), RossDesiredSettingsVO.class));
            deviceSettingsVO.setIntent(JSONObject.parseObject(productDeviceSettings.getDeviceSettingsIntent(), DeviceSettingsIntentVO.class));
            return deviceSettingsVO;
        }
    }

    public JsonResult<Map<UniversalSettingId, UniversalSettingValueDto>> getSettingsBattery(String sn) {
        var settingsDtoAsync = settingsService.getSettingsDtoAsync(sn);
        //todo update
        var reader = SettingsReaderProvider.get(settingsDtoAsync);

        if (settingsDtoAsync.getIntent() == null) {
            settingsDtoAsync.setIntent(new DeviceSettingsIntentVO());
        }
        Map<UniversalSettingId, UniversalSettingValueDto> map = new HashMap<>();
        for (var setting : reader.getSupportedUniveralSettings(1)) {
            map.put(setting, getResponseForSetting(
                    reader,
                    settingsDtoAsync.getIntent(),
                    setting,
                    sn,
                    false
            ));
        }
        return JsonResult.buildSuccess(map);
    }


    public UniversalSettingValueDto getResponseForSetting(ICommonSettingsReader reader,
                                                          DeviceSettingsIntentVO intend,
                                                          UniversalSettingId setting,
                                                          String serialNumber,
                                                          boolean redbackBatterySwitch
    ) {
        if (UniversalSettingId.BATTERY_SETTINGS == setting) {
            var batterySettingsDto = reader.getBatterySettings(UniversalSettingSource.DESIRED);
            var reportedBatterySettingsDto = reader.getBatterySettings(UniversalSettingSource.REPORTED);
            var systemStatus = redbackRepository.getLatestSystemStatusAsync(serialNumber);
            if (batterySettingsDto != null) {
                var batteryFirmwareVersion = Optional.ofNullable(systemStatus).map(x ->x.getBattery()).map(x ->x.getBatteryFirmwareVersion()).orElse(null);
                try {
                    var version = Double.parseDouble(batteryFirmwareVersion);
                    var newValue = batteryModelInfo.replaceMinOffgridSoc(Optional.ofNullable(systemStatus.getBattery()).map(x -> x.getBatteryModel()).orElse(null), version);
                    if (newValue != null) {
                        batterySettingsDto.setMinOffgridSoc(MinMaxValueDto.builder()
                                .min(newValue)
                                .max(batterySettingsDto.getMinOffgridSoc().getMax())
                                .value(batterySettingsDto.getMinOffgridSoc().getValue())
                                .build());
                        batterySettingsDto.setMinSoc(MinMaxValueDto.builder()
                                .min(newValue)
                                .max(batterySettingsDto.getMinSoc().getMax())
                                .value(batterySettingsDto.getMinSoc().getValue())
                                .build());
                    }
                } catch (Exception e) {
                    log.warn("battery firmware version is not a number {}", batteryFirmwareVersion);
                }

                batterySettingsDto.setRedbackBatterySwitch(redbackBatterySwitch);
            }
            return new UniversalSettingValueDto(batterySettingsDto, reportedBatterySettingsDto);
        } else {
            //default
            return new UniversalSettingValueDto();
        }
    }

    public void applySettingsBattery(String sn, ApllyBatterySettingRequest request) {
        var settingsDtoAsync = settingsService.getSettingsDtoAsync(sn);
        //todo update
        var reader = SettingsReaderProvider.get(settingsDtoAsync);
        var builder = SettingsBuilderProvider.get(settingsDtoAsync);

        var batterySettings = request.getSettings();

        ensureDeviceHasTimezoneSet(sn, reader, builder, batterySettings);
        ensureDeviceHasDefaultLimitsSet(sn, reader, builder, batterySettings);

        if (batterySettings == null){
            throw new BizException("batterySettings must not be null");
        }

        builder.addBatterySettings(
                batterySettings.getManufacturer(),
                batterySettings.getBatteryCount(),
                batterySettings.getMaxChargeCurrent(),
                batterySettings.getMaxDischargeCurrent(),
                batterySettings.getMinSoc(),
                batterySettings.getMinOffgridSoc()
        );

        var patch = builder.toPatchString();

        settingsService.updateDesiredDeviceSettingsFromJsonPatch(RequestUtil.getPortolUserId(),sn, patch.getJson(),patch.getIntent(),true);

    }


    private void ensureDeviceHasTimezoneSet(
            String serialNumber,
            ICommonSettingsReader reader,
            ICommonSettingsBuilder builder,
            BatterySettingsChangeRequest settingChangeRequests) {
        //todo
//        try {
//            // Ensure the system has a time zone alias setting
//            // Which is required for Relay, Smart Relay Settings and - for GEN3 - Power mode schedules
//            // (this wasn't needed for SI, because power mode schedules for SI are meaningless so not used)
//            if (reader.getSupportedUniveralSettings(null).contains(UniversalSettingId.TIME_ZONE_ALIAS_SETTINGS)) {
//
//                var desiredTZSettings = builder.getSettingsReader().getTimeZoneAliasSettings(UniversalSettingSource.DESIRED);
//
//                var installationTimeZone = productDbRepository
//                        .getTimeZoneInfoAsync(loggedInUserId, serialNumber);
//
//                var hasTimeZoneSet = Optional.ofNullable(desiredTZSettings)
//                        .map(TimeZoneAliasSettings::getAliasMappings)
//                        .map(mappings -> mappings.containsKey(installationTimeZone.getIanaId()))
//                        .orElse(false);
//
//                if (!hasTimeZoneSet) {
//                    Map<String, String> aliasMappings = new HashMap<>();
//                    aliasMappings.put(installationTimeZone.getIanaId(), installationTimeZone.getPosixId());
//
//                    TimeZoneAliasSettingsDto timeZoneAliasSettingsDto = new TimeZoneAliasSettingsDto(aliasMappings);
//
//                    builder.addTimeZoneAliasSettings(timeZoneAliasSettingsDto);
//                }
//            }
//        } catch (Exception ex) {
//            log.warn(String.format(
//                    "Failed to add time zones for serial number '%s'. Message:%s",
//                     serialNumber, ex.getMessage()));
//        }

    }

    private void ensureDeviceHasDefaultLimitsSet(
            String serialNumber,
            ICommonSettingsReader reader,
            ICommonSettingsBuilder builder,
            BatterySettingsChangeRequest settingChangeRequests) {
//
//        try {
//            // 确保系统具有默认生成设置
//            // ROSS不需要这个，因为它可以管理回退，但是SI需要这个，这样它们可以在运行计划后回到生成状态
//            if (reader.getSupportedUniveralSettings(null).contains(UniversalSettingId.AS4777_2_GENERATION_LIMITS)) {
//
//                var existingSettings = reader.getAS4777GenerationExportLimitsSettings(UniversalSettingSource.DESIRED);
//
//                // 如果现有设置中有一个为null，将其设置为false
//                if (existingSettings.getGenerationSoftLimitEnable() == null
//                        || existingSettings.getGenerationHardLimitEnable() == null) {
//
//                    builder.addGenerationLimits(new SetAS4777_2_2020_GenerationLimitSettingsDto(
//                            existingSettings.getGenerationHardLimitEnable() != null ?
//                                    existingSettings.getGenerationHardLimitEnable() : false,
//                            existingSettings.getGenerationHardLimitVA().getValue(),
//                            existingSettings.getGenerationSoftLimitEnable() != null ?
//                                    existingSettings.getGenerationSoftLimitEnable() : false,
//                            existingSettings.getGenerationSoftLimitVA().getValue()));
//                }
//            }
//
//            if (reader.getSupportedUniveralSettings(null).contains(UniversalSettingId.AS4777_2_EXPORT_LIMITS)) {
//                var existingExportSettings = reader.getAS4777SiteExportLimitsSettings(UniversalSettingSource.DESIRED);
//
//                // 如果现有设置中有一个为null，将其设置为false
//                if (existingExportSettings.getExportHardLimitEnable() == null
//                        || existingExportSettings.getExportSoftLimitEnable() == null) {
//
//                    builder.addExportLimits(new SetAS4777_2_2020_ExportLimitSettingsDto(
//                            existingExportSettings.getExportHardLimitEnable() != null ?
//                                    existingExportSettings.getExportHardLimitEnable() : false,
//                            existingExportSettings.getExportHardLimitW().getValue(),
//                            existingExportSettings.getExportSoftLimitEnable() != null ?
//                                    existingExportSettings.getExportSoftLimitEnable() : false,
//                            existingExportSettings.getExportSoftLimitW().getValue()));
//                }
//            }
//        } catch (Exception ex) {
//            log.warn(String.format("Failed to add default generation limits to serial number '%s'. Message:%s", serialNumber, ex.getMessage()));
//        }
    }
}