package com.ebon.energy.fms.service.product.control;

import com.ebon.energy.fms.common.utils.ProductUtilities;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.ZoneId;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductTimezoneService {

    private final ProductMapper productMapper;

    public ZoneId fetchTimezone(String serialNumber) {
        var s = productMapper.queryProductTimeZone(serialNumber);
        return TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(s);
    }
}
