package com.ebon.energy.fms.service.product.control;

import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.vo.product.control.ProductControlViewModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.Transient;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductControlService {

    private final ProductControlProvider productControlProvider;

    public ProductControlViewModel queryControl(String sn) {
        var adaptorFor = productControlProvider.getAdaptorFor(sn);
        return adaptorFor.getViewModel(sn);
    }


    @Transactional(rollbackFor = Exception.class)
    public void changeWorkMode(ProductControlViewModel request) {
        var adaptorFor = productControlProvider.getAdaptorFor(request.getSerialNumber());
        request.setProductDefaults(adaptorFor.getProductCapabilities(request.getSerialNumber()));
        if (!request.getProductDefaults().isSettingsV2()) {
            validateForm(request);
        }

        adaptorFor.updateAsync(request);

    }


    public void validateForm(ProductControlViewModel request) {
        if (request == null || request.getInverterOperation() == null || request.getProductDefaults() == null) {
            return;
        }
        var op = request.getInverterOperation();
        var defaults = request.getProductDefaults();
        // Type enum mapping
        String type = op.getType();
        String mode = op.getMode();
        long power = op.getPowerInWatts();
        // SET
        if ("SET".equalsIgnoreCase(type)) {
            if (power < defaults.getMinimumExportPowerInWatts()) {
                throw new BizException("Please enter a value greater than or equal to " + defaults.getMinimumExportPowerInWatts());
            }
            if ("ChargeBattery".equalsIgnoreCase(mode)) {
                if (power > defaults.getMaximumBatteryChargePowerInWatts()) {
                    throw new BizException("Please enter a value less than or equal to " + defaults.getMaximumBatteryChargePowerInWatts());
                }
            } else if ("DischargeBattery".equalsIgnoreCase(mode)) {
                if (power > defaults.getMaximumBatteryDischargePowerInWatts()) {
                    throw new BizException("Please enter a value less than or equal to " + defaults.getMaximumBatteryDischargePowerInWatts());
                }
            } else if ("ImportPower".equalsIgnoreCase(mode)) {
                if (power > defaults.getMaxInverterImportPowerPlateRatingW()) {
                    throw new BizException("Please enter a value less than or equal to " + defaults.getMaxInverterImportPowerPlateRatingW());
                }
            } else {
                if (power > defaults.getMaximumExportPowerInWatts()) {
                    throw new BizException("Please enter a value less than or equal to " + defaults.getMaximumExportPowerInWatts());
//                    errors.put("InverterOperation.PowerInWatts", String.format("Please enter a value less than or equal to %d.", defaults.getMaximumExportPowerInWatts()));
                }
            }
        }
        // SCHEDULE
        else if ("SCHEDULE".equalsIgnoreCase(type)) {
            var schedules = op.getSchedules();
            if (schedules == null || schedules.isEmpty()) return;
            for (var schedule : schedules) {
                if (schedule.getMode() == null || !"Conserve".equalsIgnoreCase(schedule.getMode().name())) {
                    Long schedulePower = schedule.getPowerInWatts();
                    if (schedulePower == null) continue;
                    if (schedulePower < defaults.getMinimumExportPowerInWatts()) {
//                        errors.put("InverterScheduleItemViewModel.PowerInWatts", "Please enter a value greater than or equal to 0.");
                        throw new BizException("Please enter a value greater than or equal to " + defaults.getMinimumExportPowerInWatts());
                    }
                    if ("ChargeBattery".equalsIgnoreCase(mode)) {
                        if (power > defaults.getMaximumBatteryChargePowerInWatts()) {
                            throw new BizException("Please enter a value less than or equal to " + defaults.getMaximumBatteryChargePowerInWatts());
                            //errors.put("InverterScheduleItemViewModel.PowerInWatts", String.format("Please enter a value less than or equal to %d.", defaults.getMaximumBatteryChargePowerInWatts()));
                        }
                    } else if ("DischargeBattery".equalsIgnoreCase(mode)) {
                        if (power > defaults.getMaximumBatteryDischargePowerInWatts()) {
//                            errors.put("InverterScheduleItemViewModel.PowerInWatts", String.format("Please enter a value less than or equal to %d.", defaults.getMaximumBatteryDischargePowerInWatts()));
                            throw new BizException("Please enter a value less than or equal to " + defaults.getMaximumBatteryDischargePowerInWatts());
                        }
                    } else if ("ImportPower".equalsIgnoreCase(mode)) {
                        if (power > defaults.getMaxInverterImportPowerPlateRatingW()) {
                            throw new BizException("Please enter a value less than or equal to " + defaults.getMaxInverterImportPowerPlateRatingW());
//                            errors.put("InverterScheduleItemViewModel.PowerInWatts", String.format("Please enter a value less than or equal to %d.", defaults.getMaxInverterImportPowerPlateRatingW()));
                        }
                    } else {
                        if (power > defaults.getMaximumExportPowerInWatts()) {
                            throw new BizException("Please enter a value less than or equal to " + defaults.getMaximumExportPowerInWatts());
//                            errors.put("InverterScheduleItemViewModel.PowerInWatts", String.format("Please enter a value less than or equal to %d.", defaults.getMaximumExportPowerInWatts()));
                        }
                    }
                }
            }
        }
    }
}
