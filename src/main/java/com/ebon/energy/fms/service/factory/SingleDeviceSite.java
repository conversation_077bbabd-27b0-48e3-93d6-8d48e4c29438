package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.domain.entity.DailyTotalVO;
import com.ebon.energy.fms.domain.vo.AllAboutDevice;
import com.ebon.energy.fms.domain.vo.AllAboutSiteOverview;
import com.ebon.energy.fms.domain.vo.DataForDashboardVO;
import com.ebon.energy.fms.domain.vo.WattHour;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import io.vavr.Tuple2;
import org.apache.commons.collections4.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public class SingleDeviceSite implements ISiteConfigurationAggregator {

    @Override
    public AllAboutSiteOverview processSiteOverview(String publicSiteId, List<AllAboutDevice> siteDevices) {
        if (CollectionUtils.isEmpty(siteDevices)) {
            return null;
        }

        // 获取第一个设备（如果存在）
        AllAboutDevice theDevice = siteDevices.get(0);

        // 处理可空属性，设置默认值
        boolean hasSupportForConnectedPV = Optional.ofNullable(theDevice.getSpecification())
                .map(InstallationSpecification::isSupportsConnectedPV)
                .orElse(true);  // 默认值为true

        boolean hasSolar = theDevice.getHasSolar();
        boolean hasBatteries = theDevice.getHasBatteries();
        LocalDate supportsLoadContributorsSince = theDevice.getSupportsLoadContributorsSince();

        DataForDashboardVO siteStatus = theDevice.getDataForDashboard();
        WattHour maximumPossiblePVOutput = theDevice.getMaximumPossiblePVOutput();
        boolean batteryMismatchProtectionEnabled = theDevice.getIsBatteryMismatchProtectionEnabled();

        boolean measuringThirdPartyInverter = Optional.ofNullable(theDevice.getSpecification())
                .map(InstallationSpecification::isMeasuringThirdPartyInverter)
                .orElse(false);

        boolean isAcCoupledMode = Optional.ofNullable(theDevice.getSpecification())
                .map(InstallationSpecification::isInAcCoupledMode)
                .orElse(false);

        // 构建并返回站点概览对象
        return new AllAboutSiteOverview(
                publicSiteId,
                siteStatus,
                siteDevices,
                theDevice.getTodaysDateInLocalTime(),
                hasSupportForConnectedPV,
                maximumPossiblePVOutput,
                hasSolar,
                hasBatteries,
                batteryMismatchProtectionEnabled,
                supportsLoadContributorsSince,
                measuringThirdPartyInverter,
                isAcCoupledMode
        );
    }

    @Override
    public List<DailyTotalVO> processSiteNDayHistory(String publicSiteId, List<Tuple2<AllAboutDevice, List<DailyTotalVO>>> deviceHistories, int twiceDays) {
        return deviceHistories.stream().findFirst().get()._2;
    }
}
