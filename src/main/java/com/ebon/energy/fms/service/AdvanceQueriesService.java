package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.constants.Tags;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQueryCreateVO;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQueryFavouritesPO;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQuerySearchPO;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQueryUpdateVO;
import com.ebon.energy.fms.domain.vo.InverterInfoVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.advanceQueries.*;
import com.ebon.energy.fms.domain.vo.telemetry.*;
import com.ebon.energy.fms.mapper.second.AdvanceQueriesMapper;
import com.ebon.energy.fms.mapper.second.AdvanceQueryUserRelationMapper;
import com.ebon.energy.fms.util.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class AdvanceQueriesService {

    @Resource
    private AdvanceQueriesMapper advanceQueriesMapper;

    @Resource
    private AdvanceQueryUserRelationMapper advanceQueryUserRelationMapper;

    @Resource
    private QueryResultColumnsService queryResultColumnsService;

    @Resource
    private TagsService tagsService;

    @Resource
    private InverterService inverterService;

    @Resource
    private FleetMonitoringUsersService fleetMonitoringUsersService;


    @Transactional
    public boolean create(AdvanceQueryCreateVO createVO) {
        AdvanceQueriesDO queryDO = new AdvanceQueriesDO();
        BeanUtils.copyProperties(createVO, queryDO);
        String email = RequestUtil.getLoginUserEmail();
        queryDO.setCreatedByEmail(email);
        queryDO.setQuery(querySerialNumberInReplace(createVO.getQuery()));
        advanceQueriesMapper.insert(queryDO);

        // 添加tags
        handlerTagsRelation(queryDO.getQueryId(), createVO.getTagsId());

        // 添加columns
        handlerColumnsRelation(queryDO.getQueryId(), createVO.getResultColumns());

        // 添加favorite
        handlerFavoriteRelation(queryDO.getQueryId(), createVO.getFavorite(), email);

        // 添加dashboard
        handlerDashboardRelation(queryDO.getQueryId(), createVO.getDashboard(), email);

        return true;
    }

    private String querySerialNumberInReplace(String queryContent) {
        Pattern pattern = Pattern.compile("SerialNumber = '([^']+)'");
        Matcher matcher = pattern.matcher(queryContent);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            // 提取匹配的序列号部分
            String serialNumbers = matcher.group(1);
            // 替换逗号为 ','，并构造 IN ('...') 格式
            String replacement = "SerialNumber IN ('" + serialNumbers.replace(",", "','") + "')";
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }


    @Transactional
    public boolean update(AdvanceQueryUpdateVO updateVO) {
        AdvanceQueriesDO queryDO = advanceQueriesMapper.selectById(updateVO.getQueryId());
        if (queryDO == null) {
            throw new BizException("查询不存在");
        }
        AdvanceQueriesDO temp = new AdvanceQueriesDO();
        temp.setQueryId(updateVO.getQueryId());
        temp.setQueryName(updateVO.getQueryName());
        temp.setQuery(updateVO.getQuery());
        temp.setQuery(querySerialNumberInReplace(updateVO.getQuery()));

        advanceQueriesMapper.updateById(temp);

        String email = RequestUtil.getLoginUserEmail();

        // 添加tags
        handlerTagsRelation(queryDO.getQueryId(), updateVO.getTagsId());

        // 添加columns
        handlerColumnsRelation(queryDO.getQueryId(), updateVO.getResultColumns());

        // 添加favorite
        handlerFavoriteRelation(queryDO.getQueryId(), updateVO.getFavorite(), email);

        // 添加dashboard
        handlerDashboardRelation(queryDO.getQueryId(), updateVO.getDashboard(), email);

        return true;
    }


    @Transactional
    public boolean delete(Integer queryId) {
        return advanceQueriesMapper.deleteById(queryId) > 0;
    }


    public AdvanceQueryFullVO detail(Integer queryId) {
        AdvanceQueriesDO queryDO = advanceQueriesMapper.selectById(queryId);
        if (queryDO == null) {
            throw new BizException("查询不存在");
        }
        AdvanceQueryFullVO queryVO = new AdvanceQueryFullVO();
        BeanUtils.copyProperties(queryDO, queryVO);
        String email = RequestUtil.getLoginUserEmail();
        queryVO.setDashboard(fleetMonitoringUsersService.isRelation(queryId, email));
        queryVO.setFavorite(existFavoriteRelation(queryId, email));
        queryVO.setTags(tagsList(queryId));
        queryVO.setQueryResultColumns(columnsList(queryId));
        return queryVO;
    }

    private void handlerDashboardRelation(Integer queryId, Boolean dashboard, String email) {
        boolean relation = fleetMonitoringUsersService.isRelation(queryId, email);

        if (dashboard) {
            if (!relation) {
                fleetMonitoringUsersService.addDashboardRelation(queryId, email);
            }
        } else {
            if (relation) {
                fleetMonitoringUsersService.removeDashboardRelation(queryId, email);
            }
        }
    }

    private void handlerFavoriteRelation(Integer queryId, Boolean favorite, String email) {
        boolean relation = existFavoriteRelation(queryId, email);
        if (relation) {
            if (!favorite) {
                removeFavoriteRelation(queryId, email);
            }
        } else {
            if (favorite) {
                addFavoriteRelation(queryId, email);
            }
        }
    }

    private void removeFavoriteRelation(Integer queryId, String email) {
        advanceQueryUserRelationMapper.delete(new LambdaQueryWrapper<AdvanceQueryUserRelationDO>()
                .eq(AdvanceQueryUserRelationDO::getAdvanceQueryId, queryId)
                .eq(AdvanceQueryUserRelationDO::getUserEmail, email));
    }

    private void addFavoriteRelation(Integer queryId, String email) {
        AdvanceQueryUserRelationDO relationDO = new AdvanceQueryUserRelationDO();
        relationDO.setAdvanceQueryId(queryId);
        relationDO.setUserEmail(email);
        advanceQueryUserRelationMapper.insert(relationDO);
    }

    private boolean existFavoriteRelation(Integer queryId, String email) {
        LambdaQueryWrapper<AdvanceQueryUserRelationDO> wrapper = new LambdaQueryWrapper<AdvanceQueryUserRelationDO>()
                .eq(AdvanceQueryUserRelationDO::getAdvanceQueryId, queryId)
                .eq(AdvanceQueryUserRelationDO::getUserEmail, email);
        return advanceQueryUserRelationMapper.exists(wrapper);
    }

    private void handlerColumnsRelation(Integer queryId, List<String> resultColumns) {

        removeColumnsRelation(queryId);

        if (!CollectionUtils.isEmpty(resultColumns)) {
            addColumnsRelation(queryId, resultColumns);
        }
    }

    private void addColumnsRelation(Integer queryId, List<String> resultColumns) {
        queryResultColumnsService.addRelationByQueryId(queryId, resultColumns);
    }

    private void removeColumnsRelation(Integer queryId) {
        queryResultColumnsService.removeRelationByQueryId(queryId);
    }

    private void handlerTagsRelation(Integer queryId, List<String> tagsId) {

        removeTagsRelation(queryId);

        if (!CollectionUtils.isEmpty(tagsId)) {
            addTagsRelation(queryId, tagsId);
        }
    }

    private void addTagsRelation(Integer queryId, List<String> tagsId) {
        tagsService.addTagsRelation(queryId, tagsId);
    }

    private void removeTagsRelation(Integer queryId) {
        tagsService.removeTagsRelation(queryId);
    }

    public PageResult<AdvanceQueryVO> favourites(AdvanceQueryFavouritesPO po) {
        String userEmail = RequestUtil.getLoginUserEmail();

        LambdaQueryWrapper<AdvanceQueryUserRelationDO> relationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        relationLambdaQueryWrapper.eq(AdvanceQueryUserRelationDO::getUserEmail, userEmail)
                        .orderByDesc(AdvanceQueryUserRelationDO::getAdvanceQueryId);
        Page<AdvanceQueryUserRelationDO> page = new Page<>(po.getCurrent(), po.getPageSize());
        Page<AdvanceQueryUserRelationDO> doPage = advanceQueryUserRelationMapper.selectPage(page, relationLambdaQueryWrapper);
        if (doPage.getTotal() == 0) {
            return PageResult.toResponse(Collections.emptyList(), 0L, po.getCurrent(), po.getPageSize());
        }

        Set<Integer> queryIds = doPage.getRecords().stream().map(AdvanceQueryUserRelationDO::getAdvanceQueryId).collect(Collectors.toSet());
        LambdaQueryWrapper<AdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AdvanceQueriesDO::getQueryId, queryIds)
                .like(StringUtils.isNotBlank(po.getQueryName()), AdvanceQueriesDO::getQueryName, po.getQueryName())
                .orderByDesc(AdvanceQueriesDO::getQueryId);
        List<AdvanceQueryVO> list = advanceQueriesMapper.selectList(wrapper).stream()
                .map(v -> {
                    AdvanceQueryVO queryVO = new AdvanceQueryVO();
                    BeanUtils.copyProperties(v, queryVO);
                    return queryVO;
                })
                .collect(Collectors.toList());

        return PageResult.toResponse(list, doPage.getTotal(), po.getCurrent(), po.getPageSize());
    }

    public PageResult<AdvanceQueryVO> search(AdvanceQuerySearchPO po) {
        LambdaQueryWrapper<AdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        String userEmail = RequestUtil.getLoginUserEmail();
        if (po.getShowMine()) {
            wrapper.eq(AdvanceQueriesDO::getCreatedByEmail, userEmail);
        }

        if (StringUtils.isNotBlank(po.getQueryName())) {
            wrapper.like(AdvanceQueriesDO::getQueryName, po.getQueryName());
        }

        wrapper.orderByDesc(AdvanceQueriesDO::getQueryId);

        Page<AdvanceQueriesDO> page = new Page<>(po.getCurrent(), po.getPageSize());
        Page<AdvanceQueriesDO> doPage = advanceQueriesMapper.selectPage(page, wrapper);
        if (doPage.getTotal() == 0) {
            return PageResult.toResponse(Collections.emptyList(), 0L, po.getCurrent(), po.getPageSize());
        }

        List<AdvanceQueryVO> list = doPage.getRecords().stream()
                .map(v -> {
                    AdvanceQueryVO queryVO = new AdvanceQueryVO();
                    BeanUtils.copyProperties(v, queryVO);
                    return queryVO;
                })
                .collect(Collectors.toList());
        return PageResult.toResponse(list, doPage.getTotal(), po.getCurrent(), po.getPageSize());
    }

    public List<TagsVO> tagsList(Integer queryId) {
        List<TagsDO> allTags = tagsService.getAllTags();
        if (CollectionUtils.isEmpty(allTags)) {
            return Collections.emptyList();
        }
        List<Integer> tagIds = new ArrayList<>();
        if (queryId != null) {
            List<Integer> tags = tagsService.getTagIdsByQueryId(queryId);
            if (!CollectionUtils.isEmpty(tags)) {
                tagIds = tags;
            }
        }

        // 获取全部tags列表，未选中
        List<Integer> finalTagIds = tagIds;
        return allTags.stream().map(v -> {
            TagsVO tagsVO = new TagsVO();
            BeanUtils.copyProperties(v, tagsVO);
            tagsVO.setIsSelected(finalTagIds.contains(v.getTagId()));
            return tagsVO;
        }).collect(Collectors.toList());
    }


    public List<QueryResultColumnsVO> columnsList(Integer queryId) {
        List<QueryResultColumnsDO> list = queryResultColumnsService.getAllColumns();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> columns = new ArrayList<>();
        if (queryId != null) {
            List<String> columnsByQueryId = queryResultColumnsService.getColumnsByQueryId(queryId);
            if (!CollectionUtils.isEmpty(columnsByQueryId)) {
                columns = columnsByQueryId;
            }
        }

        // 获取全部tags列表，未选中
        List<String> finalColumns = columns;
        return list.stream().map(v -> {
            QueryResultColumnsVO vo = new QueryResultColumnsVO();
            BeanUtils.copyProperties(v, vo);
            vo.setIsSelected(finalColumns.contains(v.getColumn()));
            return vo;
        }).collect(Collectors.toList());
    }

    public QueryResultVO result(Integer queryId) {
        AdvanceQueriesDO queryDO = advanceQueriesMapper.selectById(queryId);
        if (queryDO == null) {
            throw new BizException("查询不存在");
        }

        List<Integer> tagIds = tagsService.getTagIdsByQueryId(queryId);
        List<String> inverterSerialNumbers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tagIds)) {
            List<InverterTagsDO> invertersTags = inverterService.getInvertersByTags(tagIds);
            if (CollectionUtils.isEmpty(invertersTags)) {
                return QueryResultVO.builder().inverterList(Collections.emptyList()).build();
            }

            inverterSerialNumbers = invertersTags.stream().map(InverterTagsDO::getSerialNumber).collect(Collectors.toList());
        }

        List<InvertersDO> inverters = inverterService.getInverterByQuery(queryDO.getQuery());
        if (CollectionUtils.isEmpty(inverters)) {
            return QueryResultVO.builder().inverterList(Collections.emptyList()).build();
        }

        if (!CollectionUtils.isEmpty(inverterSerialNumbers)) {
            List<String> finalInverterSerialNumbers = inverterSerialNumbers;
            inverters = inverters.stream().filter(Objects::nonNull).filter(v -> finalInverterSerialNumbers.contains(v.getSerialNumber())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(inverters)) {
            return QueryResultVO.builder().inverterList(Collections.emptyList()).build();
        }

        List<String> columns = queryResultColumnsService.getColumnsByQueryId(queryId);
        List<Map<String, Object>> inverterList = inverters.stream().map(v -> mapInverter2Row(v, columns)).collect(Collectors.toList());

        return QueryResultVO.builder().inverterList(inverterList).build();
    }


    public Map<String, Object> mapInverter2Row(InvertersDO invertersDO, List<String> columns) {
        if (CollectionUtils.isEmpty(columns)) {
            Map<String, Object> map = new HashMap<>();
            map.put(Tags.SerialNumber, invertersDO.getSerialNumber());
            map.put(Tags.InverterModelName, invertersDO.getInverterModelName());
            return map;
        }


        // 这里直接取inverters表，如果要求保持跟详情数据一致性，可以参考 fix: bms version number 这次提交后的此方法
        Map<String, Object> map = new HashMap<>();

        map.put(Tags.SerialNumber, invertersDO.getSerialNumber());
        map.put(Tags.BMS, invertersDO.getHasBMS() != null ? invertersDO.getHasBMS() : false);
        map.put(Tags.DetectedBatteryMan, invertersDO.getDetectedBatteryManufacturer() != null ? invertersDO.getDetectedBatteryManufacturer() : "");
        map.put(Tags.FirmwareVersion, invertersDO.getInverterFirmware() != null ? invertersDO.getInverterFirmware() : "");
        map.put(Tags.DetectedBatteryModel, invertersDO.getDetectedBatteryModel() != null ? invertersDO.getDetectedBatteryModel() : "");
        map.put(Tags.IsDailyOnline, invertersDO.getIsDailyOnline());
        map.put(Tags.IsInstalled, invertersDO.getIsInstalled() != null ? invertersDO.getIsInstalled() : false);
        map.put(Tags.IsHourlyOnline, invertersDO.getIsHourlyOnline());
        map.put(Tags.OwnerName, invertersDO.getProductOwner() != null ? invertersDO.getProductOwner() : "");
        map.put(Tags.ROSSVersion, invertersDO.getRossVersion() != null ? invertersDO.getRossVersion().replace('_', ',') : "");
        map.put(Tags.WDVersion, invertersDO.getWatchDogVersion() != null ? invertersDO.getWatchDogVersion() : "");
        map.put(Tags.SCCMHeartbeat, invertersDO.getIsSCCMOnline() != null ? invertersDO.getIsSCCMOnline() : false);
        map.put(Tags.OriginalInstallerEmail, invertersDO.getOriginalInstallerEmail() != null ? invertersDO.getOriginalInstallerEmail() : "");
        map.put(Tags.MaintainingInstallerEmail, invertersDO.getMaintainingInstallerEmail() != null ? invertersDO.getMaintainingInstallerEmail() : "");
        map.put(Tags.ProductOwnerEmail, invertersDO.getProductOwnerEmail() != null ? invertersDO.getProductOwnerEmail() : "");
        map.put(Tags.OwnerPhoneNumber, invertersDO.getOwnerPhoneNumber() != null ? invertersDO.getOwnerPhoneNumber() : "");
        map.put(Tags.Timestamp_Brisbane, invertersDO.getSystemStatusTimeStampBrisbane() != null
                ? invertersDO.getSystemStatusTimeStampBrisbane().toString() : "No Data");
        map.put(Tags.IsOffComms, invertersDO.getOffComms() != null ? invertersDO.getOffComms() : false);
        map.put(Tags.IsDailyOnline_WD, invertersDO.getIsDailyOnlineWD() != null ? invertersDO.getIsDailyOnlineWD() : false);
        map.put(Tags.IsHourlyOnline_WD, invertersDO.getIsHourlyOnlineWD() != null ? invertersDO.getIsHourlyOnlineWD() : false);
        map.put(Tags.WDHeartbeatTimeStamp_Brisbane, invertersDO.getWdHeartbeatTimeStampBrisbane() != null
                ? invertersDO.getWdHeartbeatTimeStampBrisbane().toString() : "No Data");
        map.put(Tags.SoC, invertersDO.getSoc() != null ? invertersDO.getSoc() : 0);
        map.put(Tags.Postcode, invertersDO.getPostcode() != null ? invertersDO.getPostcode() : "");
        map.put(Tags.State, invertersDO.getState() != null ? invertersDO.getState() : "");
        map.put(Tags.FullAddress, invertersDO.getAddress() != null ? invertersDO.getAddress() : "");
        map.put(Tags.Suburb, invertersDO.getSuburb() != null ? invertersDO.getSuburb() : "");
        map.put(Tags.Installer, invertersDO.getOriginalInstaller() != null ? invertersDO.getOriginalInstaller() : "");
        map.put(Tags.MaintainingInstaller, invertersDO.getMaintainingInstaller() != null ? invertersDO.getMaintainingInstaller() : "");
        map.put(Tags.MaintainingInstallerPhoneNumber, invertersDO.getMaintainingInstallerPhoneNumber() != null ? invertersDO.getMaintainingInstallerPhoneNumber() : "");
        map.put(Tags.OriginalInstaller, invertersDO.getOriginalInstaller() != null ? invertersDO.getOriginalInstaller() : "");
        map.put(Tags.Comment, invertersDO.getComment() != null ? invertersDO.getComment() : "");
        map.put(Tags.WindowsVersion, invertersDO.getWindowsVersion() != null ? invertersDO.getWindowsVersion() : "");
        map.put(Tags.WindowsVersion_SS, invertersDO.getWindowsVersionSS() != null ? invertersDO.getWindowsVersionSS() : "");
        map.put(Tags.Long, invertersDO.getLongitude() != null ? invertersDO.getLongitude() : "");
        map.put(Tags.Lat, invertersDO.getLatitude() != null ? invertersDO.getLatitude() : "");
        map.put(Tags.BackupOn, invertersDO.getBackupOn() != null ? invertersDO.getBackupOn() : false);
        map.put(Tags.ACPower, invertersDO.getAcPower() != null ? Math.round(invertersDO.getAcPower() * 10) / 10.0 : 0);
        map.put(Tags.ACTotalToday, invertersDO.getAcTotalToday() != null ? Math.round(invertersDO.getAcTotalToday() * 10) / 10.0 : 0);
        map.put(Tags.AllTimeTotalExport, invertersDO.getAllTimeTotalExport() != null ? Math.round(invertersDO.getAllTimeTotalExport() * 10) / 10.0 : 0);
        map.put(Tags.AllTimeTotalImport, invertersDO.getAllTimeTotalImport() != null ? Math.round(invertersDO.getAllTimeTotalImport() * 10) / 10.0 : 0);
        map.put(Tags.DayTotalExport, invertersDO.getDayTotalExport() != null ? Math.round(invertersDO.getDayTotalExport() * 10) / 10.0 : 0);
        map.put(Tags.DayTotalImport, invertersDO.getDayTotalImport() != null ? Math.round(invertersDO.getDayTotalImport() * 10) / 10.0 : 0);
        map.put(Tags.GridPower, invertersDO.getGridPower() != null ? Math.round(invertersDO.getGridPower() * 10) / 10.0 : 0);
        map.put(Tags.PVPower, invertersDO.getPvPower() != null ? Math.round(invertersDO.getPvPower() * 10) / 10.0 : 0);
        map.put(Tags.PVTotalAllTime, invertersDO.getPvTotalAllTime() != null ? Math.round(invertersDO.getPvTotalAllTime() * 10) / 10.0 : 0);
        map.put(Tags.PVTotalToday, invertersDO.getPvTotalToday() != null ? Math.round(invertersDO.getPvTotalToday() * 10) / 10.0 : 0);
        map.put(Tags.InverterModelName, invertersDO.getInverterModelName() != null ? invertersDO.getInverterModelName() : "");
        map.put(Tags.BatteryCapacity, invertersDO.getBatteryCapacity() != null ? invertersDO.getBatteryCapacity() : 0);
        map.put(Tags.BatteryChargeCurrent_BMS, invertersDO.getBatteryChargeCurrentBMS() != null ? invertersDO.getBatteryChargeCurrentBMS() : 0);
        map.put(Tags.BatteryChargeCurrent_Custom, invertersDO.getBatteryChargeCurrentCustom() != null ? invertersDO.getBatteryChargeCurrentCustom() : 0);
        map.put(Tags.BatteryChargeCurrent_Override, invertersDO.getBatteryChargeCurrentOverride() != null ? invertersDO.getBatteryChargeCurrentOverride() : 0);
        map.put(Tags.BatteryDisChargeCurrent_BMS, invertersDO.getBatteryDisChargeCurrentBMS() != null ? invertersDO.getBatteryDisChargeCurrentBMS() : 0);
        map.put(Tags.BatteryDisChargeCurrent_Custom, invertersDO.getBatteryDisChargeCurrentCustom() != null ? invertersDO.getBatteryDisChargeCurrentCustom() : 0);
        map.put(Tags.BatteryDisChargeCurrent_Override, invertersDO.getBatteryDisChargeCurrentOverride() != null ? invertersDO.getBatteryDisChargeCurrentOverride() : 0);
        map.put(Tags.FanMode, invertersDO.getFanMode() != null ? invertersDO.getFanMode() : "");
        map.put(Tags.LimitExportPower, invertersDO.getLimitExportPower() != null ? invertersDO.getLimitExportPower() : 0);
        map.put(Tags.LimitExportPowerUserValue, invertersDO.getLimitExportPowerUserValue() != null ? invertersDO.getLimitExportPowerUserValue() : 0);
        map.put(Tags.IsLimitExportPower, invertersDO.getIsLimitExportPower() != null ? invertersDO.getIsLimitExportPower() : false);
        map.put(Tags.MinimumSoC, invertersDO.getMinimumSoC() != null ? invertersDO.getMinimumSoC() : 0);
        map.put(Tags.MinimumSoCOffGrid, invertersDO.getMinimumSoCOffGrid() != null ? invertersDO.getMinimumSoCOffGrid() : 0);
        map.put(Tags.OffGridCharge, invertersDO.getOffGridCharge() != null ? invertersDO.getOffGridCharge() : false);
        map.put(Tags.BatteryChargeVoltage_BMS, invertersDO.getBatteryChargeVoltageBMS() != null ? invertersDO.getBatteryChargeVoltageBMS() : 0);
        map.put(Tags.BatteryChargeVoltage_Custom, invertersDO.getBatteryChargeVoltageCustom() != null ? invertersDO.getBatteryChargeVoltageCustom() : 0);
        map.put(Tags.CTComms, invertersDO.getCtComms() != null ? invertersDO.getCtComms() : false);
        map.put(Tags.GridVoltage, invertersDO.getGridVoltage() != null ? invertersDO.getGridVoltage() : 0);
        map.put(Tags.InverterTime, invertersDO.getInverterTime() != null ? invertersDO.getInverterTime().toString() : "No Data");
        map.put(Tags.WindowsTime, invertersDO.getWindowsTime() != null ? invertersDO.getWindowsTime().toString() : "No Data");
        map.put(Tags.ActiveInstallationDate, invertersDO.getActiveInstallationDate());
        map.put(Tags.BMSVersionNumber, invertersDO.getBmsVersionNumber());
        map.put(Tags.BatteryPower, invertersDO.getBatteryPower());

        Map<String, Object> result = new HashMap<>();
        columns.forEach(column -> {
            if (map.containsKey(column)) {
                result.put(column, map.get(column));
            }
        });

        result.put(Tags.SerialNumber, map.get(Tags.SerialNumber));
        result.put(Tags.InverterModelName, map.get(Tags.InverterModelName));
        return result;
    }
}