package com.ebon.energy.fms.service;// Copyright (c) Redback Technologies. All Rights Reserved.

import com.ebon.energy.fms.domain.vo.BatteryModelInfoDto;
import com.ebon.energy.fms.domain.vo.MinOffgridSocDto;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class BatteryModelInfo {

    private Map<String, BatteryModelInfoDto> batteryModelInfos = new HashMap<>();

//    /**
//     * 仅用于测试
//     * @param config 配置字符串
//     */
//    public BatteryModelInfo(String config) {
//        if (config != null && !config.isEmpty()) {
//            try {
//                ObjectMapper objectMapper = new ObjectMapper();
//                List<BatteryModelInfoDto> configData = objectMapper.readValue(
//                    config,
//                    new TypeReference<List<BatteryModelInfoDto>>() {}
//                );
//
//                batteryModelInfos = configData.stream()
//                    .collect(Collectors.toMap(
//                        BatteryModelInfoDto::getModelName,
//                        dto -> dto
//                    ));
//            } catch (Exception e) {
//                throw new RuntimeException("解析配置失败", e);
//            }
//        }
//    }

    public Map<String, BatteryModelInfoDto> getBatteryModelInfos() {
        return batteryModelInfos;
    }

    public Integer replaceMinOffgridSoc(String batteryModel, Double firmwareVersion) {
        Optional<Map.Entry<String, BatteryModelInfoDto>> searchModel = 
            batteryModelInfos.entrySet().stream()
                .filter(entry -> entry.getKey().equals(batteryModel))
                .findFirst();

        if (searchModel.isPresent() && 
            searchModel.get().getValue() != null && 
            searchModel.get().getValue().getMinOffgridSoc() != null) {
            
            Integer newSoc = newOffgridSocValue(
                firmwareVersion, 
                searchModel.get().getValue().getMinOffgridSoc()
            );

            return newSoc;
        }

        return null;
    }

    Integer newOffgridSocValue(Double firmwareVersion, MinOffgridSocDto[] minOffgridSoc) {
        List<MinOffgridSocDto> minOffgridSocList = Arrays.asList(minOffgridSoc);

        // 检查固件为null的值是否为唯一值
        long nullFirmwareCount = minOffgridSocList.stream()
            .filter(p -> p.getFirmWare() == null)
            .count();

        if (nullFirmwareCount > 0 && minOffgridSocList.size() > 1) {
            throw new RuntimeException("newOffgridSocValue: 固件为null只能是唯一值");
        }

        if (nullFirmwareCount > 0) {
            return minOffgridSocList.stream()
                .filter(p -> p.getFirmWare() == null)
                .findFirst()
                .map(MinOffgridSocDto::getValue)
                .orElse(null);
        }

        class FirmwareCheck {
            private final Double firmWare;
            private final Integer value;
            private final boolean isInRange;

            public FirmwareCheck(Double firmWare, Integer value, boolean isInRange) {
                this.firmWare = firmWare;
                this.value = value;
                this.isInRange = isInRange;
            }

            public Integer getValue() { return value; }
            public boolean isInRange() { return isInRange; }
        }

        List<FirmwareCheck> checkedOrderedList = minOffgridSocList.stream()
            .sorted((a, b) -> Double.compare(
                b.getFirmWare() != null ? b.getFirmWare() : 0.0,
                a.getFirmWare() != null ? a.getFirmWare() : 0.0
            ))
            .map(p -> new FirmwareCheck(
                p.getFirmWare(),
                p.getValue(),
                firmwareVersion != null && p.getFirmWare() != null && 
                firmwareVersion >= p.getFirmWare()
            ))
            .collect(Collectors.toList());

        return checkedOrderedList.stream()
            .filter(FirmwareCheck::isInRange)
            .findFirst()
            .map(FirmwareCheck::getValue)
            .orElse(null);
    }
}
