package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.domain.entity.TagAdvanceQueriesDO;
import com.ebon.energy.fms.domain.entity.TagsDO;
import com.ebon.energy.fms.mapper.second.TagAdvanceQueriesMapper;
import com.ebon.energy.fms.mapper.second.TagsMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TagsService {

    @Resource
    private TagsMapper tagsMapper;

    @Resource
    private TagAdvanceQueriesMapper tagAdvanceQueriesMapper;

    public TagsDO getTagById(Integer tagId) {
        return tagsMapper.selectById(tagId);
    }

    public List<TagsDO> getAllTags() {
        return tagsMapper.selectList(new QueryWrapper<>());
    }

    public List<TagsDO> getTagsByIds(List<Integer> tagIds) {
        LambdaQueryWrapper<TagsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TagsDO::getTagId, tagIds);
        return tagsMapper.selectList(queryWrapper);
    }

    public List<Integer> getTagIdsByQueryId(Integer queryId) {
        LambdaQueryWrapper<TagAdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TagAdvanceQueriesDO::getQueryId, queryId);
        List<TagAdvanceQueriesDO> dos = tagAdvanceQueriesMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dos)) {
            return Collections.emptyList();
        }
        return dos.stream().map(TagAdvanceQueriesDO::getTagId).collect(Collectors.toList());
    }


    public List<TagsDO> getTagsByQueryId(Integer queryId) {
        LambdaQueryWrapper<TagAdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TagAdvanceQueriesDO::getQueryId, queryId);
        List<TagAdvanceQueriesDO> dos = tagAdvanceQueriesMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dos)) {
            return Collections.emptyList();
        }

        return getTagsByIds(dos.stream().map(TagAdvanceQueriesDO::getTagId).collect(Collectors.toList()));
    }

    public void addTagsRelation(Integer queryId, List<String> tagsId) {

        if (!CollectionUtils.isEmpty(tagsId)) {
            tagsId.forEach(tagId -> {
                TagAdvanceQueriesDO tagAdvanceQueriesDO = new TagAdvanceQueriesDO();
                tagAdvanceQueriesDO.setQueryId(queryId);
                tagAdvanceQueriesDO.setTagId(Integer.valueOf(tagId));
                tagAdvanceQueriesMapper.insert(tagAdvanceQueriesDO);
            });
        }
    }

    public void removeTagsRelation(Integer queryId) {
        LambdaQueryWrapper<TagAdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TagAdvanceQueriesDO::getQueryId, queryId);
        tagAdvanceQueriesMapper.delete(wrapper);
    }
}
