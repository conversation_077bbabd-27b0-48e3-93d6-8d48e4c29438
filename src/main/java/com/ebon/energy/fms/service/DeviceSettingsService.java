package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.domain.entity.ProductDeviceSettingsDO;
import com.ebon.energy.fms.mapper.third.DeviceSettingsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class DeviceSettingsService {

    @Autowired
    private DeviceSettingsMapper deviceSettingsMapper;

    public ProductDeviceSettingsDO getProductDeviceSettings(String serialNumber) {
        List<ProductDeviceSettingsDO> result = deviceSettingsMapper.selectWithProduct(serialNumber, ApplicationName.Ross.name());
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }

        return result.get(0);
    }
}