package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.AppVersionManagementDO;
import com.ebon.energy.fms.domain.po.AppVersionManagePagePO;
import com.ebon.energy.fms.domain.po.AppVersionQuery;
import com.ebon.energy.fms.domain.po.CreateAppVersionPO;
import com.ebon.energy.fms.domain.po.ModifyAppVersionPO;
import com.ebon.energy.fms.domain.vo.AppVersionManagementVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.repository.AppVersionManagementRepository;
import com.ebon.energy.fms.repository.converter.AppVersionManagementConverter;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.enums.CommonErrorCodeEnum.APP_VERSION_EXIST;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Service
@RequiredArgsConstructor
public class AppVersionManagementService {

    private final AppVersionManagementRepository appVersionManagementRepository;

    public PageResult<AppVersionManagementVO> getAppVersionPage(AppVersionManagePagePO appVersionManagePagePo) {
        IPage<AppVersionManagementDO> appVersionManagementPage = appVersionManagementRepository.selectAppVersionPage(appVersionManagePagePo);
        if (appVersionManagementPage != null) {
            List<AppVersionManagementVO> records = AppVersionManagementConverter.INSTANCE.batchToVo(appVersionManagementPage.getRecords());
            return PageResult.toResponse(records, appVersionManagementPage.getTotal(),
                    appVersionManagePagePo.getCurrent(), appVersionManagePagePo.getPageSize());
        }
        return null;
    }

    public void createAppVersion(CreateAppVersionPO createAppVersionPo) {
        createAppVersionPo.checkParams();
        AppVersionQuery appVersionQuery = AppVersionQuery.builder()
                .product(createAppVersionPo.getProduct())
                .osType(createAppVersionPo.getOsType())
                .version(createAppVersionPo.getVersion())
                .status(createAppVersionPo.getStatus())
                .build();
        Optional<AppVersionManagementDO> appVersionManagementOptional = appVersionManagementRepository.selectByProductAndOsType(appVersionQuery);
        if (appVersionManagementOptional.isPresent()) {
            throw new BizException(APP_VERSION_EXIST);
        }
        String userId = RequestUtil.getLoginUserEmail();
        AppVersionManagementDO appversionManamentDO = AppVersionManagementConverter.INSTANCE.toAppversionManamentDO(createAppVersionPo, userId);
        appVersionManagementRepository.save(appversionManamentDO);
    }

    public void modifyAppVersion(ModifyAppVersionPO modifyAppVersionPo) {
        modifyAppVersionPo.checkParams();
        appVersionManagementRepository.modify(modifyAppVersionPo);
    }
}
