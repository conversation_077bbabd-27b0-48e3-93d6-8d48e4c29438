package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.ebon.energy.fms.mapper.primary.UserMapper;
import com.ebon.energy.fms.util.CookieUtil;
import com.ebon.energy.fms.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import static com.ebon.energy.fms.common.constants.AuthorizeConstants.BASE_SALT;
import static com.ebon.energy.fms.common.constants.AuthorizeConstants.COOKIE_TOKEN_KEY;

@Slf4j
@Service
public class UserService {

    @Resource
    private JwtService jwtService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private FleetMonitoringUsersService fleetMonitoringUsersService;

    public void login(String email, String password, HttpServletResponse response) {
        FmsUserDO fmsUserDO = userMapper.selectByEmail(email);
        if (fmsUserDO == null) {
            throw new BizException(CommonErrorCodeEnum.USER_NOT_EXIST);
        }

        if (!checkPassword(email, password, fmsUserDO.getPassword())) {
            throw new BizException(CommonErrorCodeEnum.PASSWORD_ERROR);
        }

        String jwtToken = jwtService.generateToken(fmsUserDO.getEmail());
        CookieUtil.create(response, COOKIE_TOKEN_KEY, jwtToken, false);

        // 老系统生成此用户
        if (!fleetMonitoringUsersService.existInOldVersion(email)) {
            fleetMonitoringUsersService.addUserInOldVersion(email);
        }
    }

    public void loginOut(HttpServletResponse response) {
        CookieUtil.clear(response, COOKIE_TOKEN_KEY);
    }


    private boolean checkPassword(String email, String inputPassword, String passwordInDb) {
        return getPasswordWithSalt(inputPassword, email).equals(passwordInDb);
    }

    public UserVO getUserByEmail(String email) {
        FmsUserDO userDO = userMapper.selectByEmail(email);
        if(userDO == null){
            return null;
        }

        return UserVO.builder().email(userDO.getEmail()).portalUserId(userDO.getPortalUserId()).build();
    }

    private String getPasswordWithSalt(String password, String salt) {
        return MD5Util.getMD5(salt + password + BASE_SALT);
    }

}