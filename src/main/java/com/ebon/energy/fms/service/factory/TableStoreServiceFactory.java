package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.service.TableStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

@Service
public class TableStoreServiceFactory {
    private final Map<CloudPlatformName, TableStoreService> services;

    @Autowired
    public TableStoreServiceFactory(List<TableStoreService> tableStoreServices) {
        services = new EnumMap<>(CloudPlatformName.class);
        tableStoreServices.forEach(service -> services.put(service.getCloudPlatformName(), service));
    }

    public TableStoreService getService(CloudPlatformName type) {
        TableStoreService service = services.get(type);
        if (service == null) {
            throw new IllegalArgumentException("No service found for type: " + type);
        }
        return service;
    }
}