package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.domain.entity.FleetMonitoringUserAdvanceQueriesDO;
import com.ebon.energy.fms.domain.entity.FleetMonitoringUsersDO;
import com.ebon.energy.fms.mapper.second.FleetMonitoringUserAdvanceQueriesMapper;
import com.ebon.energy.fms.mapper.second.FleetMonitoringUsersMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FleetMonitoringUsersService {

    @Resource
    private FleetMonitoringUserAdvanceQueriesMapper fleetMonitoringUserAdvanceQueriesMapper;

    @Resource
    private FleetMonitoringUsersMapper fleetMonitoringUsersMapper;

    public void addDashboardRelation(Integer queryId, String email) {
        FleetMonitoringUserAdvanceQueriesDO queryDO = new FleetMonitoringUserAdvanceQueriesDO();
        queryDO.setAdvanceQueryId(queryId);
        queryDO.setUserEmail(email);
        fleetMonitoringUserAdvanceQueriesMapper.insert(queryDO);
    }

    public void removeDashboardRelation(Integer queryId, String email) {
        LambdaQueryWrapper<FleetMonitoringUserAdvanceQueriesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FleetMonitoringUserAdvanceQueriesDO::getUserEmail, email)
                        .eq(FleetMonitoringUserAdvanceQueriesDO::getAdvanceQueryId, queryId);
        fleetMonitoringUserAdvanceQueriesMapper.delete(queryWrapper);
    }

    public boolean isRelation(Integer queryId, String email) {
        LambdaQueryWrapper<FleetMonitoringUserAdvanceQueriesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FleetMonitoringUserAdvanceQueriesDO::getUserEmail, email)
                .eq(FleetMonitoringUserAdvanceQueriesDO::getAdvanceQueryId, queryId);
        return fleetMonitoringUserAdvanceQueriesMapper.exists(queryWrapper);
    }

    public boolean existInOldVersion(String email) {
        LambdaQueryWrapper<FleetMonitoringUsersDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FleetMonitoringUsersDO::getEmail, email);
        return fleetMonitoringUsersMapper.exists(queryWrapper);
    }

    public void addUserInOldVersion(String email) {
        FleetMonitoringUsersDO userDO = new FleetMonitoringUsersDO();
        userDO.setEmail(email);
        fleetMonitoringUsersMapper.insert(userDO);
    }
}
