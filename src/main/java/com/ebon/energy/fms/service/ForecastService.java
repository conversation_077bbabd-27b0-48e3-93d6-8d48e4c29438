package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.domain.entity.ProductDailyForecastDO;
import com.ebon.energy.fms.mapper.primary.ProductDailyForecastMapper;
import com.ebon.energy.fms.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ForecastService {
    
    @Resource
    private ProductDailyForecastMapper productDailyForecastMapper;

    public List<String> getInverterSns() {
        String res = HttpClientUtil.get("http://redback-forecast.redback.svc.ebonex-newtest:8000/api/generation/list-sns");
        return JSONObject.parseArray(res, String.class);
    }

    public Map<String, BigDecimal> getDailyGeneration(String sn) {
        String res = HttpClientUtil.get("http://redback-forecast.redback.svc.ebonex-newtest:8000/api/generation/predict?sn=" + sn);
        return JSONObject.parseObject(res, HashMap.class);
    }
    
    public void saveBatch(List<ProductDailyForecastDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        
        productDailyForecastMapper.insertBatch(list);
    }
}
