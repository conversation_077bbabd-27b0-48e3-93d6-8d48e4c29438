package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.domain.entity.DailyTotalVO;
import com.ebon.energy.fms.domain.vo.AllAboutDevice;
import com.ebon.energy.fms.domain.vo.AllAboutSiteOverview;
import io.vavr.Tuple2;

import java.util.List;

public interface ISiteConfigurationAggregator {

    /**
     * 处理站点概览信息
     * @param publicSiteId 站点公共ID
     * @param siteDevices 站点设备列表
     * @return 站点概览信息
     */
    AllAboutSiteOverview processSiteOverview(
            String publicSiteId,
            List<AllAboutDevice> siteDevices);


    /**
     * 处理站点N天历史数据
     * @param publicSiteId 站点公共ID
     * @param deviceHistories 设备历史数据对(设备, 总计列表)
     * @param twiceDays 双倍天数
     * @return 站点历史总计
     */
    List<DailyTotalVO> processSiteNDayHistory(
            String publicSiteId,
            List<Tuple2<AllAboutDevice, List<DailyTotalVO>>> deviceHistories,
            int twiceDays);

}
