package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.DirectMethodsDO;
import com.ebon.energy.fms.domain.po.DirectMethodSendPO;
import com.ebon.energy.fms.domain.vo.DeviceVO;
import com.ebon.energy.fms.domain.vo.DirectMethodsVO;
import com.ebon.energy.fms.domain.vo.GridTieFirmwareVersionsUpdateErrorVO;
import com.ebon.energy.fms.domain.vo.SendDirectMethodErrorVO;
import com.ebon.energy.fms.mapper.second.DirectMethodsMapper;
import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.azure.IoTHubUtility;
import com.ebon.energy.fms.util.tuya.TuyaApiUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DirectMethodsService {

    @Resource
    private DirectMethodsMapper directMethodsMapper;

    @Resource
    private DeviceService deviceService;

    @Resource
    private IoTHubUtility ioTHubUtility;

    @Resource
    private TuyaApiUtil tuyaApiUtil;

    public List<DirectMethodsVO> getAllDirectMethods() {
        LambdaQueryWrapper<DirectMethodsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(DirectMethodsDO::getId);
        List<DirectMethodsDO> dos = directMethodsMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dos)) {
            return Collections.emptyList();
        }

        return dos.stream().map(v -> {
            DirectMethodsVO directMethodsVO = new DirectMethodsVO();
            BeanUtils.copyProperties(v, directMethodsVO);
            return directMethodsVO;
        }).collect(Collectors.toList());
    }

    public List<SendDirectMethodErrorVO> sendDirectMethod(DirectMethodSendPO po) {
        String userId = RequestUtil.getLoginUserEmail();

        List<SendDirectMethodErrorVO> errors = new ArrayList<>();
        for (int i = 0; i < po.getSerialNumbers().size(); i++) {
            String serialNumber = po.getSerialNumbers().get(i);
            try {
                DeviceVO device = deviceService.getLastDevice(serialNumber, ApplicationName.Ross.name());
                if (Objects.isNull(device)) {
                    throw new BizException("404", "Device not found");
                }

                if (CloudPlatformName.Tuya.name().equalsIgnoreCase(device.getCloudPlatformName())) {
                    tuyaApiUtil.sendInvokeThingServiceCommandToTuya(device.getDeviceId(), po.getMethodName(), po.getPayload());
                } else {
                    ioTHubUtility.sendDirectMethodWithPayload(device.getDeviceId(), po.getMethodName(), StringUtils.isBlank(po.getPayload()) ? null : po.getPayload());
                }
            } catch (Exception e) {
                SendDirectMethodErrorVO vo =  SendDirectMethodErrorVO.builder()
                        .serialNumber(serialNumber)
                        .error(e.getMessage())
                        .build();
                errors.add(vo);
            }
        }

        return errors;
    }
}
