package com.ebon.energy.fms.service.product.control;


import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.vo.product.control.ProductControlViewModel;

/**
 * Interface for product control adaptors
 */
public interface IProductControlAdaptor {
    /**
     * Get the product control view model for a specific serial number
     * 
     * @param serialNumber The product serial number
     * @return The product control view model
     */
    ProductControlViewModel getViewModel(String serialNumber);


    InstallationSpecification getProductCapabilities(String serialNumber);


    void updateAsync(ProductControlViewModel model);

}
