package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.HardwareTypeEnum;
import com.ebon.energy.fms.config.SupportedHardwareConfigurations;
import com.ebon.energy.fms.domain.entity.ModelInfoDO;
import com.ebon.energy.fms.domain.vo.*;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class HardwareModelService {

    private static final Pattern trailingSpacePattern = Pattern.compile("[^a-zA-Z0-9_]");

    public HardwareModelEnum getHardwareModel(String serialNumber, ModelInfoDO modelInfoDO) {
        DesiredAndReportedVO desiredAndReported = new DesiredAndReportedVO();
        desiredAndReported.setDesired(JSONObject.parseObject(modelInfoDO.getDesired(), RossDesiredSettingsVO.class));
        desiredAndReported.setReported(JSONObject.parseObject(modelInfoDO.getReported(), RossReportedSettingsVO.class));
        HardwareModelEnum hardwareModel = getHardwareModel(desiredAndReported, modelInfoDO.getModelName());
        if (hardwareModel == HardwareModelEnum.Unknown) {
            hardwareModel = determineFromSerialNumber(serialNumber);
        }
        return hardwareModel;
    }

    public HardwareCapabilityVO getHardwareFirmwareSpecification(HardwareModelEnum hardwareModel, int armVersion) {
        if (hardwareModel == HardwareModelEnum.Unknown) {
            return SupportedHardwareConfigurations.getUnsupportedInverterConfiguration().getCapabilities(
                    hardwareModel);
        }

        Optional<HardwareSettingsVO> configOptional = tryIdentifyDevice(HardwareTypeEnum.Inverter, hardwareModel, armVersion).stream().findFirst();
        HardwareSettingsVO config = configOptional.orElse(null);

        if (config == null || config.getHardwareFamily() == HardwareFamilyEnum.Unknown) {
            configOptional = tryIdentifyDevice(HardwareTypeEnum.Inverter, hardwareModel,0).stream().findFirst();
            config = configOptional.orElse(null);
            if (config == null) {
                config = SupportedHardwareConfigurations.getUnsupportedInverterConfiguration();
            }
        }

        return config.getCapabilities(hardwareModel);
    }

    public List<HardwareSettingsVO> tryIdentifyDevice(HardwareTypeEnum hardwareType, HardwareModelEnum hardwareModel, int firmwareVersion) {
        if (hardwareModel == null) {
            hardwareModel = HardwareModelEnum.Unknown;
        }

        // Start with all potential inverter adapters to use for communication attempts.
        List<HardwareSettingsVO> targetDevices = new ArrayList<>(SupportedHardwareConfigurations.getCurrent().values());
        targetDevices = targetDevices.stream()
                .filter(h -> h.getHardwareType() == hardwareType)
                .collect(Collectors.toList());

        // If a hardware model has been specified, only use that model to filter available adapters
        if (hardwareModel != HardwareModelEnum.Unknown) {
            final HardwareModelEnum hm = hardwareModel;
            targetDevices = targetDevices.stream()
                    .filter(h -> h.supportsHardwareModel(hm))
                    .collect(Collectors.toList());

            // If model name and firmware version is provided then only match on the appropriate firmware version
            if (firmwareVersion > 0) {
                targetDevices = targetDevices.stream()
                        .filter(h -> firmwareVersion >= h.getMinFirmwareVersion() && firmwareVersion <= h.getMaxFirmwareVersion())
                        .collect(Collectors.toList());
            }
        }

        return targetDevices;
    }

    private HardwareModelEnum getHardwareModel(DesiredAndReportedVO desiredAndReportedVO, String modelNameFromSS) {
        // Use desired.Settings.Inverter.ModelName > reported.Settings.Inverter.ModelName > SystemStatus.Inverter.ModelName
        String modelName = Optional.ofNullable(desiredAndReportedVO)
                .map(DesiredAndReportedVO::getDesired)
                .map(RossDesiredSettingsVO::getInverter)
                .map(InverterDesiredSettingsVO::getModelName)
                .filter(name -> !name.isEmpty())
                .orElseGet(() -> Optional.ofNullable(desiredAndReportedVO)
                        .map(DesiredAndReportedVO::getReported)
                        .map(RossReportedSettingsVO::getInverter)
                        .map(InverterReportedSettingsVO::getModelName)
                        .filter(name -> !name.isEmpty())
                        .orElse(modelNameFromSS));
        return parseModelName(modelName);
    }

    private HardwareModelEnum determineFromSerialNumber(String serialNumber) {
        // Ensure we have something that looks like a serial number
        if (serialNumber == null || serialNumber.trim().isEmpty() || serialNumber.length() < 16) {
            return HardwareModelEnum.Unknown;
        }

        // Ensure model designation portion looks valid
        String modelDesignation = serialNumber.substring(8, 12);
        try {
            int modelVersion = Integer.parseInt(modelDesignation);
            // Please note the '- 100'
            modelVersion = modelVersion - 100;
            if (modelVersion < 100) {
                return HardwareModelEnum.SH4600;
            } else if (modelVersion == 124) {
                return HardwareModelEnum.SH5000_N;
            } else if (modelVersion < 130) {
                return HardwareModelEnum.SH5000;
            } else if (modelVersion < 200) {
                return HardwareModelEnum.SH5000v2;
            } else if (modelVersion == 213) {
                return HardwareModelEnum.ST10000_N;
            } else if (modelVersion < 300) {
                return HardwareModelEnum.ST10000;
            } else if (modelVersion < 400) {
                return HardwareModelEnum.SB7200;
            } else if (modelVersion == 412) {
                return HardwareModelEnum.SI5000_N;
            } else if (modelVersion < 500) {
                return HardwareModelEnum.SI5000;
            } else if (modelVersion == 512) {
                return HardwareModelEnum.SI6000_N;
            } else if (modelVersion < 600) {
                return HardwareModelEnum.SI6000;
            } else if (modelVersion == 612) {
                return HardwareModelEnum.SI8000_N;
            } else if (modelVersion < 700) {
                return HardwareModelEnum.SI8000;
            } else if (modelVersion == 712) {
                return HardwareModelEnum.SI10000_N;
            } else if (modelVersion < 800) {
                return HardwareModelEnum.SI10000;
            } else if (modelVersion < 900) {
                return HardwareModelEnum.SB9600;
            } else if (modelVersion < 1000) {
                return HardwareModelEnum.SB14200;
            } else if (modelVersion < 1100) {
                return HardwareModelEnum.SH5000_G3;
            } else if (modelVersion < 1200) {
                return HardwareModelEnum.EH1P5K;
            } else if (modelVersion < 1300) {
                return HardwareModelEnum.EH1P6K;
            } else {
                return HardwareModelEnum.Unknown;
            }
        } catch (NumberFormatException e) {
            return HardwareModelEnum.Unknown;
        }
    }

    public HardwareModelEnum parseModelName(String modelName) {
        // 检查输入的模型名称是否为空或只包含空白字符
        if (modelName == null || modelName.trim().isEmpty()) {
            return HardwareModelEnum.Unknown;
        }

        // 清理模型名称，去除首尾空格、空格，并将非字母数字和下划线的字符替换为下划线
        String cleanedModelName = trailingSpacePattern.matcher(modelName.trim().replace(" ", "")).replaceAll("_");

        try {
            // 尝试将清理后的模型名称转换为 HardwareModel 枚举值
            return HardwareModelEnum.valueOf(cleanedModelName);
        } catch (IllegalArgumentException e) {
            // 如果转换失败，返回 Unknown
            return HardwareModelEnum.Unknown;
        }
    }

}
