package com.ebon.energy.fms.service.product.control;

import com.ebon.energy.fms.repository.RedbackRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Provider for product control adaptors
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductControlProvider {

    private final RedbackRepository redbackRepository;

    private final Ross1ProductControlAdaptor ross1ProductControlAdaptor;

    private final Ross2ProductControlAdaptor ross2ProductControlAdaptor;


    /**
     * Get the appropriate product control adaptor for a given serial number
     *
     * @param serialNumber The product serial number
     * @return The product control adaptor
     */
    public IProductControlAdaptor getAdaptorFor(String serialNumber) {
        try {
            log.info("Getting product control adaptor for serial number: {}", serialNumber);

            // Get user ID (in a real implementation, this would come from authentication)
            String userId = "current-user";

            var product = redbackRepository.getProductDTO(serialNumber);

            if (product.getRossVersion().isRoss2OrAbove()) {
                log.info("Using Ross2 adaptor for serial number: {}", serialNumber);
                return ross2ProductControlAdaptor;
            } else {
                log.info("Using Ross1 adaptor for serial number: {}", serialNumber);
                return ross1ProductControlAdaptor;
            }
        } catch (Exception e) {
            log.error("Error determining product control adaptor for serial number {}: {}",
                    serialNumber, e.getMessage(), e);

            // Default to Ross1 adaptor in case of error
            log.warn("Defaulting to Ross1 adaptor for serial number: {}", serialNumber);
            return ross1ProductControlAdaptor;
        }
    }
}
