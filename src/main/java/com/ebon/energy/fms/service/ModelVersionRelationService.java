package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.ModelTypeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FmsModelVersionRelationDO;
import com.ebon.energy.fms.domain.entity.HardwareModelDO;
import com.ebon.energy.fms.mapper.primary.FmsModelVersionRelationMapper;
import com.ebon.energy.fms.mapper.third.HardwareModelMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ModelVersionRelationService {

    @Resource
    private FmsModelVersionRelationMapper fmsModelVersionRelationMapper;

    @Resource
    private HardwareModelMapper hardwareModelMapper;

    /**
     * 根据模型ID获取版本列表
     * @param modelId 模型ID
     * @return 版本列表
     */
    public List<FmsModelVersionRelationDO> listByModelId(Integer modelId) {
        LambdaQueryWrapper<FmsModelVersionRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmsModelVersionRelationDO::getModelId, modelId)
                .eq(FmsModelVersionRelationDO::getType, ModelTypeEnum.EMSFirmware.getValue())
                .orderByDesc(FmsModelVersionRelationDO::getVersion); // 按版本号降序排列
        return fmsModelVersionRelationMapper.selectList(queryWrapper);
    }

    /**
     * 根据模型ID获取版本号列表
     * @param modelId 模型ID
     * @return 版本号字符串列表
     */
    public List<String> listVersionsByModelId(Integer modelId, ModelTypeEnum modelTypeEnum) {
        LambdaQueryWrapper<FmsModelVersionRelationDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(FmsModelVersionRelationDO::getVersion)
                .eq(FmsModelVersionRelationDO::getModelId, modelId)
                .eq(FmsModelVersionRelationDO::getType, modelTypeEnum.getValue())
                .orderByDesc(FmsModelVersionRelationDO::getVersion);
        return fmsModelVersionRelationMapper.selectObjs(queryWrapper)
                .stream()
                .map(obj -> (String) obj)
                .collect(Collectors.toList());
    }

    /**
     * 根据模型Name获取版本号列表
     * @param modelName 模型Name
     * @return 版本号字符串列表
     */
    public Map<String, List<String>> listVersionsByModelName(List<String> modelNames, ModelTypeEnum modelTypeEnum) {
        Map<String, List<String>> result = new HashMap<>();
        for (String modelName : modelNames) {
            List<String> res = new ArrayList<>();
            LambdaQueryWrapper<HardwareModelDO> hardwareModelDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            hardwareModelDOLambdaQueryWrapper.eq(HardwareModelDO::getName, modelName);
            HardwareModelDO modelDO = hardwareModelMapper.selectOne(hardwareModelDOLambdaQueryWrapper);
            if (Objects.nonNull(modelDO)) {
                LambdaQueryWrapper<FmsModelVersionRelationDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.select(FmsModelVersionRelationDO::getVersion)
                        .eq(FmsModelVersionRelationDO::getModelId, modelDO.getId())
                        .eq(FmsModelVersionRelationDO::getType, modelTypeEnum.getValue())
                        .orderByDesc(FmsModelVersionRelationDO::getVersion);
                res = fmsModelVersionRelationMapper.selectObjs(queryWrapper)
                        .stream()
                        .map(obj -> (String) obj)
                        .collect(Collectors.toList());
            }

            result.put(modelName, res);
        }

        return result;
    }
}
