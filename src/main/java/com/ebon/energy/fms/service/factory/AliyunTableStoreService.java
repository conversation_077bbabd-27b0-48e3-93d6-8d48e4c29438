package com.ebon.energy.fms.service.factory;

import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.config.AliTableStoreConfig;
import com.ebon.energy.fms.domain.vo.TelemetryDataVO;
import com.ebon.energy.fms.service.TableStoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AliyunTableStoreService implements TableStoreService {

    @Resource
    private AliTableStoreConfig aliTableStoreConfig;

    private final long MAX_VALUE = 9223372036854775807L;

    @Override
    public CloudPlatformName getCloudPlatformName() {
        return CloudPlatformName.Tuya;
    }

    @Override
    public TelemetryDataVO getTelemetryData(String sn, long epoch) {
        if (epoch > 946684800000l) // This is Y2K in milliseconds and 1st of April 31,969 in seconds
        {
            // 2020 May
            // When using point-to-system-details on charts the epoch that is used
            // to load SystemDetails is the milliseconds epoch.
            // It worked because a query between epoch-1 and epoch+1 to get a system status at a point
            // - for example a query from System Details would be
            //  ((RowKey gt '1588096352999') and(RowKey lt '1588096353001')) -
            // and greater_than and less_than use lexicographical order so
            // 1588096352999 < 1588096352 < 1588096353001

            if (epoch % 1000 == 0) // Belt and bracers
            {
                epoch /= 1000;
            }
        }

        //创建并初始化客户端。
        String endPoint = aliTableStoreConfig.getEndPoint();
        // 获取环境变量里的 AccessKey ID 和 AccessKey Secret
        String accessKeyId = aliTableStoreConfig.getAccessKey();
        String accessKeySecret = aliTableStoreConfig.getSecretKey();
        String instanceName = aliTableStoreConfig.getInstanceName();
        SyncClient client = new SyncClient(endPoint, accessKeyId, accessKeySecret, instanceName);

        //创建API请求并设置参数。
        //构造主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("PartitionKey", PrimaryKeyValue.fromString(sn));
        primaryKeyBuilder.addPrimaryKeyColumn("RowKey", PrimaryKeyValue.fromString(String.format("%010d", MAX_VALUE - epoch)));
        PrimaryKey primaryKey = primaryKeyBuilder.build();

        Row row = null;
        if (epoch >= aliTableStoreConfig.getUseOnlyOldStoragePriorToEpoch()) {
            //读取一行数据，设置数据表名称。
            SingleRowQueryCriteria criteria = new SingleRowQueryCriteria("telemetrydata", primaryKey);
            //设置读取最新版本。
            criteria.setMaxVersions(1);
            //设置读取某些列。
            //criteria.addColumnsToGet("RossTelemetry");
            GetRowRequest getRowRequest = new GetRowRequest(criteria);

            //发起请求并打印返回结果。
            GetRowResponse getRowResponse = client.getRow(getRowRequest);
            row = getRowResponse.getRow();
        }

        if (row == null && epoch <= aliTableStoreConfig.getUseOnlyNewTelemetryFromEpoch()) {
            //读取一行数据，设置数据表名称。
            SingleRowQueryCriteria inverterCriteria = new SingleRowQueryCriteria("inverterdata", primaryKey);
            //设置读取最新版本。
            inverterCriteria.setMaxVersions(1);
            GetRowRequest inverterRowRequest = new GetRowRequest(inverterCriteria);

            //发起请求并打印返回结果。
            GetRowResponse inverterRowResponse = client.getRow(inverterRowRequest);
            row = inverterRowResponse.getRow();
        }

        TelemetryDataVO dataVO = new TelemetryDataVO();
        dataVO.setSerialNumber(sn);

        if (row == null) {
            return dataVO;
        }

        Column telemetryCol = row.getLatestColumn("RossTelemetry");
        Column documentCol = row.getLatestColumn("Document");
        dataVO.setTelemetry((String) telemetryCol.getValue().getValue());
        dataVO.setSystemStatus((String) documentCol.getValue().getValue());
        return dataVO;
    }



}
