package com.ebon.energy.fms.service.product.control;


import com.ebon.energy.fms.common.enums.InverterOperationTypeEnum;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.repository.RedbackRepository;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import com.ebon.energy.fms.service.SettingsService;
import com.ebon.energy.fms.util.azure.IoTHubUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Ross1 implementation of the ProductControlAdaptor
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Ross1ProductControlAdaptor implements IProductControlAdaptor {

    private final IoTHubUtility ioTHubUtility;

    private final RedbackRepository repository;

    private final DeviceControlMapper deviceControlMapper;

    private final SpecificationRepository specificationRepository;

    /**
     * Get the product control view model for a specific serial number
     *
     * @param serialNumber The product serial number
     * @return The product control view model
     */
    @Override
    public ProductControlViewModel getViewModel(String serialNumber) {
        log.info("Getting Ross1 product control view model for serial number: {}", serialNumber);

        // Get device control data
        DeviceControlDTO deviceControlDTO = repository.getDeviceControl(serialNumber);


        // Map to view model
        ProductControlViewModel viewModel = MapToViewModel(deviceControlMapper, serialNumber, deviceControlDTO);

        log.info("Successfully retrieved Ross1 product control view model for serial number: {}", serialNumber);
        return viewModel;

    }

    @Override
    public InstallationSpecification getProductCapabilities(String serialNumber) {
        return specificationRepository.getInstallationSpecAsync(serialNumber);
    }

    @Override
    public void updateAsync(ProductControlViewModel model) {
        if (InverterOperationTypeEnum.Optimise.name().equalsIgnoreCase(model.getInverterOperation().getType())) {
            //FMS 不涉及 暂时不需要
        } else {
            var existingDeviceControl = repository.getDeviceControl(model.getSerialNumber());
            var deviceControl = deviceControlMapper.mapToEntity(model, existingDeviceControl.getDeviceControl());
            var updateDeviceControlDTO = UpdateDeviceControlDTO.fromPortal(model.getSerialNumber(), deviceControl);
            repository.updateDeviceControlRoss1(updateDeviceControlDTO);
            ioTHubUtility.sendDeviceControl(model.getSerialNumber(), deviceControl);
        }
    }

    /**
     * Map device control data to a product control view model
     *
     * @param mapper           The device control mapper
     * @param serialNumber     The product serial number
     * @param deviceControlDTO The device control data
     * @return The product control view model
     */
    public static ProductControlViewModel MapToViewModel(DeviceControlMapper mapper, String serialNumber,
                                                         DeviceControlDTO deviceControlDTO) {
        ProductControlViewModel viewModel = new ProductControlViewModel();

        // Set basic properties
        viewModel.setSerialNumber(serialNumber);

        // Set inverter operation if available
        if (deviceControlDTO.getDeviceControl().getInverterOperation() != null) {
            InverterOperationViewModel inverterOperation = mapper.mapToView(
                    deviceControlDTO.getDeviceControl().getInverterOperation(),
                    LocalDateTime.now(),
                    deviceControlDTO.getInverterTimeZoneId());
            viewModel.setInverterOperation(inverterOperation);
        }

        return viewModel;
    }
}
