package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ebon.energy.fms.domain.entity.QueryResultColumnAdvanceQueriesDO;
import com.ebon.energy.fms.domain.entity.QueryResultColumnsDO;
import com.ebon.energy.fms.domain.entity.TagAdvanceQueriesDO;
import com.ebon.energy.fms.domain.entity.TagsDO;
import com.ebon.energy.fms.mapper.second.QueryResultColumnAdvanceQueriesMapper;
import com.ebon.energy.fms.mapper.second.QueryResultColumnsMapper;
import com.ebon.energy.fms.mapper.second.TagAdvanceQueriesMapper;
import com.ebon.energy.fms.mapper.second.TagsMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class QueryResultColumnsService {

    @Resource
    private QueryResultColumnsMapper queryResultColumnsMapper;

    @Resource
    private QueryResultColumnAdvanceQueriesMapper queryResultColumnAdvanceQueriesMapper;


    public List<QueryResultColumnsDO> getAllColumns() {
        return queryResultColumnsMapper.allColumns();
    }

    public List<String> getColumnsByQueryId(Integer queryId) {
        LambdaQueryWrapper<QueryResultColumnAdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryResultColumnAdvanceQueriesDO::getAdvanceQueryId, queryId);
        List<QueryResultColumnAdvanceQueriesDO> dos = queryResultColumnAdvanceQueriesMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(dos)) {
            return Collections.emptyList();
        }
        return dos.stream().map(QueryResultColumnAdvanceQueriesDO::getQueryResultColumn).collect(Collectors.toList());
    }

    public void removeRelationByQueryId(Integer queryId) {
        LambdaQueryWrapper<QueryResultColumnAdvanceQueriesDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryResultColumnAdvanceQueriesDO::getAdvanceQueryId, queryId);
        queryResultColumnAdvanceQueriesMapper.delete(wrapper);
    }

    public void addRelationByQueryId(Integer queryId, List<String> resultColumns) {
        if (!CollectionUtils.isEmpty(resultColumns)) {
            resultColumns.forEach(resultColumn -> {
                QueryResultColumnAdvanceQueriesDO queryResultColumnAdvanceQueriesDO = new QueryResultColumnAdvanceQueriesDO();
                queryResultColumnAdvanceQueriesDO.setAdvanceQueryId(queryId);
                queryResultColumnAdvanceQueriesDO.setQueryResultColumn(resultColumn);
                queryResultColumnAdvanceQueriesMapper.insert(queryResultColumnAdvanceQueriesDO);
            });
        }
    }
}
