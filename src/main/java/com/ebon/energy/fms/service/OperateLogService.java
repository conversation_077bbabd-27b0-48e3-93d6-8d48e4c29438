package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.domain.entity.OperateLogDO;
import com.ebon.energy.fms.domain.po.OperateLogListPO;
import com.ebon.energy.fms.domain.vo.OperateLogVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.mapper.primary.OperateLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OperateLogService {

    @Resource
    private OperateLogMapper operateLogMapper;

    public PageResult<OperateLogVO> getOperateLogList(OperateLogListPO po) {
        Page<OperateLogDO> page = new Page<>(po.getCurrent(), po.getPageSize());

        LambdaQueryWrapper<OperateLogDO> queryWrapper = new LambdaQueryWrapper<>();
        if (po.getDate() != null && po.getDate() > 0) {
            Timestamp startOfDay = new Timestamp(po.getDate());
            Timestamp endOfDay = new Timestamp(startOfDay.getTime() + DateUtils.MILLIS_PER_DAY);
            queryWrapper.ge(OperateLogDO::getTimeAccessed, startOfDay).lt(OperateLogDO::getTimeAccessed, endOfDay);
        }

        if (StringUtils.isNoneBlank(po.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(OperateLogDO::getContent, po.getKeyword())
                    .or().like(OperateLogDO::getIpAddress, po.getKeyword())
                    .or().like(OperateLogDO::getMethod, po.getKeyword())
                    .or().like(OperateLogDO::getUrlAccessed, po.getKeyword())
                    .or().like(OperateLogDO::getUserName, po.getKeyword()));
        }

        if (po.getOrder() != null && po.getOrder().length > 0) {
            for (OperateLogListPO.Order col : po.getOrder()) {
                switch (col.getColumn()) {
                    case 0:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(OperateLogDO::getTimeAccessed);
                        } else {
                            queryWrapper.orderByAsc(OperateLogDO::getTimeAccessed);
                        }
                        break;
                    case 1:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(OperateLogDO::getModule);
                        } else {
                            queryWrapper.orderByAsc(OperateLogDO::getModule);
                        }
                        break;
                    case 2:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(OperateLogDO::getUrlAccessed);
                        } else {
                            queryWrapper.orderByAsc(OperateLogDO::getUrlAccessed);
                        }
                        break;
                    case 3:
                        if ("desc".equals(col.getDir())) {
                            queryWrapper.orderByDesc(OperateLogDO::getUserName);
                        } else {
                            queryWrapper.orderByAsc(OperateLogDO::getUserName);
                        }
                        break;
                }
            }
        } else {
            queryWrapper.orderByDesc(OperateLogDO::getLogId);
        }

        Page<OperateLogDO> logPage = operateLogMapper.selectPage(page, queryWrapper);
        List<OperateLogVO> list = logPage.getRecords().stream().map(e -> {
            OperateLogVO vo = new OperateLogVO();
            BeanUtils.copyProperties(e, vo);
            vo.setTimeAccessed(String.valueOf(e.getTimeAccessed().getTime()));
            vo.setResult(e.getResultCode() != null && e.getResultCode().equals(CommonErrorCodeEnum.SUCCESS.getErrorCode()));
            return vo;
        }).collect(Collectors.toList());
        return PageResult.toResponse(list, logPage.getTotal(), po.getCurrent(), po.getPageSize());
    }

}
