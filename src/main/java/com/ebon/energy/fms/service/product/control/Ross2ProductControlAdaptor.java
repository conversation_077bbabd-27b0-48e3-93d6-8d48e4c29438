package com.ebon.energy.fms.service.product.control;

import com.ebon.energy.fms.common.enums.InverterOperationModeEnum;
import com.ebon.energy.fms.common.enums.InverterOperationTypeEnum;
import com.ebon.energy.fms.common.enums.InverterScheduleOperationModeEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.InverterScheduleItemViewModel;
import com.ebon.energy.fms.repository.ISettingsService;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.Ross2ProductControlAdaptorMapperHelper;
import com.ebon.energy.fms.util.ScheduleBuilderHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Ross2 implementation of the ProductControlAdaptor
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Ross2ProductControlAdaptor implements IProductControlAdaptor {

    private final ISettingsService settingService;

    private final ProductTimezoneService productTimezoneService;

    private final ProductDbRepository productDbRepository;

    private final SpecificationRepository specificationRepository;

    @Override
    public ProductControlViewModel getViewModel(String serialNumber) {
        log.info("Retrieving Ross2 product control data for serial number: {}", serialNumber);

        // Get device control data
        var dto = settingService.getSettingsDtoTrusted(serialNumber);

        // Get schedule audits
        List<ScheduleAuditDto> scheduleAudits = null;

        try {
            scheduleAudits = productDbRepository.getActiveScheduleCreateAuditsTrustedAsync(serialNumber);
        } catch (Exception e) {
            log.warn("Failed to get schedule audits for serial number: {}", serialNumber, e);
        }

        ProductControlViewModel viewModel = mapToView(dto, scheduleAudits, serialNumber);

        log.info("Successfully retrieved Ross2 product control view model for serial number: {}", serialNumber);
        return viewModel;
    }

    @Override
    public InstallationSpecification getProductCapabilities(String serialNumber) {
        return specificationRepository.getInstallationSpecAsync(serialNumber);
    }

    @Override
    public void updateAsync(ProductControlViewModel model) {
        var dto = settingService.getSettingsDtoTrusted(model.getSerialNumber());
        if (dto.getIdentityCard().getModelInfo() == null) {
            throw new BizException("Unable to establish inverter type for serial number: " + Optional.ofNullable(dto.getIdentityCard()).map(x -> x.getSerialNumber()).orElse("<unknown inverter>"));
        }

        String ianaTimeZoneId = null;
        if (!dto.getIdentityCard().getModelInfo().isSettingsV2()) {
            ianaTimeZoneId = Optional.of(dto).map(x->x.getDesired()).map(x ->x.getSchedules()).map(x ->x.getIanaTimeZoneId()).orElse(null);
        }

        if (!StringUtils.hasLength(ianaTimeZoneId)) {
            ianaTimeZoneId = productTimezoneService.fetchTimezone(model.getSerialNumber()).getId();
        }
        var updateModel = Ross2ProductControlAdaptorMapperHelper.translate(model);
        var changed = new HashMap<String, ScheduleInfoDto>();
        var jsonPatch = ScheduleBuilderHelper.updateUserSchedules(updateModel, dto, ianaTimeZoneId, changed);
        updateInverterModeSchedulesInDb(model.getSerialNumber(), ianaTimeZoneId, changed);
        settingService.updateDesiredDeviceSettingsFromJsonPatch(RequestUtil.getLoginUserEmail(), model.getSerialNumber(), jsonPatch, null, true);
    }

    private ProductControlViewModel mapToView(
            DeviceSettingsDto deviceControlDTO,
            List<ScheduleAuditDto> scheduleAudits,
            String serialNumber) {
        var zoneId = productTimezoneService.fetchTimezone(serialNumber);
        ProductControlViewModel viewModel = new ProductControlViewModel();
        viewModel.setSerialNumber(serialNumber);
        viewModel.setInverterOperation(mapInverterOperationToView(deviceControlDTO, scheduleAudits, zoneId));
        return viewModel;
    }

    private InverterOperationViewModel mapInverterOperationToView(DeviceSettingsDto dto, List<ScheduleAuditDto> scheduleAudits, ZoneId localTimeZone) {

        if (dto.getIdentityCard().getModelInfo().isSettingsV2()
                && !dto.getIdentityCard().getSofterVersion().isSiInverter()) // Inverter modes for SI have NOT
        // been done.
        {
            var reader = SettingsReaderProvider.get(dto);
            var inverterModeData = reader.getDesiredPowerModeSchedulesForPortal();
            var translated = inverterModeData.getSchedules().entrySet().stream()
                    .filter(x -> x.getValue() != null)
                    .filter(x -> currentOrFutureSchedule(x.getValue()))
                    .map(x -> mapScheduleInfoToView(
                            x.getKey(),
                            x.getValue(),
                            scheduleAudits != null ? scheduleAudits.stream()
                                    .filter(sa -> sa.getScheduleId().equals(x.getKey()))
                                    .findFirst()
                                    .orElse(null) : null,
                            x.getValue().getPriority() == ScheduleInfoDto.USER_SCHEDULE_PRIORITY,
                            localTimeZone))
                    .collect(Collectors.toList());

            var inverterOperationTypeEnum = translated.isEmpty()
                    ? InverterOperationTypeEnum.Set
                    : InverterOperationTypeEnum.Schedule;

            return new InverterOperationViewModel(
                    inverterModeData.getMode().getPowerW(),
                    translated,
                    inverterOperationTypeEnum.toString(),
                    mapInverterMode(inverterModeData.getMode().getMode()).name(),
                    localTimeZone != null ? localTimeZone.getId() : null // Timezone only displayed and Bcl or Iana
                    // names are OK
            );

        } else {
            Double workModePowerW = Optional.ofNullable(dto.getDesired()).map(x -> x.getInverter()).map(x-> x.getWorkModePowerW()).orElse(null);

            List<Map.Entry<String, ScheduleInfoDto>> validSchedules = Optional.of(dto).map(x -> x.getDesired()).map(x -> x.getSchedules())
                    .map(x -> x.getSchedules().entrySet()
                    .stream()
                    .filter(it -> it.getValue() != null)
                    .filter(it -> currentOrFutureSchedule(it.getValue()))
                    .filter(it -> !"Relay".equals(it.getValue().getActionName()))
                    .filter(it -> isValidScheduleAction(it.getValue().getActionName()))
                    .filter(it -> isValidRecurringSchedule(it.getValue()) || isValidUtcSchedule(it.getValue()))
                    .collect(Collectors.toList())).orElse(Collections.emptyList());

            InverterOperationTypeEnum inverterOperationTypeEnum = validSchedules.isEmpty()
                    ? InverterOperationTypeEnum.Set
                    : InverterOperationTypeEnum.Schedule;

            List<InverterScheduleItemViewModel> translated = validSchedules.stream()
                    .map(x -> mapScheduleInfoToView(
                            x.getKey(),
                            x.getValue(),
                            scheduleAudits != null ? scheduleAudits.stream()
                                    .filter(sa -> sa.getScheduleId().equals(x.getKey()))
                                    .findFirst()
                                    .orElse(null) : null,
                            x.getValue().getPriority() == ScheduleInfoDto.USER_SCHEDULE_PRIORITY,
                            localTimeZone))
                    .collect(Collectors.toList());

            return new InverterOperationViewModel(
                    workModePowerW != null ? workModePowerW.longValue() : 0,
                    translated,
                    inverterOperationTypeEnum.toString(),
                    mapMode(Optional.ofNullable(dto.getDesired()).map(x -> x.getInverter()).map(x->x.getWorkMode()).orElse(null)).name(),
                    localTimeZone != null ? localTimeZone.getId() : ZoneId.systemDefault().getId());

        }
    }

    /**
     * Map inverter mode value to enum
     *
     * @param workMode Work mode value
     * @return Inverter operation mode enum
     */
    private InverterOperationModeEnum mapInverterMode(SystemStatusEnum.InverterModeValue workMode) {
        if (workMode == null) {
            return InverterOperationModeEnum.Auto;
        }

        switch (workMode.name()) {
            case "Auto":
                return InverterOperationModeEnum.Auto;
            case "ChargeBattery":
                return InverterOperationModeEnum.ChargeBattery;
            case "DischargeBattery":
                return InverterOperationModeEnum.DischargeBattery;
            case "ImportPower":
                return InverterOperationModeEnum.ImportPower;
            case "ExportPower":
                return InverterOperationModeEnum.ExportPower;
            case "Conserve":
                return InverterOperationModeEnum.Conserve;
            case "BuyPower":
                return InverterOperationModeEnum.BuyPower;
            case "ForceChargeBattery":
                return InverterOperationModeEnum.ForceChargeBattery;
            case "ForceDischargeBattery":
                return InverterOperationModeEnum.ForceDischargeBattery;
            case "Offgrid":
                return InverterOperationModeEnum.Offgrid;
            case "SellPower":
                return InverterOperationModeEnum.SellPower;
            case "Hibernate":
                return InverterOperationModeEnum.Hibernate;
            case "Stop":
                return InverterOperationModeEnum.Stop;
            default:
                return InverterOperationModeEnum.Auto;
        }
    }

    /**
     * Map mode value to enum
     *
     * @param workMode Work mode value
     * @return Inverter operation mode enum
     */
    private InverterOperationModeEnum mapMode(SystemStatusEnum.InverterModeValue workMode) {
        if (workMode == null) {
            return InverterOperationModeEnum.Auto;
        }

        switch (workMode.name()) {
            case "Auto":
                return InverterOperationModeEnum.Auto;
            case "ChargeBattery":
                return InverterOperationModeEnum.ChargeBattery;
            case "DischargeBattery":
                return InverterOperationModeEnum.DischargeBattery;
            case "ImportPower":
                return InverterOperationModeEnum.ImportPower;
            case "ExportPower":
                return InverterOperationModeEnum.ExportPower;
            case "Conserve":
                return InverterOperationModeEnum.Conserve;
            case "BuyPower":
                return InverterOperationModeEnum.BuyPower;
            case "ForceChargeBattery":
                return InverterOperationModeEnum.ForceChargeBattery;
            case "ForceDischargeBattery":
                return InverterOperationModeEnum.ForceDischargeBattery;
            case "Offgrid":
                return InverterOperationModeEnum.Offgrid;
            case "SellPower":
                return InverterOperationModeEnum.SellPower;
            case "Hibernate":
                return InverterOperationModeEnum.Hibernate;
            case "Stop":
                return InverterOperationModeEnum.Stop;
            default:
                return InverterOperationModeEnum.Auto;
        }
    }

    /**
     * Checks if a schedule is current or will occur in the future
     *
     * @param schedule Schedule information
     * @return True if the schedule is recurring, has no end time, or ends in the
     * future
     */
    private boolean currentOrFutureSchedule(ScheduleInfoDto schedule) {
        return schedule.getDaysOfWeekActive() != null // Recurring
                || schedule.getEndAtUtc() == null // No ending
                || schedule.getEndAtUtc().toInstant().isAfter(Instant.now()); // end is in
        // future
    }

    /**
     * Maps schedule information to view model
     *
     * @param id            Schedule ID
     * @param schedule      Schedule information
     * @param metadata      Schedule audit metadata
     * @param canBeDeleted  Whether the schedule can be deleted
     * @param localTimeZone Local timezone
     * @return Inverter schedule item view model
     */
    private InverterScheduleItemViewModel mapScheduleInfoToView(
            String id,
            ScheduleInfoDto schedule,
            ScheduleAuditDto metadata,
            boolean canBeDeleted,
            ZoneId localTimeZone) {
        if (isValidRecurringSchedule(schedule)) {
            var startTimeOfDay = LocalDate.now(ZoneOffset.UTC).atStartOfDay().plus(schedule.getDailyStartTime()).atOffset(ZoneOffset.UTC);
            var endTimeOfDay = startTimeOfDay.plus(schedule.getScheduleDuration());
            return new InverterScheduleItemViewModel(
                    id,
                    schedule.getPriority(),
                    true,
                    schedule.getDaysOfWeekActive() != null ? schedule.getDaysOfWeekActive() : null,
                    startTimeOfDay,
                    endTimeOfDay,
                    getInverterScheduleModes(decode(schedule.getActionName())),
                    schedule.getActionParameter(),
                    null, // Not used
                    metadata != null ? metadata.getCreatedByEmail() : null,
                    !canBeDeleted);
        } else if (isValidUtcSchedule(schedule)) {
            Instant startUtc = schedule.getStartAtUtc().toInstant();
            Instant endUtc = schedule.getEndAtUtc().toInstant();
            ZonedDateTime localStart = startUtc.atZone(localTimeZone);
            ZonedDateTime localEnd = endUtc.atZone(localTimeZone);

            return new InverterScheduleItemViewModel(
                    id,
                    schedule.getPriority(),
                    false,
                    null,
                    localStart.toOffsetDateTime(),
                    localEnd.toOffsetDateTime(),
                    getInverterScheduleModes(decode(schedule.getActionName())),
                    schedule.getActionParameter(),
                    null, // Not used
                    metadata != null ? metadata.getCreatedByEmail() : null,
                    !canBeDeleted);
        } else {
            throw new IllegalArgumentException("Invalid schedule configuration.");
        }
    }

    /**
     * Checks if the schedule is a valid UTC schedule
     *
     * @param schedule Schedule information
     * @return True if valid UTC schedule, false otherwise
     */
    private boolean isValidUtcSchedule(ScheduleInfoDto schedule) {
        return !isValidRecurringSchedule(schedule)
                && schedule.getStartAtUtc() != null
                && schedule.getEndAtUtc() != null;
    }

    /**
     * Checks if the schedule is a valid recurring schedule
     *
     * @param schedule Schedule information
     * @return True if valid recurring schedule, false otherwise
     */
    private boolean isValidRecurringSchedule(ScheduleInfoDto schedule) {
        return schedule.getDailyStartTime() != null && schedule.getScheduleDuration() != null;
    }

    private static SystemStatusEnum.InverterModeValue decode(String actionName) {
        switch (actionName) {
            case "ChargeBattery":
            case "ChargeB":
                return SystemStatusEnum.InverterModeValue.ChargeBattery;
            case "DischargeBattery":
            case "DischargeB":
                return SystemStatusEnum.InverterModeValue.DischargeBattery;
            case "Auto":
                return SystemStatusEnum.InverterModeValue.Auto;
            case "Import":
                return SystemStatusEnum.InverterModeValue.ImportPower;
            case "Export":
                return SystemStatusEnum.InverterModeValue.ExportPower;
            case "Conserve":
                return SystemStatusEnum.InverterModeValue.Conserve;
            case "BuyPower":
                return SystemStatusEnum.InverterModeValue.BuyPower;
            case "ForceChargeBattery":
                return SystemStatusEnum.InverterModeValue.ForceChargeBattery;
            case "ForceDischargeBattery":
                return SystemStatusEnum.InverterModeValue.ForceDischargeBattery;
            case "Hibernate":
                return SystemStatusEnum.InverterModeValue.Hibernate;
            case "Offgrid":
                return SystemStatusEnum.InverterModeValue.Offgrid;
            case "SellPower":
                return SystemStatusEnum.InverterModeValue.SellPower;
            case "Stop":
                return SystemStatusEnum.InverterModeValue.Stop;
            default:
                throw new IllegalArgumentException("Invalid schedule action name " + actionName);
        }
    }

    /**
     * Gets inverter schedule mode from action name
     *
     * @return Inverter schedule mode
     */
    private InverterScheduleOperationModeEnum getInverterScheduleModes(
            SystemStatusEnum.InverterModeValue inverterModeValue) {
        switch (inverterModeValue) {
            case ChargeBattery:
                return InverterScheduleOperationModeEnum.ChargeBattery;
            case Auto:
                return InverterScheduleOperationModeEnum.Auto;
            case DischargeBattery:
                return InverterScheduleOperationModeEnum.DischargeBattery;
            case ImportPower:
                return InverterScheduleOperationModeEnum.ImportPower;
            case ExportPower:
                return InverterScheduleOperationModeEnum.ExportPower;
            case Conserve:
                return InverterScheduleOperationModeEnum.Conserve;
            case BuyPower:
                return InverterScheduleOperationModeEnum.BuyPower;
            case ForceChargeBattery:
                return InverterScheduleOperationModeEnum.ForceChargeBattery;
            case ForceDischargeBattery:
                return InverterScheduleOperationModeEnum.ForceDischargeBattery;
            case Offgrid:
                return InverterScheduleOperationModeEnum.Offgrid;
            case SellPower:
                return InverterScheduleOperationModeEnum.SellPower;
            case Hibernate:
                return InverterScheduleOperationModeEnum.Hibernate;
            case Stop:
                return InverterScheduleOperationModeEnum.Stop;
            default:
                throw new IllegalArgumentException("无效的调度类型 " + inverterModeValue);
        }
    }

    /**
     * 检查调度动作是否有效
     *
     * @param actionName 动作名称
     * @return 如果是有效的调度动作返回true，否则返回false
     */
    private boolean isValidScheduleAction(String actionName) {
        try {
            decode(actionName);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * Updates inverter mode schedules in the database (synchronously)
     *
     * @param serialNumber   Serial number of the device
     * @param ianaTimeZoneId IANA time zone ID
     * @param changed        Dictionary of changed schedules
     */
    private void updateInverterModeSchedulesInDb(String serialNumber, String ianaTimeZoneId, Map<String, ScheduleInfoDto> changed) {
        for (Map.Entry<String, ScheduleInfoDto> entry : changed.entrySet()) {
            try {
                if (entry.getValue() == null) {
                    // 假设有同步方法 deleteScheduleTrusted
                    productDbRepository.deleteScheduleTrustedAsync(
                            entry.getKey(),
                            RequestUtil.getPortolUserId(),
                            serialNumber,
                            "Portal");
                } else {
                    // 假设有同步方法 saveScheduleTrusted
                    productDbRepository.saveScheduleTrustedAsync(
                            RequestUtil.getPortolUserId(),
                            serialNumber,
                            entry.getKey(),
                            entry.getValue(),
                            ianaTimeZoneId,
                            "Portal",
                            null); // No user notes in portal
                }
            } catch (Exception ex) {
                Map<String, String> properties = new HashMap<>();
                properties.put("UserId", RequestUtil.getPortolUserId());
                properties.put("SerialNumber", serialNumber);
                properties.put("S", entry.getValue() != null ? entry.getValue().toString() : null);
                log.error("Error updating inverter mode schedules", ex);
            }
        }
    }


}
