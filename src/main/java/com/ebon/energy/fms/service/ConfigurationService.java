package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.domain.entity.ConfigurationsDO;
import com.ebon.energy.fms.domain.vo.ConfigurationsVO;
import com.ebon.energy.fms.mapper.third.ConfigurationsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ConfigurationService {

    @Resource
    private ConfigurationsMapper configurationsMapper;

    public ConfigurationsVO getByConfigType(String sn, ConfigurationType configurationType) {
        LambdaQueryWrapper<ConfigurationsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigurationsDO::getRedbackProductSn, sn);
        queryWrapper.eq(ConfigurationsDO::getConfigurationType, configurationType.getValue());
        List<ConfigurationsDO> list = configurationsMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        ConfigurationsDO configurationsDO = list.get(0);
        ConfigurationsVO configurationsVO = new ConfigurationsVO();
        BeanUtils.copyProperties(configurationsDO, configurationsVO);
        return configurationsVO;
    }
}
