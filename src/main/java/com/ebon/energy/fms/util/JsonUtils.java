package com.ebon.energy.fms.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON 序列化和反序列化工具类
 * 使用 Spring Boot 的 ObjectMapper 实例，确保配置一致性
 */
@Slf4j
@Component
public class JsonUtils {
    
    private static ObjectMapper staticObjectMapper;
    
    private final ObjectMapper objectMapper;

    public JsonUtils(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 初始化静态 ObjectMapper
     */
    @PostConstruct
    public void init() {
        staticObjectMapper = objectMapper;
    }
    
    /**
     * 将对象序列化为 JSON 字符串
     *
     * @param object 要序列化的对象
     * @return JSON 字符串
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return staticObjectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("序列化对象到 JSON 失败: {}", e.getMessage(), e);
            throw new RuntimeException("序列化对象到 JSON 失败", e);
        }
    }
    
    /**
     * 将对象序列化为格式化的 JSON 字符串
     *
     * @param object 要序列化的对象
     * @return 格式化的 JSON 字符串
     */
    public static String toPrettyJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return staticObjectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("序列化对象到格式化 JSON 失败: {}", e.getMessage(), e);
            throw new RuntimeException("序列化对象到格式化 JSON 失败", e);
        }
    }
    
    /**
     * 将 JSON 字符串反序列化为指定类型的对象
     *
     * @param json JSON 字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return staticObjectMapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error("从 JSON 反序列化到对象失败: {}", e.getMessage(), e);
            throw new RuntimeException("从 JSON 反序列化到对象失败", e);
        }
    }
    
    /**
     * 将 JSON 字符串反序列化为复杂类型对象（如 List, Map 等）
     *
     * @param json JSON 字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return staticObjectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("从 JSON 反序列化到复杂类型对象失败: {}", e.getMessage(), e);
            throw new RuntimeException("从 JSON 反序列化到复杂类型对象失败", e);
        }
    }
    
    /**
     * 将 JSON 字符串反序列化为 List 类型
     *
     * @param json JSON 字符串
     * @param elementClass List 元素类型
     * @param <T> 泛型类型
     * @return 反序列化后的 List
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> elementClass) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            JavaType javaType = staticObjectMapper.getTypeFactory().constructCollectionType(List.class, elementClass);
            return staticObjectMapper.readValue(json, javaType);
        } catch (IOException e) {
            log.error("从 JSON 反序列化到 List 失败: {}", e.getMessage(), e);
            throw new RuntimeException("从 JSON 反序列化到 List 失败", e);
        }
    }
    
    /**
     * 将 JSON 字符串反序列化为 Map 类型
     *
     * @param json JSON 字符串
     * @param keyClass Map 键类型
     * @param valueClass Map 值类型
     * @param <K> 键泛型类型
     * @param <V> 值泛型类型
     * @return 反序列化后的 Map
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyClass, Class<V> valueClass) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            JavaType javaType = staticObjectMapper.getTypeFactory().constructMapType(Map.class, keyClass, valueClass);
            return staticObjectMapper.readValue(json, javaType);
        } catch (IOException e) {
            log.error("从 JSON 反序列化到 Map 失败: {}", e.getMessage(), e);
            throw new RuntimeException("从 JSON 反序列化到 Map 失败", e);
        }
    }
    
    /**
     * 将对象转换为 JsonNode
     *
     * @param object 要转换的对象
     * @return JsonNode 对象
     */
    public static JsonNode toJsonNode(Object object) {
        if (object == null) {
            return null;
        }
        return staticObjectMapper.valueToTree(object);
    }
    
    /**
     * 将 JSON 字符串转换为 JsonNode
     *
     * @param json JSON 字符串
     * @return JsonNode 对象
     */
    public static JsonNode parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return staticObjectMapper.readTree(json);
        } catch (IOException e) {
            log.error("解析 JSON 到 JsonNode 失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析 JSON 到 JsonNode 失败", e);
        }
    }
    
    /**
     * 创建一个空的 ObjectNode
     *
     * @return 新的 ObjectNode 实例
     */
    public static ObjectNode createObjectNode() {
        return staticObjectMapper.createObjectNode();
    }
    
    /**
     * 递归检查Map中的值，如果值是一个Map且为null或空，则将该值设置为null
     * 
     * @param map 要检查的Map
     * @param <K> Map的键类型
     * @return 处理后的Map
     */
    public static <K> Map<K, Object> cleanEmptyMaps(Map<K, Object> map) {
        if (map == null) {
            return null;
        }
        
        map.entrySet().forEach(entry -> {
            Object value = entry.getValue();
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<Object, Object> nestedMap = (Map<Object, Object>) value;
                
                if (nestedMap == null || nestedMap.isEmpty()) {
                    entry.setValue(null);
                } else {
                    // 递归处理嵌套的Map
                    cleanEmptyMaps(nestedMap);
                    // 如果递归处理后Map变为空，也设置为null
                    if (nestedMap.isEmpty()) {
                        entry.setValue(null);
                    }
                }
            }
        });
        
        return map;
    }
    
    /**
     * 将一个对象转换为另一个类型的对象
     *
     * @param fromValue 源对象
     * @param toValueType 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        if (fromValue == null) {
            return null;
        }
        return staticObjectMapper.convertValue(fromValue, toValueType);
    }
    
    /**
     * 将一个对象转换为复杂类型的对象
     *
     * @param fromValue 源对象
     * @param toValueTypeRef 目标类型引用
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public static <T> T convertValue(Object fromValue, TypeReference<T> toValueTypeRef) {
        if (fromValue == null) {
            return null;
        }
        return staticObjectMapper.convertValue(fromValue, toValueTypeRef);
    }
    
    /**
     * 获取静态 ObjectMapper 实例
     * 
     * @return ObjectMapper 实例
     */
    public static ObjectMapper getObjectMapper() {
        return staticObjectMapper;
    }
}
