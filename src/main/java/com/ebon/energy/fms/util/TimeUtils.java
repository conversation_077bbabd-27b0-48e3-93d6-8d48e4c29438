package com.ebon.energy.fms.util;

import com.ebon.energy.fms.domain.vo.TimeSpan;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class TimeUtils {
    public static String toHumanReadableString(TimeSpan t) {
        double totalMilliseconds = t.getTotalMilliseconds();
        if (totalMilliseconds < 1) {
            return "";
        }

        double totalSeconds = t.getTotalSeconds();
        if (totalSeconds < 1) {
            return (int) totalMilliseconds + " milliseconds";
        }

        if (totalSeconds < 2) {
            return String.format("%.2f", (double) totalSeconds) + " seconds";
        }

        double totalMinutes = t.getTotalMinutes();
        if (totalMinutes < 2) {
            return (int) totalSeconds + " seconds";
        }

        double totalHours = t.getTotalHours();
        if (totalHours < 2) {
            return BigDecimal.valueOf(totalMinutes)
                    .setScale(0, RoundingMode.HALF_UP)
                    .intValue() + " minutes";
        }

        double totalDays = t.getTotalDays();
        if (totalDays < 2) {
            return (int) (totalMinutes / 60) + " hours, " + (int) (totalMinutes % 60) + " minutes";
        }

        return (int) totalDays + " days, " + (int) (totalHours % 24) + " hours";
    }

    public static String toHumanReadableStringShort(TimeSpan t) {
        double totalMilliseconds = t.getTotalMilliseconds();
        if (totalMilliseconds < 1) {
            return "";
        }

        double totalSeconds = t.getTotalSeconds();
        if (totalSeconds < 1) {
            return (int) totalMilliseconds + "ms";
        }

        if (totalSeconds < 2) {
            return String.format("%.2f", totalSeconds) + "s";
        }

        double totalMinutes = t.getTotalMinutes();
        if (totalMinutes < 2) {
            return (int) totalSeconds + "s";
        }

        double totalHours = t.getTotalHours();
        if (totalHours < 2) {
            return BigDecimal.valueOf(totalMinutes)
                    .setScale(0, RoundingMode.HALF_UP)
                    .intValue() + "m";
        }

        double totalDays = t.getTotalDays();
        if (totalDays < 2) {
            return BigDecimal.valueOf(totalHours)
                    .setScale(0, RoundingMode.HALF_UP)
                    .intValue() + "h";
        }

        return (int) totalDays + "d," + (int) (totalHours % 24) + "h";
    }

    public static String getUtcTimeStr(long timestamp) {
        // 转换为UTC时间字符串
        return Instant.ofEpochMilli(timestamp)
                .atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    }

    public static ZonedDateTime toZonedDateTime(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }

        return timestamp.toInstant()
                .atZone(ZoneId.systemDefault());
    }

    public static String getDayOfTheWeekOrToday(LocalDate date, LocalDate specialDate) {
        // 空值校验
        Objects.requireNonNull(date, "date 不能为空");
        Objects.requireNonNull(specialDate, "specialDate 不能为空");

        if (date.equals(specialDate)) {
            return "Today";
        }

        DayOfWeek dayOfWeek = date.getDayOfWeek();

        if (dayOfWeek == DayOfWeek.MONDAY) {
            return "Mon";
        } else if (dayOfWeek == DayOfWeek.TUESDAY) {
            return "Tue";
        } else if (dayOfWeek == DayOfWeek.WEDNESDAY) {
            return "Wed";
        } else if (dayOfWeek == DayOfWeek.THURSDAY) {
            return "Thu";
        } else if (dayOfWeek == DayOfWeek.FRIDAY) {
            return "Fri";
        } else if (dayOfWeek == DayOfWeek.SATURDAY) {
            return "Sat";
        } else if (dayOfWeek == DayOfWeek.SUNDAY) {
            return "Sun";
        } else {
            throw new IllegalArgumentException("不支持的星期类型: " + dayOfWeek);
        }
    }

}
