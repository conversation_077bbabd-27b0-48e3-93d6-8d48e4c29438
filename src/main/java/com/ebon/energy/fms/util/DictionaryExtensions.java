package com.ebon.energy.fms.util;

import java.util.HashMap;
import java.util.Map;

public class DictionaryExtensions {

    // 等价于 ValOrDefault
    @SuppressWarnings("unchecked")
    public static <T> T valOrDefault(Map<String, Object> baseObj, String key, T defaultVal) {
        if (!baseObj.containsKey(key)) {
            baseObj.put(key, defaultVal);
        }
        return (T) baseObj.get(key);
    }

    // 等价于 EnsureSubtree
    public static Map<String, Object> ensureSubtree(Map<String, Object> baseObj, String key) {
        return valOrDefault(baseObj, key, new HashMap<String, Object>());
    }

    // 等价于 TryGetStringValue
    public static String tryGetStringValue(Map<String, Object> baseObj, String key) {
        if (baseObj != null && baseObj.containsKey(key)) {
            Object value = baseObj.get(key);
            return value != null ? value.toString() : null;
        }
        return null;
    }
}
