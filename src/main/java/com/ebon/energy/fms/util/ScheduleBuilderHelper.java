package com.ebon.energy.fms.util;

import cn.hutool.core.lang.Pair;
import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.HardwareModelHelpers;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.common.utils.path.ESGSettingPaths;
import com.ebon.energy.fms.domain.vo.InverterDesiredSettingsVO;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;

import java.time.*;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.DictionaryExtensions.ensureSubtree;

public class ScheduleBuilderHelper {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static String updateUserSchedules(
            SchedulesForCtrlPageDto model,
            DeviceSettingsDto dto,
            String ianaTimeZoneId,
            Map<String, ScheduleInfoDto> changed
    ) {
        if (dto.getIdentityCard().getModelInfo() == null) {
            throw new BizException("Unable to establish inverter type for serial number: " + Optional.ofNullable(dto.getIdentityCard()).map(x -> x.getSerialNumber()).orElse("<unknown inverter>"));
        }
        if (dto.getIdentityCard().getModelInfo().isSettingsV2()) {
            return updateSI_and_GEN3(model, dto, ianaTimeZoneId, changed);
        } else {
            return updateV1(model, dto, ianaTimeZoneId, changed);
        }
    }

    @SneakyThrows
    public static String updateSI_and_GEN3(
            SchedulesForCtrlPageDto model,
            DeviceSettingsDto dto,
            String ianaTimeZoneId,
            Map<String, ScheduleInfoDto> changed
    ) {
        var localChanged = new HashMap<String, ScheduleInfoDto>();

        var deviceSettingsPatch = new HashMap<String, Object>();
        var currentSchedulesVersion = dto.getDesired() != null ? Optional.of(dto.getDesired()).map(x -> x.getSettingsV2()).map(x -> x.getScheduleSettings()).map(x -> x.getVersion()).orElse(0) : 0;
        var localTimeZone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(ianaTimeZoneId);

        if (HardwareModelHelpers.SIs.contains(dto.getIdentityCard().getModelInfo().getHardwareModel())) {
//            var smartRelaySchedules = dto.getDesired() != null ? dto.getDesired().getSettingsV2().getScheduleSettings().getSchedules()
//                    .entrySet().stream()
//                    .filter(x -> x.getValue().getDesiredScheduledSettings() != null && x.getValue().getDesiredScheduledSettings().getSmartRelaySettings() != null && x.getValue().getDesiredScheduledSettings().getSmartRelaySettings().getMode() != null)
//                    .map(Map.Entry::getKey)
//                    .collect(Collectors.toList()) : Collections.emptyList();
//
//            var updates = ScheduleBuilderHelper.GetPendingUpdatesV2_SI(
//                    smartRelayOperation: model.getSmartRelayOperation(),
//                    currentSmartRelayScheduleIds: smartRelaySchedules,
//                    currentSchedulesVersion: currentSchedulesVersion,
//                    localTimeZone: localTimeZone,
//                    oldRelaySettings: dto.getDesired() != null ? dto.getDesired().getSettingsV2().getSmartRelaySettings() : null);
//            updates.forEach(transform -> transform.accept(deviceSettingsPatch));
        } else {
            // GEN3 path
            //var settingsReader = SettingsReaderProvider.Get(dto);
            var updates = ScheduleBuilderHelperGEN3.getPendingUpdatesV2_GEN3(
                    model.getInverterOperation(),
                    dto.getDesired() != null ? Optional.of(dto.getDesired()).map(x -> x.getSettingsV2()).map(x -> x.getScheduleSettings()).map(x -> x.getSchedules()).orElse(null) : null,
                    currentSchedulesVersion,
                    localTimeZone,
                    dto.getDesired() != null ? dto.getDesired().getSettingsV2().getSmartRelaySettings() : null,
                    ScheduleV2Dto.USER_SCHEDULE_PRIORITY);
            updates.forEach(transform -> transform.accept(deviceSettingsPatch, localChanged));
        }

        changed.putAll(localChanged);
        //todo check
        var jsonPatch = objectMapper.writeValueAsString(deviceSettingsPatch);
        return jsonPatch;
    }

    public static String updateV1(
            SchedulesForCtrlPageDto model,
            DeviceSettingsDto dto,
            String ianaTimeZoneId,
            Map<String, ScheduleInfoDto> changed
    ) {
        var localTimeZone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(ianaTimeZoneId);

        // 获取用户级别的 scheduleId
        Set<String> userLevelScheduleIds = Optional.ofNullable(dto.getDesired())
                .flatMap(x -> Optional.ofNullable(x.getSchedules()))
                .flatMap(x -> Optional.ofNullable(x.getSchedules()))
                .map(schedules -> schedules.entrySet().stream()
                        .filter(e -> e.getValue().getPriority() == ScheduleInfoDto.USER_SCHEDULE_PRIORITY)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());

        // 获取待更新的变换器（假设GetPendingUpdates已实现）
        List<BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>>> updates = getPendingUpdates(
                model.getInverterOperation(),
                model.getRelayOperation(),
                userLevelScheduleIds,
                localTimeZone,
//                dto.getDesired() != null ? dto.getDesired().getSmartRelaySettings() : null
                null
        );

        Map<String, Object> deviceSettingsPatch = new HashMap<>();
        Map<String, ScheduleInfoDto> changedLocal = new HashMap<>();
        for (BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>> transform : updates) {
            transform.accept(deviceSettingsPatch, changedLocal);
        }
        changed.clear();
        changed.putAll(changedLocal);

        try {

            return objectMapper.writeValueAsString(deviceSettingsPatch);
        } catch (Exception e) {
            throw new RuntimeException("JSON serialization failed", e);
        }
    }

    public static List<BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>>> getPendingUpdates(
            SchedulesForCtrlPageDto.InverterOperationViewModel inverterOperation,
            List<SchedulesForCtrlPageDto.RelayOperationViewModel> relayOperation,
            Set<String> currentUserLevelScheduleIds,
            ZoneId localTimeZone,
            SmartRelaySettingsDesired smartRelaySettings) {

        List<BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>>> actions = new ArrayList<>();

        if (inverterOperation.getType() == SchedulesForCtrlPageDto.InverterOperationTypeEnum.Set) {
            actions.add((desired, changed) -> {
                Map<String, Object> inverter = ensureSubtree(desired, RossDesiredSettings.InverterSettingsName);
                SystemStatusEnum.InverterModeValue inverterMode = SystemStatusEnum.InverterModeValue.fromValue(inverterOperation.getMode());
                inverter.put(InverterDesiredSettingsVO.WORK_MODE_NAME, inverterMode);
                inverter.put(InverterDesiredSettingsVO.WORK_MODE_POWER_W_NAME, inverterMode == SystemStatusEnum.InverterModeValue.Auto ? 0 : inverterOperation.getPowerInWatts() * Watt.Unit.asLong());
            });

            actions.add((desired, changed) -> {
                Map<String, Object> schedules = ensureSubtree(desired, RossDesiredSettings.ScheduleSettingsName);
                schedules.put("IanaTimeZoneId", localTimeZone.getId());
                Map<String, Object> allSchedules = ensureSubtree(schedules, "Schedules");

                for (var scheduleId : currentUserLevelScheduleIds) {
                    allSchedules.put(scheduleId.toString(), null);
                    changed.put(scheduleId.toString(), null);
                }
            });
        }

        if (inverterOperation.getType() == SchedulesForCtrlPageDto.InverterOperationTypeEnum.Schedule) {
            actions.add((desired, changed) -> {
                List<SchedulesForCtrlPageDto.InverterScheduleItemViewModel> schedulesList = inverterOperation.getSchedules();
                List<Pair<String, ScheduleInfoDto>> newSchedules = new ArrayList<>();
                if (schedulesList != null) {
                    for (var s : schedulesList) {
                        if (!ScheduleInfoDto.REDBACK_SCHEDULE_PRIORITY.equals(s.getPriority())) {
                            newSchedules.add(new Pair<>(s.getId(), mapToDeviceTwinScheduleInfo(s, localTimeZone)));
                        }
                    }
                }

                Map<String, Object> schedules = ensureSubtree(desired, RossDesiredSettings.ScheduleSettingsName);
                schedules.put("IanaTimeZoneId", localTimeZone.getId());
                Map<String, Object> allSchedules = ensureSubtree(schedules, "Schedules");

                for (var scheduleId : currentUserLevelScheduleIds) {
                    allSchedules.put(scheduleId.toString(), null);
                    changed.put(scheduleId.toString(), null);
                }

                for (Pair<String, ScheduleInfoDto> schedulePair : newSchedules) {
                    String id = schedulePair.getKey();
                    allSchedules.put(id, schedulePair.getValue());
                    changed.put(id, schedulePair.getValue());
                }
            });
        }

//        for (BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>> t :
//                getPendingRelayUpdates(relayOperation, localTimeZone, smartRelaySettings)) {
//            actions.add(t);
//        }

        return actions;
    }

//    public static List<BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>>> getPendingRelayUpdates(
//            List<SchedulesForCtrlPageDto.RelayOperationViewModel> relayOperation,
//            ZoneId localTimeZone,
//            SmartRelaySettingsDesired smartRelaySettings) {
//
//        List<BiConsumer<Map<String, Object>, Map<String, ScheduleInfoDto>>> actions = new ArrayList<>();
//
//        actions.add((desired, changed) -> {
//            List<Pair<String, ScheduleInfoDto>> relaySchedules = new ArrayList<>();
//            if (relayOperation != null) {
//                for (SchedulesForCtrlPageDto.RelayOperationViewModel x : relayOperation) {
//                    if (x.getSchedules() != null) {
//                        for (var y : x.getSchedules()) {
//                            ScheduleInfoDto schedule = mapRelayScheduleToSchedule(y, x.getMode(), x.getRelayNumber() + 1, localTimeZone);
//                            if (schedule != null) {
//                                relaySchedules.add(new Pair<>(y.getId(), schedule));
//                            }
//                        }
//                    }
//                }
//            }
//
//            Map<String, Object> schedules = ensureSubtree(desired, RossDesiredSettings.SCHEDULE_SETTINGS_NAME);
//            schedules.put(ScheduleDesiredSettings.IANA_TIME_ZONE_ID, localTimeZone.getId());
//            Map<String, Object> allSchedules = ensureSubtree(schedules, ScheduleDesiredSettings.SCHEDULES);
//
//            for (Pair<String, ScheduleInfoDto> schedulePair : relaySchedules) {
//                String id = schedulePair.getKey();
//                allSchedules.put(id, schedulePair.getValue());
//                changed.put(id, schedulePair.getValue());
//            }
//        });
//
//        actions.add((desired, changed) -> {
//            Map<String, RelayMapValue> relayMap = new HashMap<>();
//            if (relayOperation != null) {
//                for (SchedulesForCtrlPageDto.RelayOperationViewModel x : relayOperation) {
//                    relayMap.put(x.getId(), new RelayMapValue(mapOverride(x), x.getRelayName()));
//                }
//            }
//
//            if (relayMap.isEmpty()) {
//                return;
//            }
//
//            Map<String, Object> relays = ensureSubtree(desired, RossDesiredSettings.RELAY_SETTINGS_NAME);
//
//            SchedulesForCtrlPageDto.RelayOperationViewModel relayAuto = null;
//            if (relayOperation != null) {
//                for (SchedulesForCtrlPageDto.RelayOperationViewModel p : relayOperation) {
//                    if (p.getMode() == SchedulesForCtrlPageDto.RelayModes.Auto) {
//                        relayAuto = p;
//                        break;
//                    }
//                }
//            }
//
//            for (int relayNumber1Based = 1; relayNumber1Based <= 4; relayNumber1Based++) {
//                String name = "Relay" + relayNumber1Based;
//                if (relayMap.containsKey(name)) {
//                    RelayMapValue toCopy = relayMap.get(name);
//                    Map<String, Object> specifics = ensureSubtree(relays, String.valueOf(relayNumber1Based));
//                    if (relayAuto != null && relayOperation.stream().anyMatch(p -> p.getMode() == SchedulesForCtrlPageDto.RelayModes.Auto)) {
//                        if (toCopy.getName().equals(relayAuto.getRelayName())) {
//                            specifics.put(IndividualRelayDesiredSettings.OVERRIDE_NAME, null);
//                        } else {
//                            specifics.put(IndividualRelayDesiredSettings.OVERRIDE_NAME, toCopy.getOverride());
//                        }
//                    } else {
//                        specifics.put(IndividualRelayDesiredSettings.OVERRIDE_NAME, toCopy.getOverride());
//                    }
//                }
//            }
//        });
//
//        actions.add((desired, changed) -> {
//            if (smartRelaySettings != null && relayOperation != null) {
//                SchedulesForCtrlPageDto.RelayOperationViewModel smartOne = null;
//                for (SchedulesForCtrlPageDto.RelayOperationViewModel p : relayOperation) {
//                    if (p.getRelayName().equals(smartRelaySettings.getName())) {
//                        smartOne = p;
//                        break;
//                    }
//                }
//                if (smartOne != null) {
//                    Map<String, Object> patch = ensureSubtree(desired, RossDesiredSettings.SMART_RELAY_SETTINGS_NAME);
//                    switch (smartOne.getMode()) {
//                        case Off:
//                            patch.put(SmartRelaySettingsDesired.LOAD_CONTROL_MODE_NAME, SmartRelayMode.Off);
//                            break;
//                        case On:
//                            patch.put(SmartRelaySettingsDesired.LOAD_CONTROL_MODE_NAME, SmartRelayMode.On);
//                            break;
//                        case Auto:
//                            patch.put(SmartRelaySettingsDesired.LOAD_CONTROL_MODE_NAME, SmartRelayMode.Auto);
//                            break;
//                        case Schedule:
//                            if (smartRelaySettings.getMode() == SmartRelayMode.On) {
//                                patch.put(SmartRelaySettingsDesired.LOAD_CONTROL_MODE_NAME, SmartRelayMode.Off);
//                            }
//                            break;
//                    }
//                }
//            }
//        });
//
//        return actions;
//    }

    public static ScheduleInfoDto mapToDeviceTwinScheduleInfo(
            SchedulesForCtrlPageDto.InverterScheduleItemViewModel schedule,
            java.time.ZoneId zoneId) {

        java.time.OffsetDateTime startTime = schedule.getStartTime().withOffsetSameInstant(java.time.ZoneOffset.UTC);
        java.time.Duration duration = java.time.Duration.between(schedule.getStartTime(), schedule.getEndTime());

        Duration sinceMidnight = Duration.between(LocalTime.MIDNIGHT, startTime.toLocalTime());

        if (schedule.getScheduleDays() != null) {
            return new ScheduleInfoDto(
                    ScheduleInfoDto.USER_SCHEDULE_PRIORITY,
                    null,
                    null,
                    sinceMidnight,
                    duration,
                    schedule.getScheduleDays(),
                    map(schedule.getMode()),
                    schedule.getPowerInWatts().intValue()
            );
        } else {
            java.time.LocalDateTime localDateTime = java.time.LocalDateTime.of(
                    startTime.getYear(),
                    startTime.getMonthValue(),
                    startTime.getDayOfMonth(),
                    startTime.getHour(),
                    startTime.getMinute(),
                    startTime.getSecond()
            );

            java.time.ZonedDateTime utcStart;
            try {
                List<ZoneOffset> validOffsets = zoneId.getRules().getValidOffsets(localDateTime);
                if (validOffsets.size() != 1) {
                    // 如果为0，表示时间被跳过；如果大于1，表示时间模糊
                    throw new DateTimeException("Ambiguous or skipped local date-time");
                }
                ZoneOffset offset = validOffsets.get(0);
                utcStart = ZonedDateTime.ofStrict(localDateTime, offset, zoneId);
            } catch (Exception e) {
                throw new IllegalStateException("Schedule start time coincides with a daylight savings transition period.", e);
            }


            return new ScheduleInfoDto(
                    ScheduleInfoDto.USER_SCHEDULE_PRIORITY,
                    utcStart,
                    utcStart.plus(duration),
                    null,
                    null,
                    null,
                    map(schedule.getMode()),
                    schedule.getPowerInWatts().intValue()
            );
        }
    }

    public static ScheduleV2Dto mapToDeviceTwinScheduleV2(SchedulesForCtrlPageDto.InverterScheduleItemViewModel schedule, ZoneId localTimeZone) {
        Map<String, Object> settingsValues = new HashMap<>();

        SettingsV2DesiredBase settings = new SettingsV2DesiredBase(
                null,
                null,
                null,
                settingsValues,
                null,
                null,
                null
        );

        settingsValues.put(ESGSettingPaths.InverterModeSettingName,mapToV2(schedule.getMode()).ordinal());
        settingsValues.put(ESGSettingPaths.InverterModePowerSettingName,
                schedule.getMode() == SchedulesForCtrlPageDto.InverterScheduleOperationModeEnum.Auto ? 0 : schedule.getPowerInWatts());

        var startTime = schedule.getStartTime().withOffsetSameInstant(java.time.ZoneOffset.UTC);
        var between = Duration.between(schedule.getStartTime(), schedule.getEndTime());
        Duration sinceMidnight = Duration.between(LocalTime.MIDNIGHT, startTime.toLocalTime());

        if (schedule.getScheduleDays() != null) {
            return new ScheduleV2Dto(
                    ScheduleV2Dto.USER_SCHEDULE_PRIORITY,
                    localTimeZone.getId(),
                    null,
                    null,
                    schedule.getScheduleDays().getValue(),
                    sinceMidnight,
                    null,
                    between,
                    settings
            );
        } else {
            java.time.LocalDateTime localDateTime = java.time.LocalDateTime.of(
                    startTime.getYear(),
                    startTime.getMonthValue(),
                    startTime.getDayOfMonth(),
                    startTime.getHour(),
                    startTime.getMinute(),
                    startTime.getSecond()
            );

            java.time.ZonedDateTime utcStart;
            try {
                List<ZoneOffset> validOffsets = localTimeZone.getRules().getValidOffsets(localDateTime);
                if (validOffsets.size() != 1) {
                    // 如果为0，表示时间被跳过；如果大于1，表示时间模糊
                    throw new DateTimeException("Ambiguous or skipped local date-time");
                }
                ZoneOffset offset = validOffsets.get(0);
                utcStart = ZonedDateTime.ofStrict(localDateTime, offset, localTimeZone);
            } catch (Exception e) {
                throw new IllegalStateException("Schedule start time coincides with a daylight savings transition period.", e);
            }

            return new ScheduleV2Dto(
                    ScheduleV2Dto.USER_SCHEDULE_PRIORITY,
                    localTimeZone.getId(),
                    utcStart,
                    utcStart.plus(between),
                    null,
                    null,
                    null,
                    null,
                    settings
            );
        }
    }

    private static String map(SchedulesForCtrlPageDto.InverterScheduleOperationModeEnum mode) {
        if (mode == null) {
            return null;
        }

        switch (mode) {
            case Auto:
                return "Auto";
            case ChargeBattery:
                return "ChargeB";
            case DischargeBattery:
                return "DischargeB";
            case ImportPower:
                return "Import";
            case ExportPower:
                return "Export";
            case Conserve:
                return "Conserve";
            default:
                throw new IllegalArgumentException("Invalid inverter schedule mode " + mode);
        }
    }

    private static SystemStatusEnum.InverterModeValue mapToV2(SchedulesForCtrlPageDto.InverterScheduleOperationModeEnum mode) {
        if (mode == null) {
            return null;
        }

       return Arrays.stream(SystemStatusEnum.InverterModeValue.values()).filter(value -> value.name().equals(mode.name())).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid inverter schedule mode " + mode));
    }
}