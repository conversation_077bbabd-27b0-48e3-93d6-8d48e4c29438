package com.ebon.energy.fms.util;

import com.ebon.energy.fms.common.enums.Endianness;
import com.ebon.energy.fms.common.enums.Endianness.*;
import com.google.common.primitives.Bytes;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class BytesUtils {

    public static void writeUInt64(List<Byte> buffer, int index, Endianness endianness, int size, long value) {
        if (buffer == null) {
            throw new NullPointerException("buffer");
        }

        if (size <= 0 || size > 8) {
            throw new IllegalArgumentException("Size must be between 1 and 8 (inclusive), got: " + size);
        }

        if (index < 0 || size + index > buffer.size()) {
            throw new IndexOutOfBoundsException("Trying to write " + size + " bytes at " + index + " in array of size " + buffer.size());
        }

        switch (endianness) {
            case Big:
            case BigEndian:
                int endOffset = index + size - 1;
                for (int i = 0; i < size; i++) {
                    buffer.set(endOffset - i, (byte)(value & 0xff));
                    value >>>= 8;
                }
                break;
            case Little:
            case LittleEndian:
                for (int i = 0; i < size; i++) {
                    buffer.set(i + index, (byte)(value & 0xff));
                    value >>>= 8;
                }
                break;
        }
    }

    public static void writeInt64(List<Byte> buffer, int index, Endianness endianness, int size, long value) {
        writeUInt64(buffer, index, endianness, size, value);
    }

    public static long readUInt64(List<Byte> buffer, int index, Endianness endianness, int size) {
        if (buffer == null) {
            throw new NullPointerException("buffer");
        }

        if (size <= 0 || size > 8) {
            throw new IllegalArgumentException("Size must be between 1 and 8 (inclusive), got: " + size);
        }

        if (index < 0 || size + index > buffer.size()) {
            throw new IndexOutOfBoundsException("Trying to read " + size + " bytes at " + index + " in array of size " + buffer.size());
        }

        long ret = 0;
        switch (endianness) {
            case Big:
            case BigEndian:
                for (int i = 0; i < size; i++) {
                    ret = (ret << 8) | (buffer.get(index + i) & 0xff);
                }
                return ret;
            case Little:
            case LittleEndian:
                for (int i = 0; i < size; i++) {
                    ret = (ret << 8) | (buffer.get(index + size - 1 - i) & 0xff);
                }
                return ret;
            default:
                return 0;
        }
    }

    public static long readInt64(List<Byte> buffer, int index, Endianness endianness, int size) {
        return readUInt64(buffer, index, endianness, size);
    }

    public static byte getUInt8(byte[] data, int index, Endianness endianness) {
        return (byte)readUInt64(Bytes.asList(data), index, endianness, 1);
    }

    public static byte getInt8(byte[] data, int index, Endianness endianness) {
        return (byte)readInt64(Bytes.asList(data), index, endianness, 1);
    }

    public static short getInt16(byte[] data, int index, Endianness endianness) {
        return (short)readInt64(Bytes.asList(data), index, endianness, 2);
    }

    public static int getUInt16(byte[] data, int index, Endianness endianness) {
        return (int)readUInt64(Bytes.asList(data), index, endianness, 2);
    }

    public static int getInt32(byte[] data, int index, Endianness endianness) {
        return (int)readInt64(Bytes.asList(data), index, endianness, 4);
    }

    public static long getUInt32(byte[] data, int index, Endianness endianness) {
        return readUInt64(Bytes.asList(data), index, endianness, 4);
    }

    public static float getSingle(byte[] data, int index, Endianness endianness) {
        return Float.intBitsToFloat((int)getUInt32(data, index, endianness));
    }

    public static int getUInt24(byte[] data, int index, Endianness endianness) {
        return (int)readUInt64(Bytes.asList(data), index, endianness, 3);
    }

    public static long getUInt48(byte[] data, int index, Endianness endianness) {
        return readUInt64(Bytes.asList(data), index, endianness, 6);
    }

    public static long getInt64(byte[] data, int index, Endianness endianness) {
        return readInt64(Bytes.asList(data), index, endianness, 8);
    }

    public static long getUInt64(byte[] data, int index, Endianness endianness) {
        return readUInt64(Bytes.asList(data), index, endianness, 8);
    }

    public static void set(List<Byte> data, int index, Endianness endianness, byte value) {
        writeUInt64(data, index, endianness, 1, value);
    }

    public static void set(List<Byte> data, int index, Endianness endianness, short value) {
        writeInt64(data, index, endianness, 2, value);
    }

    public static void set(List<Byte> data, int index, Endianness endianness, int value) {
        writeInt64(data, index, endianness, 4, value);
    }

    public static void set(List<Byte> data, int index, Endianness endianness, long value) {
        writeInt64(data, index, endianness, 8, value);
    }

    public static void set(List<Byte> data, int index, Endianness endianness, float value) {
        set(data, index, endianness, Float.floatToIntBits(value));
    }

    public static void set24(List<Byte> data, int index, Endianness endianness, int value) {
        writeUInt64(data, index, endianness, 3, value);
    }

    public static int bitRoll(int value, int by) {
        return (value >>> by) | (value << (32 - by));
    }

    public static short bitRoll(short value, int by) {
        return (short)((value >>> by) | (value << (16 - by)));
    }

    public static int swapEndianness(int x) {
        return Integer.reverseBytes(x);
    }

    public static short swapEndianness(short x) {
        return Short.reverseBytes(x);
    }

    public static long swapEndianness(long x) {
        return Long.reverseBytes(x);
    }

    public static byte setBit(byte bitflag, byte mask, boolean value) {
        return (byte)(value ? (bitflag | mask) : (bitflag & ~mask));
    }

    public static short setBit(short bitflag, short mask, boolean value) {
        return (short)(value ? (bitflag | mask) : (bitflag & ~mask));
    }

    public static int setBit(int bitflag, int mask, boolean value) {
        return value ? (bitflag | mask) : (bitflag & ~mask);
    }

    public static String toString(List<Byte> data, char delimiter) {
        return toString(data, delimiter, 0, data.size());
    }

    public static String toString(List<Byte> data, char delimiter, int start, int len) {
        if (data == null) {
            return "";
        }

        if (start < 0 || len <= 0 || start >= data.size()) {
            return "";
        }

        if (start + len > data.size()) {
            len = data.size() - start;
        }

        StringBuilder sb = new StringBuilder((len * 3) - 1);
        for (int i = 0; i < len; i++) {
            if (i > 0) {
                sb.append(delimiter);
            }

            byte b = data.get(start + i);
            write4(sb, (b >> 4) & 0xF);
            write4(sb, b & 0xF);
        }

        return sb.toString();
    }

    private static void write4(StringBuilder sb, int b) {
        if (b < 10) {
            sb.append((char)('0' + b));
        } else {
            sb.append((char)('A' + (b - 10)));
        }
    }

    public static String toCSharp(byte[] buffer) {
        StringBuilder sb = new StringBuilder(6 * buffer.length);
        for (byte b : buffer) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append("0x");
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

    public static String toASCIIString(byte[] data, int start, int len) {
        if (len == 0) {
            return "";
        }

        if (start < 0 || len < 0 || start + len > data.length) {
            throw new IndexOutOfBoundsException("Out of bound get, start: " + start + ", count: " + len + ", Length: " + data.length);
        }

        return new String(data, start, len);
    }

    public static byte[] fromString(String str) {
        if (str == null) {
            return new byte[0];
        }

        List<Byte> result = new ArrayList<>(str.length() / 2);
        int value = 0;
        int index = 0;
        for (char c : str.toCharArray()) {
            if (c >= '0' && c <= '9') {
                value += (c - '0');
            } else if (c >= 'a' && c <= 'f') {
                value += (c - 'a' + 10);
            } else if (c >= 'A' && c <= 'F') {
                value += (c - 'A' + 10);
            } else if (c == ' ') {
                continue;
            } else {
                throw new IllegalArgumentException("Bad string '" + str + "'");
            }

            index++;
            if (index == 2) {
                index = 0;
                result.add((byte)value);
                value = 0;
            } else {
                value <<= 4;
            }
        }

        byte[] bytes = new byte[result.size()];
        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = result.get(i);
        }
        return bytes;
    }
}