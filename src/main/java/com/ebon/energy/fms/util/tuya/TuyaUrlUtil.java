package com.ebon.energy.fms.util.tuya;

import java.net.URLEncoder;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2024/5/30 10:39
 */
public class TuyaUrlUtil {

    private final static String CHARSET_UTF8 = "utf8";

    public static String generateQueryString(Map<String, String> params, boolean isEncodeKV) {
        StringBuilder canonicalizedQueryString = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (isEncodeKV) {
                canonicalizedQueryString.append(percentEncode(entry.getKey())).append("=")
                        .append(percentEncode(entry.getValue())).append("&");
            } else {
                canonicalizedQueryString.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        if (canonicalizedQueryString.length() > 1) {
            canonicalizedQueryString.setLength(canonicalizedQueryString.length() - 1);
        }
        return canonicalizedQueryString.toString();
    }

    public static String percentEncode(String value) {
        try {
            // After encoding with URLEncoder.encode, replace '+', '*', and '%7E' to meet the API's encoding specifications.
            return value == null ? null
                    : URLEncoder.encode(value, CHARSET_UTF8).replace("+", "%20").replace("*", "%2A").replace("%7E",
                    "~");
        } catch (Exception e) {
        }
        return "";
    }
}