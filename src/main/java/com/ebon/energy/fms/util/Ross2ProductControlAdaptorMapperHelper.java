package com.ebon.energy.fms.util;

import com.ebon.energy.fms.common.enums.InverterOperationModeEnum;
import com.ebon.energy.fms.common.enums.InverterOperationTypeEnum;
import com.ebon.energy.fms.domain.vo.product.control.InverterOperationViewModel;
import com.ebon.energy.fms.domain.vo.product.control.ProductControlViewModel;
import com.ebon.energy.fms.domain.vo.product.control.SchedulesForCtrlPageDto;
import com.ebon.energy.fms.domain.vo.product.control.invert.GenericScheduleItemViewModel;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class Ross2ProductControlAdaptorMapperHelper {

    public static SchedulesForCtrlPageDto translate(ProductControlViewModel model) {
        SchedulesForCtrlPageDto.InverterOperationViewModel io = null;
        if (model.getInverterOperation() != null) {
            InverterOperationViewModel sourceIO = model.getInverterOperation();
            List<SchedulesForCtrlPageDto.InverterScheduleItemViewModel> schedules = null;
            if (sourceIO.getSchedules() != null) {
                schedules = sourceIO.getSchedules().stream()
                        .map(s -> s == null ? null : new SchedulesForCtrlPageDto.InverterScheduleItemViewModel(
                                s.getId() != null ? s.getId() : UUID.randomUUID().toString(),
                                s.getStartTime(),
                                s.getEndTime(),
                                s.getScheduleDaysInternal(),
                                s.getTimezone(),
                                SchedulesForCtrlPageDto.InverterScheduleOperationModeEnum.valueOf(s.getMode().name()),
                                s.getPowerInWatts(),
                                s.getPriority()
                        ))
                        .collect(Collectors.toList());
            }
            io = new SchedulesForCtrlPageDto.InverterOperationViewModel(
                    SchedulesForCtrlPageDto.InverterOperationTypeEnum.valueOf(sourceIO.getType()),
                    SchedulesForCtrlPageDto.InverterOperationModeEnum.valueOf(sourceIO.getMode()),
                    sourceIO.getPowerInWatts(),
                    schedules
            );
        }

        List<SchedulesForCtrlPageDto.RelayOperationViewModel> ro = null;
//        if (model.getRelayOperation() != null) {
//            ro = model.getRelayOperation().stream()
//                    .map(r -> {
//                        List<GenericScheduleItemViewModel> schedules = null;
//                        if (r.getSchedules() != null) {
//                            schedules = r.getSchedules().stream()
//                                    .map(s -> s == null ? null : new GenericScheduleItemViewModel(
//                                            s.getId() != null ? s.getId() : UUID.randomUUID().toString(),
//                                            s.getStartTime(),
//                                            s.getEndTime(),
//                                            s.getScheduleDays(),
//                                            s.getTimezone()
//                                    ))
//                                    .collect(Collectors.toList());
//                        }
//                        return new RelayOperationViewModel(
//                                r.getId(),
//                                r.getRelayNumber(),
//                                r.getRelayName(),
//                                RelayModes.valueOf(r.getMode().name()),
//                                schedules,
//                                r.getInstalled(),
//                                r.getBaseState(),
//                                r.getOverride()
//                        );
//                    })
//                    .collect(Collectors.toList());
//        }

        SchedulesForCtrlPageDto.SmartRelayOperationViewModel smo = null;
//        if (model.getSmartRelayOperation() != null) {
//            SmartRelayOperationViewModel sourceSmo = model.getSmartRelayOperation();
//            List<SmartRelayScheduleItemViewModel> schedules = null;
//            if (sourceSmo.getSchedules() != null) {
//                schedules = sourceSmo.getSchedules().stream()
//                        .map(s -> s == null ? null : new SmartRelayScheduleItemViewModel(
//                                s.getId() != null ? s.getId() : UUID.randomUUID().toString(),
//                                s.getStartTime(),
//                                s.getEndTime(),
//                                s.getScheduleDays(),
//                                s.getTimezone()
//                        ))
//                        .collect(Collectors.toList());
//            }
//            smo = new SmartRelayOperationViewModel(
//                    sourceSmo.getMode(),
//                    schedules
//            );
//        }

        return new SchedulesForCtrlPageDto(io, ro, smo);
    }
}
