package com.ebon.energy.fms.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseSchedule {

    // 日期格式
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    // 时间格式
    public static final String TIME_FORMAT = "hh:mma";

    @JsonProperty("StartTime")
    private String startTime;

    @JsonProperty("EndTime")
    private String endTime;

    @JsonProperty("Recurring")
    private boolean recurring;

    @JsonProperty("ScheduleDate")
    private String scheduleDate;

    public void setScheduleDate(Date d) {
        this.scheduleDate = new SimpleDateFormat(DATE_FORMAT, Locale.ROOT).format(d);
    }

    public void setStartTime(Date d) {
        this.startTime = new SimpleDateFormat(TIME_FORMAT, Locale.ROOT).format(d);
    }

    public void setEndTime(Date d) {
        this.endTime = new SimpleDateFormat(TIME_FORMAT, Locale.ROOT).format(d);
    }

    public Date getStartTimeAsDate() throws ParseException {
        return new SimpleDateFormat(TIME_FORMAT, Locale.ROOT).parse(this.startTime);
    }

    public Date getEndTimeAsDate() throws ParseException {
        return new SimpleDateFormat(TIME_FORMAT, Locale.ROOT).parse(this.endTime);
    }

    public void fixStartAndEndTimesOrThrow() throws Exception {
        this.startTime = formatTimeOrThrow(this.startTime);
        this.endTime = formatTimeOrThrow(this.endTime);
    }

    public static String formatTimeOrThrow(String timeString) throws Exception {
        String[] cultures = {"en-AU", "mi-NZ", "zh"};
        for (String culture : cultures) {
            try {
                Locale locale = Locale.forLanguageTag(culture);
                Date date = tryParseTime(timeString, locale);
                if (date != null) {
                    return new SimpleDateFormat(TIME_FORMAT, Locale.ROOT).format(date);
                }
            } catch (Exception ignored) {}
        }
        throw new UnsupportedOperationException("Unable to parse time '" + timeString + "'");
    }

    private static Date tryParseTime(String timeString, Locale locale) {
        String[] patterns = {"hh:mma", "hh:mm a", "hh:mm aaaa", "hh:mm", "hh:mmaaa"};
        for (String pattern : patterns) {
            try {
                return new SimpleDateFormat(pattern, locale).parse(timeString);
            } catch (Exception ignored) {}
        }
        return null;
    }

    public Date getScheduleDateAsDate() {
        try {
            return new SimpleDateFormat(DATE_FORMAT, Locale.ROOT).parse(this.scheduleDate);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查找并返回第一个错误
     * @return 没有错误返回null
     */
    public String validate() {
        try {
            getStartTimeAsDate();
            getEndTimeAsDate();
            return null;
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }
}
