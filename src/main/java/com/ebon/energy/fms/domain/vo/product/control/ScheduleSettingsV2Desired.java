package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@NoArgsConstructor
public class ScheduleSettingsV2Desired implements Serializable {

    public static final String VERSION_SETTINGS_NAME = "version";
    public static final String SCHEDULE_SETTINGS_NAME = "schedules";

    @JsonProperty(VERSION_SETTINGS_NAME)
    private int version;

    @JsonProperty(SCHEDULE_SETTINGS_NAME)
    @Getter(AccessLevel.NONE)
    private Map<String, ScheduleV2Dto> schedules = Collections.emptyMap();

    @JsonCreator
    public ScheduleSettingsV2Desired(
            @JsonProperty(VERSION_SETTINGS_NAME) int version,
            @JsonProperty(SCHEDULE_SETTINGS_NAME) Map<String, ScheduleV2Dto> schedules) {
        this.version = version;
        this.schedules = schedules != null ? Collections.unmodifiableMap(new HashMap<>(schedules)) : Collections.emptyMap();
    }

    public static ScheduleSettingsV2Desired getDefault() {
        return new ScheduleSettingsV2Desired();
    }

    public Map<String, ScheduleV2Dto> getSchedules() {
        return schedules;
    }

    public ScheduleSettingsV2Desired withUpdatedSchedules(Map<String, ScheduleV2Dto> extras) {
        Map<String, ScheduleV2Dto> newSchedules = new HashMap<>(this.schedules);
        for (Map.Entry<String, ScheduleV2Dto> entry : extras.entrySet()) {
            newSchedules.put(entry.getKey(), entry.getValue());
        }
        return new ScheduleSettingsV2Desired(this.version + 1, Collections.unmodifiableMap(newSchedules));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ScheduleSettingsV2Desired)) return false;
        ScheduleSettingsV2Desired that = (ScheduleSettingsV2Desired) o;
        return version == that.version &&
                Objects.equals(schedules, that.schedules);
    }

    @Override
    public int hashCode() {
        return Objects.hash(version, schedules);
    }
}
