package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PV {
    private Double V;
    private Double I;
    private Double P;
    private PVStatusValue Status;
    private Double VFaultLevel;
    private Long VFaultEpoch;
    private Double DayTotalE;
    private Double AllTimeTotalE;

    public PV() {
    }

    public PV(Double v, Double i, Double p, PVStatusValue status,
              Double vFaultLevel, Long vFaultEpoch,
              Double dayTotalE, Double allTimeTotalE) {
        this.V = v;
        this.I = i;
        this.P = p;
        this.Status = status;
        this.VFaultLevel = vFaultLevel;
        this.VFaultEpoch = vFaultEpoch;
        this.DayTotalE = dayTotalE;
        this.AllTimeTotalE = allTimeTotalE;
    }

    public Double getV() {
        return V;
    }

    public void setV(Double v) {
        V = v;
    }

    public Double getI() {
        return I;
    }

    public void setI(Double i) {
        I = i;
    }

    public Double getP() {
        return P;
    }

    public void setP(Double p) {
        P = p;
    }

    public PVStatusValue getStatus() {
        return Status;
    }

    public void setStatus(PVStatusValue status) {
        Status = status;
    }

    public Double getVFaultLevel() {
        return VFaultLevel;
    }

    public void setVFaultLevel(Double vFaultLevel) {
        VFaultLevel = vFaultLevel;
    }

    public Long getVFaultEpoch() {
        return VFaultEpoch;
    }

    public void setVFaultEpoch(Long vFaultEpoch) {
        VFaultEpoch = vFaultEpoch;
    }

    public Double getDayTotalE() {
        return DayTotalE;
    }

    public void setDayTotalE(Double dayTotalE) {
        DayTotalE = dayTotalE;
    }

    public Double getAllTimeTotalE() {
        return AllTimeTotalE;
    }

    public void setAllTimeTotalE(Double allTimeTotalE) {
        AllTimeTotalE = allTimeTotalE;
    }
}