package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.UpdateResult;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;


public class BandESA extends DataBackedBand
{
	public BandESA()
	{
		super(BandForge.<BandESA>getMetadataFor(BandESA.class));
	}


	public BandESA(byte[] bytes)
	{
		super(bytes, BandForge.<BandESA>getMetadataFor(BandESA.class));
	}

	public BandESA(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESA>getMetadataFor(BandESA.class));
	}


	
	public final String getSerialNumber()
	{
		return GetBufS(0, 16, StringProcessors.GoodweDecode);
	}

	
	public final String getNominalVpv()
	{
		return GetBufS(16, 4, StringProcessors.GoodweDecode);
	}

	
	public final String getFirmwareVersionString()
	{
		return GetBufS(20, 12, StringProcessors.GoodweDecode);
	}

	
	public final String getModelName()
	{
		return GetBufS(32, 10, StringProcessors.GoodweDecode);
	}

	
	public final String getDSPFirmwareVersion()
	{
		return GetBufS(42, 12, StringProcessors.GoodweDecode);
	}

	
	public final String getARMFirmwareVersion()
	{
		return GetBufS(54, 12, StringProcessors.GoodweDecode);
	}

	
	public final String getManufacturerInfo()
	{
		return GetBufS(66, 16, StringProcessors.GoodweDecode);
	}

	


	public final int getFirmwareVersion() { return GetU16(82); }

	
	public final Object getArmUpdateResult()
	{
		return UpdateResult.parse(GetU16(84));
	}

	
	public final Object getDspUpdateResult()
	{
		return UpdateResult.parse(GetU16(86));
	}


	/** 
	 Gets the compiled FirmwareVersionString to satisfy the IPrimaryBands implementation
	 
	 @return 
	 Extracted version string
	 
	*/
	public final String GetFirmwareVersionString()
	{
		return InverterAdapterHelper.getDspVersionFromInternal(getDSPFirmwareVersion()) + InverterAdapterHelper.getDspVersionFromInternal(getDSPFirmwareVersion()) + InverterAdapterHelper.getArmVersionFromInternal(getARMFirmwareVersion());
	}
}
