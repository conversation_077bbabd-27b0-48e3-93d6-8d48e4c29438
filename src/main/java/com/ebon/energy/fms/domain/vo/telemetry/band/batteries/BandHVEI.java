package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;

public class BandHVEI extends DataBackedBand implements IBandHVEI
{
	public BandHVEI()
	{
		super(BandForge.<BandHVEI>getMetadataFor(BandHVEI.class));
	}



	public BandHVEI(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVEI>getMetadataFor(BandHVEI.class));
	}

	public BandHVEI(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVEI>getMetadataFor(BandHVEI.class));
	}


	
	public final String getManufacturersName()
	{
		return GetBufS(0, 10, StringProcessors.PylonHVDecode);
	}

	public final String getDeviceCode()
	{
		return GetBufS(10, 10, StringProcessors.PylonHVDecode);
	}

	public final int getVersionNumber()
	{
		return GetU16(20);
	}

	public final int getInternalVersionNumber()
	{
		return GetU16(22);
	}


	public final int getNumberOfPiles()
	{
		return GetU16(24);
	}
}
