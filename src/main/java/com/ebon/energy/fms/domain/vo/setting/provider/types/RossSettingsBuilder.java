package com.ebon.energy.fms.domain.vo.setting.provider.types;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.RossSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.provider.ICommonSettingsBuilder;
import com.ebon.energy.fms.domain.vo.setting.provider.RossSettingPaths;
import com.ebon.energy.fms.domain.vo.setting.provider.SchedulePriority;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class RossSettingsBuilder extends SettingsBuilder<RossSettingsReader, RossSettingPaths> {


    public RossSettingsBuilder(DeviceInfoAndSettings deviceInfoAndSettings, Instant nowInUtc) {
        super(deviceInfoAndSettings, nowInUtc, new RossSettingsReader(deviceInfoAndSettings), new RossSettingPaths());
    }

    @Override
    public ICommonSettingsBuilder patchManagedInverterSetting(String settingName, JsonNode value, String settingIndex, ManagedInverterSettingDesired.InverterSettingValueType settingType, ManagedInverterSettingDesired.InverterSettingExecutionType executionType, ManagedInverterSettingDesired.InverterSettingSource source, String uniqueId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addShadowScan(boolean enableShadowScan) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addACCoupled(boolean enableACCoupledMode) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(BigDecimal powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(Double powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteExportLimit(Watt limit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftAndHardSiteExportLimit(Watt soft, Watt hard) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteGenerationLimit(VoltAmps generationLimit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSiteExportLimit(boolean enabled, Watt power) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder overrideDesiredPowerRampRateLimit(Duration rampTime, String settingId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addBatterySettings(ManufacturerEnum manufacturer, Integer batteryCount, Integer maxChargeCurrent, Integer maxDischargeCurrent, Integer minSoc, Integer minOffgridSoc) {
        ObjectMapper objectMapper = new ObjectMapper();

        // 设置 Intent 字段
        getDeviceSettings().getIntent().setBatteryManufacturer(manufacturer != null ? manufacturer.toString() : null);
        getDeviceSettings().getIntent().setBatteryCount(batteryCount);
        getDeviceSettings().getIntent().setBatteryMaxChargeCurrent(maxChargeCurrent);
        getDeviceSettings().getIntent().setBatteryMaxDischargeCurrent(maxDischargeCurrent);
        getDeviceSettings().getIntent().setBatteryMinSoc(minSoc);
        getDeviceSettings().getIntent().setBatteryMinOffgridSoc(minOffgridSoc);
        getDeviceSettings().getIntent().setBatterySettingsModifiedUtc(getNowInUtc());

        // 添加设置到 DesiredPatch
        addValue(getDesiredPatch(), getSettingPaths().getBatteryManufacturerSetting(),
                objectMapper.valueToTree(getDeviceSettings().getIntent().getBatteryManufacturer()));
        addValue(getDesiredPatch(), getSettingPaths().getBatteryCountSetting(),
                objectMapper.valueToTree(batteryCount));
        addValue(getDesiredPatch(), getSettingPaths().getBatteryMaxChargeCurrentSetting(),
                objectMapper.valueToTree(maxChargeCurrent));
        addValue(getDesiredPatch(), getSettingPaths().getBatteryMaxDischargeCurrentSetting(),
                objectMapper.valueToTree(maxDischargeCurrent));
        addValue(getDesiredPatch(), getSettingPaths().getBatteryMinSocSetting(),
                objectMapper.valueToTree(minSoc));
        addValue(getDesiredPatch(), getSettingPaths().getBatteryMinOffgridSocSetting(),
                objectMapper.valueToTree(minOffgridSoc));

        return this;
    }

    @Override
    public ICommonSettingsBuilder addCtFlipSettings(Boolean flipCt1, Boolean flipCt2) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDefaultTelemetryPeriod() {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addTelemetryPeriod(Duration period, Instant endDateUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDredSettings(Boolean dredSubscribed) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder removeSchedule(String scheduleId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, Instant endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, Instant startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, LocalDateTime endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, LocalDateTime startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public SchedulePriority getSchedulePriority() {
        return null;
    }

    @Override
    public ICommonSettingsBuilder disableAgeingMode() {
        return null;
    }

    @Override
    public void deleteRelaySchedule(int relayNumber1Based, String id, Map patch) {

    }

    @Override
    public ICommonSettingsBuilder buildInverterPatch(RossDesiredSettings desired, List expectedSettings, Map toPatch) {
        return null;
    }
}
