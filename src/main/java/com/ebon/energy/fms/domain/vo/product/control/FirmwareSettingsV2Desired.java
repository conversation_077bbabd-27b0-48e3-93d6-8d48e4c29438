package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Data
@NoArgsConstructor
public class FirmwareSettingsV2Desired implements Serializable {

    public static final String VERSION_NUMBER_SETTING_NAME = "versionNumber";
    public static final String SOURCE_URL_SETTING_NAME = "url";
    public static final String FORCE_UPDATE_SETTING_NAME = "forceUpdate";
    public static final String FILE_HASH_SETTING_NAME = "sha256";

    @JsonProperty(VERSION_NUMBER_SETTING_NAME)
    private String versionNumber;

    @JsonProperty(SOURCE_URL_SETTING_NAME)
    private String sourceUrl;

    @JsonProperty(FORCE_UPDATE_SETTING_NAME)
    private Boolean force;

    @JsonProperty(FILE_HASH_SETTING_NAME)
    private String fileHash;

    // Copy constructor
    public FirmwareSettingsV2Desired(FirmwareSettingsV2Desired f) {
        this.versionNumber = f.versionNumber;
        this.sourceUrl = f.sourceUrl;
        this.force = f.force;
        this.fileHash = f.fileHash;
    }

    @JsonCreator
    public FirmwareSettingsV2Desired(
            @JsonProperty(VERSION_NUMBER_SETTING_NAME) String versionNumber,
            @JsonProperty(SOURCE_URL_SETTING_NAME) String sourceUrl,
            @JsonProperty(FORCE_UPDATE_SETTING_NAME) Boolean force,
            @JsonProperty(FILE_HASH_SETTING_NAME) String fileHash) {
        this.versionNumber = versionNumber;
        this.sourceUrl = sourceUrl;
        this.force = force;
        this.fileHash = fileHash;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FirmwareSettingsV2Desired)) return false;
        FirmwareSettingsV2Desired that = (FirmwareSettingsV2Desired) o;
        return Objects.equals(versionNumber, that.versionNumber) &&
                Objects.equals(sourceUrl, that.sourceUrl) &&
                Objects.equals(force, that.force) &&
                Objects.equals(fileHash, that.fileHash);
    }

    @Override
    public int hashCode() {
        return Objects.hash(versionNumber, sourceUrl, force, fileHash);
    }
}
