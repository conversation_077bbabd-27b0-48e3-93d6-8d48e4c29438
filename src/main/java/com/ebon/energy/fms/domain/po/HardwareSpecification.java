package com.ebon.energy.fms.domain.po;

import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.utils.HardwareModelHelpers;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class HardwareSpecification {

    @JsonProperty("HardwareFamily")
    private final HardwareFamilyEnum hardwareFamily;

    @JsonProperty("HardwareModel")
    private final HardwareModelEnum hardwareModel;

    @JsonProperty("IsThreePhaseInverter")
    public boolean isThreePhaseInverter() {
        return hardwareFamily == HardwareFamilyEnum.Inverter_Goodwe_ET;
    }

    @JsonProperty("IsSmartBatteryInverter")
    public boolean isSmartBatteryInverter() {
        return hardwareFamily == HardwareFamilyEnum.Inverter_Goodwe_BH;
    }

    @JsonProperty("IsSinglePhaseInverter")
    public boolean isSinglePhaseInverter() {
        return isSinglePhaseOfAnyModel() && !HardwareModelHelpers.isGen3(hardwareModel);
    }

    @JsonProperty("IsGridTieInverter")
    public boolean isGridTieInverter() {
        return hardwareFamily == HardwareFamilyEnum.Inverter_Senergy_SG;
    }

    @JsonProperty("IsGen3SinglePhaseInverter")
    public boolean isGen3SinglePhaseInverter() {
        return isSinglePhaseOfAnyModel() && HardwareModelHelpers.isGen3(hardwareModel);
    }

    @JsonProperty("IsSinglePhaseOfAnyModel")
    public boolean isSinglePhaseOfAnyModel() {
        return hardwareFamily == HardwareFamilyEnum.Inverter_Goodwe_ES;
    }

    public String getDisplayName() {
        String attr = hardwareFamily.getDisplayName();
        if (attr != null) {
            return attr;
        } else {
            return hardwareFamily.name();
        }
    }

    public String getDisplayShortName() {
        String attr = hardwareFamily.getShortName();
        if (attr != null) {
            return attr;
        } else {
            return hardwareFamily.name();
        }
    }
}
