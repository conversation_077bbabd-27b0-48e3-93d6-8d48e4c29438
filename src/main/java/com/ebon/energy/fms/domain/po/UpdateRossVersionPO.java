package com.ebon.energy.fms.domain.po;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ROSS 版本更新模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRossVersionPO {
    /**
     * 设备序列号
     */
    private List<String> serialNumbers;

    /**
     * ROSS 版本号
     */
    private String rossVersion;

    /**
     * Watchdog 版本号
     */
    private String watchdogVersion;

    /**
     * 是否强制更新
     */
    private Boolean force;
}