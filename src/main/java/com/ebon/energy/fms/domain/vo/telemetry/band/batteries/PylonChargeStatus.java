package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonChargeStatus
{
    Unused1((byte)(1 << 0)),
    Unused2((byte)(1 << 1)),
    BalanceChargeRequest((byte)(1 << 2)), // 均充请求标志位/Equalized request flag bit 1：ON；0：OFF
    FullChargeRequest((byte)(1 << 3)), // Unused
    ChargeRequestNoBMS((byte)(1 << 4)),
    ChargeRequestMandatory((byte)(1 << 5)),
    DischargeEnabled((byte)(1 << 6)),
    ChargeEnabled((byte)(1 << 7));

    public static final int SIZE = java.lang.Byte.SIZE;

    private byte byteValue;

    private PylonChargeStatus(byte value)
    {
        byteValue = value;
    }

    public byte getValue()
    {
        return byteValue;
    }

    public static PylonChargeStatus forValue(byte value)
    {
        for (PylonChargeStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static String parse(byte value) {
        PylonChargeStatus o = forValue(value);
        if (o != null) {
            return o.name();
        }

        Set<PylonChargeStatus> result = EnumSet.noneOf(PylonChargeStatus.class);
        PylonChargeStatus[] values = PylonChargeStatus.values();
        for (PylonChargeStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return String.valueOf(value);
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
