package com.ebon.energy.fms.domain.vo.setting.provider;

import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class SetAS4777_2_2020_GenerationLimitSettingsDto implements ISetAS4777_2_2020_GenerationLimitSettingsDto {
    @JsonProperty("GenerationHardLimitEnable")
    private final Boolean generationHardLimitEnable;

    @JsonProperty("GenerationHardLimitVA")
    private final VoltAmps generationHardLimitVA;

    @JsonProperty("GenerationSoftLimitEnable")
    private final Boolean generationSoftLimitEnable;

    @JsonProperty("GenerationSoftLimitVA")
    private final VoltAmps generationSoftLimitVA;
}