package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ACLoadPhase {

    public ACLoadPhase() { }

    public ACLoadPhase(BigDecimal i, BigDecimal p) {
        this.I = i;
        this.P = p;
    }

    // @SystemStatus(minValue = -100, maxValue = 100, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Current", description = "Current")
    private BigDecimal I;

    // @SystemStatus(minValue = -25000, maxValue = 25000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "")
    // @Display(name = "Power", description = "Power flow")
    private BigDecimal P;

    // Getters and Setters
    public BigDecimal getI() {
        return I;
    }

    public void setI(BigDecimal i) {
        I = i;
    }

    public BigDecimal getP() {
        return P;
    }

    public void setP(BigDecimal p) {
        P = p;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ACLoadPhase that = (ACLoadPhase) o;
        return Objects.equals(I, that.I) &&
                Objects.equals(P, that.P);
    }

    @Override
    public int hashCode() {
        return Objects.hash(I, P);
    }

    @Override
    public String toString() {
        return "ACLoadPhase{" +
                "I=" + I +
                ", P=" + P +
                '}';
    }
}