package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESH extends DataBackedBand
{
	public BandESH()
	{
		super(BandForge.<BandESH>getMetadataFor(BandESH.class));
	}



	public BandESH(byte[] bytes)
	{
		super(bytes, BandForge.<BandESH>getMetadataFor(BandESH.class));
	}

	public BandESH(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESH>getMetadataFor(BandESH.class));
	}


	
	public final Volt getLowestFeedingPVVoltage()
	{
		return GetU16(0, Volt.Deci);
	}

	
	public final TimeSpan getReconnectTime()
	{
		return GetU16(2, TimeSpan.fromSeconds(1));
	}

	
	public final Volt getLowLimitGridVoltage()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Volt getHighLimitGridVoltage()
	{
		return GetU16(6, Volt.Deci);
	}

	
	public final Frequency getLowLimitGridFrequency()
	{
		return GetU16(8, Frequency.Centi);
	}

	
	public final Frequency getHighLimitGridFreqency()
	{
		return GetU16(10, Frequency.Centi);
	}
}
