package com.ebon.energy.fms.domain.vo.product.control;


import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.domain.vo.BatterySettingsDto;
import com.ebon.energy.fms.domain.vo.GetInverterModeSettingsDto;

import java.util.List;

public interface ICommonSettingsReader {

    GetInverterModeSettingsDto getDesiredPowerModeSchedulesForPortal();

    ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source);

    List<UniversalSettingId> getSupportedUniveralSettings(Integer version);

    BatterySettingsDto getBatterySettings(UniversalSettingSource source);
}
