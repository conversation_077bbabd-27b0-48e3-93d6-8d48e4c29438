package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Volt implements IUnit {
    public static final Volt Unit = new Volt(new BigDecimal("1.0"));
    public static final Volt Zero = new Volt(new BigDecimal("0.0"));
    public static final Volt Tera = new Volt(new BigDecimal("1000000000000"));
    public static final Volt Giga = new Volt(new BigDecimal("1000000000"));
    public static final Volt Mega = new Volt(new BigDecimal("1000000"));
    public static final Volt Kilo = new Volt(new BigDecimal("1000"));
    public static final Volt Hecto = new Volt(new BigDecimal("100"));
    public static final Volt Deca = new Volt(new BigDecimal("10"));
    public static final Volt Deci = new Volt(new BigDecimal("0.1"));
    public static final Volt Centi = new Volt(new BigDecimal("0.01"));
    public static final Volt Milli = new Volt(new BigDecimal("0.001"));

    public static final String SYMBOL = "V";

    private BigDecimal value;

    public Volt() {
    }

    public Volt(BigDecimal value) {
        this.value = value;
    }

    public Volt(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Volt(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public BigDecimal getValue() {
        return value;
    }

    @Override
    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }

    public Volt add(Volt other) {
        return new Volt(value.add(other.value));
    }

    public Volt subtract(Volt other) {
        return new Volt(value.subtract(other.value));
    }

    public Volt multiply(BigDecimal multiplier) {
        return new Volt(value.multiply(multiplier));
    }

    public Volt divide(BigDecimal divisor) {
        return new Volt(value.divide(divisor));
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Volt)) {
            return false;
        }
        Volt other = (Volt) obj;
        return value.compareTo(other.value) == 0;
    }

    @Override
    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public BigDecimal asDecimal(Volt unit) {
        return value.divide(unit.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    public int compareTo(Volt other) {
        return value.compareTo(other.value);
    }
}
