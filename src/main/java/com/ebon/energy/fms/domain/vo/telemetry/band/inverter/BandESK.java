package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;




 





public class BandESK extends DataBackedBand
{
	public BandESK()
	{
		super(BandForge.<BandESK>getMetadataFor(BandESK.class));
	}



	public BandESK(byte[] bytes)
	{
		super(bytes, BandForge.<BandESK>getMetadataFor(BandESK.class));
	}

	public BandESK(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESK>getMetadataFor(BandESK.class));
	}


	
	public final BigDecimal getBattStartChgSoc()
	{
		return new BigDecimal(GetU16(0)).multiply(Percentage._1);
	}

	
	public final BigDecimal getBattStopChgSoc()
	{
		return new BigDecimal(GetU16(2)).multiply(Percentage._1);
	}

	
	public final boolean getGoodWeActiveEnable()
	{
		return GetBool((int) GetU16(4), 1, 0, false);
	}
}
