package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.Map;
import java.util.Objects;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SettingsV2Desired extends SettingsV2DesiredBase {

    public static final String SCHEDULES_SETTINGS_NAME = "schedules";
    public static final String TIME_ZONE_ALIAS_SETTINGS_NAME = "TzAlias";
    public static final String INVERTER_FIRMWARE_SETTINGS_NAME = "inverterFirmware";
    public static final String COMMS_FIRMWARE_SETTINGS_NAME = "commsFirmware";
    public static final String METER_FIRMWARE_SETTINGS_NAME = "meterFirmware";
    public static final String METER_SETTINGS_NAME = "meter";
    public static final String METERS_SETTINGS_NAME = "meters";

    @JsonProperty(INVERTER_FIRMWARE_SETTINGS_NAME)
    private FirmwareSettingsV2Desired inverterFirmwareSettings;

    @JsonProperty(COMMS_FIRMWARE_SETTINGS_NAME)
    private FirmwareSettingsV2Desired commsFirmwareSettings;

    @JsonProperty(METER_FIRMWARE_SETTINGS_NAME)
    private FirmwareSettingsV2Desired meterFirmwareSettings;

    @JsonProperty(METER_SETTINGS_NAME)
    private Map<String, Object> meterSettings;

    @JsonProperty(METERS_SETTINGS_NAME)
    private Map<String, Object> meters;

    @JsonProperty(TIME_ZONE_ALIAS_SETTINGS_NAME)
    private Map<String, String> timeZoneAliasSettings;

    @JsonProperty(SCHEDULES_SETTINGS_NAME)
    private ScheduleSettingsV2Desired scheduleSettings;

}
