package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.enums.Country;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.*;
import java.math.*;


public class BandETB extends DataBackedBand {
    public BandETB() {
        super(BandForge.<BandETB>getMetadataFor(BandETB.class));
    }


    public BandETB(byte[] bytes) {
        super(bytes, BandForge.<BandETB>getMetadataFor(BandETB.class));
    }

    public BandETB(String encodedBytes) {
        super(encodedBytes, BandForge.<BandETB>getMetadataFor(BandETB.class));
    }

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public final LocalDateTime getRTCTime() {
        return GetDateTime(0);
    }


    public final Volt getVpv1() {
        return GetU16(6, Volt.Deci);
    }


    public final Ampere getIpv1() {
        return GetU16(8, Ampere.Deci);
    }


    public final Watt getPpv1() {
        return GetU32(10, Watt.Unit);
    }


    public final Volt getVpv2() {
        return GetU16(14, Volt.Deci);
    }


    public final Ampere getIpv2() {
        return GetU16(16, Ampere.Deci);
    }


    public final Watt getPpv2() {
        return GetU32(18, Watt.Unit);
    }


    public final Volt getVpv3() {
        return GetU16(22, Volt.Deci);
    }


    public final Ampere getIpv3() {
        return GetU16(24, Ampere.Deci);
    }


    public final Watt getPpv3() {
        return GetU32(26, Watt.Unit);
    }


    public final Volt getVpv4() {
        return GetU16(30, Volt.Deci);
    }


    public final Ampere getIpv4() {
        return GetU16(32, Ampere.Deci);
    }


    public final Watt getPpv4() {
        return GetU32(34, Watt.Unit);
    }


    public final Object getPV1Mode() {
        return PvMode.parse((byte) GetMaskedU32(38, 0x00000003, (short) 0));
    }


    public final Object getPV2Mode() {
        return PvMode.parse((byte) GetMaskedU32(38, 0x00000300, (short) 8));
    }


    public final Object getPV3Mode() {
        return PvMode.parse((byte) GetMaskedU32(38, 0x00030000, (short) 16));
    }


    public final Object getPV4Mode() {


        return PvMode.parse((byte) GetMaskedU32(38, 0x03000000, (short) 24));
    }


    public final Volt getVgridL1() {
        return GetU16(42, Volt.Deci);
    }


    public final Volt getVgridL1Nullable() {


        return GetU16Nullable(42, Volt.Deci, Short.MAX_VALUE);
    }


    public final Ampere getIgridL1() {
        return GetU16(44, Ampere.Deci);
    }


    public final Ampere getIgridL1Nullable() {


        return GetU16Nullable(44, Ampere.Deci, Short.MAX_VALUE);
    }


    public final Frequency getFgridL1() {
        return GetU16(46, Frequency.Centi);
    }


    public final Frequency getFgridL1Nullable() {


        return GetU16Nullable(46, Frequency.Centi, Short.MAX_VALUE);
    }


    public final int getReserved0x8934() {
        return GetU16(48);
    }


    public final Watt getPgridL1() {
        return GetS16(50, Watt.Unit);
    }


    public final Watt getPgridL1Nullable() {
        return GetS16Nullable(50, Watt.Unit, (short) -1);
    }


    public final Volt getVgridL2() {
        return GetU16(52, Volt.Deci);
    }


    public final Volt getVgridL2Nullable() {


        return GetU16Nullable(52, Volt.Deci, Short.MAX_VALUE);
    }


    public final Ampere getIgridL2() {
        return GetU16(54, Ampere.Deci);
    }


    public final Ampere getIgridL2Nullable() {


        return GetU16Nullable(54, Ampere.Deci, Short.MAX_VALUE);
    }


    public final Frequency getFgridL2() {
        return GetU16(56, Frequency.Centi);
    }


    public final Frequency getFgridL2Nullable() {


        return GetU16Nullable(56, Frequency.Centi, Short.MAX_VALUE);
    }


    public final int getReserved0x8939() {
        return GetU16(58);
    }


    public final Watt getPgridL2() {
        return GetS16(60, Watt.Unit);
    }


    public final Watt getPgridL2Nullable() {
        return GetS16Nullable(60, Watt.Unit, (short) -1);
    }


    public final Volt getVgridL3() {
        return GetU16(62, Volt.Deci);
    }


    public final Volt getVgridL3Nullable() {


        return GetU16Nullable(62, Volt.Deci, Short.MAX_VALUE);
    }


    public final Ampere getIgridL3() {
        return GetU16(64, Ampere.Deci);
    }


    public final Ampere getIgridL3Nullable() {


        return GetU16Nullable(64, Ampere.Deci, Short.MAX_VALUE);
    }


    public final Frequency getFgridL3() {
        return GetU16(66, Frequency.Centi);
    }


    public final Frequency getFgridL3Nullable() {


        return GetU16Nullable(66, Frequency.Centi, Short.MAX_VALUE);
    }


    public final int getReserved0x893E() {
        return GetU16(68);
    }


    public final Watt getPgridL3() {
        return GetS16(70, Watt.Unit);
    }


    public final Watt getPgridL3Nullable() {
        return GetS16Nullable(70, Watt.Unit, (short) -1);
    }


    public final Object getGridMode() {
        return GridMode.parse((byte) GetU16(72));
    }


    public final int getReserved0x8941() {
        return GetU16(74);
    }


    public final Watt getTotalINVPower() {
        return GetS16(76, Watt.Unit);
    }


    public final int getReserved0x8943() {
        return GetU16(78);
    }


    public final Watt getACActivePower() {
        return GetS16(80, Watt.Unit);
    }


    public final int getReserved0x8945() {
        return GetU16(82);
    }


    public final VoltAmpsReactive getACReactivePower() {
        return GetS16(84, VoltAmpsReactive.Unit);
    }


    public final int getReserved0x8947() {
        return GetU16(86);
    }


    public final VoltAmps getACApparentPower() {
        return GetS16(88, VoltAmps.Unit);
    }


    public final Volt getBackUpVloadL1() {
        return GetU16(90, Volt.Deci);
    }


    public final Volt getBackUpVloadL1Nullable() {


        return GetU16Nullable(90, Volt.Deci, Short.MAX_VALUE);
    }


    public final Ampere getBackUpIloadL1() {
        return GetU16(92, Ampere.Deci);
    }


    public final Ampere getBackUpIloadL1Nullable() {


        return GetU16Nullable(92, Ampere.Deci, Short.MAX_VALUE);
    }


    public final Frequency getBackUpFloadL1() {
        return GetU16(94, Frequency.Centi);
    }


    public final Frequency getBackUpFloadL1Nullable() {


        return GetU16Nullable(94, Frequency.Centi, Short.MAX_VALUE);
    }


    public final Object getBackUpLoadModeL1() {
        return LoadMode.parse(GetU16(96));
    }


    public final int getReserved0x894D() {
        return GetU16(98);
    }


    public final Watt getBackUpPloadL1() {
        return GetS16(100, Watt.Unit);
    }


    public final Watt getBackUpPloadL1Nullable() {
        return GetS16Nullable(100, Watt.Unit, (short) -1);
    }


    public final Volt getBackUpVloadL2() {
        return GetU16(102, Volt.Deci);
    }


    public final Volt getBackUpVloadL2Nullable() {


        return GetU16Nullable(102, Volt.Deci, Short.MAX_VALUE);
    }


    public final Ampere getBackUpIloadL2() {
        return GetU16(104, Ampere.Deci);
    }


    public final Ampere getBackUpIloadL2Nullable() {


        return GetU16Nullable(104, Ampere.Deci, Short.MAX_VALUE);
    }


    public final Frequency getBackUpFloadL2() {
        return GetU16(106, Frequency.Centi);
    }


    public final Frequency getBackUpFloadL2Nullable() {


        return GetU16Nullable(106, Frequency.Centi, Short.MAX_VALUE);
    }


    public final Object getBackUpLoadModeL2() {
        return LoadMode.parse(GetU16(108));
    }


    public final int getReserved0x8953() {
        return GetU16(110);
    }


    public final Watt getBackUpPloadL2() {
        return GetS16(112, Watt.Unit);
    }


    public final Watt getBackUpPloadL2Nullable() {
        return GetS16Nullable(112, Watt.Unit, (short) -1);
    }


    public final Volt getBackUpVloadL3() {
        return GetU16(114, Volt.Deci);
    }


    public final Volt getBackUpVloadL3Nullable() {
        return GetU16Nullable(114, Volt.Deci, Short.MAX_VALUE);
    }


    public final Ampere getBackUpIloadL3() {
        return GetU16(116, Ampere.Deci);
    }


    public final Ampere getBackUpIloadL3Nullable() {


        return GetU16Nullable(116, Ampere.Deci, Short.MAX_VALUE);
    }


    public final Frequency getBackUpFloadL3() {
        return GetU16(118, Frequency.Centi);
    }


    public final Frequency getBackUpFloadL3Nullable() {


        return GetU16Nullable(118, Frequency.Centi, Short.MAX_VALUE);
    }


    public final Object getBackUpLoadModeL3() {
        return LoadMode.parse(GetU16(120));
    }


    public final int getReserved0x8959() {
        return GetU16(122);
    }


    public final Watt getBackUpPloadL3() {
        return GetS16(124, Watt.Unit);
    }


    public final Watt getBackUpPloadL3Nullable() {
        return GetS16Nullable(124, Watt.Unit, (short) -1);
    }


    public final int getReserved0x895B() {
        return GetU16(126);
    }


    public final Watt getPloadL1() {
        return GetS16(128, Watt.Unit);
    }


    public final int getReserved0x895D() {
        return GetU16(130);
    }


    public final Watt getPloadL2() {
        return GetS16(132, Watt.Unit);
    }


    public final int getReserved0x895F() {
        return GetU16(134);
    }


    public final Watt getPloadL3() {
        return GetS16(136, Watt.Unit);
    }


    public final int getReserved0x8961() {
        return GetU16(138);
    }


    public final Watt getBackUpLoadPower() {
        return GetU16(140, Watt.Unit);
    }


    public final int getReserved0x8963() {
        return GetU16(142);
    }


    public final Watt getTotalLoad() {
        return GetS16(144, Watt.Unit);
    }


    public final BigDecimal getUPSLoadPercent() {
        return new BigDecimal(GetU16(146)).multiply(Percentage._1);
    }


    public final Celsius getTemperature() {
        return GetS16(148, Celsius.Deci);
    }


    public final Celsius getModuleTemperature() {
        return GetS16(150, Celsius.Deci);
    }


    public final Celsius getRadiatorTemperature() {
        return GetS16(152, Celsius.Deci);
    }


    public final Volt getBUSVoltage() {
        return GetU16(156, Volt.Deci);
    }


    public final Volt getNBUSVoltage() {
        return GetU16(158, Volt.Deci);
    }


    public final Volt getVbattery() {
        return GetU16(160, Volt.Deci);
    }


    public final Ampere getIbattery() {
        return GetS16(162, Ampere.Deci);
    }


    public final int getReserved0x896E() {
        return GetU16(164);
    }


    public final Watt getPbattery() {
        return GetS16(166, Watt.Unit);
    }


    public final Object getBatteryMode() {
        return BattMode.parse((byte) GetU16(168));
    }


    public final Object getInverterWarningCode() {
        return InverterWarningCode.parse(GetU16(170));
    }


    public final Object getSafetyCountry() {
        return Country.parse((byte) GetU16(172));
    }


    public final Object getWorkMode() {
        return InverterETWorkMode.parse((byte) GetU16(174));
    }


    public final Object getOperationMode() {
        return InverterWorkMode.parse(GetU16(176));
    }

    public final Object getErrorMessage() {
        return InverterErrorMode.parse(GetU32(178));
    }


    public final WattHour getEPVTotal() {
        return GetU32(182, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getEPVDay() {
        return GetU32(186, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getINTETotalSell() {
        return GetU32(190, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final TimeSpan getHTotal() {
        return GetU32(194, TimeSpan.fromHours(1));
    }


    public final WattHour getEDaySell() {
        return GetU16(198, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getINTETotalBuy() {
        return GetU32(200, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getEDayBuy() {
        return GetU16(204, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getETotalLoad() {
        return GetU32(206, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getELoadDay() {
        return GetU16(210, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getEBatteryCharge() {
        return GetU32(212, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getEChargeDay() {
        return GetU16(216, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getEBatteryDischarge() {
        return GetU32(218, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final WattHour getEDischargeDay() {
        return GetU16(222, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
    }


    public final int getBattStrings() {
        return GetU16(224);
    }


    public final Object getCPLDWarningCode() {
        return CPLDWarningCode.parse(GetU16(226));
    }


    public final boolean getChargerControlFlag() {
        return GetBool((int) GetU16(228), 1, 0, false);
    }


    public final Object getDerateFlag() {
        return DerateFlag.parse(GetU16(230));
    }


    public final int getReserved() {
        return GetU16(232);
    }


    public final Watt getDerateFrozenPower() {
        return GetS16(234, Watt.Unit);
    }


    public final Object getDiagStatusH() {
        return DiagStatus.parse(GetU32(236));
    }


    public final Object getDiagStatusL() {
        return DiagStatus.parse(GetU32(240));
    }
}
