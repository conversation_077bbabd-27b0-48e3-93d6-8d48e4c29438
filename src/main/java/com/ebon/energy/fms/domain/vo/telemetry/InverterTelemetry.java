package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class InverterTelemetry {
    public String InverterSN;
    public String ModelName;
    public String FirmwareVersion;
    public String HardwareConfig;
    public String InverterCommsStatus;
    public Integer PMCat;
    public PVVFaultTelemetry PVVFault;
    public InverterAggregatesTelemetry Aggregate;
    public InverterBandsTelemetry Bands;
    public InverterRawBandsTelemetry RawBands;
    public List<TelemetryFrame> RawDataBands;
    public List<TelemetryFrame> ExtraBands;
    public List<IDataFrame<DataBackedBand>> DataBands;
    public PowerModeTarget PowerModeTarget;

    public InverterTelemetry() {}

    public InverterTelemetry(String inverterSN, String modelName, String firmwareVersion,
                             String hardwareConfig, String inverterCommsStatus,
                             List<TelemetryFrame> rawDataBands) {
        this.InverterSN = inverterSN;
        this.ModelName = modelName;
        this.FirmwareVersion = firmwareVersion;
        this.HardwareConfig = hardwareConfig;
        this.InverterCommsStatus = inverterCommsStatus;
        this.RawDataBands = rawDataBands;
    }

    public InverterTelemetry(String inverterSN, Integer pMCat, PVVFaultTelemetry pVVFault,
                             InverterBandsTelemetry bands, InverterRawBandsTelemetry rawBands,
                             InverterAggregatesTelemetry aggregates) {
        this.InverterSN = inverterSN;
        this.PMCat = pMCat;
        this.PVVFault = pVVFault;
        this.Bands = bands;
        this.RawBands = rawBands;
        this.Aggregate = aggregates;
    }

    public InverterTelemetry(String inverterSN, String modelName, String firmwareVersion,
                             String hardwareConfig, String inverterCommsStatus, Integer pMCat,
                             PVVFaultTelemetry pVVFault, List<TelemetryFrame> bands,
                             InverterAggregatesTelemetry aggregates) {
        this.InverterSN = inverterSN;
        this.ModelName = modelName;
        this.FirmwareVersion = firmwareVersion;
        this.HardwareConfig = hardwareConfig;
        this.InverterCommsStatus = inverterCommsStatus;
        this.PMCat = pMCat;
        this.PVVFault = pVVFault;
        this.RawDataBands = bands;
        this.Aggregate = aggregates;
    }

    public InverterTelemetry(String inverterSN, String modelName, String firmwareVersion,
                             String hardwareConfig, String inverterCommsStatus, Integer pMCat,
                             PVVFaultTelemetry pVVFault, List<TelemetryFrame> bands,
                             InverterAggregatesTelemetry aggregates, List<TelemetryFrame> extraBands,
                             PowerModeTarget powerModeTarget) {
        this.InverterSN = inverterSN;
        this.ModelName = modelName;
        this.FirmwareVersion = firmwareVersion;
        this.HardwareConfig = hardwareConfig;
        this.InverterCommsStatus = inverterCommsStatus;
        this.PMCat = pMCat;
        this.PVVFault = pVVFault;
        this.RawDataBands = bands;
        this.Aggregate = aggregates;
        this.ExtraBands = extraBands;
        this.PowerModeTarget = powerModeTarget;
    }
}