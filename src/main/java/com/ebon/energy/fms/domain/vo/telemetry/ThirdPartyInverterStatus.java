package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ThirdPartyInverterStatus implements Cloneable {
    public ThirdPartyInverterStatus() { }

    public ThirdPartyInverterStatus(Double i, Double p) {
        this.I = i;
        this.P = p;
    }

    /**
     * Create a new ThirdPartyInverterStatus object.
     * @param i The current generated by the third party inverter in Amperes.
     * @param p The power generated by the third party inverter in Watts.
     * @param allTimeTotalE The total energy generated by the third party AC Coupled inverter since installation in kilo-Watt-hours.
     * @param dayTotalE The total energy generated by the third party AC Coupled inverter today in kilo-watt-hours.
     * @param s The apparent power generated by the third party inverter in VA.
     */
    public ThirdPartyInverterStatus(Double i, Double p, Double allTimeTotalE,
                                    Double dayTotalE, Double s) {
        this.I = i;
        this.P = p;
        this.AllTimeTotalE = allTimeTotalE;
        this.DayTotalE = dayTotalE;
        this.S = s;
    }

    // voltage not included here as it would just be repeating values

    // @SystemStatus(minValue = 0, maxValue = 50, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "A")
    // @Display(name = "Current", description = "The current generated by the third party inverter")
    private Double I;

    // @SystemStatus(minValue = 0, maxValue = 20000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "W")
    // @Display(name = "Power", description = "The power generated by the third party inverter")
    private Double P;

    // @SystemStatus(minValue = 0, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "kWh")
    // @Display(name = "All time total generation",
    //          description = "The total energy generated by the third party AC Coupled inverter since installation")
    private Double AllTimeTotalE;

    /**
     * The total energy generated by the third party AC Coupled inverter today in kWh.
     */
    // @SystemStatus(minValue = 0, maxValue = 2000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "kWh")
    // @Display(name = "Day total generation",
    //          description = "The total energy generated by the third party AC Coupled inverter today")
    private Double DayTotalE;

    /**
     * Apparent Power in VoltAmps.
     */
    // @SystemStatus(minValue = 0, maxValue = 20000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "VA")
    // @Display(name = "Apparent Power",
    //          description = "The apparent power generated by the third party inverter")
    private Double S;

    // Getters and Setters
    public Double getI() {
        return I;
    }

    public void setI(Double i) {
        I = i;
    }

    public Double getP() {
        return P;
    }

    public void setP(Double p) {
        P = p;
    }

    public Double getAllTimeTotalE() {
        return AllTimeTotalE;
    }

    public void setAllTimeTotalE(Double allTimeTotalE) {
        AllTimeTotalE = allTimeTotalE;
    }

    public Double getDayTotalE() {
        return DayTotalE;
    }

    public void setDayTotalE(Double dayTotalE) {
        DayTotalE = dayTotalE;
    }

    public Double getS() {
        return S;
    }

    public void setS(Double s) {
        S = s;
    }

    @Override
    public ThirdPartyInverterStatus clone() {
        try {
            return (ThirdPartyInverterStatus) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // Can't happen
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ThirdPartyInverterStatus that = (ThirdPartyInverterStatus) o;
        return Objects.equals(I, that.I) &&
                Objects.equals(P, that.P) &&
                Objects.equals(AllTimeTotalE, that.AllTimeTotalE) &&
                Objects.equals(DayTotalE, that.DayTotalE) &&
                Objects.equals(S, that.S);
    }

    @Override
    public int hashCode() {
        return Objects.hash(I, P, AllTimeTotalE, DayTotalE, S);
    }

    @Override
    public String toString() {
        return "ThirdPartyInverterStatus{" +
                "I=" + I +
                ", P=" + P +
                ", AllTimeTotalE=" + AllTimeTotalE +
                ", DayTotalE=" + DayTotalE +
                ", S=" + S +
                '}';
    }
}