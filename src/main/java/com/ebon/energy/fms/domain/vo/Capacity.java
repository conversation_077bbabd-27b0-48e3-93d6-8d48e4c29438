package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Capacity implements IUnit {

    public static final String SYMBOL = "Ah";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final Capacity Unit = new Capacity(new BigDecimal("1.0"));
    public static final Capacity Zero = new Capacity(new BigDecimal("0.0"));
    public static final Capacity Tera = new Capacity(new BigDecimal("1000000000000"));
    public static final Capacity Giga = new Capacity(new BigDecimal("1000000000"));
    public static final Capacity Mega = new Capacity(new BigDecimal("1000000"));
    public static final Capacity Kilo = new Capacity(new BigDecimal("1000"));
    public static final Capacity Hecto = new Capacity(new BigDecimal("100"));
    public static final Capacity Deca = new Capacity(new BigDecimal("10"));
    public static final Capacity Deci = new Capacity(new BigDecimal("0.1"));
    public static final Capacity Centi = new Capacity(new BigDecimal("0.01"));
    public static final Capacity Milli = new Capacity(new BigDecimal("0.001"));

    public Capacity() {
    }

    public Capacity(BigDecimal value) {
        this.value = value;
    }

    public Capacity(String value) {
        this.value = new BigDecimal(value);
    }


    public Capacity(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Capacity(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Capacity abs() {
        return new Capacity(value.abs());
    }

    public Capacity subtract(Capacity other) {
        return new Capacity(this.value.subtract(other.value));
    }

    public Capacity add(Capacity other) {
        return new Capacity(this.value.add(other.value));
    }

    public static Capacity add(Capacity a, Capacity b) {
        return new Capacity(a.value.add(b.value));
    }

    public static Capacity subtract(Capacity a, Capacity b) {
        return new Capacity(a.value.subtract(b.value));
    }

    public boolean greaterThan(Capacity other) {
        return this.value.compareTo(other.value) > 0;
    }

    public boolean lessThan(Capacity other) {
        return this.value.compareTo(other.value) < 0;
    }

    public boolean greaterThanOrEqual(Capacity other) {
        return this.value.compareTo(other.value) >= 0;
    }

    public boolean lessThanOrEqual(Capacity other) {
        return this.value.compareTo(other.value) <= 0;
    }

    public static Capacity getValueOrZero(Capacity watt) {
        return watt == null || watt.getValue() == null ? Capacity.Zero : watt;
    }

    // 重载一元 + 运算符
    public static Capacity operatorPlus(Capacity a) {
        return a;
    }

    // 重载一元 - 运算符
    public static Capacity operatorMinus(Capacity a) {
        return new Capacity(a.value.negate());
    }

    // 重载二元 + 运算符
    public static Capacity operatorAdd(Capacity a, Capacity b) {
        return new Capacity(a.value.add(b.value));
    }

    // 重载二元 - 运算符
    public static Capacity operatorSubtract(Capacity a, Capacity b) {
        return new Capacity(a.value.subtract(b.value));
    }

    // 重载二元 / 运算符，返回 BigDecimal
    public static BigDecimal operatorDivide(Capacity a, Capacity b) {
        return a.value.divide(b.value);
    }

    // 重载 Watt 除以 BigDecimal 的 / 运算符
    public static Capacity operatorDivide(Capacity a, BigDecimal b) {
        return new Capacity(a.value.divide(b));
    }

    public static Capacity operatorMultiply(Capacity a, Capacity b) {
        return new Capacity(a.value.multiply(b.value));
    }

    // 重载 Watt 乘以 BigDecimal 的 * 运算符
    public static Capacity operatorMultiply(Capacity a, BigDecimal value) {
        return new Capacity(a.value.multiply(value));
    }

    // 重载 BigDecimal 乘以 Watt 的 * 运算符
    public static Capacity operatorMultiply(BigDecimal value, Capacity a) {
        return new Capacity(value.multiply(a.value));
    }

    // 重载 == 运算符
    public static boolean operatorEqual(Capacity a, Capacity b) {
        return a.value.equals(b.value);
    }

    // 重载 != 运算符
    public static boolean operatorNotEqual(Capacity a, Capacity b) {
        return !a.value.equals(b.value);
    }

    // 重载 < 运算符
    public static boolean operatorLessThan(Capacity a, Capacity b) {
        return a.value.compareTo(b.value) < 0;
    }

    // 重载 <= 运算符
    public static boolean operatorLessThanOrEqual(Capacity a, Capacity b) {
        return a.value.compareTo(b.value) <= 0;
    }

    // 重载 > 运算符
    public static boolean operatorGreaterThan(Capacity a, Capacity b) {
        return a.value.compareTo(b.value) > 0;
    }

    // 重载 >= 运算符
    public static boolean operatorGreaterThanOrEqual(Capacity a, Capacity b) {
        return a.value.compareTo(b.value) >= 0;
    }

    // 从 BigDecimal 隐式转换为 Watt
    public static Capacity fromBigDecimal(BigDecimal d) {
        return new Capacity(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public BigDecimal asDecimal(Capacity unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(Capacity unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(Capacity unit) {
        return value.divide(unit.value);
    }

    public double asDouble(Capacity unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(Capacity unit) {
        return value.divide(unit.value).longValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Capacity other = (Capacity) obj;
        return value.compareTo(other.value) == 0;
    }

    public int compareTo(Capacity other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}
