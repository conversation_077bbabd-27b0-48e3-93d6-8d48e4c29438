package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.NetworkStatusEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class NetworkAdaptorsStatus {
    private String WlanName;
    private NetworkStatusEnum WlanStatus;
    private Byte WlanSignal;
    private NetworkStatusEnum WwanStatus;
    private Byte WwanSignal;
    private NetworkStatusEnum LanStatus;
    private boolean HasLanIana;
    private boolean IsBluetoothAdvertising;
    private BigDecimal WifiRssi;
    private boolean IsBluetoothConnected;

    public NetworkAdaptorsStatus(
            NetworkStatusEnum wlanStatus,
            Byte wlanSignal,
            String wlanName,
            NetworkStatusEnum wwanStatus,
            Byte wwanSignal,
            NetworkStatusEnum lanStatus,
            boolean hasLanIana,
            boolean isBluetoothAdvertising,
            BigDecimal wifiRssi,
            boolean isBluetoothConnected)
    {
        this.WlanStatus = wlanStatus;
        this.WlanSignal = wlanSignal;
        this.WlanName = wlanName;
        this.WwanStatus = wwanStatus;
        this.WwanSignal = wwanSignal;
        this.LanStatus = lanStatus;
        this.HasLanIana = hasLanIana;
        this.IsBluetoothAdvertising = isBluetoothAdvertising;
        this.WifiRssi = wifiRssi;
        this.IsBluetoothConnected = isBluetoothConnected;
    }

    // Getters保持原始属性名大小写
    public String getWlanName() { return WlanName; }
    public NetworkStatusEnum getWlanStatus() { return WlanStatus; }
    public Byte getWlanSignal() { return WlanSignal; }
    public NetworkStatusEnum getWwanStatus() { return WwanStatus; }
    public Byte getWwanSignal() { return WwanSignal; }
    public NetworkStatusEnum getLanStatus() { return LanStatus; }
    public boolean getHasLanIana() { return HasLanIana; }
    public boolean getIsBluetoothAdvertising() { return IsBluetoothAdvertising; }
    public BigDecimal getWifiRssi() { return WifiRssi; }
    public boolean getIsBluetoothConnected() { return IsBluetoothConnected; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NetworkAdaptorsStatus that = (NetworkAdaptorsStatus) o;
        return HasLanIana == that.HasLanIana &&
                IsBluetoothAdvertising == that.IsBluetoothAdvertising &&
                Objects.equals(WlanName, that.WlanName) &&
                WlanStatus == that.WlanStatus &&
                Objects.equals(WlanSignal, that.WlanSignal) &&
                WwanStatus == that.WwanStatus &&
                Objects.equals(WwanSignal, that.WwanSignal) &&
                LanStatus == that.LanStatus;
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                WlanName != null ? WlanName.hashCode() : 0,
                WlanStatus,
                WlanSignal != null ? WlanSignal : 0,
                WwanStatus,
                WwanSignal != null ? WwanSignal : 0,
                LanStatus,
                HasLanIana ? 1 : 0,
                IsBluetoothAdvertising ? 1 : 0
        );
    }
}