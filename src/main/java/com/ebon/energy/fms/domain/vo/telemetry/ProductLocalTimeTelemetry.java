package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.LocalTimeSource;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.ZonedDateTime;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ProductLocalTimeTelemetry {

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime localTime;
    private String timeZone;
    private LocalTimeSource source;

    public ProductLocalTimeTelemetry(
            ZonedDateTime localTime,
            String timeZone,
            LocalTimeSource source) {

        this.localTime = localTime;
        this.timeZone = timeZone;
        this.source = source;
    }

    public ZonedDateTime getLocalTime() {
        return localTime;
    }

    public void setLocalTime(ZonedDateTime localTime) {
        this.localTime = localTime;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public LocalTimeSource getSource() {
        return source;
    }

    public void setSource(LocalTimeSource source) {
        this.source = source;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductLocalTimeTelemetry that = (ProductLocalTimeTelemetry) o;
        return Objects.equals(localTime, that.localTime) &&
                Objects.equals(timeZone, that.timeZone) &&
                source == that.source;
    }

    @Override
    public int hashCode() {
        return Objects.hash(localTime, timeZone, source);
    }
}