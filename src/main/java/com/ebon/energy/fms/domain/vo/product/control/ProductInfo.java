package com.ebon.energy.fms.domain.vo.product.control;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Product information entity
 */
@Data
public class ProductInfo {
    private String serialNumber;
    private String cloudConfiguration;
    private String deviceConfiguration;
    private String latestSystemStatus;
    private String appliedTariffId;
    private boolean optInForOptimization;
    private String canBeOptimised;
    private String ownerId;
    private String inverterTimeZone;
    
    // Additional fields needed for ProductDTO
    private LocalDateTime lastSystemStatusReceived;
    private LocalDateTime lastSCCMHeartbeat;
    private String sCCMUniqueId;
    private boolean isInWarranty;
    private String isSupposedToHaveInternetConnection;
    
}
