package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryCabinetTelemetry {
    private List<IndividualBatteryCabinetTelemetry> Cabinets;

    public BatteryCabinetTelemetry() {}

    public BatteryCabinetTelemetry(List<IndividualBatteryCabinetTelemetry> Cabinets) {
        this.Cabinets = Cabinets;
    }

    public List<IndividualBatteryCabinetTelemetry> getCabinets() {
        return Cabinets;
    }
}