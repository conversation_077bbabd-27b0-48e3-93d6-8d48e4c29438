package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.PylonInfoFlagState;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



 





public class BandPylon47 extends DataBackedBand
{
	public BandPylon47()
	{
		super(BandForge.<BandPylon47>getMetadataFor(BandPylon47.class));
	}



	public BandPylon47(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon47>getMetadataFor(BandPylon47.class));
	}

	public BandPylon47(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon47>getMetadataFor(BandPylon47.class));
	}



	public final Object getStatus()
	{
		return PylonInfoFlagState.parse(GetU8(0));
	}


	public final Volt getCellUpperVoltageLimit()
	{
		return GetU16(1, Volt.Milli);
	}


	public final Volt getCellLowerVoltageLimit()
	{
		return GetU16(3, Volt.Milli);
	}


	public final Volt getCellUnderVoltageLimit()
	{
		return GetU16(5, Volt.Milli);
	}


	public final Kelvin getChargeUpperTempLimit()
	{
		return GetU16(7, Kelvin.Deci);
	}


	public final Kelvin getChargeLowerTempLimit()
	{
		return GetU16(9, Kelvin.Deci);
	}


	public final Ampere getChargeCurrentLimit()
	{
		return GetS16(11, Ampere.Deci);
	}


	public final Volt getPackUpperVoltageLimit()
	{
		return GetU16(13, Volt.Milli);
	}


	public final Volt getPackLowerVoltageLimit()
	{
		return GetU16(15, Volt.Milli);
	}


	public final Volt getPackUnderVoltageLimit()
	{
		return GetU16(17, Volt.Milli);
	}


	public final Kelvin getDischargeUpperTempLimit()
	{
		return GetU16(19, Kelvin.Deci);
	}


	public final Kelvin getDischargeLowerTempLimit()
	{
		return GetU16(21, Kelvin.Deci);
	}


	public final Ampere getDischargeCurrentLimit()
	{
		return GetS16(23, Ampere.Deci);
	}
}
