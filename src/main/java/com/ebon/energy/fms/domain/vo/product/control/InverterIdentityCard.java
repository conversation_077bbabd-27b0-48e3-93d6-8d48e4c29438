package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.utils.ModelInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InverterIdentityCard {

    private String serialNumber;

    private String modelName;

    private String hardwareConfig;

    private RossVersion softerVersion;

    private String firmwareVersion;

    public ModelInfo getModelInfo() {
        return (firmwareVersion != null && modelName != null)
                ? new ModelInfo(modelName, firmwareVersion)
                : null;
    }
}