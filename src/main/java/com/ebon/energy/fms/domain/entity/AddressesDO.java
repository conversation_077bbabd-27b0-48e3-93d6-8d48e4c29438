package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("Addresses")
public class AddressesDO {

    @TableField(value = "Id")
    private Integer id;

    @TableField(value = "GooglePlaceId")
    private String googlePlaceId;

    @TableField(value = "AddressLineOne")
    private String addressLineOne;

    @TableField(value = "AddressLineTwo")
    private String addressLineTwo;

    @TableField(value = "Suburb")
    private String suburb;

    @TableField(value = "State")
    private String state;

    @TableField(value = "Country")
    private String country;

    @TableField(value = "PostCode")
    private String postCode;

    @TableField(value = "Latitude")
    private String latitude;

    @TableField(value = "Longitude")
    private String longitude;

    @TableField(value = "TimeZoneId")
    private String timeZoneId;

}