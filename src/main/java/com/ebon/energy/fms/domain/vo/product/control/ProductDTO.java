package com.ebon.energy.fms.domain.vo.product.control;


import com.ebon.energy.fms.common.utils.ProductUtilities;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Data Transfer Object for Product information
 */
@Data
@NoArgsConstructor
public class ProductDTO {

    private String serialNumber;
    private String productFriendlyName;
    private LocalDateTime dateCreated;
    private String generation;
    private LocalDateTime lastSystemStatusReceived;
    private String productModel;
    private boolean isOnline;
    private boolean isRegistered;
    private boolean isPending;
    private String ownerId;
    private String originalInstallerCompanyId;
    private String maintainingInstallerCompanyId;
    private LocalDateTime lastSCCMHeartbeat;
    private String sCCMUniqueId;
    private boolean isInWarranty;
    private boolean isOptInForOptimization;
    private boolean isSupposedToHaveInternetConnection;
    private Integer groupId;
    private RossVersion rossVersion;
    private String displayModelName;

    /**
     * Constructor that builds a ProductDTO from a product projection and system status
     *
     * @param product      The product information
     * @param systemStatus The system status information
     */
    public ProductDTO(ProductInfo product, SystemStatus systemStatus) {
        this.serialNumber = product.getSerialNumber();

        // Set system status related fields
        if (systemStatus != null) {
            this.productModel = systemStatus.getInverter() != null ? systemStatus.getInverter().getModelName() : null;
            this.rossVersion = new RossVersion(systemStatus.getOuijaBoard() != null ?
                    systemStatus.getOuijaBoard().getSoftwareVersion() : null);
        }

        this.lastSystemStatusReceived = product.getLastSystemStatusReceived();
        this.isOnline = ProductUtilities.isOnline(product.getLastSystemStatusReceived());
        this.ownerId = product.getOwnerId();
        this.isInWarranty = product.isInWarranty();
        this.isOptInForOptimization = product.isOptInForOptimization();


        // Set SCCM related fields
        this.lastSCCMHeartbeat = product.getLastSCCMHeartbeat();
        this.sCCMUniqueId = product.getSCCMUniqueId();

        // Set internet connection related fields
        this.isSupposedToHaveInternetConnection = product.getIsSupposedToHaveInternetConnection() == null || Boolean.parseBoolean(product.getIsSupposedToHaveInternetConnection());
        this.isPending = product.getLastSystemStatusReceived() == null && this.isSupposedToHaveInternetConnection;

        this.displayModelName = displayModelName;
    }

}
