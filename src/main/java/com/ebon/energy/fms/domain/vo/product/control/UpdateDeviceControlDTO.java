package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.domain.entity.DeviceControl;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(staticName = "of")
public class UpdateDeviceControlDTO {

    @JsonProperty("SerialNumber")
    private final String serialNumber;

    @JsonProperty("DeviceControl")
    private final DeviceControl deviceControl;

    @JsonProperty("IsFromDevice")
    private final boolean isFromDevice;

    @JsonProperty("IsFromPortal")
    private final boolean isFromPortal;

    // 工厂方法
    public static UpdateDeviceControlDTO fromPortal(String serialNumber, DeviceControl deviceControl) {
        return new UpdateDeviceControlDTO(serialNumber, deviceControl, false, true);
    }

    public static UpdateDeviceControlDTO fromApi(String serialNumber, DeviceControl deviceControl) {
        return new UpdateDeviceControlDTO(serialNumber, deviceControl, false, false);
    }

    public static UpdateDeviceControlDTO fromDevice(String serialNumber, DeviceControl deviceControl) {
        return new UpdateDeviceControlDTO(serialNumber, deviceControl, true, false);
    }

    public static UpdateDeviceControlDTO fromWebJob(String serialNumber, DeviceControl deviceControl) {
        return new UpdateDeviceControlDTO(serialNumber, deviceControl, false, false);
    }
}
