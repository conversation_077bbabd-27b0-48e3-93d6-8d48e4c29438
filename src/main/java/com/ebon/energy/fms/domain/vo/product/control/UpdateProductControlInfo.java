package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.domain.vo.ConfigurationsVO;
import lombok.Data;

@Data
public class UpdateProductControlInfo {
    private String ownerId;
    private String configuration;
    private String canBeOptimised;
    private Boolean isOptimised;



    public boolean canBeOptimised() {
        if (canBeOptimised == null) {
            return false;
        }
        return Boolean.parseBoolean(canBeOptimised);
    }

}
