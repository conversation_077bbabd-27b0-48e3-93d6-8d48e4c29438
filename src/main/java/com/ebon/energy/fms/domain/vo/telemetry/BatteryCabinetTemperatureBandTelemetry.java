package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.Celsius;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryCabinetTemperatureBandTelemetry {
    private final Celsius Actual;
    private final Celsius Uncompensated;

    public BatteryCabinetTemperatureBandTelemetry(
            Celsius Actual,
            Celsius Uncompensated)
    {
        this.Actual = Actual;
        this.Uncompensated = Uncompensated;
    }

    public Celsius getActual() {
        return Actual;
    }

    public Celsius getUncompensated() {
        return Uncompensated;
    }
}
