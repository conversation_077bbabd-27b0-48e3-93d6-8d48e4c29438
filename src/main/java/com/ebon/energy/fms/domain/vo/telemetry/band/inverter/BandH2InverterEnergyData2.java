package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandH2InverterEnergyData2 extends DataBackedBand
{
	public BandH2InverterEnergyData2()
	{
		super(BandForge.<BandH2InverterEnergyData2>getMetadataFor(BandH2InverterEnergyData2.class));
	}



	public BandH2InverterEnergyData2(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterEnergyData2>getMetadataFor(BandH2InverterEnergyData2.class));
	}

	public BandH2InverterEnergyData2(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterEnergyData2>getMetadataFor(BandH2InverterEnergyData2.class));
	}


	
	public final WattHour getPV2EnergyToday()
	{
		return GetU32(0, WattHour.Centi);
	}

	
	public final WattHour getPV2EnergyMonth()
	{
		return GetU32(4, WattHour.Centi);
	}

	
	public final WattHour getPV2EnergyYear()
	{
		return GetU32(8, WattHour.Centi);
	}

	
	public final WattHour getPV2EnergyTotal()
	{
		return GetU32(12, WattHour.Centi);
	}

	
	public final WattHour getPV3EnergyToday()
	{
		return GetU32(16, WattHour.Centi);
	}

	
	public final WattHour getPV3EnergyMonth()
	{
		return GetU32(20, WattHour.Centi);
	}

	
	public final WattHour getPV3EnergyYear()
	{
		return GetU32(24, WattHour.Centi);
	}

	
	public final WattHour getPV3EnergyTotal()
	{
		return GetU32(28, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyTodayL2()
	{
		return GetU32(32, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyMonthL2()
	{
		return GetU32(36, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyYearL2()
	{
		return GetU32(40, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyTotalL2()
	{
		return GetU32(44, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyTodayL3()
	{
		return GetU32(48, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyMonthL3()
	{
		return GetU32(52, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyYearL3()
	{
		return GetU32(56, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyTotalL3()
	{
		return GetU32(60, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyTodayL2()
	{
		return GetU32(64, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyMonthL2()
	{
		return GetU32(68, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyYearL2()
	{
		return GetU32(72, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyTotalL2()
	{
		return GetU32(76, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyTodayL3()
	{
		return GetU32(80, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyMonthL3()
	{
		return GetU32(84, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyYearL3()
	{
		return GetU32(88, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyTotalL3()
	{
		return GetU32(92, WattHour.Centi);
	}

	
	public final WattHour getVectorSumBuyEnergyToday()
	{
		return GetU32(96, WattHour.Centi);
	}

	
	public final WattHour getVectorSumBuyEnergyMonth()
	{
		return GetU32(100, WattHour.Centi);
	}

	
	public final WattHour getVectorSumBuyEnergyYear()
	{
		return GetU32(104, WattHour.Centi);
	}

	
	public final WattHour getVectorSumBuyEnergyTotal()
	{
		return GetU32(108, WattHour.Centi);
	}

	
	public final WattHour getVectorSumSellEnergyToday()
	{
		return GetU32(112, WattHour.Centi);
	}

	
	public final WattHour getVectorSumSellEnergyMonth()
	{
		return GetU32(116, WattHour.Centi);
	}

	
	public final WattHour getVectorSumSellEnergyYear()
	{
		return GetU32(120, WattHour.Centi);
	}

	
	public final WattHour getVectorSumSellEnergyTotal()
	{
		return GetU32(124, WattHour.Centi);
	}

	
	public final WattHour getBat1ChargeEnergyToday()
	{
		return GetU32(128, WattHour.Centi);
	}

	
	public final WattHour getBat1ChargeEnergyMonth()
	{
		return GetU32(132, WattHour.Centi);
	}

	
	public final WattHour getBat1ChargeEnergyYear()
	{
		return GetU32(136, WattHour.Centi);
	}

	
	public final WattHour getBat1ChargeEnergyTotal()
	{
		return GetU32(140, WattHour.Centi);
	}

	
	public final WattHour getBat1DischargeEnergyToday()
	{
		return GetU32(144, WattHour.Centi);
	}

	
	public final WattHour getBat1DischargeEnergyMonth()
	{
		return GetU32(148, WattHour.Centi);
	}

	
	public final WattHour getBat1DischargeEnergyYear()
	{
		return GetU32(152, WattHour.Centi);
	}

	
	public final WattHour getBat1DischargeEnergyTotal()
	{
		return GetU32(156, WattHour.Centi);
	}

	
	public final WattHour getBat2ChargeEnergyToday()
	{
		return GetU32(160, WattHour.Centi);
	}

	
	public final WattHour getBat2ChargeEnergyMonth()
	{
		return GetU32(164, WattHour.Centi);
	}

	
	public final WattHour getBat2ChargeEnergyYear()
	{
		return GetU32(168, WattHour.Centi);
	}

	
	public final WattHour getBat2ChargeEnergyTotal()
	{
		return GetU32(172, WattHour.Centi);
	}

	
	public final WattHour getBat2DischargeEnergyToday()
	{
		return GetU32(176, WattHour.Centi);
	}

	
	public final WattHour getBat2DischargeEnergyMonth()
	{
		return GetU32(180, WattHour.Centi);
	}

	
	public final WattHour getBat2DischargeEnergyYear()
	{
		return GetU32(184, WattHour.Centi);
	}

	
	public final WattHour getBat2DischargeEnergyTotal()
	{
		return GetU32(188, WattHour.Centi);
	}
}
