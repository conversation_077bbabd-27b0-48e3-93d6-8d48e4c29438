package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;





public class BandBHK extends DataBackedBand
{
	public BandBHK()
	{
		super(BandForge.<BandBHK>getMetadataFor(BandBHK.class));
	}



	public BandBHK(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHK>getMetadataFor(BandBHK.class));
	}

	public BandBHK(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHK>getMetadataFor(BandBHK.class));
	}


	


	public final int getModbusAddress() { return GetU16(0); }

	
	public final String getManufacturer()
	{
		return GetBufS(2, 8, StringProcessors.GoodweDecode);
	}

	


	public final int getRS485ModbusBaudrate()
	{
		return GetU32(10);
	}
}
