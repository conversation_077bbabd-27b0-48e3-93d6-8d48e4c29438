package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class EnergyFlowExtendedVO {

    private EnergyFlowInputVO Input;
    private EnergyFlowExtVO Flow;

    public EnergyFlowExtendedVO(EnergyFlowInputVO input, EnergyFlowExtVO flow){
        this.Input = input;
        this.Flow = flow;
    }

    public EnergyFlowInputVO getInput() {
        return Input;
    }

    public void setInput(EnergyFlowInputVO input) {
        this.Input = input;
    }

    public EnergyFlowExtVO getFlow() {
        return Flow;
    }

    public void setFlow(EnergyFlowExtVO flow) {
        this.Flow = flow;
    }
}
