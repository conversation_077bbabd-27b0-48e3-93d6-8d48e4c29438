package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandEH1PInverterLoadEnergy extends DataBackedBand
{
	public BandEH1PInverterLoadEnergy()
	{
		super(BandForge.<BandEH1PInverterLoadEnergy>getMetadataFor(BandEH1PInverterLoadEnergy.class));
	}



	public BandEH1PInverterLoadEnergy(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterLoadEnergy>getMetadataFor(BandEH1PInverterLoadEnergy.class));
	}

	public BandEH1PInverterLoadEnergy(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterLoadEnergy>getMetadataFor(BandEH1PInverterLoadEnergy.class));
	}


	
	public final WattHour getACLoadEnergyTotal()
	{
		return GetU32(0, WattHour.Kilo);
	}

	
	public final WattHour getACLoadEnergyYear()
	{
		return GetU32(4, WattHour.Kilo);
	}

	
	public final WattHour getACLoadEnergyMonth()
	{
		return GetU32(8, WattHour.Kilo);
	}

	
	public final WattHour getACLoadEnergyDay()
	{
		return GetU16(12, WattHour.Hecto);
	}

	
	public final WattHour getACLoadEnergyYesterday()
	{
		return GetU16(14, WattHour.Hecto);
	}

	
	public final WattHour getBackupLoadEnergyTotal()
	{
		return GetU32(20, WattHour.Kilo);
	}

	
	public final WattHour getBackupLoadEnergyYear()
	{
		return GetU32(24, WattHour.Kilo);
	}

	
	public final WattHour getBackupLoadEnergyMonth()
	{
		return GetU32(28, WattHour.Kilo);
	}

	
	public final WattHour getBackupLoadEnergyDay()
	{
		return GetU16(32, WattHour.Hecto);
	}

	
	public final WattHour getBackupLoadEnergyYesterday()
	{
		return GetU16(34, WattHour.Hecto);
	}
}
