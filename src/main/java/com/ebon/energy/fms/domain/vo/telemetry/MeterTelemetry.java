package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.*;
import java.time.*;
import java.math.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterTelemetry
{
    /**
     The time the meter was last connected.
     */
    private ZonedDateTime LastConnectedTime;
    public final ZonedDateTime getLastConnectedTime()
    {
        return LastConnectedTime;
    }
    public final void setLastConnectedTime(ZonedDateTime value)
    {
        LastConnectedTime = value;
    }

    /**
     Meter Type.
     enum MeterType {
     METER_TYPE_UNSPECIFIED = 0;
     // PA110 - Single channel, 2 CT meter
     METER_TYPE_PA110 = 1;
     // PA300 - Three channel, 4 CT meter
     METER_TYPE_PA300 = 2;
     }
     */
    private int MeterType;
    public final int getMeterType()
    {
        return MeterType;
    }
    public final void setMeterType(int value)
    {
        MeterType = value;
    }

    /**
     Extended measurements for a channel - typically used.
     */
    private ArrayList<MeterChannelExtended> ChannelMeasurementsExtended;
    public final ArrayList<MeterChannelExtended> getChannelMeasurementsExtended()
    {
        return ChannelMeasurementsExtended;
    }
    public final void setChannelMeasurementsExtended(ArrayList<MeterChannelExtended> value)
    {
        ChannelMeasurementsExtended = value;
    }

    /**
     channel average readings - Used for reduced bandwidth cases.
     */
    private ArrayList<MeterChannelBasic> ChannelMeasurementsAverage;
    public final ArrayList<MeterChannelBasic> getChannelMeasurementsAverage()
    {
        return ChannelMeasurementsAverage;
    }
    public final void setChannelMeasurementsAverage(ArrayList<MeterChannelBasic> value)
    {
        ChannelMeasurementsAverage = value;
    }

    /**
     Current only measurements (typically used on neutral) - Not used at present.
     */
    private ArrayList<MeterChannelCurrentOnly> ChannelMeasurementsCurrent;
    public final ArrayList<MeterChannelCurrentOnly> getChannelMeasurementsCurrent()
    {
        return ChannelMeasurementsCurrent;
    }
    public final void setChannelMeasurementsCurrent(ArrayList<MeterChannelCurrentOnly> value)
    {
        ChannelMeasurementsCurrent = value;
    }

    /**
     Total power (across all phases on a 3-phase system).
     */
    private MeterTotalPhase TotalPhase;
    public final MeterTotalPhase getTotalPhase()
    {
        return TotalPhase;
    }
    public final void setTotalPhase(MeterTotalPhase value)
    {
        TotalPhase = value;
    }

    /**
     Active energy telemetry per phase.
     */
    private ArrayList<MeterChannelEnergy> ChannelMeasurementsActiveEnergy;
    public final ArrayList<MeterChannelEnergy> getChannelMeasurementsActiveEnergy()
    {
        return ChannelMeasurementsActiveEnergy;
    }
    public final void setChannelMeasurementsActiveEnergy(ArrayList<MeterChannelEnergy> value)
    {
        ChannelMeasurementsActiveEnergy = value;
    }

    /**
     Reactive energy telemetry per phase.
     */
    private ArrayList<MeterChannelEnergy> ChannelMeasurementsReactiveEnergy;
    public final ArrayList<MeterChannelEnergy> getChannelMeasurementsReactiveEnergy()
    {
        return ChannelMeasurementsReactiveEnergy;
    }
    public final void setChannelMeasurementsReactiveEnergy(ArrayList<MeterChannelEnergy> value)
    {
        ChannelMeasurementsReactiveEnergy = value;
    }

    /**
     Total Active Energy across all phases.
     */
    private MeterChannelEnergy TotalActiveEnergy;
    public final MeterChannelEnergy getTotalActiveEnergy()
    {
        return TotalActiveEnergy;
    }
    public final void setTotalActiveEnergy(MeterChannelEnergy value)
    {
        TotalActiveEnergy = value;
    }

    /**
     Total Reactive Energy across all phases.
     */
    private MeterChannelEnergy TotalReactiveEnergy;
    public final MeterChannelEnergy getTotalReactiveEnergy()
    {
        return TotalReactiveEnergy;
    }
    public final void setTotalReactiveEnergy(MeterChannelEnergy value)
    {
        TotalReactiveEnergy = value;
    }

    /**
     Raw data bands containing the Modbus reads. This is optional debug info.
     */
    private ArrayList<TelemetryFrame> RawDataBands;
    public final ArrayList<TelemetryFrame> getRawDataBands()
    {
        return RawDataBands;
    }
    public final void setRawDataBands(ArrayList<TelemetryFrame> value)
    {
        RawDataBands = value;
    }

    /**
     Meter Status

     [pbr::OriginalName("METER_STATUS_UNSPECIFIED")] Unspecified = 0,
     Communications failed
     [pbr::OriginalName("METER_STATUS_COMMS_FAILED")] CommsFailed = 1,
     Comms are operational
     [pbr::OriginalName("METER_STATUS_COMMS_OK")] CommsOk = 2,
     The EMS-Pro is scanning the bus for the PA120
     [pbr::OriginalName("METER_STATUS_SCANNING_BUS")] ScanningBus = 3,
     The EMS-Pro was scanning the bus for the PA120 and failed to detect the device
     [pbr::OriginalName("METER_STATUS_NOT_FOUND")] NotFound = 4,

     */
    private int MeterStatus;
    public final int getMeterStatus()
    {
        return MeterStatus;
    }
    public final void setMeterStatus(int value)
    {
        MeterStatus = value;
    }
}