package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandSGMeterWindowed3P extends DataBackedBand
{
	public BandSGMeterWindowed3P()
	{
		super(BandForge.<BandSGMeterWindowed3P>getMetadataFor(BandSGMeterWindowed3P.class));
	}



	public BandSGMeterWindowed3P(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterWindowed3P>getMetadataFor(BandSGMeterWindowed3P.class));
	}

	public BandSGMeterWindowed3P(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterWindowed3P>getMetadataFor(BandSGMeterWindowed3P.class));
	}



	public final Volt getVoltageL1Max()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Volt getVoltageL1Min()
	{
		return GetU16(2, Volt.Deci);
	}


	public final Volt getVoltageL1Avg()
	{
		return GetU16(4, Volt.Deci);
	}


	public final Ampere getCurrentL1Max()
	{
		return GetS32(6, Ampere.Centi);
	}


	public final Ampere getCurrentL1Min()
	{
		return GetS32(10, Ampere.Centi);
	}


	public final Ampere getCurrentL1Avg()
	{
		return GetS32(14, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL1Max()
	{
		return new BigDecimal(GetS16(18)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL1Min()
	{
		return new BigDecimal(GetS16(20)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL1Avg()
	{
		return new BigDecimal(GetS16(22)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL1Max()
	{
		return GetS32(24, Watt.Deci);
	}


	public final Watt getActivePowerL1Min()
	{
		return GetS32(28, Watt.Deci);
	}


	public final Watt getActivePowerL1Avg()
	{
		return GetS32(32, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL1Max()
	{
		return GetU32(36, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL1Min()
	{
		return GetU32(40, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL1Avg()
	{
		return GetU32(44, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL1Max()
	{
		return GetS32(48, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL1Min()
	{
		return GetS32(52, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL1Avg()
	{
		return GetS32(56, VoltAmpsReactive.Deci);
	}


	public final Volt getVoltageL2Max()
	{
		return GetU16(60, Volt.Deci);
	}


	public final Volt getVoltageL2Min()
	{
		return GetU16(62, Volt.Deci);
	}


	public final Volt getVoltageL2Avg()
	{
		return GetU16(64, Volt.Deci);
	}


	public final Ampere getCurrentL2Max()
	{
		return GetS32(66, Ampere.Centi);
	}


	public final Ampere getCurrentL2Min()
	{
		return GetS32(70, Ampere.Centi);
	}


	public final Ampere getCurrentL2Avg()
	{
		return GetS32(74, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL2Max()
	{
		return new BigDecimal(GetS16(78)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL2Min()
	{
		return new BigDecimal(GetS16(80)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL2Avg()
	{
		return new BigDecimal(GetS16(82)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL2Max()
	{
		return GetS32(84, Watt.Deci);
	}


	public final Watt getActivePowerL2Min()
	{
		return GetS32(88, Watt.Deci);
	}


	public final Watt getActivePowerL2Avg()
	{
		return GetS32(92, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL2Max()
	{
		return GetU32(96, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL2Min()
	{
		return GetU32(100, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL2Avg()
	{
		return GetU32(104, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL2Max()
	{
		return GetS32(108, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL2Min()
	{
		return GetS32(112, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL2Avg()
	{
		return GetS32(116, VoltAmpsReactive.Deci);
	}


	public final Volt getVoltageL3Max()
	{
		return GetU16(120, Volt.Deci);
	}


	public final Volt getVoltageL3Min()
	{
		return GetU16(122, Volt.Deci);
	}


	public final Volt getVoltageL3Avg()
	{
		return GetU16(124, Volt.Deci);
	}


	public final Ampere getCurrentL3Max()
	{
		return GetS32(126, Ampere.Centi);
	}


	public final Ampere getCurrentL3Min()
	{
		return GetS32(130, Ampere.Centi);
	}


	public final Ampere getCurrentL3Avg()
	{
		return GetS32(134, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL3Max()
	{
		return new BigDecimal(GetS16(138)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL3Min()
	{
		return new BigDecimal(GetS16(140)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL3Avg()
	{
		return new BigDecimal(GetS16(142)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL3Max()
	{
		return GetS32(144, Watt.Deci);
	}


	public final Watt getActivePowerL3Min()
	{
		return GetS32(148, Watt.Deci);
	}


	public final Watt getActivePowerL3Avg()
	{
		return GetS32(152, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL3Max()
	{
		return GetU32(156, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL3Min()
	{
		return GetU32(160, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL3Avg()
	{
		return GetU32(164, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL3Max()
	{
		return GetS32(168, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL3Min()
	{
		return GetS32(172, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL3Avg()
	{
		return GetS32(176, VoltAmpsReactive.Deci);
	}


	public final Frequency getFrequencyL1Max()
	{
		return GetU16(180, Frequency.Centi);
	}


	public final Frequency getFrequencyL1Min()
	{
		return GetU16(182, Frequency.Centi);
	}


	public final Frequency getFrequencyL1Avg()
	{
		return GetU16(184, Frequency.Centi);
	}


	public final BigDecimal getTotalHarmonicDistortionVoltageL1()
	{
		return new BigDecimal(GetU16(186)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getTotalHarmonicDistortionVoltageL2()
	{
		return new BigDecimal(GetU16(188)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getTotalHarmonicDistortionVoltageL3()
	{
		return new BigDecimal(GetU16(190)).multiply(Percentage.Tenth);
	}
}
