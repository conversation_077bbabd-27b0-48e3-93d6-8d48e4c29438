package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.HardwareTypeEnum;
import com.ebon.energy.fms.domain.vo.HardwareSettingsVO;

import java.util.List;

// 假设 HardwareType 和 HardwareModel 是枚举或类，需自行实现
public interface IHardwareConfigurationProvider {
    /**
     * Attempts to identify the correct hardware configuration to use from the available profiles based upon filters provided.
     *
     * @param hardwareType   Required, The hardware type.
     * @param hardwareModel  The model name, if known.
     * @param firmwareVersion The firmware version, if known.
     * @return The list of potential config matches.
     */
    List<HardwareSettingsVO> tryIdentifyDevice(
            HardwareTypeEnum hardwareType,
            HardwareModelEnum hardwareModel,
            int firmwareVersion
    );
}