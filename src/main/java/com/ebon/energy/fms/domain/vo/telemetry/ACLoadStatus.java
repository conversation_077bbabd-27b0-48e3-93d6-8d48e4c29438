package com.ebon.energy.fms.domain.vo.telemetry;

import java.util.List;
import java.util.ArrayList;
import java.util.Objects;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ACLoadStatus implements Cloneable {
    public ACLoadStatus() { }

    /**
     * Please consider using the constructor with typed units instead.
     */
    public ACLoadStatus(Double v, Double i, Double p, Double f, Status status,
                        List<ACLoadPhase> phases, Double dayTotalE, Double dayMaxP,
                        Long maxPEpoch) {
        this.V = v;
        this.I = i;
        this.P = p;
        this.F = f;
        this.Status = status;
        this.Phases = phases != null ? new ArrayList<>(phases) : null;
        this.DayTotalE = dayTotalE;
        this.DayMaxP = dayMaxP;
        this.MaxPEpoch = maxPEpoch;
    }

    /*public ACLoadStatus(Volt v, Ampere i, Watt p, Frequency f, Status status,
                        List<ACLoadPhase> phases, WattHour dayTotalE, Watt dayMaxP,
                        Long maxPEpoch) {
        this.V = v != null ? v.asDouble(Volt.UNIT, SystemStatus.VOLT_PRECISION) : null;
        this.I = i != null ? i.asDouble(Ampere.UNIT, SystemStatus.AMPERE_PRECISION) : null;
        this.P = p != null ? p.asDouble(Watt.UNIT, SystemStatus.WATT_PRECISION) : null;
        this.F = f != null ? f.asDouble(Frequency.UNIT, SystemStatus.FREQUENCY_PRECISION) : null;
        this.Status = status;
        this.Phases = phases != null ? new ArrayList<>(phases) : null;
        this.DayTotalE = dayTotalE != null ? dayTotalE.asDouble(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) : null;
        this.DayMaxP = dayMaxP != null ? dayMaxP.asDouble(Watt.UNIT, SystemStatus.WATT_PRECISION) : null;
        this.MaxPEpoch = maxPEpoch;
    }*/

    /**
     * Voltage in the household circuits.
     * Units: V
     */
    // @SystemStatus(minValue = 230, maxValue = 250, logLevel = LogLevels.LEVEL3,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "V")
    // @Display(name = "Voltage", description = "Voltage in the household circuits")
    private Double V;

    /**
     * Electric current being used in the household (not including the backup circuit).
     * Units: A
     */
    // @SystemStatus(minValue = 0, maxValue = 200, logLevel = LogLevels.LEVEL3,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "A")
    // @Display(name = "Current", description = "Electric current being used in the household (not including the backup circuit)")
    // @DisplayExtended(name = "Current", description = "Electric current being used in the household")
    private Double I;

    /**
     * Power being used by household loads (not including the backup circuit).
     * Units: W
     */
    // @SystemStatus(minValue = 0, maxValue = 10000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "W")
    // @Display(name = "Power", description = "Power being used by household loads (not including the backup circuit)")
    // @DisplayExtended(name = "Power", description = "Power being used by household loads")
    private Double P;

    /**
     * Frequency of electricity in the household circuits (matches the grid frequency).
     * Units: Hz
     */
    // @SystemStatus(minValue = 45, maxValue = 55, logLevel = LogLevels.LEVEL5,
    //               role = Roles.USER, visibility = Visibilities.NONE, units = "Hz")
    // @Display(name = "Frequency", description = "Frequency of electricity in the household circuits (matches the grid frequency)")
    private Double F;

    /**
     * Connection state.
     */
    // @SystemStatus(logLevel = LogLevels.LEVEL1, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Status", description = "Connection state")
    private Status Status;

    /**
     * Different AC power phases for the Load.
     */
    // @SystemStatus(logLevel = LogLevels.LEVEL1, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Phases", description = "Different AC power phases for the Load")
    private List<ACLoadPhase> Phases;

    /**
     * Total energy used this day by the household including the backup circuit in kWh.
     * Units: kWh
     */
    // @SystemStatus(minValue = 0, maxValue = 2000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "kWh")
    // @Display(name = "Day Total", description = "Total energy used this day by the household including the backup circuit")
    // @DisplayExtended(name = "Day Total", description = "Total energy used this day by the household")
    private Double DayTotalE;

    /**
     * Peak household load for the day (10 sec average).
     * Units: W
     */
    // @SystemStatus(minValue = 0, maxValue = 50000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "W")
    // @Display(name = "Maximum Power", description = "Peak household load for the day (10 sec average)")
    private Double DayMaxP;

    /**
     * Time of max power.
     */
    // @SystemStatus(minValue = 0, maxValue = 9999999999L, logLevel = LogLevels.LEVEL3,
    //               role = Roles.SYSTEM_ADMIN, visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Time of max power", description = "Time of max power")
    private Long MaxPEpoch;

    // Getters and Setters
    public Double getV() {
        return V;
    }

    public void setV(Double v) {
        V = v;
    }

    public Double getI() {
        return I;
    }

    public void setI(Double i) {
        I = i;
    }

    public Double getP() {
        return P;
    }

    public void setP(Double p) {
        P = p;
    }

    public Double getF() {
        return F;
    }

    public void setF(Double f) {
        F = f;
    }

    public Status getStatus() {
        return Status;
    }

    public void setStatus(Status status) {
        Status = status;
    }

    public List<ACLoadPhase> getPhases() {
        return Phases != null ? new ArrayList<>(Phases) : null;
    }

    public void setPhases(List<ACLoadPhase> phases) {
        Phases = phases != null ? new ArrayList<>(phases) : null;
    }

    public Double getDayTotalE() {
        return DayTotalE;
    }

    public void setDayTotalE(Double dayTotalE) {
        DayTotalE = dayTotalE;
    }

    public Double getDayMaxP() {
        return DayMaxP;
    }

    public void setDayMaxP(Double dayMaxP) {
        DayMaxP = dayMaxP;
    }

    public Long getMaxPEpoch() {
        return MaxPEpoch;
    }

    public void setMaxPEpoch(Long maxPEpoch) {
        MaxPEpoch = maxPEpoch;
    }

    @Override
    public ACLoadStatus clone() {
        try {
            ACLoadStatus clone = (ACLoadStatus) super.clone();
            clone.Phases = getPhases(); // Returns a new ArrayList or null
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // Can't happen
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ACLoadStatus that = (ACLoadStatus) o;
        return Objects.equals(V, that.V) &&
                Objects.equals(I, that.I) &&
                Objects.equals(P, that.P) &&
                Objects.equals(F, that.F) &&
                Status == that.Status &&
                Objects.equals(Phases, that.Phases) &&
                Objects.equals(DayTotalE, that.DayTotalE) &&
                Objects.equals(DayMaxP, that.DayMaxP) &&
                Objects.equals(MaxPEpoch, that.MaxPEpoch);
    }

    @Override
    public int hashCode() {
        return Objects.hash(V, I, P, F, Status, Phases, DayTotalE, DayMaxP, MaxPEpoch);
    }

    @Override
    public String toString() {
        return "ACLoadStatus{" +
                "V=" + V +
                ", I=" + I +
                ", P=" + P +
                ", F=" + F +
                ", Status=" + Status +
                ", Phases=" + Phases +
                ", DayTotalE=" + DayTotalE +
                ", DayMaxP=" + DayMaxP +
                ", MaxPEpoch=" + MaxPEpoch +
                '}';
    }
}