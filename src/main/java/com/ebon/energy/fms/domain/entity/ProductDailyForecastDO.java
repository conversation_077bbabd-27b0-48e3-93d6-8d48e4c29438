package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 每日电理预测
 */
@Data
@TableName("ProductDailyForecast")
public class ProductDailyForecastDO {

    @TableField(value = "RedbackProductSn")
    private String redbackProductSn;

    @TableField(value = "Date")
    private String date;

    @TableField(value = "Generation")
    private BigDecimal generation;

}