package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;

public class ManagedInverterSettingDesired {
    public enum InverterSettingValueType {
        Numeric((short)0),
        RawString((short)1),
        TimeSpan((short)2),
        Bo<PERSON>an((short)3),
        Enum((short)4);

        private final short value;

        InverterSettingValueType(short value) {
            this.value = value;
        }

        public short getValue() {
            return value;
        }
    }

    public enum InverterSettingExecutionType {
        Synchronised((short)0),
        StartUp((short)1),
        OneTime((short)2);

        private final short value;

        InverterSettingExecutionType(short value) {
            this.value = value;
        }

        public short getValue() {
            return value;
        }
    }

    public enum InverterSettingSource {
        CustomSetting((short)0),
        GridSafetySetting((short)10),
        InverterDefaults((short)100);

        private final short value;

        InverterSettingSource(short value) {
            this.value = value;
        }

        public short getValue() {
            return value;
        }
    }

    @JsonProperty("n")
    private final String name;

    @JsonProperty("v")
    private final String value;

    @JsonProperty("t")
    private final InverterSettingValueType valueType;

    @JsonProperty("e")
    private final InverterSettingExecutionType executionType;

    @JsonProperty("s")
    private final InverterSettingSource source;

    @JsonProperty("id")
    private final String settingId;

    public ManagedInverterSettingDesired(
            String name,
            String value,
            InverterSettingValueType valueType,
            InverterSettingExecutionType executionType,
            InverterSettingSource source,
            String settingId) {
        this.name = name;
        this.value = value;
        this.valueType = valueType != null ? valueType : InverterSettingValueType.Numeric;
        this.executionType = executionType != null ? executionType : InverterSettingExecutionType.Synchronised;
        this.source = source != null ? source : InverterSettingSource.CustomSetting;
        this.settingId = settingId;
    }

    // Getters
    public String getName() { return name; }
    public String getValue() { return value; }
    public InverterSettingValueType getValueType() { return valueType; }
    public InverterSettingExecutionType getExecutionType() { return executionType; }
    public InverterSettingSource getSource() { return source; }
    public String getSettingId() { return settingId; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ManagedInverterSettingDesired that = (ManagedInverterSettingDesired) o;
        return Objects.equals(name, that.name) &&
                Objects.equals(value, that.value) &&
                valueType == that.valueType &&
                executionType == that.executionType &&
                source == that.source &&
                Objects.equals(settingId, that.settingId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, value, valueType, executionType, source, settingId);
    }

    public boolean isEquivalent(ManagedInverterSettingDesired other) {
        return other != null &&
                Objects.equals(name, other.name) &&
                Objects.equals(value, other.value) &&
                valueType == other.valueType &&
                executionType == other.executionType;
    }

    public boolean hasSameTargetAs(ManagedInverterSettingDesired other) {
        return other != null && Objects.equals(name, other.name);
    }

    public boolean hasPriorityOver(ManagedInverterSettingDesired other) {
        return hasSameTargetAs(other) &&
                (source.getValue() < other.source.getValue() ||
                        (source.getValue() == other.source.getValue() &&
                                executionType.getValue() < other.executionType.getValue()));
    }

    public String getBandName() {
        return BandNameParser.parseBandPath(name).band;
    }

    public String getPropertyName() {
        return BandNameParser.parseBandPath(name).property;
    }

    public ManagedInverterSettingDesired withDifferentBand(String newBandName) {
        return new ManagedInverterSettingDesired(
                newBandName + "." + getPropertyName(),
                value,
                valueType,
                executionType,
                source,
                settingId);
    }

    public static class BandNameParser {
        public static BandPath parseBandPath(String bandAndPropertyPath) {
            String[] parts = bandAndPropertyPath.split("\\.");
            if (parts.length != 2 || !parts[0].toLowerCase().startsWith("band")) {
                return new BandPath(null, null);
            }
            return new BandPath(parts[0], parts[1]);
        }
    }

    public static class BandPath {
        public final String band;
        public final String property;

        public BandPath(String band, String property) {
            this.band = band;
            this.property = property;
        }
    }
}