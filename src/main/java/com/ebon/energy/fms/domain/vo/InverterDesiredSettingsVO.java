package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.GoodWeSiteExportLimitType;
import com.ebon.energy.fms.common.enums.GoodWeSiteGenerationLimitType;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.InverterModeValue;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Data
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterDesiredSettingsVO {
    public static String GEN_LIMIT_CONTROL_TYPE_NAME = "GenLimitControlType";
    public static String GEN_LIMIT_CONTROL_SOFT_LIM_VA_NAME = "GenLimitControlSoftLimVA";
    public static String GEN_LIMIT_CONTROL_HARD_LIM_VA_NAME = "GenLimitControlHardLimVA";
    public static String BATTERY_SETTINGS_NAME = "battery";
    public static String FIRMWARE_SETTINGS_NAME = "firmware";
    public static String MANAGED_SETTINGS_NAME = "managedSettings";
    public static String ARBITRARY_READER_SETTINGS_NAME = "arbitraryReaderSettings";
    public static String WORK_MODE_NAME = "workMode";
    public static String WORK_MODE_POWER_W_NAME = "workModePowerW";

    private BatteryDesiredSettingsVO battery;
    private InverterFirmwareDesiredSettingsVO firmwareSettings;
    private Map<String, ManagedInverterSettingDesiredVO> managedSettings;
    private Map<String, ArbitraryReaderSettingsDesiredVO> arbitraryReaderSettings;
    private Boolean disableManagedSettings;
    private String modelName;
    private Integer safetyCountry;
    private Boolean isShadowScanEnabled;
    private InverterModeValue workMode;
    private Double workModePowerW;
    private Integer SiteExportLimitW;
    private Boolean doesSiteRequireExportLimit;
    private GoodWeSiteExportLimitType siteExportLimitType;
    private BigDecimal powerFactor;
    private Short backupStartDelay;
    private Short recoverTimeEE;
    private String arbitrarySettingRegister;
    private String arbitrarySettingValue;
    private String arbitrarySettingToken;
    private String arbitrarySettingProtocol;
    private Boolean disablePowerModeManager;
    private Boolean disableBMSProcessor;
    private UUID gridProfileId;
    private String gridProfileCorrelationId;
    private InverterRelayCheckDesiredSettingsVO relayCheckSettings;
    private GoodWeSiteGenerationLimitType genLimitControlType;
    private VoltAmps genLimitControlSoftLimVA;
    private VoltAmps genLimitControlHardLimVA;

    public InverterDesiredSettingsVO(
            BatteryDesiredSettingsVO battery,
            InverterFirmwareDesiredSettingsVO firmwareSettings,
            Map<String, ManagedInverterSettingDesiredVO> managedSettings,
            Map<String, ArbitraryReaderSettingsDesiredVO> arbitraryReaderSettings,
            Boolean disableManagedSettings,
            String modelName,
            Integer safetyCountry,
            Boolean isShadowScanEnabled,
            InverterModeValue workMode,
            Double workModePowerW,
            Integer siteExportLimitW,
            Boolean doesSiteRequireExportLimit,
            GoodWeSiteExportLimitType siteExportLimitType,
            BigDecimal powerFactor,
            Short backupStartDelay,
            Short recoverTimeEE,
            String arbitrarySettingRegister,
            String arbitrarySettingValue,
            String arbitrarySettingToken,
            String arbitrarySettingProtocol,
            Boolean disablePowerModeManager,
            Boolean disableBMSProcessor,
            UUID gridProfileId,
            String gridProfileCorrelationId,
            InverterRelayCheckDesiredSettingsVO relayCheckSettings,
            GoodWeSiteGenerationLimitType genLimitControlType,
            VoltAmps genLimitControlSoftLimVA,
            VoltAmps genLimitControlHardLimVA) {

        this.battery = battery;
        this.firmwareSettings = firmwareSettings;
        this.managedSettings = managedSettings != null ? new HashMap<>(managedSettings) : new HashMap<>();
        this.arbitraryReaderSettings = arbitraryReaderSettings != null ? new HashMap<>(arbitraryReaderSettings) : new HashMap<>();
        this.disableManagedSettings = disableManagedSettings;
        this.modelName = modelName;
        this.safetyCountry = safetyCountry;
        this.isShadowScanEnabled = isShadowScanEnabled;
        this.workMode = workMode;
        this.workModePowerW = workModePowerW;
        this.SiteExportLimitW = siteExportLimitW;
        this.doesSiteRequireExportLimit = doesSiteRequireExportLimit;
        this.siteExportLimitType = siteExportLimitType;
        this.powerFactor = powerFactor;
        this.backupStartDelay = backupStartDelay;
        this.recoverTimeEE = recoverTimeEE;
        this.arbitrarySettingRegister = arbitrarySettingRegister;
        this.arbitrarySettingValue = arbitrarySettingValue;
        this.arbitrarySettingToken = arbitrarySettingToken;
        this.arbitrarySettingProtocol = arbitrarySettingProtocol;
        this.disablePowerModeManager = disablePowerModeManager;
        this.disableBMSProcessor = disableBMSProcessor;
        this.gridProfileId = gridProfileId;
        this.gridProfileCorrelationId = gridProfileCorrelationId;
        this.relayCheckSettings = relayCheckSettings;
        this.genLimitControlType = genLimitControlType;
        this.genLimitControlSoftLimVA = genLimitControlSoftLimVA;
        this.genLimitControlHardLimVA = genLimitControlHardLimVA;
    }

    public static InverterDesiredSettingsVO getDefault() {
        return new InverterDesiredSettingsVO(
                BatteryDesiredSettingsVO.getDefault(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                new InverterRelayCheckDesiredSettingsVO(),
                null,
                null,
                null);
    }

    // Builder methods
    public InverterDesiredSettingsVO withBatterySettings(BatteryDesiredSettingsVO batterySettings) {
        return new InverterDesiredSettingsVO(
                batterySettings, firmwareSettings, managedSettings, arbitraryReaderSettings,
                disableManagedSettings, modelName, safetyCountry, isShadowScanEnabled,
                workMode, workModePowerW, SiteExportLimitW, doesSiteRequireExportLimit,
                siteExportLimitType, powerFactor, backupStartDelay, recoverTimeEE,
                arbitrarySettingRegister, arbitrarySettingValue, arbitrarySettingToken,
                arbitrarySettingProtocol, disablePowerModeManager, disableBMSProcessor,
                gridProfileId, gridProfileCorrelationId, relayCheckSettings,
                genLimitControlType, genLimitControlSoftLimVA, genLimitControlHardLimVA);
    }

    // ... other builder methods ...

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InverterDesiredSettingsVO that = (InverterDesiredSettingsVO) o;
        return Objects.equals(battery, that.battery) &&
                Objects.equals(firmwareSettings, that.firmwareSettings) &&
                managedSettings.equals(that.managedSettings) &&
                arbitraryReaderSettings.equals(that.arbitraryReaderSettings) &&
                Objects.equals(disableManagedSettings, that.disableManagedSettings) &&
                Objects.equals(modelName, that.modelName) &&
                Objects.equals(safetyCountry, that.safetyCountry) &&
                Objects.equals(isShadowScanEnabled, that.isShadowScanEnabled) &&
                workMode == that.workMode &&
                Objects.equals(workModePowerW, that.workModePowerW) &&
                Objects.equals(SiteExportLimitW, that.SiteExportLimitW) &&
                Objects.equals(doesSiteRequireExportLimit, that.doesSiteRequireExportLimit) &&
                siteExportLimitType == that.siteExportLimitType &&
                Objects.equals(powerFactor, that.powerFactor) &&
                Objects.equals(backupStartDelay, that.backupStartDelay) &&
                Objects.equals(recoverTimeEE, that.recoverTimeEE) &&
                Objects.equals(arbitrarySettingRegister, that.arbitrarySettingRegister) &&
                Objects.equals(arbitrarySettingValue, that.arbitrarySettingValue) &&
                Objects.equals(arbitrarySettingToken, that.arbitrarySettingToken) &&
                Objects.equals(arbitrarySettingProtocol, that.arbitrarySettingProtocol) &&
                Objects.equals(disablePowerModeManager, that.disablePowerModeManager) &&
                Objects.equals(disableBMSProcessor, that.disableBMSProcessor) &&
                Objects.equals(gridProfileId, that.gridProfileId) &&
                Objects.equals(gridProfileCorrelationId, that.gridProfileCorrelationId) &&
                Objects.equals(relayCheckSettings, that.relayCheckSettings) &&
                genLimitControlType == that.genLimitControlType &&
                Objects.equals(genLimitControlSoftLimVA, that.genLimitControlSoftLimVA) &&
                Objects.equals(genLimitControlHardLimVA, that.genLimitControlHardLimVA);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                battery, firmwareSettings, managedSettings, arbitraryReaderSettings,
                disableManagedSettings, modelName, safetyCountry, isShadowScanEnabled,
                workMode, workModePowerW, SiteExportLimitW, doesSiteRequireExportLimit,
                siteExportLimitType, powerFactor, backupStartDelay, recoverTimeEE,
                arbitrarySettingRegister, arbitrarySettingValue, arbitrarySettingToken,
                arbitrarySettingProtocol, disablePowerModeManager, disableBMSProcessor,
                gridProfileId, gridProfileCorrelationId, relayCheckSettings,
                genLimitControlType, genLimitControlSoftLimVA, genLimitControlHardLimVA);
    }
}