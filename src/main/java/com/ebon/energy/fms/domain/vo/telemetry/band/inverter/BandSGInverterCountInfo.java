package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandSGInverterCountInfo extends DataBackedBand
{
	public BandSGInverterCountInfo()
	{
		super(BandForge.<BandSGInverterCountInfo>getMetadataFor(BandSGInverterCountInfo.class));
	}



	public BandSGInverterCountInfo(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterCountInfo>getMetadataFor(BandSGInverterCountInfo.class));
	}

	public BandSGInverterCountInfo(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterCountInfo>getMetadataFor(BandSGInverterCountInfo.class));
	}


	


	public final int getRelayCycleCount() { return GetU16(0); }

	


	public final int getGridDisconnectionCount() { return GetU16(2); }
}
