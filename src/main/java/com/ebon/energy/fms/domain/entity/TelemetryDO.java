package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("Telemetry")
public class TelemetryDO {

    @TableField(value = "Id")
    private String id;

    @TableField(value = "SerialNumber")
    private String serialNumber;

    @TableField(value = "TelemetryDateUtc")
    private Timestamp telemetryDateUtc;

    @TableField(value = "RossTelemetry")
    private String rossTelemetry;

    @TableField(value = "HasInsertError")
    private int hasInsertError;

    @TableField(value = "RecordChangedUtc")
    private Timestamp recordChangedUtc;

}