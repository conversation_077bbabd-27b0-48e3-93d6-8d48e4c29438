package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.util.VersionParser;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.ArrayList;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryStatus implements Cloneable {
    public BatteryStatus() { }

    /**
     * Create a new BatteryStatus object.
     * Please consider using the constructor with typed units instead.
     */
    public BatteryStatus(
            Double V,
            Double I,
            Double P,
            Double SoC,
            Double SOH,
            Double Capacity,
            Double DayTotalInputE,
            Double DayTotalOutputE,
            Double DayMaxInputP,
            Long DayMaxInputPEpoch,
            Double DayMaxOutputP,
            Long DayMaxOutputPEpoch,
            BatteryStatusValue Status,
            Boolean AutoDetect,
            String BatteryManufacturer,
            String BatteryModel,
            String BatteryFirmwareVersion,
            List<Error> Errors,
            List<Battery> Batteries,
            Double TotalChargingEnergykWh,
            Double TotalDischargingEnergykWh,
            Integer MismatchedTimeMinutes,
            BatteryDisabledReason DisabledReason,
            Double RemainingCapacity,
            Double RatedCapacity,
            Double MinCellVoltage,
            Double MaxCellVoltage,
            BatteryVoltageType BatteryVoltageType,
            Double HighestMinCellVoltageWhileIdle) {

        this.V = V;
        this.I = I;
        this.P = P;
        this.SoC = SoC;
        this.RemainingCapacity = RemainingCapacity;
        this.SOH = SOH;
        this.Capacity = Capacity;
        this.RatedCapacity = RatedCapacity;
        this.DayTotalInputE = DayTotalInputE;
        this.DayTotalOutputE = DayTotalOutputE;
        this.DayMaxInputP = DayMaxInputP;
        this.DayMaxInputPEpoch = DayMaxInputPEpoch;
        this.DayMaxOutputP = DayMaxOutputP;
        this.DayMaxOutputPEpoch = DayMaxOutputPEpoch;
        this.Status = Status;
        this.AutoDetect = AutoDetect;
        this.BatteryManufacturer = BatteryManufacturer;
        this.BatteryModel = BatteryModel;
        this.BatteryFirmwareVersion = BatteryFirmwareVersion;
        this.Errors = Errors != null ? new ArrayList<>(Errors) : null;
        this.Batteries = Batteries != null ? new ArrayList<>(Batteries) : null;
        this.TotalChargingEnergykWh = TotalChargingEnergykWh;
        this.TotalDischargingEnergykWh = TotalDischargingEnergykWh;
        this.MismatchedTimeMinutes = MismatchedTimeMinutes;
        this.DisabledReason = DisabledReason;
        this.MinCellVoltage = MinCellVoltage;
        this.MaxCellVoltage = MaxCellVoltage;
        this.BatteryVoltageType = BatteryVoltageType == null ? BatteryVoltageType.Unknown : BatteryVoltageType;
        this.HighestMinCellVoltageWhileIdle = HighestMinCellVoltageWhileIdle;
    }

    // [SystemStatus] and [Display] attributes would be custom annotations in Java
    private Double V;
    private Double I;
    private Double P;
    private Double SoC;
    private Double RemainingCapacity;
    private Double SOH;
    private Double Capacity;
    private Double RatedCapacity;
    private Double DayTotalInputE;
    private Double DayTotalOutputE;
    private Double DayMaxInputP;
    private Long DayMaxInputPEpoch;
    private Double DayMaxOutputP;
    private Long DayMaxOutputPEpoch;
    private BatteryStatusValue Status;
    private Boolean AutoDetect;
    private String BatteryManufacturer;
    private String BatteryModel;
    private String BatteryFirmwareVersion;
    private List<Error> Errors;
    private List<Battery> Batteries;
    private Double TotalChargingEnergykWh;
    private Double TotalDischargingEnergykWh;
    private Integer MismatchedTimeMinutes;
    private BatteryDisabledReason DisabledReason;
    private Double MinCellVoltage;
    private Double MaxCellVoltage;
    private BatteryVoltageType BatteryVoltageType = SystemStatusEnum.BatteryVoltageType.Unknown;
    private Double HighestMinCellVoltageWhileIdle;

    // Getters and Setters (maintaining original casing)
    public Double getV() { return V; }
    public void setV(Double V) { this.V = V; }

    @JsonProperty("I")
    public Double getI() {
        // 解决-0.0场景
        if (I != null && I == 0.0) {
            return 0.0;
        }

        return this.I;
    }

    public void setI(Double I) { this.I = I; }

    @JsonProperty("P")
    public Double getP() {
        // 解决-0.0场景
        if (P != null && P == 0.0) {
            return 0.0;
        }

        return this.P;
    }

    public void setP(Double P) { this.P = P; }

    public Double getSoC() { return SoC; }
    public void setSoC(Double SoC) { this.SoC = SoC; }

    public Double getRemainingCapacity() { return RemainingCapacity; }
    public void setRemainingCapacity(Double RemainingCapacity) { this.RemainingCapacity = RemainingCapacity; }

    public Double getSOH() { return SOH; }
    public void setSOH(Double SOH) { this.SOH = SOH; }

    public Double getCapacity() { return Capacity; }
    public void setCapacity(Double Capacity) { this.Capacity = Capacity; }

    public Double getRatedCapacity() { return RatedCapacity; }
    public void setRatedCapacity(Double RatedCapacity) { this.RatedCapacity = RatedCapacity; }

    public Double getDayTotalInputE() { return DayTotalInputE; }
    public void setDayTotalInputE(Double DayTotalInputE) { this.DayTotalInputE = DayTotalInputE; }

    public Double getDayTotalOutputE() { return DayTotalOutputE; }
    public void setDayTotalOutputE(Double DayTotalOutputE) { this.DayTotalOutputE = DayTotalOutputE; }

    public Double getDayMaxInputP() { return DayMaxInputP; }
    public void setDayMaxInputP(Double DayMaxInputP) { this.DayMaxInputP = DayMaxInputP; }

    public Long getDayMaxInputPEpoch() { return DayMaxInputPEpoch; }
    public void setDayMaxInputPEpoch(Long DayMaxInputPEpoch) { this.DayMaxInputPEpoch = DayMaxInputPEpoch; }

    public Double getDayMaxOutputP() { return DayMaxOutputP; }
    public void setDayMaxOutputP(Double DayMaxOutputP) { this.DayMaxOutputP = DayMaxOutputP; }

    public Long getDayMaxOutputPEpoch() { return DayMaxOutputPEpoch; }
    public void setDayMaxOutputPEpoch(Long DayMaxOutputPEpoch) { this.DayMaxOutputPEpoch = DayMaxOutputPEpoch; }

    public BatteryStatusValue getStatus() { return Status; }
    public void setStatus(BatteryStatusValue Status) { this.Status = Status; }

    public Boolean getAutoDetect() { return AutoDetect; }
    public void setAutoDetect(Boolean AutoDetect) { this.AutoDetect = AutoDetect; }

    public String getBatteryManufacturer() { return BatteryManufacturer; }
    public void setBatteryManufacturer(String BatteryManufacturer) { this.BatteryManufacturer = BatteryManufacturer; }

    public String getBatteryModel() { return BatteryModel; }
    public void setBatteryModel(String BatteryModel) { this.BatteryModel = BatteryModel; }

    public String getBatteryFirmwareVersion() { return BatteryFirmwareVersion; }
    public void setBatteryFirmwareVersion(String BatteryFirmwareVersion) { this.BatteryFirmwareVersion = BatteryFirmwareVersion; }

    public List<Error> getErrors() { return Errors != null ? new ArrayList<>(Errors) : null; }
    public void setErrors(List<Error> Errors) { this.Errors = Errors != null ? new ArrayList<>(Errors) : null; }

    public List<Battery> getBatteries() { return Batteries != null ? new ArrayList<>(Batteries) : null; }
    public void setBatteries(List<Battery> Batteries) { this.Batteries = Batteries != null ? new ArrayList<>(Batteries) : null; }

    public Double getTotalChargingEnergykWh() { return TotalChargingEnergykWh; }
    public void setTotalChargingEnergykWh(Double TotalChargingEnergykWh) { this.TotalChargingEnergykWh = TotalChargingEnergykWh; }

    public Double getTotalDischargingEnergykWh() { return TotalDischargingEnergykWh; }
    public void setTotalDischargingEnergykWh(Double TotalDischargingEnergykWh) { this.TotalDischargingEnergykWh = TotalDischargingEnergykWh; }

    public Integer getMismatchedTimeMinutes() { return MismatchedTimeMinutes; }
    public void setMismatchedTimeMinutes(Integer MismatchedTimeMinutes) { this.MismatchedTimeMinutes = MismatchedTimeMinutes; }

    public BatteryDisabledReason getDisabledReason() { return DisabledReason; }
    public void setDisabledReason(BatteryDisabledReason DisabledReason) { this.DisabledReason = DisabledReason; }

    public Double getMinCellVoltage() { return MinCellVoltage; }
    public void setMinCellVoltage(Double MinCellVoltage) { this.MinCellVoltage = MinCellVoltage; }

    public Double getMaxCellVoltage() { return MaxCellVoltage; }
    public void setMaxCellVoltage(Double MaxCellVoltage) { this.MaxCellVoltage = MaxCellVoltage; }

    public BatteryVoltageType getBatteryVoltageType() { return BatteryVoltageType; }
    public void setBatteryVoltageType(BatteryVoltageType BatteryVoltageType) { this.BatteryVoltageType = BatteryVoltageType; }

    public Double getHighestMinCellVoltageWhileIdle() { return HighestMinCellVoltageWhileIdle; }
    public void setHighestMinCellVoltageWhileIdle(Double HighestMinCellVoltageWhileIdle) {
        this.HighestMinCellVoltageWhileIdle = HighestMinCellVoltageWhileIdle;
    }

    @JsonProperty("BatteryFirmwareVersionParsed")
    public String getBatteryFirmwareVersionParsed() {
        return VersionParser.parseBatteryFirmware(this.BatteryFirmwareVersion);
    }

    @Override
    public BatteryStatus clone() {
        try {
            BatteryStatus clone = (BatteryStatus) super.clone();
            clone.Errors = getErrors();
            clone.Batteries = getBatteries();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // Can't happen
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BatteryStatus that = (BatteryStatus) o;
        return Objects.equals(V, that.V) &&
                Objects.equals(I, that.I) &&
                Objects.equals(P, that.P) &&
                Objects.equals(SoC, that.SoC) &&
                Objects.equals(RemainingCapacity, that.RemainingCapacity) &&
                Objects.equals(SOH, that.SOH) &&
                Objects.equals(Capacity, that.Capacity) &&
                Objects.equals(RatedCapacity, that.RatedCapacity) &&
                Objects.equals(DayTotalInputE, that.DayTotalInputE) &&
                Objects.equals(DayTotalOutputE, that.DayTotalOutputE) &&
                Objects.equals(DayMaxInputP, that.DayMaxInputP) &&
                Objects.equals(DayMaxInputPEpoch, that.DayMaxInputPEpoch) &&
                Objects.equals(DayMaxOutputP, that.DayMaxOutputP) &&
                Objects.equals(DayMaxOutputPEpoch, that.DayMaxOutputPEpoch) &&
                Status == that.Status &&
                Objects.equals(AutoDetect, that.AutoDetect) &&
                Objects.equals(BatteryManufacturer, that.BatteryManufacturer) &&
                Objects.equals(BatteryModel, that.BatteryModel) &&
                Objects.equals(BatteryFirmwareVersion, that.BatteryFirmwareVersion) &&
                Objects.equals(Errors, that.Errors) &&
                Objects.equals(Batteries, that.Batteries) &&
                Objects.equals(TotalChargingEnergykWh, that.TotalChargingEnergykWh) &&
                Objects.equals(TotalDischargingEnergykWh, that.TotalDischargingEnergykWh) &&
                Objects.equals(MismatchedTimeMinutes, that.MismatchedTimeMinutes) &&
                DisabledReason == that.DisabledReason &&
                Objects.equals(MinCellVoltage, that.MinCellVoltage) &&
                Objects.equals(MaxCellVoltage, that.MaxCellVoltage) &&
                BatteryVoltageType == that.BatteryVoltageType &&
                Objects.equals(HighestMinCellVoltageWhileIdle, that.HighestMinCellVoltageWhileIdle);
    }

    @Override
    public int hashCode() {
        return Objects.hash(V, I, P, SoC, RemainingCapacity, SOH, Capacity, RatedCapacity,
                DayTotalInputE, DayTotalOutputE, DayMaxInputP, DayMaxInputPEpoch,
                DayMaxOutputP, DayMaxOutputPEpoch, Status, AutoDetect, BatteryManufacturer,
                BatteryModel, BatteryFirmwareVersion, Errors, Batteries, TotalChargingEnergykWh,
                TotalDischargingEnergykWh, MismatchedTimeMinutes, DisabledReason,
                MinCellVoltage, MaxCellVoltage, BatteryVoltageType, HighestMinCellVoltageWhileIdle);
    }

    @Override
    public String toString() {
        return "BatteryStatus{" +
                "V=" + V +
                ", I=" + I +
                ", P=" + P +
                ", SoC=" + SoC +
                ", RemainingCapacity=" + RemainingCapacity +
                ", SOH=" + SOH +
                ", Capacity=" + Capacity +
                ", RatedCapacity=" + RatedCapacity +
                ", DayTotalInputE=" + DayTotalInputE +
                ", DayTotalOutputE=" + DayTotalOutputE +
                ", DayMaxInputP=" + DayMaxInputP +
                ", DayMaxInputPEpoch=" + DayMaxInputPEpoch +
                ", DayMaxOutputP=" + DayMaxOutputP +
                ", DayMaxOutputPEpoch=" + DayMaxOutputPEpoch +
                ", Status=" + Status +
                ", AutoDetect=" + AutoDetect +
                ", BatteryManufacturer='" + BatteryManufacturer + '\'' +
                ", BatteryModel='" + BatteryModel + '\'' +
                ", BatteryFirmwareVersion='" + BatteryFirmwareVersion + '\'' +
                ", Errors=" + Errors +
                ", Batteries=" + Batteries +
                ", TotalChargingEnergykWh=" + TotalChargingEnergykWh +
                ", TotalDischargingEnergykWh=" + TotalDischargingEnergykWh +
                ", MismatchedTimeMinutes=" + MismatchedTimeMinutes +
                ", DisabledReason=" + DisabledReason +
                ", MinCellVoltage=" + MinCellVoltage +
                ", MaxCellVoltage=" + MaxCellVoltage +
                ", BatteryVoltageType=" + BatteryVoltageType +
                ", HighestMinCellVoltageWhileIdle=" + HighestMinCellVoltageWhileIdle +
                '}';
    }
}