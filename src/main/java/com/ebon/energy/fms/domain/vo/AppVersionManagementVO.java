package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.config.ToStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppVersionManagementVO {

    private Long id;

    private String product;

    private String osType;

    private String version;

    private String releaseNotes;

    private String downloadUrl;

    private String upgradeMethod;

    private String status;

    private String createdBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long createdAt;

    private String updatedBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long updatedAt;
}
