package com.ebon.energy.fms.domain.vo.product.control.report;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor()
@EqualsAndHashCode
public final class InverterFirmwareReportedSettings {

    @JsonProperty("ARM")
    private String arm;

    @JsonProperty("DSP")
    private String dsp;

    // Copy constructor
    public InverterFirmwareReportedSettings(InverterFirmwareReportedSettings f) {
        this.arm = f.arm;
        this.dsp = f.dsp;
    }

    /**
     * 获取差异列表
     */
    public static List<ConfigurationDifference> getDifferences(InverterFirmwareReportedSettings reported, InverterFirmwareDesiredSettings desired) {
        List<ConfigurationDifference> differences = new ArrayList<>();
        ConfigurationDifference.add(differences, "ARM", 
            desired != null ? desired.getArm() : null, 
            reported != null ? reported.getArm() : null);
        ConfigurationDifference.add(differences, "DSP", 
            desired != null ? desired.getDsp() : null, 
            reported != null ? reported.getDsp() : null);
        return differences;
    }
}
