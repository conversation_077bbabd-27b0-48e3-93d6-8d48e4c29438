package com.ebon.energy.fms.domain.po;

import com.ebon.energy.fms.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateAppVersionPO {

    @NotBlank
    private String product;

    @NotBlank
    private String osType;

    @NotBlank
    private String version;

    private String releaseNotes;

    private String downloadUrl;

    @NotBlank
    private String upgradeMethod;

    @NotBlank
    private String status;

    public void checkParams() {
        if (!"android".equalsIgnoreCase(osType) && !"ios".equalsIgnoreCase(osType)) {
            throw new BizException("osType must be android or ios");
        }
        if (!"effective".equalsIgnoreCase(status) && !"invalid".equalsIgnoreCase(status)) {
            throw new BizException("status must be effective or invalid");
        }
        if (!"forced upgrade".equalsIgnoreCase(upgradeMethod) && !"elective upgrade".equalsIgnoreCase(upgradeMethod)) {
            throw new BizException("upgradeMethod must be \'forced upgrade\' or \'elective upgrade\' ");
        }
    }
}
