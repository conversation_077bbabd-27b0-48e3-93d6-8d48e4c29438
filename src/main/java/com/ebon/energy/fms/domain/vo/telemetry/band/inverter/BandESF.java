package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.InverterErrorMode;
import com.ebon.energy.fms.common.enums.ReadWorkMode;
import com.ebon.energy.fms.common.enums.TestResult;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandESF extends DataBackedBand
{
	public BandESF()
	{
		super(BandForge.<BandESF>getMetadataFor(BandESF.class));
	}



	public BandESF(byte[] bytes)
	{
		super(bytes, BandForge.<BandESF>getMetadataFor(BandESF.class));
	}

	public BandESF(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESF>getMetadataFor(BandESF.class));
	}



	public final Volt getVac1()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Frequency getFac1()
	{
		return GetU16(2, Frequency.Centi);
	}


	public final Watt getPacL()
	{
		return GetS16(4, Watt.Unit);
	}


	public final Object getReadWorkMode()
	{
		return ReadWorkMode.parse(GetU16(6));
	}


	public final Object getInverterErrorMode()
	{
		return InverterErrorMode.parse(GetU32(8));
	}


	public final Volt getLine1AvgFaultValue()
	{
		return GetU16(12, Volt.Deci);
	}


	public final TimeSpan getLine1AvgFaultTime()
	{
		return GetU16(14, TimeSpan.fromSeconds(1));
	}


	public final Volt getLine1VHighfaultValue()
	{
		return GetU16(16, Volt.Deci);
	}


	public final TimeSpan getLine1VHighfaultTime()
	{
		return GetU16(18, TimeSpan.fromMilliseconds(1));
	}


	public final Volt getLine1VLowfaultValue()
	{
		return GetU16(20, Volt.Deci);
	}


	public final TimeSpan getLine1VLowfaultTime()
	{
		return GetU16(22, TimeSpan.fromMilliseconds(1));
	}


	public final Frequency getLine1FHighfaultValueCom()
	{
		return GetU16(24, Frequency.Centi);
	}


	public final TimeSpan getLine1FhighfaultTimeCom()
	{
		return GetU16(26, TimeSpan.fromMilliseconds(1));
	}


	public final Frequency getLine1FlowfaultValueCom()
	{
		return GetU16(28, Frequency.Centi);
	}


	public final TimeSpan getLine1FlowfaultTimeCom()
	{
		return GetU16(30, TimeSpan.fromMilliseconds(1));
	}


	public final Frequency getLine1FHighfaultValue()
	{
		return GetU16(32, Frequency.Centi);
	}


	public final TimeSpan getLine1FHighfaultTime()
	{
		return GetU16(34, TimeSpan.fromMilliseconds(1));
	}


	public final Frequency getLine1FLowfaultValue()
	{
		return GetU16(36, Frequency.Centi);
	}


	public final TimeSpan getLine1FLowfaultTime()
	{
		return GetU16(38, TimeSpan.fromMilliseconds(1));
	}


	public final Volt getSimVoltage()
	{
		return GetU16(40, Volt.Deci);
	}


	public final Frequency getSimFrequency()
	{
		return GetU16(42, Frequency.Centi);
	}


	public final TestResult getTestResult()
	{
		return TestResult.forValue(GetU16(44));
	}




	public final int getSelfTestStep() { return GetU16(46); }


	public final boolean getStartTest()
	{
		return GetBool((int) GetU16(48), 1, 0, false);
	}
}
