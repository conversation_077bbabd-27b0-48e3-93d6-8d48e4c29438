package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandTGTMeterData extends DataBackedBand {
    public BandTGTMeterData() {
        super(BandForge.<BandTGTMeterData>getMetadataFor(BandTGTMeterData.class));
    }


    public BandTGTMeterData(byte[] bytes) {
        super(bytes, BandForge.<BandTGTMeterData>getMetadataFor(BandTGTMeterData.class));
    }

    public BandTGTMeterData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandTGTMeterData>getMetadataFor(BandTGTMeterData.class));
    }


    public final Volt getVoltageL1Tp() {
        return GetU16(0, Volt.Deci);
    }


    public final Ampere getCurrentL1Tp() {
        return GetS32(2, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL1Tp() {
        return new BigDecimal(GetS16(6)).multiply(Percentage.Tenth);
    }


    public final Watt getActivePowerL1Tp() {
        return GetS32(8, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL1Tp() {
        return GetU32(12, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL1Tp() {
        return GetS32(16, VoltAmpsReactive.Deci);
    }


    public final Frequency getFrequencyL1Tp() {
        return GetU16(20, Frequency.Centi);
    }


    public final Volt getVoltageL2Tp() {
        return GetU16(22, Volt.Deci);
    }


    public final Ampere getCurrentL2Tp() {
        return GetS32(24, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL2Tp() {
        return new BigDecimal(GetS16(28)).multiply(Percentage.Tenth);
    }


    public final Watt getActivePowerL2Tp() {
        return GetS32(30, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL2Tp() {
        return GetU32(34, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL2Tp() {
        return GetS32(38, VoltAmpsReactive.Deci);
    }


    public final Volt getVoltageL3Tp() {
        return GetU16(42, Volt.Deci);
    }


    public final Ampere getCurrentL3Tp() {
        return GetS32(44, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL3Tp() {
        return new BigDecimal(GetS16(48)).multiply(Percentage.Tenth);
    }


    public final Watt getActivePowerL3Tp() {
        return GetS32(50, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL3Tp() {
        return GetU32(54, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL3Tp() {
        return GetS32(58, VoltAmpsReactive.Deci);
    }


    public final Watt getActivePowerTotalTp() {
        return GetS32(62, Watt.Deci);
    }


    public final VoltAmps getReactivePowerTotalTp() {
        return GetS32(66, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getApparentPowerTotalTp() {
        return GetU32(70, VoltAmpsReactive.Deci);
    }


    public final BigDecimal getPowerFactorTotalTp() {
        return new BigDecimal(GetS16(74)).multiply(Percentage.Tenth);
    }
}
