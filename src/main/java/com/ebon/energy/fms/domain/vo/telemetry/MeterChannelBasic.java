package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;

/**
 * Basic (instantaneous or average) measurements for a channel.
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterChannelBasic {
    /**
     * Channel identifier (0=Total, 1=L1, 2=L2, 3=L3, 4=N).
     */
    private int ChannelId;

    public final int getChannelId() {
        return ChannelId;
    }

    public final void setChannelId(int value) {
        ChannelId = value;
    }

    /**
     * Channel voltage.
     */
    private BigDecimal Voltage = null;

    public final BigDecimal getVoltage() {
        return Voltage;
    }

    public final void setVoltage(BigDecimal value) {
        Voltage = value;
    }

    /**
     * Channel current.
     */
    private BigDecimal Current = null;

    public final BigDecimal getCurrent() {
        return Current;
    }

    public final void setCurrent(BigDecimal value) {
        Current = value;
    }

    /**
     * Channel power factor.
     */
    private BigDecimal PowerFactor = null;

    public final BigDecimal getPowerFactor() {
        return PowerFactor;
    }

    public final void setPowerFactor(BigDecimal value) {
        PowerFactor = value;
    }

    /**
     * Channel frequency.
     */
    private BigDecimal Frequency = null;

    public final BigDecimal getFrequency() {
        return Frequency;
    }

    public final void setFrequency(BigDecimal value) {
        Frequency = value;
    }
}
