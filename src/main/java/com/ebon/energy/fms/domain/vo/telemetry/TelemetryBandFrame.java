package com.ebon.energy.fms.domain.vo.telemetry;

import java.time.Instant;

public class TelemetryBandFrame<T> {
    private Instant TimestampUtc;
    private T Data;

    public TelemetryBandFrame(
            Instant TimestampUtc,
            T Data) {
        this.TimestampUtc = TimestampUtc;
        this.Data = Data;
    }

    public Instant getTimestampUtc() {
        return this.TimestampUtc;
    }

    public T getData() {
        return this.Data;
    }
}
