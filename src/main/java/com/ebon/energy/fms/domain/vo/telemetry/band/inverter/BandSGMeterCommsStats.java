package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandSGMeterCommsStats extends DataBackedBand
{
	public BandSGMeterCommsStats()
	{
		super(BandForge.<BandSGMeterCommsStats>getMetadataFor(BandSGMeterCommsStats.class));
	}



	public BandSGMeterCommsStats(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterCommsStats>getMetadataFor(BandSGMeterCommsStats.class));
	}

	public BandSGMeterCommsStats(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterCommsStats>getMetadataFor(BandSGMeterCommsStats.class));
	}


	


	public final int getMeterTotalMessageCount()
	{
		return GetU32(0);
	}

	


	public final int getMeterCorruptMessageCount()
	{
		return GetU32(4);
	}

	


	public final int getMeterModbusExceptionCount()
	{
		return GetU32(8);
	}

	


	public final int getMeterExcessBytesCount()
	{
		return GetU32(12);
	}

	


	public final int getMeterNoResponseCount()
	{
		return GetU32(16);
	}
}
