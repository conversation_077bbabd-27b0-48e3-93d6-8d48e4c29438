package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



 


// Copyright (c) Redback Technologies. All Rights Reserved.





public class BandPylon92 extends DataBackedBand implements IBandPylonPackIndex
{
	public BandPylon92()
	{
		super(BandForge.<BandPylon92>getMetadataFor(BandPylon92.class));
	}



	public BandPylon92(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon92>getMetadataFor(BandPylon92.class));
	}

	public BandPylon92(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon92>getMetadataFor(BandPylon92.class));
	}


	


	public final byte getPackIndexUnadjusted()
	{
		return GetU8(0);
	}

	
	public final Volt getChargeVoltageLimit()
	{
		return GetU16(1, Volt.Milli);
	}

	
	public final Volt getDischargeVoltageLimit()
	{
		return GetU16(3, Volt.Milli);
	}

	
	public final Ampere getChargeCurrentLimit()
	{
		return GetS16(5, Ampere.Deci);
	}

	
	public final Ampere getDischargeCurrentLimit()
	{
		return GetS16(7, Ampere.Deci);
	}

	
	public final String getChargeStatus()
	{
		return PylonChargeStatus.parse(GetU8(9));
	}


	public int getPackIndex()
	{
		return getPackIndexUnadjusted() - 2;
	}
}
