package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Frequency implements IUnit {

    public static final String SYMBOL = "Hz";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final Frequency Unit = new Frequency(new BigDecimal("1.0"));
    public static final Frequency Zero = new Frequency(new BigDecimal("0.0"));
    public static final Frequency Tera = new Frequency(new BigDecimal("1000000000000"));
    public static final Frequency Giga = new Frequency(new BigDecimal("1000000000"));
    public static final Frequency Mega = new Frequency(new BigDecimal("1000000"));
    public static final Frequency Kilo = new Frequency(new BigDecimal("1000"));
    public static final Frequency Hecto = new Frequency(new BigDecimal("100"));
    public static final Frequency Deca = new Frequency(new BigDecimal("10"));
    public static final Frequency Deci = new Frequency(new BigDecimal("0.1"));
    public static final Frequency Centi = new Frequency(new BigDecimal("0.01"));
    public static final Frequency Milli = new Frequency(new BigDecimal("0.001"));

    public Frequency() {
    }

    public Frequency(BigDecimal value) {
        this.value = value;
    }

    public Frequency(String value) {
        this.value = new BigDecimal(value);
    }


    public Frequency(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Frequency(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Frequency abs() {
        return new Frequency(value.abs());
    }

    public Frequency subtract(Frequency other) {
        return new Frequency(this.value.subtract(other.value));
    }

    public Frequency add(Frequency other) {
        return new Frequency(this.value.add(other.value));
    }

    public static Frequency add(Frequency a, Frequency b) {
        return new Frequency(a.value.add(b.value));
    }

    public static Frequency subtract(Frequency a, Frequency b) {
        return new Frequency(a.value.subtract(b.value));
    }

    public boolean greaterThan(Frequency other) {
        return this.value.compareTo(other.value) > 0;
    }

    public boolean lessThan(Frequency other) {
        return this.value.compareTo(other.value) < 0;
    }

    public boolean greaterThanOrEqual(Frequency other) {
        return this.value.compareTo(other.value) >= 0;
    }

    public boolean lessThanOrEqual(Frequency other) {
        return this.value.compareTo(other.value) <= 0;
    }

    public static Frequency getValueOrZero(Frequency watt) {
        return watt == null || watt.getValue() == null ? Frequency.Zero : watt;
    }

    // 重载一元 + 运算符
    public static Frequency operatorPlus(Frequency a) {
        return a;
    }

    // 重载一元 - 运算符
    public static Frequency operatorMinus(Frequency a) {
        return new Frequency(a.value.negate());
    }

    // 重载二元 + 运算符
    public static Frequency operatorAdd(Frequency a, Frequency b) {
        return new Frequency(a.value.add(b.value));
    }

    // 重载二元 - 运算符
    public static Frequency operatorSubtract(Frequency a, Frequency b) {
        return new Frequency(a.value.subtract(b.value));
    }

    // 重载二元 / 运算符，返回 BigDecimal
    public static BigDecimal operatorDivide(Frequency a, Frequency b) {
        return a.value.divide(b.value);
    }

    // 重载 Watt 除以 BigDecimal 的 / 运算符
    public static Frequency operatorDivide(Frequency a, BigDecimal b) {
        return new Frequency(a.value.divide(b));
    }

    // 重载 Watt 乘以 BigDecimal 的 * 运算符
    public static Frequency operatorMultiply(Frequency a, BigDecimal value) {
        return new Frequency(a.value.multiply(value));
    }

    // 重载 BigDecimal 乘以 Watt 的 * 运算符
    public static Frequency operatorMultiply(BigDecimal value, Frequency a) {
        return new Frequency(value.multiply(a.value));
    }

    // 重载 == 运算符
    public static boolean operatorEqual(Frequency a, Frequency b) {
        return a.value.equals(b.value);
    }

    // 重载 != 运算符
    public static boolean operatorNotEqual(Frequency a, Frequency b) {
        return !a.value.equals(b.value);
    }

    // 重载 < 运算符
    public static boolean operatorLessThan(Frequency a, Frequency b) {
        return a.value.compareTo(b.value) < 0;
    }

    // 重载 <= 运算符
    public static boolean operatorLessThanOrEqual(Frequency a, Frequency b) {
        return a.value.compareTo(b.value) <= 0;
    }

    // 重载 > 运算符
    public static boolean operatorGreaterThan(Frequency a, Frequency b) {
        return a.value.compareTo(b.value) > 0;
    }

    // 重载 >= 运算符
    public static boolean operatorGreaterThanOrEqual(Frequency a, Frequency b) {
        return a.value.compareTo(b.value) >= 0;
    }

    // 从 BigDecimal 隐式转换为 Watt
    public static Frequency fromBigDecimal(BigDecimal d) {
        return new Frequency(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public BigDecimal asDecimal(Frequency unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(Frequency unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(Frequency unit) {
        return value.divide(unit.value);
    }

    public double asDouble(Frequency unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(Frequency unit) {
        return value.divide(unit.value).longValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Frequency other = (Frequency) obj;
        return value.compareTo(other.value) == 0;
    }

    public int compareTo(Frequency other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}
