package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.Instant;
import java.util.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class RossDeviceTelemetry {
    public String SchemaVersion = "4.2";
    public String MessageId;
    public String InstanceId;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant TimestampUtc;

    public NetworkAdaptorsStatus Network;
    public InverterTelemetry Inverter;
    public BatteryTelemetry Batteries;
    public OuijaBoardTelemetry OuijaBoard;
    public BatteryCabinetTelemetry BatteryCabinet;
    public FirmwareUpdateTelemetry FirmwareUpdate;
    public BatteryFirmwareUpdateTelemetry BatteryFirmwareUpdate;
    public ScheduleTelemetry Schedules;
    public Map<Integer, RelayTelemetry> Relays = new HashMap<>();
    public Map<Integer, SmartRelayTelemetry> SmartRelays = new HashMap<>();
    public FlagsTelemetry Flags;
    public DredTelemetry Dred;
    public SiteTelemetry Site;
    public Map<String, Map<String, Object>> Stats;
    public String DeviceId;
    public String EdgeId;
    public List<ErrorTelemetry> Errors = new ArrayList<>();
    public List<EventTelemetry> Events = new ArrayList<>();
    public Integer RelayCheckFailureReboots;
    public ModbusServerTelemetry ModbusServer;
    public SiteCoordinatorTelemetry SiteCoordinator;
    public MeterTelemetry GridMeterTelemetry;
    public MeterTelemetry ThirdPartyGenerationTelemetry;
    public List<LoadControlTelemetry> ControlledLoads = new ArrayList<>();
    public PerformanceMonitoringTelemetry PerformanceMonitoring;

    public RossDeviceTelemetry() {
        this.Inverter = new InverterTelemetry();
        this.Batteries = new BatteryTelemetry();
        this.OuijaBoard = new OuijaBoardTelemetry();
        this.BatteryCabinet = new BatteryCabinetTelemetry();
        this.Relays = new HashMap<>();
        this.SmartRelays = new HashMap<>();
        this.Dred = new DredTelemetry();
        this.Site = new SiteTelemetry();
        this.ControlledLoads = new ArrayList<>();
        this.PerformanceMonitoring = new PerformanceMonitoringTelemetry();
    }
}