package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.ExtGridDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandETGridStatus extends DataBackedBand
{
	public BandETGridStatus()
	{
		super(BandForge.<BandETGridStatus>getMetadataFor(BandETGridStatus.class));
	}



	public BandETGridStatus(byte[] bytes)
	{
		super(bytes, BandForge.<BandETGridStatus>getMetadataFor(BandETGridStatus.class));
	}

	public BandETGridStatus(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETGridStatus>getMetadataFor(BandETGridStatus.class));
	}


	
	public final Object getExtGridDetailedErr()
	{
		return ExtGridDetailedErr.parse(GetU64(0));
	}

	
	public final Object getExtInvDetailedErr()
	{
		return ExtInvDetailedErr.parse(GetU64(8));
	}

	
	public final Object getExtInvDetailedStatus()
	{
		return ExtInvDetailedStatus.parse(GetU64(16));
	}
}
