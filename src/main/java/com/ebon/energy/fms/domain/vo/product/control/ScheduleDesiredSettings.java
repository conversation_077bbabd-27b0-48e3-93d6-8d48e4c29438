// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.product.control;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data Transfer Object for schedule desired settings.
 * This is a Java conversion of the C# ScheduleDesiredSettings class.
 */
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.*;

@Data
@NoArgsConstructor
public class ScheduleDesiredSettings {

    public static final String SCHEDULE_SETTINGS_NAME = "Schedules";

    @JsonProperty(SCHEDULE_SETTINGS_NAME)
    private Map<String, ScheduleInfoDto> schedules = new HashMap<>();

    /**
     * Gets the Iana Time Zone Identifier used which is used to derive local time
     */
    @JsonProperty("IanaTimeZoneId")
    private String ianaTimeZoneId;

    /**
     * Gets the version of the schedule set that is found above.
     * Once these schedules are used, the version in the reported value will change to match this.
     */
    @JsonProperty("Version")
    private int version;

    /**
     * Gets the last local sync version that was used to generate the schedule list above.
     * This allows the system to know if the cloud has received what ROSS schedules have been sent.
     */
    @JsonProperty("LocalSync")
    private int localSync;

    @JsonCreator
    public ScheduleDesiredSettings(
            @JsonProperty(SCHEDULE_SETTINGS_NAME) Map<String, ScheduleInfoDto> schedules,
            @JsonProperty("IanaTimeZoneId") String ianaTimeZoneId,
            @JsonProperty("Version") int version,
            @JsonProperty("LocalSync") int localSync) {
        this.schedules = schedules != null ? schedules : new HashMap<>();
        this.ianaTimeZoneId = ianaTimeZoneId;
        this.version = version;
        this.localSync = localSync;
    }

    public static ScheduleDesiredSettings defaultInstance() {
        return new ScheduleDesiredSettings();
    }

    public ScheduleDesiredSettings withUpdatedSchedules(Map<String, ScheduleInfoDto> extras) {
        Map<String, ScheduleInfoDto> newSchedules = new HashMap<>(this.schedules);
        newSchedules.putAll(extras);
        return new ScheduleDesiredSettings(
                newSchedules,
                this.ianaTimeZoneId,
                this.version,
                this.localSync
        );
    }

    // equals and hashCode are handled by Lombok's @Data
}

