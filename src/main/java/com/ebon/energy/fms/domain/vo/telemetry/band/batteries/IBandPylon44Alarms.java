package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



public interface IBandPylon44Alarms extends IBand
{
	Object getStatus();

	byte getPackIndexUnadjusted();

	byte getCellCount();

	byte getTempSensorCount();

	Object getChargeCurrentStatus();

	Object getPackVoltageStatus();

	Object getDischargeCurrentStatus();

	Object getStatus1();

	Object getStatus2();

	Object getStatus3();

	Object getStatus4();

	Object getStatus5();

	Object[] getCellVoltageStatuses();

	Object[] getTempSensorStatuses();
}
