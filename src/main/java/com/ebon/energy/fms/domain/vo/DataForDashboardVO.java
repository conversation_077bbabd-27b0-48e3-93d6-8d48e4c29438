package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.DREDCommand;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.GridStatusValue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.Status;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.util.SystemStatusHelper;

import java.time.ZonedDateTime;

public class DataForDashboardVO {
    private Integer SiteExportLimitW;
    private String SchemaVersion;
    private ZonedDateTime LastStatusDateUtc;
    private Watt ACLoadP;
    private Status BackupLoadStatus;
    private Watt BackupLoadP;
    private GridStatusValue GridStatus;
    private Watt GridP;
    private String InverterSN;
    private String ModelName;
    private String FirmwareVersion;
    private Watt InverterP;
    private Watt ThirdPartyInverterP;
    private Watt PVP;
    private Double PvDayTotalE;
    private BatteryStatus BatterySummary;
    private ZonedDateTime WindowsTime;
    private String TimeZone;
    private Boolean CTComms;
    private boolean BmsVersionMismatch;
    private DailyEnergyTotalsVO DailyEnergyTotals;
    private Boolean DRM0Enable;

    public DataForDashboardVO(
            Integer SiteExportLimitW,
            String SchemaVersion,
            ZonedDateTime LastStatusDateUtc,
            Watt ACLoadP,
            Status BackupLoadStatus,
            Watt BackupLoadP,
            GridStatusValue GridStatus,
            Watt GridP,
            String InverterSN,
            String ModelName,
            String FirmwareVersion,
            Watt InverterP,
            Watt ThirdPartyInverterP,
            Watt PVP,
            BatteryStatus BatterySummary,
            ZonedDateTime WindowsTime,
            String TimeZone,
            Boolean CTComms,
            Boolean BmsVersionMismatch,
            DailyEnergyTotalsVO DailyEnergyTotals,
            Boolean DRM0Enable) {
        this.SiteExportLimitW = SiteExportLimitW;
        this.SchemaVersion = SchemaVersion;
        this.LastStatusDateUtc = LastStatusDateUtc;
        this.ACLoadP = ACLoadP;
        this.BackupLoadStatus = BackupLoadStatus;
        this.BackupLoadP = BackupLoadP;
        this.GridStatus = GridStatus;
        this.GridP = GridP;
        this.InverterSN = InverterSN;
        this.ModelName = ModelName;
        this.FirmwareVersion = FirmwareVersion;
        this.InverterP = InverterP;
        this.ThirdPartyInverterP = ThirdPartyInverterP;
        this.PVP = PVP;
        this.PvDayTotalE = null;
        this.BatterySummary = BatterySummary;
        this.WindowsTime = WindowsTime;
        this.TimeZone = TimeZone;
        this.CTComms = CTComms;
        this.BmsVersionMismatch = BmsVersionMismatch;
        this.DailyEnergyTotals = DailyEnergyTotals;
        this.DRM0Enable = DRM0Enable;
    }

    private DataForDashboardVO(SystemStatus status) {
        this.SiteExportLimitW = status.getSiteLimits() != null ? status.getSiteLimits().getFunctionalSiteExportLimitW() : null;
        this.SchemaVersion = status.getSchemaVersion();
        this.LastStatusDateUtc = status.getDate();
        this.ACLoadP = status.getACLoad() != null && status.getACLoad().getP() != null ? new Watt(status.getACLoad().getP()) : null;
        this.BackupLoadStatus = status.getBackupLoad() != null ? status.getBackupLoad().getStatus() : null;
        this.BackupLoadP = status.getBackupLoad() != null && status.getBackupLoad().getP() != null ? new Watt(status.getBackupLoad().getP()) : null;
        this.ThirdPartyInverterP = status.getThirdPartyInverter() != null && status.getThirdPartyInverter().getP() != null ? new Watt(status.getThirdPartyInverter().getP()) : null;
        this.GridStatus = status.getGrid() != null ? status.getGrid().getStatus() : null;
        this.GridP = status.getGrid() != null && status.getGrid().getP() != null ? new Watt(status.getGrid().getP()) : null;
        this.InverterSN = status.getInverter() != null ? status.getInverter().getInverterSN() : null;
        this.ModelName = status.getInverter() != null ? status.getInverter().getModelName() : null;
        this.FirmwareVersion = status.getInverter() != null ? status.getInverter().getFirmwareVersion() : null;
        this.InverterP = status.getInverter() != null && status.getInverter().getInverterP() != null ? new Watt(status.getInverter().getInverterP()) : null;
        this.BatterySummary = SystemStatusHelper.getBatterySummary(status);
        this.PVP = status.getPV() != null ? new Watt(status.getPV().getP()) : null;
        this.DailyEnergyTotals = SystemStatusHelper.getDailyEnergyTotals(status);
        this.WindowsTime = status.getOuijaBoard() != null ? status.getOuijaBoard().getWindowsTime() : null;
        this.TimeZone = status.getOuijaBoard() != null ? status.getOuijaBoard().getTimeZone() : null;
        this.CTComms = status.getOuijaBoard() != null ? status.getOuijaBoard().getCTComms() : null;
        this.BmsVersionMismatch = false;
        this.DRM0Enable = status != null && status.getDRED() != null?status.getDRED().getDREDCommand() == DREDCommand.DRM0 && status.getDRED().getIsOn() : false;
        this.PvDayTotalE = null;
    }

    public Integer getSiteExportLimitW() {
        return SiteExportLimitW;
    }

    public String getSchemaVersion() {
        return SchemaVersion;
    }

    public ZonedDateTime getLastStatusDateUtc() {
        return LastStatusDateUtc;
    }

    public Watt getACLoadP() {
        return ACLoadP;
    }

    public Status getBackupLoadStatus() {
        return BackupLoadStatus;
    }

    public Watt getBackupLoadP() {
        return BackupLoadP;
    }

    public GridStatusValue getGridStatus() {
        return GridStatus;
    }

    public Watt getGridP() {
        return GridP;
    }

    public String getInverterSN() {
        return InverterSN;
    }

    public String getModelName() {
        return ModelName;
    }

    public String getFirmwareVersion() {
        return FirmwareVersion;
    }

    public Watt getInverterP() {
        return InverterP;
    }

    public Watt getThirdPartyInverterP() {
        return ThirdPartyInverterP;
    }

    public Watt getPVP() {
        return PVP;
    }

    public Double getPvDayTotalE() {
        return PvDayTotalE;
    }

    public BatteryStatus getBatterySummary() {
        return BatterySummary;
    }

    public ZonedDateTime getWindowsTime() {
        return WindowsTime;
    }

    public String getTimeZone() {
        return TimeZone;
    }

    public Boolean getCTComms() {
        return CTComms;
    }

    public boolean isBmsVersionMismatch() {
        return BmsVersionMismatch;
    }

    public DailyEnergyTotalsVO getDailyEnergyTotals() {
        return DailyEnergyTotals;
    }

    public Boolean getDRM0Enable() {
        return DRM0Enable;
    }

    public static DataForDashboardVO make(SystemStatus status) {
        if (status == null) {
            return null;
        }
        return new DataForDashboardVO(status);
    }
}
