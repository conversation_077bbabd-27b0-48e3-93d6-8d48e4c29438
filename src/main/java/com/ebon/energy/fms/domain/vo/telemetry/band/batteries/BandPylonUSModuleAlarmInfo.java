package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandPylonUSModuleAlarmInfo extends DataBackedBand
{
	public BandPylonUSModuleAlarmInfo()
	{
		super(BandForge.<BandPylonUSModuleAlarmInfo>getMetadataFor(BandPylonUSModuleAlarmInfo.class));
	}



	public BandPylonUSModuleAlarmInfo(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylonUSModuleAlarmInfo>getMetadataFor(BandPylonUSModuleAlarmInfo.class));
	}

	public BandPylonUSModuleAlarmInfo(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylonUSModuleAlarmInfo>getMetadataFor(BandPylonUSModuleAlarmInfo.class));
	}



	public final Object getInfoFlag44()
	{
		return PylonInfoFlagState.parse(GetU8(0));
	}




	public final byte getCommandValue44()
	{
		return GetU8(1);
	}


	public final Object getNumberOfCells44()
	{
		return PylonAlarmState.parse(GetU8(2));
	}


	public final Object getCell1VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(3));
	}


	public final Object getCell2VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(4));
	}


	public final Object getCell3VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(5));
	}


	public final Object getCell4VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(6));
	}


	public final Object getCell5VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(7));
	}


	public final Object getCell6VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(8));
	}


	public final Object getCell7VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(9));
	}


	public final Object getCell8VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(10));
	}


	public final Object getCell9VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(11));
	}


	public final Object getCell10VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(12));
	}


	public final Object getCell11VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(13));
	}


	public final Object getCell12VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(14));
	}


	public final Object getCell13VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(15));
	}


	public final Object getCell14VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(16));
	}


	public final Object getCell15VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(17));
	}


	public final Object getNumberOfTempSensors44()
	{
		return PylonAlarmState.parse(GetU8(18));
	}


	public final Object getTempBMSBoardStatus()
	{
		return PylonAlarmState.parse(GetU8(19));
	}


	public final Object getCellTempAvgGroup1Status()
	{
		return PylonAlarmState.parse(GetU8(20));
	}


	public final Object getCellTempAvgGroup2Status()
	{
		return PylonAlarmState.parse(GetU8(21));
	}


	public final Object getCellTempAvgGroup3Status()
	{
		return PylonAlarmState.parse(GetU8(22));
	}


	public final Object getCellTempAvgGroup4Status()
	{
		return PylonAlarmState.parse(GetU8(23));
	}


	public final Object getModuleChargeCurrentStatus()
	{
		return PylonAlarmState.parse(GetU8(25));
	}


	public final Object getModuleVoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(26));
	}


	public final Object getModuleDischargeCurrentStatus()
	{
		return PylonAlarmState.parse(GetU8(27));
	}


	public final Object getStatus1()
	{
		return PylonUSPackStatus1.parse(GetU8(28));
	}


	public final Object getStatus2()
	{
		return PylonPackStatus2.parse(GetU8(29));
	}


	public final Object getStatus3()
	{
		return PylonUSPackStatus3.parse( GetU8(30));
	}


	public final Object getStatus4()
	{
		return PylonPackStatus4.parse(GetU8(31));
	}


	public final Object getStatus5()
	{
		return PylonPackStatus5.parse(GetU8(32));
	}


	public final short getStatus6()
	{
		return (short)GetU8(33);
	}
}
