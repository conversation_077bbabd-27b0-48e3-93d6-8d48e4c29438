package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.SGErrorCode;
import com.ebon.energy.fms.common.enums.SGInverterMode;
import com.ebon.energy.fms.common.enums.SGPowerLimitingCode;
import com.ebon.energy.fms.common.enums.SGWarningCode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.fasterxml.jackson.annotation.JsonProperty;


import java.math.*;


public class BandSGInverterData extends DataBackedBand {
    public BandSGInverterData() {
        super(BandForge.<BandSGInverterData>getMetadataFor(BandSGInverterData.class));
    }


    public BandSGInverterData(byte[] bytes) {
        super(bytes, BandForge.<BandSGInverterData>getMetadataFor(BandSGInverterData.class));
    }

    public BandSGInverterData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandSGInverterData>getMetadataFor(BandSGInverterData.class));
    }

    @JsonProperty("PhaseL1Voltage")
    public final Volt getPhaseL1Voltage() {
        return GetU16(0, Volt.Deci);
    }

    @JsonProperty("PhaseL1Current")
    public final Ampere getPhaseL1Current() {
        return GetU16(2, Ampere.Centi);
    }

    @JsonProperty("PhaseL1Power")
    public final Watt getPhaseL1Power() {
        return GetU32(4, Watt.Deci);
    }


    public final Frequency getPhaseL1Frequency() {
        return GetU16(8, Frequency.Centi);
    }


    public final Volt getPhaseL2Voltage() {
        return GetU16(10, Volt.Deci);
    }


    public final Ampere getPhaseL2Current() {
        return GetU16(12, Ampere.Centi);
    }


    public final Watt getPhaseL2Power() {
        return GetU32(14, Watt.Deci);
    }


    public final Frequency getPhaseL2Frequency() {
        return GetU16(18, Frequency.Centi);
    }


    public final Volt getPhaseL3Voltage() {
        return GetU16(20, Volt.Deci);
    }


    public final Ampere getPhaseL3Current() {
        return GetU16(22, Ampere.Centi);
    }


    public final Watt getPhaseL3Power() {
        return GetU32(24, Watt.Deci);
    }


    public final Frequency getPhaseL3Frequency() {
        return GetU16(28, Frequency.Centi);
    }


    public final Volt getPV1Voltage() {
        return GetU16(30, Volt.Deci);
    }


    public final Ampere getPV1Current() {
        return GetU16(32, Ampere.Centi);
    }


    public final Watt getPV1Power() {
        return GetU32(34, Watt.Deci);
    }


    public final Volt getPV2Voltage() {
        return GetU16(38, Volt.Deci);
    }


    public final Ampere getPV2Current() {
        return GetU16(40, Ampere.Centi);
    }


    public final Watt getPV2Power() {
        return GetU32(42, Watt.Deci);
    }


    public final Volt getPV3Voltage() {
        return GetU16(46, Volt.Deci);
    }


    public final Ampere getPV3Current() {
        return GetU16(48, Ampere.Centi);
    }


    public final Watt getPV3Power() {
        return GetU32(50, Watt.Deci);
    }


    public final Celsius getInnerTemperature() {
        return GetS16(54, Celsius.Unit);
    }


    public final Object getInverterMode() {
        return SGInverterMode.parse(GetU16(56));
    }


    public final Object getErrorCode() {
        return SGErrorCode.parse(GetU32(58));
    }


    public final Object getWarningCode() {
        return SGWarningCode.parse(GetU16(62));
    }


    public final WattHour getTotalEnergy() {
        return GetU32(64, WattHour.Kilo);
    }


    public final TimeSpan getTotalGenerationTime() {
        return GetU32(68, TimeSpan.fromHours(1));
    }


    public final WattHour getTodayEnergyLessPrecision() {
        return GetU32(72, WattHour.Kilo);
    }


    public final WattHour getTodayEnergy() {
        return GetU32(76, WattHour.Unit);
    }


    public final Volt getFailurePhaseL1GridVoltage() {
        return GetU16(80, Volt.Deci);
    }


    public final Volt getFailurePhaseL2GridVoltage() {
        return GetU16(82, Volt.Deci);
    }


    public final Volt getFailurePhaseL3GridVoltage() {
        return GetU16(84, Volt.Deci);
    }


    public final Frequency getFailurePhaseL1GridFreq() {
        return GetU16(86, Frequency.Centi);
    }


    public final Frequency getFailurePhaseL2GridFreq() {
        return GetU16(88, Frequency.Centi);
    }


    public final Frequency getFailurePhaseL3GridFreq() {
        return GetU16(90, Frequency.Centi);
    }


    public final Volt getFailurePV1Voltage() {
        return GetU16(92, Volt.Deci);
    }


    public final Volt getFailurePV2Voltage() {
        return GetU16(94, Volt.Deci);
    }


    public final Volt getFailurePV3Voltage() {
        return GetU16(96, Volt.Deci);
    }


    public final Celsius getFailureTemp() {
        return GetU16(98, Celsius.Deci);
    }


    public final Ampere getFailureGFCI() {
        return GetU16(100, Ampere.Milli);
    }


    public final Ohm getFailureIsolation() {
        return GetU16(102, Ohm.Kilo);
    }


    public final int getFailureOther() { return GetU16(104); }


    public final Object getPowerLimitingFunction() {
        return SGPowerLimitingCode.parse(GetU16(106));
    }


    public final Watt getInverterActivePower() {
        return GetU32(108, Watt.Deci);
    }


    public final VoltAmpsReactive getInverterReactivePower() {
        return GetS32(112, VoltAmpsReactive.Deci);
    }


    public final Watt getTodayPeakPower() {
        return GetU32(116, Watt.Deci);
    }


    public final BigDecimal getInverterPowerFactor() {
        return new BigDecimal(GetS16(120)).multiply(Percentage.Tenth);
    }
}
