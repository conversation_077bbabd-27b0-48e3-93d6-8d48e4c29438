package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@TableName("ProductDailyCaches")
public class ProductDailyCachesDO {

    @TableField(value = "RedbackProductSn")
    private String redbackProductSn;

    @TableField(value = "Date")
    private String date;

    @TableField(value = "Time")
    private Timestamp time;

    @TableField(value = "TotalConsumption")
    private BigDecimal totalConsumption;

    @TableField(value = "TotalExport")
    private BigDecimal totalExport;

    @TableField(value = "TotalImport")
    private BigDecimal totalImport;

    @TableField(value = "TotalGeneration")
    private BigDecimal totalGeneration;

}