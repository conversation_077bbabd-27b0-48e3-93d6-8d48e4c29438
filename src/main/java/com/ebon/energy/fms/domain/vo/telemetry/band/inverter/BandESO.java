package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandESO extends DataBackedBand
{
	public BandESO()
	{
		super(BandForge.<BandESO>getMetadataFor(BandESO.class));
	}



	public BandESO(byte[] bytes)
	{
		super(bytes, BandForge.<BandESO>getMetadataFor(BandESO.class));
	}

	public BandESO(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESO>getMetadataFor(BandESO.class));
	}


	


	public final int getMeterSoftwareVersion() { return GetU16(0); }
}
