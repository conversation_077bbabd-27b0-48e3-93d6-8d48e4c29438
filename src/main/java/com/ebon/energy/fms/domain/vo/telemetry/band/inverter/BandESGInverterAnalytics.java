package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.ExtGridDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESGInverterAnalytics extends DataBackedBand
{
	public BandESGInverterAnalytics()
	{
		super(BandForge.<BandESGInverterAnalytics>getMetadataFor(BandESGInverterAnalytics.class));
	}



	public BandESGInverterAnalytics(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGInverterAnalytics>getMetadataFor(BandESGInverterAnalytics.class));
	}

	public BandESGInverterAnalytics(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGInverterAnalytics>getMetadataFor(BandESGInverterAnalytics.class));
	}


	
	public final Object getSafetyDetailedErrorMsg()
	{
		return ExtGridDetailedErr.parse(GetU64(0));
	}

	
	public final Object getInvDetailedErrorMsg()
	{
		return ExtInvDetailedErr.parse(GetU64(8));
	}

	
	public final Object getInvDetailedStatusMsg()
	{
		return ExtInvDetailedStatus.parse(GetU64(16));
	}

	


	public final byte[] getReserved0x89BE()
	{
		return GetRaw(24, 12);
	}

	
	public final Frequency getMaxGridFreqWithin1Min()
	{
		return GetU16(36, Frequency.Centi);
	}

	
	public final Frequency getMinGridFreqWithin1Min()
	{
		return GetU16(38, Frequency.Centi);
	}

	
	public final Volt getMaxGridVoltageWithin1MinL1()
	{
		return GetU16(40, Volt.Deci);
	}

	
	public final Volt getMinGridVoltageWithin1MinL1()
	{
		return GetU16(42, Volt.Deci);
	}

	
	public final Volt getMaxGridVoltageWithin1MinL2()
	{
		return GetU16(44, Volt.Deci);
	}

	
	public final Volt getMinGridVoltageWithin1MinL2()
	{
		return GetU16(46, Volt.Deci);
	}

	
	public final Volt getMaxGridVoltageWithin1MinL3()
	{
		return GetU16(48, Volt.Deci);
	}

	
	public final Volt getMinGridVoltageWithin1MinL3()
	{
		return GetU16(50, Volt.Deci);
	}

	
	public final Watt getMaxBackupPowerWithin1MinL1()
	{
		return GetU32(52, Watt.Unit);
	}

	
	public final Watt getMaxBackupPowerWithin1MinL2()
	{
		return GetU32(56, Watt.Unit);
	}

	
	public final Watt getMaxBackupPowerWithin1MinL3()
	{
		return GetU32(60, Watt.Unit);
	}

	
	public final Watt getMaxBackupPowerWithin1MinTotal()
	{
		return GetU32(64, Watt.Unit);
	}

	


	public final int getGridHvrtEventTimes() { return GetU16(68); }

	


	public final int getGridLvrtEventTimes() { return GetU16(70); }

	


	public final int getInvErrorMsgRecordForEms()
	{


		return (int)GetU32(72);
	}

	


	public final int getInvWarningCodeRecordForEms()
	{


		return (int)GetU32(76);
	}

	


	public final int getInvCpldWarningRecordForEms()
	{


		return (int)GetU32(80);
	}
}
