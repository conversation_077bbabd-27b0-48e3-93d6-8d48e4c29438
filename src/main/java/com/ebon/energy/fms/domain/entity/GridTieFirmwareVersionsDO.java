package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

@Data
@Accessors
@TableName("GridTieFirmwareVersions")
public class GridTieFirmwareVersionsDO {

    @TableField(value = "ID")
    private Integer id;

    @TableField(value = "EMSFirmwareVersion")
    private String emsFirmwareVersion;

    @TableField(value = "BinFileName")
    private String binFileName;

    @TableField(value = "IsOfficialVersion")
    private Boolean isOfficialVersion;

    @TableField(value = "LastModifiedUtc")
    private Timestamp lastModifiedUtc;

    @TableField(value = "CreatedBy")
    private String createdBy;
}
