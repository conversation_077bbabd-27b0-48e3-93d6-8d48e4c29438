// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.product.control;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.Objects;

import com.ebon.energy.fms.common.json.CustomZonedDateTimeDeserializer;
import com.ebon.energy.fms.common.json.CustomZonedDateTimeSerializer;
import com.ebon.energy.fms.common.json.DurationFromTimeDeserializer;
import com.ebon.energy.fms.common.json.DurationToTimeSerializer;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.Getter;

/**
 * Data Transfer Object for schedule information.
 * This is a Java conversion of the C# ScheduleInfoDto class.
 */
@Data
public class ScheduleInfoDto {
    public static final Integer USER_SCHEDULE_PRIORITY = 2;
    public static final Integer REDBACK_SCHEDULE_PRIORITY = 3;
    public static final String END_AT_UTC_JSON_NAME = "e";

    @JsonProperty("p")
    private final Integer priority;

    @JsonProperty("s")
    @JsonSerialize(using = CustomZonedDateTimeSerializer.class)
    @JsonDeserialize(using = CustomZonedDateTimeDeserializer.class)
    private final ZonedDateTime startAtUtc;

    @JsonProperty(END_AT_UTC_JSON_NAME)
    @JsonSerialize(using = CustomZonedDateTimeSerializer.class)
    @JsonDeserialize(using = CustomZonedDateTimeDeserializer.class)
    private final ZonedDateTime endAtUtc;

    @JsonProperty("st")
    @JsonSerialize(using = DurationToTimeSerializer.class)
    @JsonDeserialize(using = DurationFromTimeDeserializer.class)
    private final Duration dailyStartTime;

    @JsonProperty("d")
    @JsonSerialize(using = DurationToTimeSerializer.class)
    @JsonDeserialize(using = DurationFromTimeDeserializer.class)
    private final Duration scheduleDuration;

    @JsonProperty("da")
    private final ScheduleDays daysOfWeekActive;

    @JsonProperty("a")
    private final String actionName;

    @JsonProperty("ap")
    private final long actionParameter;

    /**
     * Creates a new instance of ScheduleInfoDto.
     */
    @JsonCreator
    public ScheduleInfoDto(
            @JsonProperty("p") Integer priority,
            @JsonProperty("s") ZonedDateTime startAtUtc,
            @JsonProperty("e") ZonedDateTime endAtUtc,
            @JsonProperty("st") Duration dailyStartTime,
            @JsonProperty("d") Duration scheduleDuration,
            @JsonProperty("da") ScheduleDays daysOfWeekActive,
            @JsonProperty("a") String actionName,
            @JsonProperty("ap") int actionParameter) {
        this.priority = priority;
        this.startAtUtc = startAtUtc;
        this.endAtUtc = endAtUtc;
        this.dailyStartTime = dailyStartTime;
        this.scheduleDuration = scheduleDuration;
        this.daysOfWeekActive = daysOfWeekActive;
        this.actionName = actionName;
        this.actionParameter = actionParameter;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScheduleInfoDto that = (ScheduleInfoDto) o;
        return actionParameter == that.actionParameter &&
                Objects.equals(priority, that.priority) &&
                Objects.equals(startAtUtc, that.startAtUtc) &&
                Objects.equals(endAtUtc, that.endAtUtc) &&
                Objects.equals(dailyStartTime, that.dailyStartTime) &&
                Objects.equals(scheduleDuration, that.scheduleDuration) &&
                daysOfWeekActive == that.daysOfWeekActive &&
                Objects.equals(actionName, that.actionName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                priority,
                startAtUtc,
                endAtUtc,
                dailyStartTime,
                scheduleDuration,
                daysOfWeekActive,
                actionName,
                actionParameter);
    }
}
