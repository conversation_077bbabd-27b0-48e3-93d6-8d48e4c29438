package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandH2InverterRunningData1 extends DataBackedBand {
    public BandH2InverterRunningData1() {
        super(BandForge.<BandH2InverterRunningData1>getMetadataFor(BandH2InverterRunningData1.class));
    }


    public BandH2InverterRunningData1(byte[] bytes) {
        super(bytes, BandForge.<BandH2InverterRunningData1>getMetadataFor(BandH2InverterRunningData1.class));
    }

    public BandH2InverterRunningData1(String encodedBytes) {
        super(encodedBytes, BandForge.<BandH2InverterRunningData1>getMetadataFor(BandH2InverterRunningData1.class));
    }


    //public DateTime InverterTime //spec not ready
    //{
    //    get => GetU16(0);
    //}


    public final Object getInverterMode() {
        return InverterWorkModeH2.parse(GetU16(8));
    }


    public final Object getDisplayFaultMsg() {
        return H2DisplayFaultMsg.parse(GetU32(10));
    }


    public final Object getMasterCtrlFaultMsg1() {
        return H2MasterCtrlFaultMsg1.parse(GetU32(14));
    }


    public final Object getMasterCtrlFaultMsg2() {
        return H2MasterCtrlFaultMsg2.parse(GetU32(18));
    }


    public final Object getBMSFaultMsg() {
        return H2BMSFaultMsg.parse(GetU32(22));
    }


    public final int getReserved0x400D() {
        return GetU32(26);
    }


    public final int getInverterErrorCount() {
        return GetU16(30);
    }


    public final Celsius getInverterHeatSinkTemp() {
        return GetS16(32, Celsius.Deci);
    }


    public final Celsius getInverterAmbientTemp() {
        return GetS16(34, Celsius.Deci);
    }


    public final Ampere getInverterGFCI() {
        return GetS16(36, Ampere.Unit);
    }


    public final long getReserved0x4013() {
        return GetU8(38);
    }


    public final int getDRMHardwareStatus() {
        return GetU16(46);
    }


    public final int getDRMSoftwareStatus() {
        return GetU16(48);
    }


    public final TimeSpan getConnTime() {
        return GetU16(50, TimeSpan.fromSeconds(1));
    }


    public final int getErrorDataSN() {
        return GetU16(52);
    }


    public final int getSettingDataSN() {
        return GetU16(54);
    }


    public final int getFCASTriggerFlag() {
        return GetU16(56);
    }


    public final int getFunctionOne() {
        return GetU16(58);
    }


    public final int getFunctionTwo() {
        return GetU16(60);
    }


    public final int getFunctionThree() {
        return GetU16(62);
    }


    public final int getFunctionFour() {
        return GetU16(64);
    }


    public final int getFunctionFive() {
        return GetU16(66);
    }
}
