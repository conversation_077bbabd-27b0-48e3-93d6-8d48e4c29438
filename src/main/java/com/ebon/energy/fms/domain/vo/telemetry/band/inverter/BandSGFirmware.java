package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;





public class BandSGFirmware extends DataBackedBand
{
	public BandSGFirmware()
	{
		super(BandForge.<BandSGFirmware>getMetadataFor(BandSGFirmware.class));
	}



	public BandSGFirmware(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGFirmware>getMetadataFor(BandSGFirmware.class));
	}

	public BandSGFirmware(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGFirmware>getMetadataFor(BandSGFirmware.class));
	}



	public final String getInverterFirmwareUpdateStatus()
	{
		return GetBufS(0, 50, StringProcessors.GoodweDecode);
	}


	public final String getEMSFirmwareUpdateStatus()
	{
		return GetBufS(0, 50, StringProcessors.GoodweDecode);
	}


	public final String getMeterFirmwareUpgradeStatus()
	{
		return GetBufS(0, 50, StringProcessors.GoodweDecode);
	}
}
