package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 硬件型号实体类
 */
@Data
@Accessors(chain = true)
@TableName("HardwareModel")
public class HardwareModelDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID (与HardwareFamilyId组成复合主键)
     */
    @TableId(value = "Id")
    private Integer id;

    /**
     * 硬件系列ID (外键关联HardwareFamily表)
     */
    @TableField("HardwareFamilyId")
    private Integer hardwareFamilyId;

    /**
     * 型号名称
     */
    @TableField("Name")
    private String name;

    /**
     * 显示名称
     */
    @TableField("DisplayName")
    private String displayName;

    /**
     * 是否并网型号
     */
    @TableField("IsGridTie")
    private Boolean isGridTie;
}