package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.Country;
import com.ebon.energy.fms.common.enums.GridWaveQualityLevelCheck;
import com.ebon.energy.fms.common.enums.UpsStdVoltType;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.*;




 





public class BandETE extends DataBackedBand
{
	public BandETE()
	{
		super(BandForge.<BandETE>getMetadataFor(BandETE.class));
	}



	public BandETE(byte[] bytes)
	{
		super(bytes, BandForge.<BandETE>getMetadataFor(BandETE.class));
	}

	public BandETE(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETE>getMetadataFor(BandETE.class));
	}


	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
	public final LocalDateTime getRTCTimeW()
	{
		return GetDateTime(0);
	}

	
	public final String getSerialNumberOfInverter()
	{
		return GetBufS(6, 16, StringProcessors.GoodweDecode);
	}

	
	public final String getModelNameofInverter()
	{
		return GetBufS(22, 10, StringProcessors.GoodweDecode);
	}

	
	public final boolean getResumeFactorySetting()
	{
		return GetBool((int) GetU16(32), 1, 0, false);
	}

	


	public final int getCleardata() { return GetU16(34); }

	


	public final int getStartAllowConnectToGrid() { return GetU16(36); }

	


	public final int getStopForbiddenConnectToGrid() { return GetU16(38); }

	


	public final int getReset() { return GetU16(40); }

	


	public final int getResetSPS() { return GetU16(42); }

	
	public final WattHour getPVETotal()
	{
		return GetU32(44, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getPVEDay()
	{
		return GetU32(48, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getINTETotalSell()
	{
		return GetU32(52, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final TimeSpan getHTotal()
	{
		return GetU32(56, TimeSpan.fromHours(1));
	}

	
	public final WattHour getEDaySell()
	{
		return GetU16(60, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getINTETotalBuy()
	{
		return GetU32(62, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getEDayBuy()
	{
		return GetU16(66, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getETotalLoad()
	{
		return GetU32(68, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getELoadDay()
	{
		return GetU16(72, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getEBatteryCharge()
	{
		return GetU32(74, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getEChargeDay()
	{
		return GetU16(78, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getEBatteryDischarge()
	{
		return GetU32(80, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	
	public final WattHour getEdischargeDay()
	{
		return GetU16(84, WattHour.operatorMultiply(new BigDecimal("0.1"), WattHour.Kilo));
	}

	


	public final int getLanguage() { return GetU16(86); }

	
	public final Object getSafetyCountry()
	{
		return Country.parse((byte) GetU16(88));
	}

	
	public final Ohm getIsoLimit()
	{
		return GetU16(90, Ohm.Kilo);
	}

	
	public final boolean getLVRT()
	{
		return GetBool((int) GetU16(92), 1, 2, false);
	}

	
	public final boolean getIslanding()
	{
		return GetBool((int) GetU16(94), 1, 0, false);
	}

	
	public final TimeSpan getBurnInResetTime()
	{
		return GetU16(98, TimeSpan.fromMinutes(1));
	}

	
	public final Volt getLowestFeedingVoltageofPV()
	{
		return GetU16(100, Volt.Deci);
	}

	
	public final boolean getEnableMPPT4Shadow()
	{
		return GetBool((int) GetU16(102), 1, 0, false);
	}

	
	public final boolean getBackUpEnable()
	{
		return GetBool((int) GetU16(104), 1, 0, true);
	}

	
	public final boolean getAutoStartBackup()
	{
		return GetBool((int) GetU16(106), 1, 0, true);
	}

	
	public final Object getGridWaveCheckLevel()
	{
		return GridWaveQualityLevelCheck.parse((byte) GetU16(108));
	}

	
	public final boolean getRapidCutOff()
	{
		return GetBool((int) GetU16(110), 1, 0, true);
	}

	
	public final TimeSpan getBackupStartDly()
	{
		return GetU16(112, TimeSpan.fromMinutes(1));
	}

	
	public final Object getUpsStdVoltType()
	{
		return UpsStdVoltType.parse(GetU16(114));
	}


	public final int getReserved0xB0CA() { return GetU16(116); }

	
	public final boolean getBurnInMode()
	{
		return GetBool((int) GetU16(118), 1, 0, false);
	}

	
	public final TimeSpan getBackupoverloaddelay()
	{
		return GetU16(120, TimeSpan.fromSeconds(1));
	}

	
	public final boolean getUpsPhaseType()
	{
		return GetBool((int) GetU16(122), 0, 1, true);
	}

	


	public final int getDerateRateVDE() { return GetU16(126); }

	
	public final boolean getThreePhaseUnbalancedoutput()
	{
		return GetBool((int) GetU16(128), 1, 0, false);
	}

	
	public final boolean getPreRelayCheckEnable()
	{
		return GetBool((int) GetU16(130), 1, 0, true);
	}

	
	public final boolean getHighImpMode()
	{
		return GetBool((int) GetU16(132), 1, 0, false);
	}
}
