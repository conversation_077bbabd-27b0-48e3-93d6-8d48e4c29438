package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.domain.vo.Ampere;
import com.ebon.energy.fms.domain.vo.Volt;
import com.ebon.energy.fms.domain.vo.setting.provider.SettingPaths;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * Constants for HVH3P settings paths
 */
public class HVH3PSettingPaths extends SettingPaths {
    public static final String InverterModeSettingName = "InverterMode";
    public static final String InverterModePowerSettingName = "InverterModePower";


    static final String SCHEDULE_SETTINGS_NAME = ScheduleSettingsV2Desired.SCHEDULE_SETTINGS_NAME;
    static final String SCHEDULE_VERSION_SETTINGS_NAME = ScheduleSettingsV2Desired.VERSION_SETTINGS_NAME;

    public HVH3PSettingPaths() {
        super(
                null, // enableSiteExportLimitSetting
                null, // siteExportLimitSetting
                "v2.ems.GridProfileId", // gridProfileIdSetting
                "v2.ems.GridProfileCorrelationId", // gridProfileCorrelationIdSetting
                null, // batteryManufacturerSetting
                null, // batteryCountSetting
                null, // batteryMaxChargeCurrentSetting
                null, // batteryMaxDischargeCurrentSetting
                null, // batteryMinSocSetting
                null, // batteryMinOffgridSocSetting
                null, // thirdPartyExportCt
                "v2.ems.Relay{0}Name", // relayNameSettingTemplate
                "v2.ems.Relay{0}Installed", // relayInstalledSettingTemplate
                "v2.Inverter.Relay{0}Active", // relayActiveSettingTemplate
                "v2.site" // siteSetting
        );
    }

    static List<String> getBandUpdateRates() {
        return Arrays.asList("v2.ems.TelemetryRate");
    }

    static String getDisableAgeingModePath() {
        return "v2.ems.DisableAgeingMode";
    }

    static V2Setting getPowerFactorMinus1To1() {
        return new V2Setting(V2Section.Inverter, "FixedPowerFactor");
    }

    static V2Setting getDredSubscribed() {
        return new V2Setting(V2Section.Inverter, "StandardModeReceoverConfig");
    }

    static String getCtFlipSettingPath() {
        return "v2.meters.grid.CurrentDirection";
    }

    static V2Setting getExportLimitType() {
        return new V2Setting(V2Section.Constraints, "ExportLimitControlEn");
    }

    static V2Setting getExportLimitSoft() {
        return new V2Setting(V2Section.Constraints, "ExportLimitControlSoftLim");
    }

    static V2Setting getExportLimitSoftMax() {
        return new V2Setting(V2Section.Constraints, "ExportLimitControlSoftLimMax");
    }

    static V2Setting getExportLimitHardMax() {
        return new V2Setting(V2Section.Constraints, "ExportLimitControlHardLimMax");
    }

    static V2Setting getGenLimitType() {
        return new V2Setting(V2Section.Constraints, "GenLimitControlEn");
    }

    static V2Setting getGenLimitSoft() {
        return new V2Setting(V2Section.Constraints, "GenLimitControlSoftLim");
    }

    static V2Setting getGenLimitSoftMax() {
        return new V2Setting(V2Section.Constraints, "GenLimitControlSoftLimMax");
    }

    static V2Setting getGenLimitHardMax() {
        return new V2Setting(V2Section.Constraints, "GenLimitControlHardLimMax");
    }

    public static V2Setting getBatteryProtocol() {
        return new V2Setting(V2Section.BatteryManager, "Protocol");
    }

    public static V2Setting getBatteryArchitecture() {
        return new V2Setting(V2Section.BatteryManager, "BatteryArchitecture");
    }

    public static V2Setting getBatteryCount() {
        return new V2Setting(V2Section.BatteryManager, "BatteryCount");
    }

    // 8.10.2   Battery Limit Overrides
    public static V2Setting getBatteryMaxChargeCurrent() {
        return new V2Setting(V2Section.BatteryManager, "MaxChargeCurrentLimitOverride");
    }

    // 8.10.2 Battery Limit Overrides
    static V2Setting getBatteryMaxChargeVoltage() {
        return new V2Setting(V2Section.BatteryManager, "MaxChargeVoltageLimitOverride");
    }

    // 8.10.3 Battery Power Dynamic Limits
    static V2Setting getBatteryMaxChargePower() {
        return new V2Setting(V2Section.BatteryManager, "MaxChargePower");
    }

    // 8.10.2 Battery Limit Overrides
    public static V2Setting getBatteryMaxDischargeCurrent() {
        return new V2Setting(V2Section.BatteryManager, "MaxDischargeCurrentLimitOverride");
    }

    // 8.10.3 Battery Power Dynamic Limits
    static V2Setting getBatteryMaxDischargePower() {
        return new V2Setting(V2Section.BatteryManager, "MaxDischargePower");
    }

    public static V2Setting getBatteryMinSoc0to1() {
        return new V2Setting(V2Section.BatteryManager, "MinSOC");
    }

    public static V2Setting getBatteryMinOffgridSoc0to1() {
        return new V2Setting(V2Section.BatteryManager, "MinSOCOffgrid");
    }

    public static V2Setting getBatteryModuleRatedMaxChargeCurrent() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMaxChargeCurrent");
    }

    public static V2Setting getBatteryModuleRatedMaxDischargeCurrent() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMaxDischargeCurrent");
    }

    public static V2Setting getBatteryModuleRatedMaxChargeVoltage() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMaxChargeVoltage");
    }

    public static V2Setting getBatteryModuleRatedMinDischargeVoltage() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMinDischargeVoltage");
    }

    public static V2Setting getBatteryModuleRatedMinDischargeVoltageOffgrid() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMinDischargeVoltageOffgrid");
    }

    public static V2Setting getBatteryModuleRatedMinSOC() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMinSOC");
    }

    public static V2Setting getBatteryModuleRatedMinSOCOffgrid() {
        return new V2Setting(V2Section.BatteryManager, "ModuleRatedMinSOCOffgrid");
    }

    public static V2Setting getBatteryInverterRatedMaxChargeCurrent() {
        return new V2Setting(V2Section.BatteryManager, "InverterRatedMaxChargeCurrent");
    }

    public static V2Setting getBatteryInverterRatedMaxDischargeCurrent() {
        return new V2Setting(V2Section.BatteryManager, "InverterRatedMaxDischargeCurrent");
    }

    static V2Setting getBatteryWakeTriggerVoltage() {
        return new V2Setting(V2Section.BatteryManager, "BatteryWakeTriggerVoltage");
    }

    public static Ampere getBatteryModuleRatedMaxChargeCurrentDefault() {
        return new Ampere(80);
    }

    public static Ampere getBatteryModuleRatedMaxDischargeCurrentDefault() {
        return new Ampere(80);
    }

    public static Volt getBatteryModuleRatedMaxChargeVoltageDefault() {
        return new Volt(new BigDecimal("53.2"));
    }

    public static Volt getBatteryModuleRatedMinDischargeVoltageDefault() {
        return new Volt(46);
    }

    public static Volt getBatteryModuleRatedMinDischargeVoltageOffgridDefault() {
        return new Volt(46);
    }

    public static BigDecimal getBatteryModuleRatedMinSOCDefault() {
        return new BigDecimal("0.1");
    }

    public static BigDecimal getBatteryModuleRatedMinSOCOffgridDefault() {
        return new BigDecimal("0.05");
    }

    public static Ampere getBatteryInverterRatedMaxChargeCurrentDefault() {
        return new Ampere(120);
    }

    public static Ampere getBatteryInverterRatedMaxDischargeCurrentDefault() {
        return new Ampere(120);
    }

    /**
     * 系统能够正常工作而不在遥测中产生伪影的最低已知遥测速率。
     */
    static int getTelemetryRateMinSupported() {
        return 5;
    }

    static V2Setting getACCoupledOnOff() {
        return new V2Setting(V2Section.Site, "measuringThirdPartyInverter");
    }

    static V2Setting getACCoupledCtNumber() {
        return new V2Setting(V2Section.Site, "measuringThirdPartySource");
    }

    static V2Setting getShadowScan() {
        return new V2Setting(V2Section.Inverter, "SpecialFunctionSwitch");
    }

    static V2Setting getSerialNumberActual() {
        return new V2Setting(V2Section.Inverter, "SerialNumberActual");
    }

    static final String TELEMETRY_PERIOD_END_UTC = "v2.ems.TelemetryPeriodEndEpochS";

    static String getGridReferenceCTSettingPath() {
        return "v2.meters.grid.GridReferenceCT";
    }

    static final String POWER_RAMP_GENERATE_KEY = "PowerRampGenerate_WgraPos";

    static String getPowerRampGeneratePath() {
        return "v2.Inverter." + POWER_RAMP_GENERATE_KEY;
    }

    static final String RELAY_KEY = "v2.relaySettings";

    static String getSmartRelayName() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.LOAD_CONTROL_NAME;
    }

    static String getSmartRelayInstalled() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.LOAD_CONTROL_INSTALLED_NAME;
    }

    static String getSmartRelayLoadType() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_TYPE_NAME;
    }

    static String getSmartRelayEnabled() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_ENABLED_NAME;
    }

    static String getSmartRelaySource() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_SOURCE_NAME;
    }

    static String getSmartRelayOnTrigger() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_ON_TRIGGER_NAME;
    }

    static String getSmartRelayOnDelay() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_ON_DELAY_NAME;
    }

    static String getSmartRelayOffTrigger() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME;
    }

    static String getSmartRelayOffDelay() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_OFF_DELAY_NAME;
    }

    static String getSmartRelayMinRunTime() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME;
    }

    static String getSmartRelayMinOffTime() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME;
    }

    static String getSmartRelayRunTimeTarget() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME;
    }

    static String getSmartRelayRunTimeCompletionTime() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME;
    }

    static String getSmartRelayRunTimeUseExcessPower() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME;
    }

    static String getSmartRelayActiveHigh() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME;
    }

    static String getSmartRelayControlMode() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.LOAD_CONTROL_MODE_NAME;
    }

    static String getSmartRelayTimeZone() {
        return RELAY_KEY + "." + SmartRelaySettingsV2Desired.LOAD_CONTROL_TIME_ZONE_NAME;
    }

    static String getTimeZoneAlias() {
        return "v2.TzAlias";
    }

    static String getInverterModeSettingName() {
        return "WorkMode";
    }

    static String getInverterModePowerSettingName() {
        return "WorkModePowerW";
    }

    static String getInverterAllPowerCurveDisableName() {
        return "AllPowerCurveDisable";
    }

    // 以下属性标记为过时，GEN3版本不再使用SettingsPaths范式
    @Deprecated
    public String getEnableSiteExportLimitSetting() {
        return super.getEnableSiteExportLimitSetting();
    }

    @Deprecated
    public String getSiteExportLimitSetting() {
        return super.getSiteExportLimitSetting();
    }

    @Deprecated
    public String getGridProfileIdSetting() {
        return super.getGridProfileIdSetting();
    }

    @Deprecated
    public String getGridProfileCorrelationIdSetting() {
        return super.getGridProfileCorrelationIdSetting();
    }

    @Deprecated
    public String getBatteryManufacturerSetting() {
        return super.getBatteryManufacturerSetting();
    }

    @Deprecated
    public String getBatteryCountSetting() {
        return super.getBatteryCountSetting();
    }

    @Deprecated
    public String getBatteryMaxChargeCurrentSetting() {
        return super.getBatteryMaxChargeCurrentSetting();
    }

    @Deprecated
    public String getBatteryMaxDischargeCurrentSetting() {
        return super.getBatteryMaxDischargeCurrentSetting();
    }

    @Deprecated
    public String getBatteryMinSocSetting() {
        return super.getBatteryMinSocSetting();
    }

    @Deprecated
    public String getBatteryMinOffgridSocSetting() {
        return super.getBatteryMinOffgridSocSetting();
    }

    @Deprecated
    public String getThirdPartyExportCt() {
        return super.getThirdPartyExportCt();
    }

    @Deprecated
    public String getSiteSetting() {
        return super.getSiteSetting();
    }

    /**
     * 根据制造商获取电池配置信息
     */
    public static class BatteryConfig {
        private final String protocol;
        private final String batteryArchitecture;

        public BatteryConfig(String protocol, String batteryArchitecture) {
            this.protocol = protocol;
            this.batteryArchitecture = batteryArchitecture;
        }

        public String getProtocol() {
            return protocol;
        }

        public String getBatteryArchitecture() {
            return batteryArchitecture;
        }
    }

}
