package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESInverterAnalytics extends DataBackedBand
{
	public BandESInverterAnalytics()
	{
		super(BandForge.<BandESInverterAnalytics>getMetadataFor(BandESInverterAnalytics.class));
	}



	public BandESInverterAnalytics(byte[] bytes)
	{
		super(bytes, BandForge.<BandESInverterAnalytics>getMetadataFor(BandESInverterAnalytics.class));
	}

	public BandESInverterAnalytics(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESInverterAnalytics>getMetadataFor(BandESInverterAnalytics.class));
	}


	
	public final Frequency getMaxGridFreqWithin1Min()
	{
		return GetU16(0, Frequency.Centi);
	}

	
	public final Frequency getMinGridFreqWithin1Min()
	{
		return GetU16(2, Frequency.Centi);
	}

	
	public final Volt getMaxGridVoltageWithin1MinL1()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Volt getMinGridVoltageWithin1MinL1()
	{
		return GetU16(6, Volt.Deci);
	}

	
	public final Volt getMaxGridVoltageWithin1MinL2()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Volt getMinGridVoltageWithin1MinL2()
	{
		return GetU16(10, Volt.Deci);
	}

	
	public final Volt getMaxGridVoltageWithin1MinL3()
	{
		return GetU16(12, Volt.Deci);
	}

	
	public final Volt getMinGridVoltageWithin1MinL3()
	{
		return GetU16(14, Volt.Deci);
	}

	
	public final Watt getMaxBackupPowerWithin1MinL1()
	{
		return GetU32(16, Watt.Unit);
	}

	
	public final Watt getMaxBackupPowerWithin1MinL2()
	{
		return GetU32(20, Watt.Unit);
	}

	
	public final Watt getMaxBackupPowerWithin1MinL3()
	{
		return GetU32(24, Watt.Unit);
	}

	
	public final Watt getMaxBackupPowerWithin1MinTotal()
	{
		return GetU32(28, Watt.Unit);
	}

	


	public final int getGridHvrtEventTimes() { return GetU16(32); }

	


	public final int getGridLvrtEventTimes() { return GetU16(34); }

	


	public final int getInvErrorMsgRecordForEms()
	{


		return (int)GetU32(36);
	}

	


	public final int getInvWarningCodeRecordForEms()
	{


		return (int)GetU32(40);
	}

	


	public final int getInvCpldWarningRecordForEms()
	{


		return (int)GetU32(44);
	}
}
