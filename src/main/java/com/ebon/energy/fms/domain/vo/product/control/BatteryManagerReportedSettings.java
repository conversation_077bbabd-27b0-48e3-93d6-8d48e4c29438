package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.LVControlMethod;
import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatteryManagerReportedSettings {

    @JsonProperty("Manufacturer")
    private ManufacturerEnum manufacturer;

    @JsonProperty("BatteryCount")
    private Integer batteryCount;

    @JsonProperty("DelayMs")
    private Integer delayMs;

    @JsonProperty("DisallowAutomaticSoftwareStartup")
    private Boolean disallowAutomaticSoftwareStartup;

    @JsonProperty("DisallowAutomaticHardwareStartup")
    private Boolean disallowAutomaticHardwareStartup;

    @JsonProperty("DisallowAutomaticSoftwareShutdown")
    private Boolean disallowAutomaticSoftwareShutdown;

    @JsonProperty("DisallowAutomaticInternalProtectionRecovery")
    private Boolean disallowAutomaticInternalProtectionRecovery;

    @JsonProperty("CommsFailureDisconnectTimeS")
    private Integer commsFailureDisconnectTimeS;

    @JsonProperty("Firmware")
    private BatteryFirmwareReportedSettings firmware;

    @JsonProperty("DisableAutoChargeBelowMinSoC")
    private Boolean disableAutoChargeBelowMinSoC;

    @JsonProperty("AutoChargeBelowMinSoCThresholdPercent")
    private Integer autoChargeBelowMinSoCThresholdPercent;

    @JsonProperty("AutoChargeBelowMinSoCPowerW")
    private Watt autoChargeBelowMinSoCPowerW;

    // 只报告互锁的启用状态（不报告每个互锁的启用或远程互锁）
    @JsonProperty("InterlocksEnabled")
    private Boolean interlocksEnabled;

    @JsonProperty("AutoForceChargeFromBMSRequest")
    private Boolean autoForceChargeFromBMSRequest;

    @JsonProperty("AutoBalanceChargeFromBMSRequest")
    private Boolean autoBalanceChargeFromBMSRequest;

    @JsonProperty("AutoChargePowerW")
    private Watt autoChargePowerW;

    @JsonProperty("LvControlMethod")
    private LVControlMethod lvControlMethod;
}
