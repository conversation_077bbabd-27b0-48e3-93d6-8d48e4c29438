package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.Volt;
import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterAggregatesTelemetry {

    public Watt ACLoadDayMaxP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant ACLoadDayMaxPTimeStampUtc;
    public Watt BackupLoadDayMaxP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant BackupLoadDayMaxPTimeStampUtc;
    public Watt GridDayMaxInputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant GridDayMaxInputPTimestampUtc;
    public Watt GridDayMaxOuputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant GridDayMaxOuputPTimestampUtc;
    public Watt BatteryMeasurementsDayMaxInputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant BatteryMeasurementsDayMaxInputPTimestampUtc;
    public Watt BatteryMeasurementsDayMaxOutputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant BatteryMeasurementsDayMaxOutputPTimestampUtc;
    public Volt BatteryMeasurementsHighestMinCellVoltageWhileIdle;

    public InverterAggregatesTelemetry(
            Watt aCLoadDayMaxP,
            Instant aCLoadDayMaxPTimeStampUtc,
            Watt backupLoadDayMaxP,
            Instant backupLoadDayMaxPTimeStampUtc,
            Watt gridDayMaxInputP,
            Instant gridDayMaxInputPTimestampUtc,
            Watt gridDayMaxOuputP,
            Instant gridDayMaxOuputPTimestampUtc,
            Watt batteryMeasurementsDayMaxInputP,
            Instant batteryMeasurementsDayMaxInputPTimestampUtc,
            Watt batteryMeasurementsDayMaxOutputP,
            Instant batteryMeasurementsDayMaxOutputPTimestampUtc,
            Volt batteryMeasurementsHighestMinCellVoltageWhileIdle) {

        this.ACLoadDayMaxP = aCLoadDayMaxP;
        this.ACLoadDayMaxPTimeStampUtc = aCLoadDayMaxPTimeStampUtc;
        this.BackupLoadDayMaxP = backupLoadDayMaxP;
        this.BackupLoadDayMaxPTimeStampUtc = backupLoadDayMaxPTimeStampUtc;
        this.GridDayMaxInputP = gridDayMaxInputP;
        this.GridDayMaxInputPTimestampUtc = gridDayMaxInputPTimestampUtc;
        this.GridDayMaxOuputP = gridDayMaxOuputP;
        this.GridDayMaxOuputPTimestampUtc = gridDayMaxOuputPTimestampUtc;
        this.BatteryMeasurementsDayMaxInputP = batteryMeasurementsDayMaxInputP;
        this.BatteryMeasurementsDayMaxInputPTimestampUtc = batteryMeasurementsDayMaxInputPTimestampUtc;
        this.BatteryMeasurementsDayMaxOutputP = batteryMeasurementsDayMaxOutputP;
        this.BatteryMeasurementsDayMaxOutputPTimestampUtc = batteryMeasurementsDayMaxOutputPTimestampUtc;
        this.BatteryMeasurementsHighestMinCellVoltageWhileIdle = batteryMeasurementsHighestMinCellVoltageWhileIdle;
    }
}