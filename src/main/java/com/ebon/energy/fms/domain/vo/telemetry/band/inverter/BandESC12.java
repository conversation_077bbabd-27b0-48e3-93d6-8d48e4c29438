package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandESC12 extends DataBackedBand implements IBandESC
{
	public BandESC12()
	{
		super(BandForge.<BandESC12>getMetadataFor(BandESC12.class));
	}



	public BandESC12(byte[] bytes)
	{
		super(bytes, BandForge.<BandESC12>getMetadataFor(BandESC12.class));
	}

	public BandESC12(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESC12>getMetadataFor(BandESC12.class));
	}


	


	public final int getChargerTimeStart() { return GetU16(0); }

	


	public final int getChargerTimeEnd() { return GetU16(2); }

	
	public final BigDecimal getBatChargePowerMax()
	{
		return new BigDecimal(GetU16(4)).multiply(Percentage._1);
	}

	


	public final int getDisChargerTimeStart() { return GetU16(6); }

	


	public final int getDisChargerTimeEnd() { return GetU16(8); }

	
	public final BigDecimal getBatDisPowerSet()
	{
		return new BigDecimal(GetU16(10)).multiply(Percentage._1);
	}

	
	public final boolean getBackUpEnable()
	{
		return GetBool((int) GetU16(12), 1, 0, true);
	}

	
	public final boolean getOffGridAutoCharge()
	{
		return GetBool((int) GetU16(14), 1, 0, true);
	}

	
	public final boolean getEnableMPPT4Shadow()
	{
		return GetBool((int) GetU16(16), 1, 0, true);
	}

	
	public final boolean getFeedPowerEnable()
	{
		return GetBool((int) GetU16(18), 1, 0, true);
	}

	


	public final int getManufacturerCode() { return GetU16(20); }

	
	public final Capacity getLeadBatCapacity()
	{
		return GetU16(22, Capacity.Unit);
	}

	
	public final Volt getBattChargeVoltMax()
	{
		return GetU16(24, Volt.Deci);
	}

	
	public final Ampere getBattChargeCurrMax()
	{
		return GetS16(26, Ampere.Deci);
	}

	
	public final Ampere getBattDisChargeCurrMax()
	{
		return GetS16(28, Ampere.Deci);
	}

	
	public final Volt getBattVoltUnderMin()
	{
		return GetU16(30, Volt.Deci);
	}

	
	public final BigDecimal getBattSOCUnderMin()
	{
		return new BigDecimal(GetU16(32)).multiply(Percentage._1);
	}

	
	public final TimeSpan getBatActivePeriod()
	{
		return GetU16(34, TimeSpan.fromMinutes(1));
	}

	
	public final short getRPControlPara()
	{
		return GetS16(36);
	}

	
	public final Volt getBattFloatVolt()
	{
		return GetU16(38, Volt.Deci);
	}

	
	public final Ampere getBattFloatCurr()
	{
		return GetS16(40, Ampere.Deci);
	}

	
	public final TimeSpan getBattToFloatTime()
	{
		return GetU16(42, TimeSpan.fromMinutes(1));
	}

	
	public final Object getBattTypeIndex()
	{
		return FloatBattType.parse(GetU16(44));
	}

	
	public final Watt getFeedPowerPara()
	{
		return GetS16(46, Watt.Unit);
	}

	
	public final boolean getAutoStartBackup()
	{
		return GetBool((int) GetU16(48), 1, 0, true);
	}

	
	public final boolean getStopSocProtect()
	{
		return GetBool((int) GetU16(50), 1, 0, true);
	}

	
	public final boolean getDCVoltOutput()
	{
		return GetBool((int) GetU16(52), 1, 0, true);
	}

	
	public final Volt getBattAvgChagVolt()
	{
		return GetU16(54, Volt.Deci);
	}

	
	public final TimeSpan getBattAvgChgHours()
	{
		return GetU16(56, TimeSpan.fromHours(1));
	}

	
	public final Object getAS4777_2Parameters()
	{
		return AS477Parameters.parse(GetU16(58));
	}

	
	public final Object getWgPowerMode()
	{
		return InverterESPowerMode.parse((byte) GetU16(60));
	}

	
	public final Watt getWgPowerSet()
	{
		return GetU16(62, Watt.Unit);
	}

	


	public final int getTBDx0570() { return GetU16(64); }

	
	public final boolean getNoGridChargeEnable()
	{
		return GetBool((int) GetU16(66), 1, 2, true);
	}

	
	public final boolean getDisChgWithPVEnable()
	{
		return GetBool((int) GetU16(68), 1, 2, true);
	}

	


	public final int getTBDx0573() { return GetU16(70); }

	


	public final int getAppModeIndex() { return GetU16(72); }

	
	public final Object getGridWaveCheckLevel()
	{
		return GridWaveQualityLevelCheck.parse((byte) GetU16(74));
	}

	
	public final Watt getMeterCheckValue()
	{
		return GetU16(76, Watt.Unit);
	}

	
	public final boolean getRapidCutOff()
	{
		return GetBool((int) GetU16(78), 1, 0, true);
	}

	
	public final Volt getGridVoltQuality()
	{
		return GetU16(80, Volt.Deci);
	}

	
	public final Volt getGridVoltHighS2()
	{
		return GetU16(82, Volt.Deci);
	}

	
	public final Volt getGridVoltLowS2()
	{
		return GetU16(84, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltHighS2Time()
	{
		return GetU16(86, TimeSpan.fromSeconds(0.02));
	}

	
	public final TimeSpan getGridVoltLowS2Time()
	{
		return GetU16(88, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltHighS1()
	{
		return GetU16(90, Volt.Deci);
	}

	
	public final Volt getGridVoltLowS1()
	{
		return GetU16(92, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltHighS1Time()
	{
		return GetU16(94, TimeSpan.fromSeconds(0.02));
	}

	
	public final TimeSpan getGridVoltLowS1Time()
	{
		return GetU16(96, TimeSpan.fromSeconds(0.02));
	}

	
	public final Frequency getGridFreqHighS2()
	{
		return GetU16(98, Frequency.Centi);
	}

	
	public final Frequency getGridFreqLowS2()
	{
		return GetU16(100, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqHighS2Time()
	{
		return GetU16(102, TimeSpan.fromSeconds(0.02));
	}

	
	public final TimeSpan getGridFreqLowS2Time()
	{
		return GetU16(104, TimeSpan.fromSeconds(0.02));
	}

	
	public final Frequency getGridFreqHighS1()
	{
		return GetU16(106, Frequency.Centi);
	}

	
	public final Frequency getGridFreqLowS1()
	{
		return GetU16(108, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqHighS1Time()
	{
		return GetU16(110, TimeSpan.fromSeconds(0.02));
	}

	
	public final TimeSpan getGridFreqLowS1Time()
	{
		return GetU16(112, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltRecoverHigh()
	{
		return GetU16(114, Volt.Deci);
	}

	
	public final Volt getGridVoltRecoverLow()
	{
		return GetU16(116, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltRecoverTime()
	{
		return GetU16(118, TimeSpan.fromSeconds(0.02));
	}

	
	public final Frequency getGridFreqRecoverHigh()
	{
		return GetU16(120, Frequency.Centi);
	}

	
	public final Frequency getGridFreqRecoverLow()
	{
		return GetU16(122, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqRecoverTime()
	{
		return GetU16(124, TimeSpan.fromSeconds(0.02));
	}

	
	public final BigDecimal getPointBValue()
	{
		return new BigDecimal(GetU16(126)).multiply(Percentage._1);
	}

	
	public final BigDecimal getPointCValue()
	{
		return new BigDecimal(GetU16(128)).multiply(Percentage._1);
	}

	
	public final Volt getGridLimitByVolStartVol()
	{
		return GetU16(130, Volt.Deci);
	}

	
	public final BigDecimal getGridLimitByVolStartPer()
	{
		return new BigDecimal(GetU16(132)).multiply(Percentage._1);
	}

	


	public final int getGridLimitByVolSlope() { return GetU16(134); }

	
	public final Volt getActiveCurveVolt()
	{
		return GetU16(136, Volt.Deci);
	}

	
	public final Volt getDesactiveCurveVolt()
	{
		return GetU16(138, Volt.Deci);
	}

	
	public final boolean getEnableCurve()
	{
		return GetBool((int) GetU16(140), 2, 1, false);
	}

	


	public final int getBackupStartDelayMinutes() { return GetU16(142); }

	


	public final int getRecoverTimeEE() { return GetU16(144); }

	
	public final Object getSafetyCountry()
	{
		return Country.parse((byte) GetU16(146));
	}

	
	public final Ohm getIsoLimit()
	{
		return GetU16(148, Ohm.operatorMultiply(new BigDecimal("10"),Ohm.Kilo));
	}

	
	public final BigDecimal getBatBMSCurrLmtCoff()
	{
		return new BigDecimal(GetU16(150)).multiply(Percentage._1);
	}

	
	public final boolean getMeterConnectCheckFlag()
	{
		return GetBool((int) GetU16(152), 1, 0, true);
	}

	
	public final Object getMeterConnectStatus()
	{
		return MeterConnectStatus.parse(GetU16(154));
	}

	
	public final Object getUpsStdVoltType()
	{
		return UpsStdVoltType.parse(GetU16(156));
	}

	
	public final Object getFunctionStatus()
	{
		return FunctionStatus.parse(GetU16(158));
	}

	
	public final Volt getBattOfflineVoltUnderMin()
	{
		return GetU16(160, Volt.Deci);
	}

	
	public final BigDecimal getBattOfflineSOCUnderMin0to1()
	{
		return new BigDecimal(GetU16(162)).multiply(Percentage._1);
	}

	
	public final boolean getOnlyNightDischarge()
	{
		return GetBool((int) GetU16(164), 2, 1, false);
	}

	
	public final Object getBMSProtocolCode()
	{
		return BMSProtocolCode.parse(GetU16(166));
	}

	


	public final int getHVBatString() { return GetU16(168); }

	


	public final int getOfflineMPPTScanEnable() { return GetU16(170); }

	


	public final int getPVExtendenable() { return GetU16(172); }

	


	public final int getPERelayCheckEn() { return GetU16(174); }

	


	public final int getBatteryModePVCharge() { return GetU16(176); }
}
