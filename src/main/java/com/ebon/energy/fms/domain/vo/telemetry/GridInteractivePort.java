package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class GridInteractivePort implements Cloneable {
    private Double V;
    private Double I;
    private Double P;
    private Double F;
    private Double S;
    private Double PowerFactor;
    private Boolean IsMeterInstalled;

    public GridInteractivePort() {
    }

    public GridInteractivePort(Double v, Double i, Double p, Double f, Double powerFactor, Boolean isMeterInstalled, Double s) {
        V = v;
        I = i;
        P = p;
        F = f;
        S = s;
        PowerFactor = powerFactor;
        IsMeterInstalled = isMeterInstalled;
    }

    public Double getV() {
        return V;
    }

    public void setV(Double v) {
        V = v;
    }

    public Double getI() {
        return I;
    }

    public void setI(Double i) {
        I = i;
    }

    public Double getP() {
        return P;
    }

    public void setP(Double p) {
        P = p;
    }

    public Double getF() {
        return F;
    }

    public void setF(Double f) {
        F = f;
    }

    public Double getS() {
        return S;
    }

    public void setS(Double s) {
        S = s;
    }

    public Double getPowerFactor() {
        return PowerFactor;
    }

    public void setPowerFactor(Double powerFactor) {
        PowerFactor = powerFactor;
    }

    public Boolean getIsMeterInstalled() {
        return IsMeterInstalled;
    }

    public void setIsMeterInstalled(Boolean isMeterInstalled) {
        IsMeterInstalled = isMeterInstalled;
    }

    @Override
    public GridInteractivePort clone() {
        try {
            return (GridInteractivePort) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}