package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import java.math.BigDecimal;


public class BandESGMeterEnergy extends DataBackedBand
{
	public BandESGMeterEnergy()
	{
		super(BandForge.<BandESGMeterEnergy>getMetadataFor(BandESGMeterEnergy.class));
	}



	public BandESGMeterEnergy(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGMeterEnergy>getMetadataFor(BandESGMeterEnergy.class));
	}

	public BandESGMeterEnergy(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGMeterEnergy>getMetadataFor(BandESGMeterEnergy.class));
	}


	
	public final WattHour getActiveETotalSellL1()
	{
		return GetU64(0, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellL2()
	{
		return GetU64(8, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellL3()
	{
		return GetU64(16, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellTotal()
	{
		return GetU64(24, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyL1()
	{
		return GetU64(32, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyL2()
	{
		return GetU64(40, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyL3()
	{
		return GetU64(48, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyTotal()
	{
		return GetU64(56, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}
}
