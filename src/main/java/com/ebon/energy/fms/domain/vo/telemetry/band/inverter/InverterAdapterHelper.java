package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class InverterAdapterHelper {
	private static final Pattern REGEX_COMBINED =
			Pattern.compile("^(?<dspmaster>\\d{2})(?<dspslave>\\d{2})(?<arm>\\d{1,2})$", Pattern.CASE_INSENSITIVE);

	public static class VersionPair {
		public final String arm;
		public final String dsp;

		public VersionPair(String arm, String dsp) {
			this.arm = arm != null ? arm : "";
			this.dsp = dsp != null ? dsp : "";
		}
	}

	public static VersionPair extractArmAndDsp(String combinedVersion) {
		combinedVersion = combinedVersion != null ? combinedVersion : "";

		if (combinedVersion.length() != 9) {
			Matcher matcher = REGEX_COMBINED.matcher(combinedVersion);
			String extractedArm = null;
			String extractedDsp = null;

			if (matcher.matches()) {
				extractedArm = matcher.group("arm");
				extractedDsp = matcher.group("dspmaster");
				if (extractedDsp == null) {
					extractedDsp = matcher.group("dspslave");
				}
			}

			// Remove zero padding
			if (extractedArm != null) {
				extractedArm = extractedArm.replaceFirst("^0+", "");
			}
			if (extractedDsp != null) {
				extractedDsp = extractedDsp.replaceFirst("^0+", "");
			}

			return new VersionPair(extractedArm, extractedDsp);
		} else {
			return new VersionPair(
					combinedVersion.substring(4, 9).replaceFirst("^0+", ""),
					combinedVersion.substring(0, 4).replaceFirst("^0+", "")
			);
		}
	}

	public static String getDspVersionFromInternal(String s) {
		return extractVersionFromInternal(s);
	}

	public static String getArmVersionFromInternal(String s) {
		return extractVersionFromInternal(s);
	}

	private static String extractVersionFromInternal(String s) {
		if (s == null || s.isEmpty()) {
			return null;
		}

		String[] parts = s.split("-");
		if (parts.length < 2) {
			return null;
		}

		return parts[1];
	}
}