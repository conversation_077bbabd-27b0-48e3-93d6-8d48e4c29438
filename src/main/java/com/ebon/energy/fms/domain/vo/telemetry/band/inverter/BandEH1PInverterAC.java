package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.FunSwitchState;
import com.ebon.energy.fms.common.enums.InverterstState;
import com.ebon.energy.fms.common.enums.NationalStandards;
import com.ebon.energy.fms.common.enums.StandardOperatingMode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandEH1PInverterAC extends DataBackedBand
{
	public BandEH1PInverterAC()
	{
		super(BandForge.<BandEH1PInverterAC>getMetadataFor(BandEH1PInverterAC.class));
	}



	public BandEH1PInverterAC(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterAC>getMetadataFor(BandEH1PInverterAC.class));
	}

	public BandEH1PInverterAC(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterAC>getMetadataFor(BandEH1PInverterAC.class));
	}


	


	public final int getAlarmInfo() { return GetU16(0); }

	
	public final Volt getDCBusVoltage()
	{
		return GetU16(2, Volt.Deci);
	}

	
	public final Volt getDCBusHalfVoltage()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Volt getInverterVGridL1()
	{
		return GetU16(6, Volt.Deci);
	}

	
	public final Volt getInverterVGridL2()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Volt getInverterVGridL3()
	{
		return GetU16(10, Volt.Deci);
	}

	
	public final Ampere getInverterIGridL1()
	{
		return GetU16(12, Ampere.Deci);
	}

	
	public final Ampere getInverterIGridL2()
	{
		return GetU16(14, Ampere.Deci);
	}

	
	public final Ampere getInverterIGridL3()
	{
		return GetU16(16, Ampere.Deci);
	}

	
	public final Watt getInverterActivePower()
	{
		return GetS32(18, Watt.Unit);
	}

	
	public final VoltAmpsReactive getInverterReactivePower()
	{
		return GetS32(22, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmps getInverterApparentPower()
	{
		return GetS32(26, VoltAmps.Unit);
	}

	


	public final int getVariableData1() { return GetU16(30); }

	


	public final int getVariableData2() { return GetU16(32); }

	


	public final int getVariableData11() { return GetU16(34); }

	


	public final int getVariableData12() { return GetU16(36); }

	


	public final int getVariableData13() { return GetU16(38); }

	


	public final int getAFCIArcFaultNum() { return GetU16(40); }

	
	public final Object getGridResponseMode()
	{
		return StandardOperatingMode.parse(GetU16(42));
	}

	
	public final NationalStandards getSafetyCountry()
	{
		return NationalStandards.forValue(GetU16(44));
	}

	
	public final Celsius getTempIGBT()
	{
		return GetS16(46, Celsius.Deci);
	}

	
	public final Frequency getFgrid()
	{
		return GetU16(48, Frequency.Centi);
	}

	
	public final Object getAlarmCode()
	{
		return InverterstState.parse(GetU16(50));
	}

	
	public final Celsius getTempBattery()
	{
		return GetS16(52, Celsius.Deci);
	}

	
	public final Object getFunctionState()
	{
		return FunSwitchState.parse(GetU16(54));
	}


	public final int getDRMStatus() { return GetU16(56); }

	
	public final Celsius getTempInverterEnclosure()
	{
		return GetS16(58, Celsius.Deci);
	}

	


	public final int getReserved0x814C()
	{
		return GetU32(60);
	}

	


	public final int getReserved0x814E()
	{
		return GetU32(64);
	}

	
	public final BigDecimal getPowerLimitPct()
	{
		return new BigDecimal(GetU16(68)).multiply(Percentage._001);
	}

	
	public final BigDecimal getPowerFactor()
	{
		return new BigDecimal(GetS16(70)).multiply(Percentage._1);
	}

	
	public final BigDecimal getReactivePowerLimitPct()
	{
		return new BigDecimal(GetU16(72)).multiply(Percentage._001);
	}

	
	public final Celsius getTempIGBT2()
	{
		return GetU16(74, Celsius.Deci);
	}

	
	public final Volt getVolt2Vref()
	{
		return GetU16(76, Volt.Deci);
	}

	
	public final int getInternalCommStatus() { return GetU16(78); }
}
