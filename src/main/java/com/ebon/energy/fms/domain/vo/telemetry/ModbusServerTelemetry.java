package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ModbusServerTelemetry {
    private Integer OutgoingConnections;
    private Integer IncomingConnections;
    private Boolean ServerRunning;

    public ModbusServerTelemetry(Integer OutgoingConnections, Integer IncomingConnections, Boolean ServerRunning) {
        this.OutgoingConnections = OutgoingConnections;
        this.IncomingConnections = IncomingConnections;
        this.ServerRunning = ServerRunning;
    }

    public Integer getOutgoingConnections() {
        return OutgoingConnections;
    }

    public Integer getIncomingConnections() {
        return IncomingConnections;
    }

    public Boolean getServerRunning() {
        return ServerRunning;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        ModbusServerTelemetry that = (ModbusServerTelemetry) obj;

        if (OutgoingConnections != null ? !OutgoingConnections.equals(that.OutgoingConnections) : that.OutgoingConnections != null)
            return false;
        if (IncomingConnections != null ? !IncomingConnections.equals(that.IncomingConnections) : that.IncomingConnections != null)
            return false;
        return ServerRunning != null ? ServerRunning.equals(that.ServerRunning) : that.ServerRunning == null;
    }

    @Override
    public int hashCode() {
        int result = OutgoingConnections != null ? OutgoingConnections.hashCode() : 0;
        result = 31 * result + (IncomingConnections != null ? IncomingConnections.hashCode() : 0);
        result = 31 * result + (ServerRunning != null ? ServerRunning.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ModbusServerTelemetry{" +
                "OutgoingConnections=" + OutgoingConnections +
                ", IncomingConnections=" + IncomingConnections +
                ", ServerRunning=" + ServerRunning +
                '}';
    }
}