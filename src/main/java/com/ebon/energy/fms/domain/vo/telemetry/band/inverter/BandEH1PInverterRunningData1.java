package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.*;


public class BandEH1PInverterRunningData1 extends DataBackedBand
{
	public BandEH1PInverterRunningData1()
	{
		super(BandForge.<BandEH1PInverterRunningData1>getMetadataFor(BandEH1PInverterRunningData1.class));
	}



	public BandEH1PInverterRunningData1(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterRunningData1>getMetadataFor(BandEH1PInverterRunningData1.class));
	}

	public BandEH1PInverterRunningData1(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterRunningData1>getMetadataFor(BandEH1PInverterRunningData1.class));
	}


	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
	public final LocalDateTime getRTCTime()
	{
		return GetDateTime6BytesSolis(0);
	}

	


	public final int getReserved0x8104() { return GetU16(12); }

	
	public final WattHour getEPVTotal()
	{
		return GetU32(14, WattHour.Kilo);
	}

	
	public final WattHour getEPVMonth()
	{
		return GetU32(18, WattHour.Kilo);
	}

	
	public final WattHour getEPVLastMonth()
	{
		return GetU32(22, WattHour.Kilo);
	}

	
	public final WattHour getEPVDay()
	{
		return GetU16(26, WattHour.Hecto);
	}

	
	public final WattHour getEPVYesterday()
	{
		return GetU16(28, WattHour.Hecto);
	}

	
	public final WattHour getEPVYear()
	{
		return GetU32(30, WattHour.Kilo);
	}

	
	public final WattHour getEPVLastYear()
	{
		return GetU32(34, WattHour.Kilo);
	}

	


	public final int getReserved0x8111()
	{
		return GetU32(38);
	}

	


	public final int getReserved0x8113() { return GetU16(42); }

	
	public final Volt getGroundVoltage()
	{
		return GetU16(44, Volt.Deci);
	}

	
	public final Volt getBMSBatteryVoltageHighByte()
	{
		return GetU16(46, Volt.Centi);
	}

	
	public final Celsius getTempMOSFET()
	{
		return GetS16(48, Celsius.Deci);
	}
}
