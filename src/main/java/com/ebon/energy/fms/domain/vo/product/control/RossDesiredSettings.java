package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.domain.vo.BatteryManagerDesiredSettingsVO;
import com.ebon.energy.fms.domain.vo.InverterDesiredSettingsVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Java equivalent of the C# RossDesiredSettings class
 */
@Data
public class RossDesiredSettings {

    @JsonProperty("v2")
    private SettingsV2Desired settingsV2;

    private InverterDesiredSettingsVO inverter;

    private ScheduleDesiredSettings schedules;

    private boolean measuringThirdPartyInverter;

    @JsonProperty(BatteryManagerSettingsName)
    private BatteryManagerDesiredSettingsVO batteryManager;


    public static final String SettingsV2SectionName = "v2";
    public static final String BatteryManagerSettingsName = "batteryManager";
    public static final String LogLevelConfigName = "logLevels";
    public static final String InverterSettingsName = "inverter";
    public static final String ScheduleSettingsName = "schedules";
    public static final String ConstraintSettingsName = "constraints";
    public static final String ConnectivitySettingsName = "connectivity";
    public static final String RelaySettingsName = "relays";
    public static final String SmartRelaySettingsName = "relaySettings";
    public static final String MeterTestSettingsName = "metertest";
    public static final String DredSubscribedName = "dredSubscribed";
    public static final String MeasuringThirdPartyInverterName = "measuringThirdPartyInverter";
    public static final String SerialPortVersionName = "serialPortVersion";
    public static final String ThirdPartyExportCtName = "thirdPartyExportCt";


}
