package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandESI extends DataBackedBand
{
	public BandESI()
	{
		super(BandForge.<BandESI>getMetadataFor(BandESI.class));
	}

	public BandESI(byte[] bytes)
	{
		super(bytes, BandForge.<BandESI>getMetadataFor(BandESI.class));
	}

	public BandESI(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESI>getMetadataFor(BandESI.class));
	}

	public final BigDecimal getRangeRealPowerAdjust()
	{
		return new BigDecimal(GetU16(0)).multiply(Percentage._1);
	}

	public final int getRangeReactPowerAdjust() { return GetU16(2); }
}
