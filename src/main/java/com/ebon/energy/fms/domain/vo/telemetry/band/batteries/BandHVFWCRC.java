package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



 





public class BandHVFWCRC extends DataBackedBand
{
	public BandHVFWCRC()
	{
		super(BandForge.<BandHVFWCRC>getMetadataFor(BandHVFWCRC.class));
	}



	public BandHVFWCRC(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVFWCRC>getMetadataFor(BandHVFWCRC.class));
	}

	public BandHVFWCRC(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVFWCRC>getMetadataFor(BandHVFWCRC.class));
	}





	public final int getFirmwareCRC() { return GetU16(0); }
}
