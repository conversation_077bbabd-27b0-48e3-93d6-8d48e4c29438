package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandSGInverterVRT extends DataBackedBand
{
	public BandSGInverterVRT()
	{
		super(BandForge.<BandSGInverterVRT>getMetadataFor(BandSGInverterVRT.class));
	}



	public BandSGInverterVRT(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterVRT>getMetadataFor(BandSGInverterVRT.class));
	}

	public BandSGInverterVRT(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterVRT>getMetadataFor(BandSGInverterVRT.class));
	}



	public final boolean getVRTTriggered()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}


	public final Watt getPFreqRefPowerLevelStart()
	{
		return GetU16(2, Watt.Unit);
	}


	public final TimeSpan getConnectTimeRemaining()
	{
		return GetU16(4, TimeSpan.fromSeconds(1));
	}
}
