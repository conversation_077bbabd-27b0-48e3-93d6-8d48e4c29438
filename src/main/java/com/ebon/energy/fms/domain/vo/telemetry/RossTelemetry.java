package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class RossTelemetry {
    private RossDeviceTelemetry RossDeviceTelemetry;
    private ProductAggregateTelemetry ProductAggregateTelemetry;
    private ProductLocalTimeTelemetry ProductLocalTimeTelemetry;
    private String SchemaVersion = "3.2";

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant TimestampProcessedUtc;

    public RossTelemetry(
            RossDeviceTelemetry RossDeviceTelemetry,
            ProductAggregateTelemetry ProductAggregateTelemetry,
            ProductLocalTimeTelemetry ProductLocalTimeTelemetry,
            Instant TimestampProcessedUtc) {

        this.RossDeviceTelemetry = RossDeviceTelemetry;
        this.ProductAggregateTelemetry = ProductAggregateTelemetry;
        this.ProductLocalTimeTelemetry = ProductLocalTimeTelemetry;
        this.TimestampProcessedUtc = TimestampProcessedUtc;
    }

    public RossDeviceTelemetry getRossDeviceTelemetry() {
        return RossDeviceTelemetry;
    }

    public ProductAggregateTelemetry getProductAggregateTelemetry() {
        return ProductAggregateTelemetry;
    }

    public ProductLocalTimeTelemetry getProductLocalTimeTelemetry() {
        return ProductLocalTimeTelemetry;
    }

    public String getSchemaVersion() {
        return SchemaVersion;
    }

    public Instant getTimestampProcessedUtc() {
        return TimestampProcessedUtc;
    }

    public void setTimestampProcessedUtc(Instant TimestampProcessedUtc) {
        this.TimestampProcessedUtc = TimestampProcessedUtc;
    }
}