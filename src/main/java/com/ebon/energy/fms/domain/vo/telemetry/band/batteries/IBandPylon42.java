package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;

import com.ebon.energy.fms.common.enums.PylonInfoFlagState;
import com.ebon.energy.fms.common.enums.PylonUserDefined;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public interface IBandPylon42 extends IBand
{
	PylonInfoFlagState getStatus();

	int getPackIndex();

	Volt[] getCellVoltages();

	Kelvin[] getTempReadings();

	Ampere getPackCurrent();

	Volt getPackVoltage();

	Watt getPackPower();

	PylonUserDefined getUserDefined();

	Capacity getPackRemains();

	Capacity getPackTotal();

	short getCycle();
}
