package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;

/**
 * 固件版本信息实体类
 */
@Data
@Accessors(chain = true)
@TableName("FirmwareVersions")
public class FirmwareVersionsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("ARMVersion")
    private String armVersion;

    @TableField("DSPVersion")
    private String dspVersion;

    @TableField("IsOfficialVersion")
    private Boolean isOfficialVersion;

    @TableField("LastModifiedUtc")
    private Date lastModifiedUtc;

    @TableField("CreatedBy")
    private String createdBy;
}