package com.ebon.energy.fms.domain.vo.telemetry;

import java.math.*;

public final class Percentage
{
    public static final BigDecimal _1 = new BigDecimal("0.01");
    public static final BigDecimal _01 = new BigDecimal("0.001");
    public static final BigDecimal _001 = new BigDecimal("0.0001");

    public static final BigDecimal Ten = new BigDecimal("0.1");
    public static final BigDecimal One = new BigDecimal("0.01");
    public static final BigDecimal Tenth = new BigDecimal("0.001");
    public static final BigDecimal Hundredth = new BigDecimal("0.0001");
    public static final BigDecimal Thousandth = new BigDecimal("0.00001");

    public static BigDecimal AsDecimal(BigDecimal value, BigDecimal unit)
    {
        return value.divide(unit);
    }
}
