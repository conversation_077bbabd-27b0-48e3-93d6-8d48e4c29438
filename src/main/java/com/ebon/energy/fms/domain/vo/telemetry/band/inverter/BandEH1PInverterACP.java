package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandEH1PInverterACP extends DataBackedBand
{
	public BandEH1PInverterACP()
	{
		super(BandForge.<BandEH1PInverterACP>getMetadataFor(BandEH1PInverterACP.class));
	}



	public BandEH1PInverterACP(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterACP>getMetadataFor(BandEH1PInverterACP.class));
	}

	public BandEH1PInverterACP(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterACP>getMetadataFor(BandEH1PInverterACP.class));
	}


	
	public final Watt getGridActivePowerL1()
	{
		return GetS16(0, Watt.Deca);
	}

	
	public final VoltAmpsReactive getGridReactivePowerL1()
	{
		return GetS16(2, VoltAmpsReactive.Deca);
	}

	
	public final VoltAmps getGridApparentPowerL1()
	{
		return GetS16(4, VoltAmps.Deca);
	}

	
	public final Watt getGridActivePowerL2()
	{
		return GetS16(6, Watt.Deca);
	}

	
	public final VoltAmpsReactive getGridReactivePowerL2()
	{
		return GetS16(8, VoltAmpsReactive.Deca);
	}

	
	public final VoltAmps getGridApparentPowerL2()
	{
		return GetS16(10, VoltAmps.Deca);
	}

	
	public final Watt getGridActivePowerL3()
	{
		return GetS16(12, Watt.Deca);
	}

	
	public final VoltAmpsReactive getGridReactivePowerL3()
	{
		return GetS16(14, VoltAmpsReactive.Deca);
	}

	
	public final VoltAmps getGridApparentPowerL3()
	{
		return GetS16(16, VoltAmps.Deca);
	}

	
	public final Watt getBackupActivePowerL1()
	{
		return GetS16(18, Watt.Deca);
	}

	
	public final VoltAmpsReactive getBackupReactivePowerL1()
	{
		return GetS16(20, VoltAmpsReactive.Deca);
	}

	
	public final VoltAmps getBackupApparentPowerL1()
	{
		return GetS16(22, VoltAmps.Deca);
	}

	
	public final Watt getBackupActivePowerL2()
	{
		return GetS16(24, Watt.Deca);
	}

	
	public final VoltAmpsReactive getBackupReactivePowerL2()
	{
		return GetS16(26, VoltAmpsReactive.Deca);
	}

	
	public final VoltAmps getBackupApparentPowerL2()
	{
		return GetS16(28, VoltAmps.Deca);
	}

	
	public final Watt getBackupActivePowerL3()
	{
		return GetS16(30, Watt.Deca);
	}

	
	public final VoltAmpsReactive getBackupReactivePowerL3()
	{
		return GetS16(32, VoltAmpsReactive.Deca);
	}

	
	public final VoltAmps getBackupApparentPowerL3()
	{
		return GetS16(34, VoltAmps.Deca);
	}
}
