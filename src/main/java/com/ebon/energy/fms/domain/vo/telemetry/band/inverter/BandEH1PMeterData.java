package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.AA55;
import com.ebon.energy.fms.common.enums.FunctionOperationStatus2;
import com.ebon.energy.fms.common.enums.InverterOperatingStatus;
import com.ebon.energy.fms.common.enums.SingleHandleResult;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;




 





public class BandEH1PMeterData extends DataBackedBand
{
	public BandEH1PMeterData()
	{
		super(BandForge.<BandEH1PMeterData>getMetadataFor(BandEH1PMeterData.class));
	}



	public BandEH1PMeterData(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PMeterData>getMetadataFor(BandEH1PMeterData.class));
	}

	public BandEH1PMeterData(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PMeterData>getMetadataFor(BandEH1PMeterData.class));
	}



	public final Volt getMeterVoltageL1()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Ampere getMeterCurrentL1()
	{
		return GetU16(2, Ampere.Centi);
	}


	public final Volt getMeterVoltageL2()
	{
		return GetU16(4, Volt.Deci);
	}


	public final Ampere getMeterCurrentL2()
	{
		return GetU16(6, Ampere.Centi);
	}


	public final Volt getMeterVoltageL3()
	{
		return GetU16(8, Volt.Deci);
	}


	public final Ampere getMeterCurrentL3()
	{
		return GetU16(10, Ampere.Centi);
	}


	public final Watt getMeterActivePowerL1()
	{
		return GetS32(12, Watt.Unit);
	}


	public final Watt getMeterActivePowerL2()
	{
		return GetS32(16, Watt.Unit);
	}


	public final Watt getMeterActivePowerL3()
	{
		return GetS32(20, Watt.Unit);
	}


	public final Watt getMeterActivePowerTotal()
	{
		return GetS32(24, Watt.Unit);
	}


	public final VoltAmpsReactive getMeterReactivePowerL1()
	{
		return GetS32(28, VoltAmpsReactive.Unit);
	}


	public final VoltAmpsReactive getMeterReactivePowerL2()
	{
		return GetS32(32, VoltAmpsReactive.Unit);
	}


	public final VoltAmpsReactive getMeterReactivePowerL3()
	{
		return GetS32(36, VoltAmpsReactive.Unit);
	}


	public final VoltAmpsReactive getMeterReactivePowerTotal()
	{
		return GetS32(40, VoltAmpsReactive.Unit);
	}


	public final VoltAmps getMeterApparentPowerL1()
	{
		return GetS32(44, VoltAmps.Unit);
	}


	public final VoltAmps getMeterApparentPowerL2()
	{
		return GetS32(48, VoltAmps.Unit);
	}


	public final VoltAmps getMeterApparentPowerL3()
	{
		return GetS32(52, VoltAmps.Unit);
	}


	public final VoltAmps getMeterApparentPowerTotal()
	{
		return GetS32(56, VoltAmps.Unit);
	}


	public final BigDecimal getMeterPowerFactor()
	{
		return new BigDecimal(GetS16(60)).multiply(Percentage._1);
	}


	public final Frequency getMeterFrequency()
	{
		return GetU16(62, Frequency.Centi);
	}


	public final WattHour getMeterEnergyBuy()
	{
		return GetU32(64, WattHour.Deca);
	}


	public final WattHour getMeterEnergySell()
	{
		return GetU32(68, WattHour.Deca);
	}


	public final Object getInverterOperatingStatus()
	{
		return InverterOperatingStatus.parse(GetU16(72));
	}


	public final Object getFunctionOperationStatus2()
	{
		return FunctionOperationStatus2.parse(GetU16(74));
	}


	public final Object getOptimizeRevenueFeature()
	{
		return AA55.parse(GetU16(76));
	}


	public final SingleHandleResult getInverterMeterTestResult()
	{
		return SingleHandleResult.fromValue(GetU16(78));
	}




	public final int getGridFaultCode() { return GetU16(80); }




	public final int getEquipmentFaultCode() { return GetU16(82); }




	public final int getBatteryFaultCode1() { return GetU16(84); }




	public final int getBatteryFaultCode2() { return GetU16(86); }
}
