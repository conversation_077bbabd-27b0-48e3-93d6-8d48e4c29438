package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Celsius implements IUnit {

    public static final String SYMBOL = "C";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final Celsius Unit = new Celsius(new BigDecimal("1.0"));
    public static final Celsius Zero = new Celsius(new BigDecimal("0.0"));
    public static final Celsius Tera = new Celsius(new BigDecimal("1000000000000"));
    public static final Celsius Giga = new Celsius(new BigDecimal("1000000000"));
    public static final Celsius Mega = new Celsius(new BigDecimal("1000000"));
    public static final Celsius Kilo = new Celsius(new BigDecimal("1000"));
    public static final Celsius Hecto = new Celsius(new BigDecimal("100"));
    public static final Celsius Deca = new Celsius(new BigDecimal("10"));
    public static final Celsius Deci = new Celsius(new BigDecimal("0.1"));
    public static final Celsius Centi = new Celsius(new BigDecimal("0.01"));
    public static final Celsius Milli = new Celsius(new BigDecimal("0.001"));

    public Celsius() {
    }

    public Celsius(BigDecimal value) {
        this.value = value;
    }

    public Celsius(String value) {
        this.value = new BigDecimal(value);
    }


    public Celsius(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Celsius(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Celsius abs() {
        return new Celsius(value.abs());
    }

    public Celsius subtract(Celsius other) {
        return new Celsius(this.value.subtract(other.value));
    }

    public Celsius add(Celsius other) {
        return new Celsius(this.value.add(other.value));
    }

    public static Celsius add(Celsius a, Celsius b) {
        return new Celsius(a.value.add(b.value));
    }

    public static Celsius subtract(Celsius a, Celsius b) {
        return new Celsius(a.value.subtract(b.value));
    }

    public boolean greaterThan(Celsius other) {
        return this.value.compareTo(other.value) > 0;
    }

    public boolean lessThan(Celsius other) {
        return this.value.compareTo(other.value) < 0;
    }

    public boolean greaterThanOrEqual(Celsius other) {
        return this.value.compareTo(other.value) >= 0;
    }

    public boolean lessThanOrEqual(Celsius other) {
        return this.value.compareTo(other.value) <= 0;
    }

    public static Celsius getValueOrZero(Celsius watt) {
        return watt == null || watt.getValue() == null ? Celsius.Zero : watt;
    }

    // 重载一元 + 运算符
    public static Celsius operatorPlus(Celsius a) {
        return a;
    }

    // 重载一元 - 运算符
    public static Celsius operatorMinus(Celsius a) {
        return new Celsius(a.value.negate());
    }

    // 重载二元 + 运算符
    public static Celsius operatorAdd(Celsius a, Celsius b) {
        return new Celsius(a.value.add(b.value));
    }

    // 重载二元 - 运算符
    public static Celsius operatorSubtract(Celsius a, Celsius b) {
        return new Celsius(a.value.subtract(b.value));
    }

    // 重载二元 / 运算符，返回 BigDecimal
    public static BigDecimal operatorDivide(Celsius a, Celsius b) {
        return a.value.divide(b.value);
    }

    // 重载 Watt 除以 BigDecimal 的 / 运算符
    public static Celsius operatorDivide(Celsius a, BigDecimal b) {
        return new Celsius(a.value.divide(b));
    }

    // 重载 Watt 乘以 BigDecimal 的 * 运算符
    public static Celsius operatorMultiply(Celsius a, BigDecimal value) {
        return new Celsius(a.value.multiply(value));
    }

    // 重载 BigDecimal 乘以 Watt 的 * 运算符
    public static Celsius operatorMultiply(BigDecimal value, Celsius a) {
        return new Celsius(value.multiply(a.value));
    }

    // 重载 == 运算符
    public static boolean operatorEqual(Celsius a, Celsius b) {
        return a.value.equals(b.value);
    }

    // 重载 != 运算符
    public static boolean operatorNotEqual(Celsius a, Celsius b) {
        return !a.value.equals(b.value);
    }

    // 重载 < 运算符
    public static boolean operatorLessThan(Celsius a, Celsius b) {
        return a.value.compareTo(b.value) < 0;
    }

    // 重载 <= 运算符
    public static boolean operatorLessThanOrEqual(Celsius a, Celsius b) {
        return a.value.compareTo(b.value) <= 0;
    }

    // 重载 > 运算符
    public static boolean operatorGreaterThan(Celsius a, Celsius b) {
        return a.value.compareTo(b.value) > 0;
    }

    // 重载 >= 运算符
    public static boolean operatorGreaterThanOrEqual(Celsius a, Celsius b) {
        return a.value.compareTo(b.value) >= 0;
    }

    // 从 BigDecimal 隐式转换为 Watt
    public static Celsius fromBigDecimal(BigDecimal d) {
        return new Celsius(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public BigDecimal asDecimal(Celsius unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(Celsius unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(Celsius unit) {
        return value.divide(unit.value);
    }

    public double asDouble(Celsius unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(Celsius unit) {
        return value.divide(unit.value).longValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Celsius other = (Celsius) obj;
        return value.compareTo(other.value) == 0;
    }

    public int compareTo(Celsius other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}
