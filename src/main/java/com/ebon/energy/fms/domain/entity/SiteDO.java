package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("Site")
public class SiteDO {

    @TableField(value = "Id")
    private String id;

    @TableField(value = "AddressId")
    private Integer addressId;

    @TableField(value = "SystemId")
    private String systemId;

    @TableField(value = "SupportsLoadContributors")
    private Timestamp supportsLoadContributors;

    @TableField(value = "HasReports")
    private Boolean hasReports;

    @TableField(value = "Nmi")
    private String nmi;
    
    @TableField(value = "OriginalPrimarySN")
    private String originalPrimarySN;

}