package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandITelemetry implements ITelemetryBand {
    public InverterBandITelemetry(BigDecimal rangeRealPowerAdjust, short rangeReactPowerAdjust) {
        RangeRealPowerAdjust = rangeRealPowerAdjust;
        RangeReactPowerAdjust = rangeReactPowerAdjust;
    }

    private final BigDecimal RangeRealPowerAdjust;

    public final BigDecimal getRangeRealPowerAdjust() {
        return RangeRealPowerAdjust;
    }

    private final short RangeReactPowerAdjust;

    public final short getRangeReactPowerAdjust() {
        return RangeReactPowerAdjust;
    }
}
