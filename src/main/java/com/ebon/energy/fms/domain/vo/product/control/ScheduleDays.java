// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.json.ScheduleDaysDeserializer;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

/**
 * Enum representing days of the week for scheduling.
 * This is a Java conversion of the C# ScheduleDays enum.
 */
@JsonDeserialize(using = ScheduleDaysDeserializer.class)
public enum ScheduleDays {
    /**
     * No day
     */
    NEVER((byte) 0x00),

    /**
     * Sunday
     */
    SUNDAY((byte) 0x01),

    /**
     * Monday
     */
    MONDAY((byte) 0x02),

    /**
     * Tuesday
     */
    TUESDAY((byte) 0x04),

    /**
     * Wednesday
     */
    WEDNESDAY((byte) 0x08),

    /**
     * Thursday
     */
    THURSDAY((byte) 0x10),

    /**
     * Friday
     */
    FRIDAY((byte) 0x20),

    /**
     * Saturday
     */
    SATURDAY((byte) 0x40),

    /**
     * Monday through Friday
     */
    WEEKDAYS((byte) (0x02 | 0x04 | 0x08 | 0x10 | 0x20)),

    /**
     * Saturday and Sunday
     */
    WEEKENDS((byte) (0x40 | 0x01)),

    /**
     * All days of the week
     */
    EVERYDAY((byte) (0x02 | 0x04 | 0x08 | 0x10 | 0x20 | 0x40 | 0x01));

    private final byte value;

    ScheduleDays(byte value) {
        this.value = value;
    }

    public byte getValue() {
        return value;
    }

    public static ScheduleDays fromStringIgnoreCase(String name) {
        for (ScheduleDays day : ScheduleDays.values()) {
            if (day.name().equalsIgnoreCase(name)) {
                return day;
            }
        }
        throw new IllegalArgumentException("No enum constant " + name);
    }

    /**
     * Check if this ScheduleDays contains the specified day
     *
     * @param day The day to check
     * @return true if this ScheduleDays contains the specified day
     */
    public boolean contains(ScheduleDays day) {
        return (this.value & day.value) == day.value;
    }

    /**
     * Combine multiple ScheduleDays
     *
     * @param days The days to combine
     * @return A new ScheduleDays representing the combination
     */
    public static ScheduleDays combine(ScheduleDays... days) {
        byte combined = 0;
        for (ScheduleDays day : days) {
            combined |= day.value;
        }

        // Find the matching enum value
        for (ScheduleDays scheduleDays : values()) {
            if (scheduleDays.value == combined) {
                return scheduleDays;
            }
        }

        // If no exact match, return NEVER (this is a simplification, in a real implementation
        // you might want to create a more sophisticated approach)
        return NEVER;
    }


    public static ScheduleDays fromValue(Byte value) {
        if (value == null) {
            return null;
        }
        for (ScheduleDays e : values()) {
            if (e.value == value) {
                return e;
            }
        }
        return null;
    }
}
