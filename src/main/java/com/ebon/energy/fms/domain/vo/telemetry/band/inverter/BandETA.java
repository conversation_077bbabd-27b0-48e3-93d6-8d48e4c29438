package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.domain.vo.telemetry.band.IPrimaryBand;
import com.ebon.energy.fms.util.StringProcessors;

public class BandETA extends DataBackedBand implements IPrimaryBand {
    public BandETA() {
        super(BandForge.<BandETA>getMetadataFor(BandETA.class));
    }


    public BandETA(byte[] bytes) {
        super(bytes, BandForge.<BandETA>getMetadataFor(BandETA.class));
    }

    public BandETA(String encodedBytes) {
        super(encodedBytes, BandForge.<BandETA>getMetadataFor(BandETA.class));
    }


    public final int getModbusProtocolVersion() { return GetU16(0); }


    public final Watt getRatedPower() {
        return GetU16(2, Watt.Unit);
    }


    public final int getACOutputType() { return GetU16(4); }


    public final String getSerialNumber() {
        return GetBufS(6, 16, StringProcessors.GoodweDecode);
    }


    public final String getModelName() {
        return GetBufS(22, 10, StringProcessors.GoodweDecode);
    }


    public final int getDSP1SoftwareVersion() { return GetU16(32); }


    public final int getDSP2SoftwareVersion() { return GetU16(34); }


    public final int getDSPSVNVersion() { return GetU16(36); }


    public final int getARMSoftwareVersion() { return GetU16(38); }


    public final int getARMSVNVersion() { return GetU16(40); }


    public final String getDSPInternalFirmwareVer() {
        return GetBufS(42, 12, StringProcessors.GoodweDecode);
    }


    public final String getARMInternalFirmwareVer() {
        return GetBufS(54, 12, StringProcessors.GoodweDecode);
    }


    public final String GetFirmwareVersionString() {
        return InverterAdapterHelper.getDspVersionFromInternal(getDSPInternalFirmwareVer()) + InverterAdapterHelper.getDspVersionFromInternal(getDSPInternalFirmwareVer()) + InverterAdapterHelper.getArmVersionFromInternal(getARMInternalFirmwareVer());
    }
}
