package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandH2InverterRunningData2 extends DataBackedBand
{
	public BandH2InverterRunningData2()
	{
		super(BandForge.<BandH2InverterRunningData2>getMetadataFor(BandH2InverterRunningData2.class));
	}



	public BandH2InverterRunningData2(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterRunningData2>getMetadataFor(BandH2InverterRunningData2.class));
	}

	public BandH2InverterRunningData2(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterRunningData2>getMetadataFor(BandH2InverterRunningData2.class));
	}



	public final Volt getGridPortVoltageL1()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Ampere getGridPortCurrentL1()
	{
		return GetS16(2, Ampere.Centi);
	}


	public final Frequency getGridPortFrequencyL1()
	{
		return GetU16(4, Frequency.Centi);
	}


	public final Ampere getGridPortDCComponentL1()
	{
		return GetS16(6, Ampere.Milli);
	}


	public final Watt getGridPortActivePowerL1()
	{
		return GetS16(8, Watt.Unit);
	}


	public final VoltAmps getGridPortApparentPowerL1()
	{
		return GetU16(10, VoltAmps.Unit);
	}


	public final BigDecimal getGridPortPowerFactorL1()
	{
		return new BigDecimal(GetS16(12)).multiply(Percentage._1);
	}


	public final Volt getGridPortVoltageL2()
	{
		return GetU16(14, Volt.Deci);
	}


	public final Ampere getGridPortCurrentL2()
	{
		return GetS16(16, Ampere.Centi);
	}


	public final Frequency getGridPortFrequencyL2()
	{
		return GetU16(18, Frequency.Centi);
	}


	public final Ampere getGridPortDCComponentL2()
	{
		return GetS16(20, Ampere.Milli);
	}


	public final Watt getGridPortActivePowerL2()
	{
		return GetS16(22, Watt.Unit);
	}


	public final VoltAmps getGridPortApparentPowerL2()
	{
		return GetU16(24, VoltAmps.Unit);
	}


	public final BigDecimal getGridPortPowerFactorL2()
	{
		return new BigDecimal(GetS16(26)).multiply(Percentage._1);
	}


	public final Volt getGridPortVoltageL3()
	{
		return GetU16(28, Volt.Deci);
	}


	public final Ampere getGridPortCurrentL3()
	{
		return GetS16(30, Ampere.Centi);
	}


	public final Frequency getGridPortFrequencyL3()
	{
		return GetU16(32, Frequency.Centi);
	}


	public final Ampere getGridPortDCComponentL3()
	{
		return GetS16(34, Ampere.Milli);
	}


	public final Watt getGridPortActivePowerL3()
	{
		return GetS16(36, Watt.Unit);
	}


	public final VoltAmps getGridPortApparentPowerL3()
	{
		return GetU16(38, VoltAmps.Unit);
	}


	public final BigDecimal getGridPortPowerFactorL3()
	{
		return new BigDecimal(GetS16(40)).multiply(Percentage._1);
	}


	public final Volt getInverterVoltageL1()
	{
		return GetU16(42, Volt.Deci);
	}


	public final Ampere getInverterCurrentL1()
	{
		return GetS16(44, Ampere.Centi);
	}


	public final Frequency getInverterFrequencyL1()
	{
		return GetU16(46, Frequency.Centi);
	}


	public final Watt getInverterActivePowerL1()
	{
		return GetS16(48, Watt.Unit);
	}


	public final VoltAmps getInverterApparentPowerL1()
	{
		return GetU16(50, VoltAmps.Unit);
	}


	public final Volt getInverterVoltageL2()
	{
		return GetU16(52, Volt.Deci);
	}


	public final Ampere getInverterCurrentL2()
	{
		return GetS16(54, Ampere.Centi);
	}


	public final Frequency getInverterFrequencyL2()
	{
		return GetU16(56, Frequency.Centi);
	}


	public final Watt getInverterActivePowerL2()
	{
		return GetS16(58, Watt.Unit);
	}


	public final VoltAmps getInverterApparentPowerL2()
	{
		return GetU16(60, VoltAmps.Unit);
	}


	public final Volt getInverterVoltageL3()
	{
		return GetU16(62, Volt.Deci);
	}


	public final Ampere getInverterCurrentL3()
	{
		return GetS16(64, Ampere.Centi);
	}


	public final Frequency getInverterFrequencyL3()
	{
		return GetU16(66, Frequency.Centi);
	}


	public final Watt getInverterActivePowerL3()
	{
		return GetS16(68, Watt.Unit);
	}


	public final VoltAmps getInverterApparentPowerL3()
	{
		return GetU16(70, VoltAmps.Unit);
	}
}
