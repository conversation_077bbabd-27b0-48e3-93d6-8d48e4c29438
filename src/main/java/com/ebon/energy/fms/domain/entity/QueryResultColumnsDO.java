package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 标签实体类
 */
@Data
@Accessors(chain = true)
@TableName("QueryResultColumns")
public class QueryResultColumnsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签值
     */
    @TableField(value = "Column")
    private String column;

}