package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class IndividualFirmwareUpdateTelemetry {

    private final Byte PackIndex;
    private final String Section;
    private final int BlocksSent;
    private final int BlocksFailed;
    private final int BlocksTotal;
    private final String TargetVersion;
    private final String TargetHash;
    private final int Attempt;

    public IndividualFirmwareUpdateTelemetry() {
        this(null, null, 0, 0, 0, null, null, 0);
    }

    public IndividualFirmwareUpdateTelemetry(
            Byte PackIndex,
            String Section,
            int BlocksSent,
            int BlocksFailed,
            int BlocksTotal,
            String TargetVersion,
            String TargetHash,
            int Attempt) {
        this.PackIndex = PackIndex;
        this.Section = Section;
        this.BlocksSent = BlocksSent;
        this.BlocksFailed = BlocksFailed;
        this.BlocksTotal = BlocksTotal;
        this.TargetVersion = TargetVersion;
        this.TargetHash = TargetHash;
        this.Attempt = Attempt;
    }

    public Byte getPackIndex() {
        return PackIndex;
    }

    public String getSection() {
        return Section;
    }

    public int getBlocksSent() {
        return BlocksSent;
    }

    public int getBlocksFailed() {
        return BlocksFailed;
    }

    public int getBlocksTotal() {
        return BlocksTotal;
    }

    public String getTargetVersion() {
        return TargetVersion;
    }

    public String getTargetHash() {
        return TargetHash;
    }

    public int getAttempt() {
        return Attempt;
    }
}
