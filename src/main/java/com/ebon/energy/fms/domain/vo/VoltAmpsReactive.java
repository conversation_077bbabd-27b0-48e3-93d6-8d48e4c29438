package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class VoltAmpsReactive implements IUnit {

    public static final String SYMBOL = "Var";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final VoltAmpsReactive Unit = new VoltAmpsReactive(new BigDecimal("1.0"));
    public static final VoltAmpsReactive Zero = new VoltAmpsReactive(new BigDecimal("0.0"));
    public static final VoltAmpsReactive Tera = new VoltAmpsReactive(new BigDecimal("1000000000000"));
    public static final VoltAmpsReactive Giga = new VoltAmpsReactive(new BigDecimal("1000000000"));
    public static final VoltAmpsReactive Mega = new VoltAmpsReactive(new BigDecimal("1000000"));
    public static final VoltAmpsReactive Kilo = new VoltAmpsReactive(new BigDecimal("1000"));
    public static final VoltAmpsReactive Hecto = new VoltAmpsReactive(new BigDecimal("100"));
    public static final VoltAmpsReactive Deca = new VoltAmpsReactive(new BigDecimal("10"));
    public static final VoltAmpsReactive Deci = new VoltAmpsReactive(new BigDecimal("0.1"));
    public static final VoltAmpsReactive Centi = new VoltAmpsReactive(new BigDecimal("0.01"));
    public static final VoltAmpsReactive Milli = new VoltAmpsReactive(new BigDecimal("0.001"));

    public VoltAmpsReactive() {

    }

    public VoltAmpsReactive(BigDecimal value) {
        this.value = value;
    }

    public VoltAmpsReactive(String value) {
        this.value = new BigDecimal(value);
    }


    public VoltAmpsReactive(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public VoltAmpsReactive(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public VoltAmpsReactive abs() {
        return new VoltAmpsReactive(value.abs());
    }

    public VoltAmpsReactive subtract(VoltAmpsReactive other) {
        return new VoltAmpsReactive(this.value.subtract(other.value));
    }

    public VoltAmpsReactive add(VoltAmpsReactive other) {
        return new VoltAmpsReactive(this.value.add(other.value));
    }

    public static VoltAmpsReactive add(VoltAmpsReactive a, VoltAmpsReactive b) {
        return new VoltAmpsReactive(a.value.add(b.value));
    }

    public static VoltAmpsReactive subtract(VoltAmpsReactive a, VoltAmpsReactive b) {
        return new VoltAmpsReactive(a.value.subtract(b.value));
    }

    public boolean greaterThan(VoltAmpsReactive other) {
        return this.value.compareTo(other.value) > 0;
    }

    public boolean lessThan(VoltAmpsReactive other) {
        return this.value.compareTo(other.value) < 0;
    }

    public boolean greaterThanOrEqual(VoltAmpsReactive other) {
        return this.value.compareTo(other.value) >= 0;
    }

    public boolean lessThanOrEqual(VoltAmpsReactive other) {
        return this.value.compareTo(other.value) <= 0;
    }

    public static VoltAmpsReactive getValueOrZero(VoltAmpsReactive watt) {
        return watt == null || watt.getValue() == null ? VoltAmpsReactive.Zero : watt;
    }

    // 重载一元 + 运算符
    public static VoltAmpsReactive operatorPlus(VoltAmpsReactive a) {
        return a;
    }

    // 重载一元 - 运算符
    public static VoltAmpsReactive operatorMinus(VoltAmpsReactive a) {
        return new VoltAmpsReactive(a.value.negate());
    }

    // 重载二元 + 运算符
    public static VoltAmpsReactive operatorAdd(VoltAmpsReactive a, VoltAmpsReactive b) {
        return new VoltAmpsReactive(a.value.add(b.value));
    }

    // 重载二元 - 运算符
    public static VoltAmpsReactive operatorSubtract(VoltAmpsReactive a, VoltAmpsReactive b) {
        return new VoltAmpsReactive(a.value.subtract(b.value));
    }

    // 重载二元 / 运算符，返回 BigDecimal
    public static BigDecimal operatorDivide(VoltAmpsReactive a, VoltAmpsReactive b) {
        return a.value.divide(b.value);
    }

    // 重载 Watt 除以 BigDecimal 的 / 运算符
    public static VoltAmpsReactive operatorDivide(VoltAmpsReactive a, BigDecimal b) {
        return new VoltAmpsReactive(a.value.divide(b));
    }

    // 重载 Watt 乘以 BigDecimal 的 * 运算符
    public static VoltAmpsReactive operatorMultiply(VoltAmpsReactive a, BigDecimal value) {
        return new VoltAmpsReactive(a.value.multiply(value));
    }

    // 重载 BigDecimal 乘以 Watt 的 * 运算符
    public static VoltAmpsReactive operatorMultiply(BigDecimal value, VoltAmpsReactive a) {
        return new VoltAmpsReactive(value.multiply(a.value));
    }

    // 重载 == 运算符
    public static boolean operatorEqual(VoltAmpsReactive a, VoltAmpsReactive b) {
        return a.value.equals(b.value);
    }

    // 重载 != 运算符
    public static boolean operatorNotEqual(VoltAmpsReactive a, VoltAmpsReactive b) {
        return !a.value.equals(b.value);
    }

    // 重载 < 运算符
    public static boolean operatorLessThan(VoltAmpsReactive a, VoltAmpsReactive b) {
        return a.value.compareTo(b.value) < 0;
    }

    // 重载 <= 运算符
    public static boolean operatorLessThanOrEqual(VoltAmpsReactive a, VoltAmpsReactive b) {
        return a.value.compareTo(b.value) <= 0;
    }

    // 重载 > 运算符
    public static boolean operatorGreaterThan(VoltAmpsReactive a, VoltAmpsReactive b) {
        return a.value.compareTo(b.value) > 0;
    }

    // 重载 >= 运算符
    public static boolean operatorGreaterThanOrEqual(VoltAmpsReactive a, VoltAmpsReactive b) {
        return a.value.compareTo(b.value) >= 0;
    }

    // 从 BigDecimal 隐式转换为 Watt
    public static VoltAmpsReactive fromBigDecimal(BigDecimal d) {
        return new VoltAmpsReactive(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public BigDecimal asDecimal(VoltAmpsReactive unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(VoltAmpsReactive unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(VoltAmpsReactive unit) {
        return value.divide(unit.value);
    }

    public double asDouble(VoltAmpsReactive unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(VoltAmpsReactive unit) {
        return value.divide(unit.value).longValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        VoltAmpsReactive other = (VoltAmpsReactive) obj;
        return value.compareTo(other.value) == 0;
    }

    public int compareTo(VoltAmpsReactive other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}
