package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandHVFWSize extends DataBackedBand
{
	public BandHVFWSize()
	{
		super(BandForge.<BandHVFWSize>getMetadataFor(BandHVFWSize.class));
	}



	public BandHVFWSize(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVFWSize>getMetadataFor(BandHVFWSize.class));
	}

	public BandHVFWSize(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVFWSize>getMetadataFor(BandHVFWSize.class));
	}


	


	public final int getFirmwareSize()
	{
		return GetU32(0);
	}
}
