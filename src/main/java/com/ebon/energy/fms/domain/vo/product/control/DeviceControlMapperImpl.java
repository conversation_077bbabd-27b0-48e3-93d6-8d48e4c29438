package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.InverterOperationModeEnum;
import com.ebon.energy.fms.common.enums.InverterOperationTypeEnum;
import com.ebon.energy.fms.common.enums.InverterScheduleOperationModeEnum;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.InverterOperationVO;
import com.ebon.energy.fms.domain.vo.InverterScheduleVO;
import com.ebon.energy.fms.domain.vo.product.control.invert.InverterScheduleItemViewModel;
import com.ebon.energy.fms.util.BaseSchedule;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the DeviceControlMapper interface
 */
@Service
public class DeviceControlMapperImpl implements DeviceControlMapper {

    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public InverterOperationViewModel mapToView(InverterOperationVO model, LocalDateTime ouijaBoardWindowsTime,
            String inverterTimeZone) {
        InverterOperationViewModel viewModel = new InverterOperationViewModel();
        viewModel.setPowerInWatts(model.getPowerInWatts());

        List<InverterScheduleItemViewModel> schedules = new ArrayList<>();
        if (model.getSchedules() != null) {
            schedules = model.getSchedules().stream()
                    .filter(this::isKnownInverterScheduleMode)
                    .map(this::mapToView)
                    .filter(s -> s.getIsRecurringDaily()
                            || (ouijaBoardWindowsTime != null && s.getEndTime().isAfter(ouijaBoardWindowsTime.atZone(ZoneOffset.systemDefault()).toOffsetDateTime())))
                    .collect(Collectors.toList());
        }

        viewModel.setSchedules(schedules);
        viewModel.setType(model.getType());
        viewModel.setMode(model.getMode());
        viewModel.setInverterTimeZone(inverterTimeZone);

        return viewModel;
    }

    @Override
    public DeviceControl mapToEntity(ProductControlViewModel model, DeviceControl existingDeviceControl) {
        DeviceControl deviceControl = new DeviceControl();
        deviceControl.setDateTimeUpdated(
                java.time.format.DateTimeFormatter.ofPattern(DeviceControl.LOCAL_DATE_TIME_FORMAT)
                        .format(java.time.Instant.now().atZone(java.time.ZoneOffset.UTC))
        );
        deviceControl.setDeviceId(existingDeviceControl != null ? existingDeviceControl.getDeviceId() : 0);
        deviceControl.setDeviceModel(existingDeviceControl != null ? existingDeviceControl.getDeviceModel() : null);
        deviceControl.setDeviceSerialNumber(model.getSerialNumber());
//        if (model.getRelayOperation() != null) {
//            List<DispatchableLoadSetting> relayList = model.getRelayOperation().stream()
//                    .map(this::mapToEntity)
//                    .collect(Collectors.toList());
//            deviceControl.setDispatchableLoads(relayList);
//        } else {
//            deviceControl.setDispatchableLoads(new ArrayList<>());
//        }
        deviceControl.setGroupEventId(existingDeviceControl != null ? existingDeviceControl.getGroupEventId() : null);
        deviceControl.setOnDRED(existingDeviceControl != null ? existingDeviceControl.isOnDRED() : false);
        deviceControl.setInverterOperation(mapToEntity(model.getInverterOperation()));
        return deviceControl;
    }

    public InverterScheduleItemViewModel mapToView(InverterScheduleVO model) {
        InverterScheduleItemViewModel viewModel = new InverterScheduleItemViewModel();

        // Parse start and end times
        LocalDateTime start = LocalDateTime.parse(model.getStartTime(), TIME_FORMAT);
        LocalDateTime end = LocalDateTime.parse(model.getEndTime(), TIME_FORMAT);

        LocalDateTime scheduleDate = null;
        if (model.getScheduleDate() != null) {
            scheduleDate = LocalDateTime.parse(model.getScheduleDate(), DATE_FORMAT);
        }

        if (model.isRecurring()) {
            viewModel.setStartTime(start.atZone(ZoneOffset.systemDefault()).toOffsetDateTime());
            viewModel.setEndTime(end.atZone(ZoneOffset.systemDefault()).toOffsetDateTime());
        } else {
            // Combine date and time
            viewModel.setStartTime(combineDateTime(scheduleDate, start).atZone(ZoneOffset.systemDefault()).toOffsetDateTime());
            viewModel.setEndTime(combineDateTime(scheduleDate, end).atZone(ZoneOffset.systemDefault()).toOffsetDateTime());
        }

        viewModel.setIsRecurringDaily(model.isRecurring());
        viewModel.setMode(InverterScheduleOperationModeEnum.fromString(model.getMode()));
        viewModel.setPowerInWatts(model.getPowerInWatts());

        return viewModel;
    }

    /**
     * Combine date and time
     *
     * @param date The date
     * @param time The time
     * @return The combined date and time
     */
    private LocalDateTime combineDateTime(LocalDateTime date, LocalDateTime time) {
        if (date == null) {
            return time;
        }

        return date.withHour(time.getHour()).withMinute(time.getMinute()).withSecond(time.getSecond());
    }

    /**
     * Check if the inverter schedule mode is known
     *
     * @param schedule The inverter schedule
     * @return True if the mode is known, false otherwise
     */
    private boolean isKnownInverterScheduleMode(InverterScheduleVO schedule) {
        return "ChargeBattery".equals(schedule.getMode()) ||
                "DischargeBattery".equals(schedule.getMode());
    }

    public InverterOperationVO mapToEntity(InverterOperationViewModel model) {
        List<InverterScheduleVO> schedules = (model.getSchedules() != null)
                ? model.getSchedules().stream().map(this::mapToEntity).collect(Collectors.toList())
                : new ArrayList<>();

        // Clear schedules if mode is not set to schedule
        if (model.getType() != InverterOperationTypeEnum.Schedule.name()) {
            schedules = new ArrayList<>();
        }

        // Set to auto if schedule mode set but no schedules
        if (model.getType() == InverterOperationTypeEnum.Schedule.name() && schedules.isEmpty()) {
            model.setType(InverterOperationTypeEnum.Set.name());
            model.setMode(InverterOperationModeEnum.Auto.name());
        }

        InverterOperationVO inverterOperation = new InverterOperationVO();
        inverterOperation.setType(model.getType());
        inverterOperation.setMode(model.getMode());
        inverterOperation.setPowerInWatts(model.getPowerInWatts());
        inverterOperation.setSchedules(schedules);

        return inverterOperation;
    }

    public InverterScheduleVO mapToEntity(InverterScheduleItemViewModel model) {
        InverterScheduleVO schedule = new InverterScheduleVO();
        schedule.setStartTime(model.getStartTime().atZoneSimilarLocal(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern(BaseSchedule.TIME_FORMAT)));
        schedule.setEndTime(model.getEndTime().atZoneSimilarLocal(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern(BaseSchedule.TIME_FORMAT)));
        schedule.setPowerInWatts(model.getPowerInWatts());
        schedule.setMode(model.getMode().name());
        schedule.setRecurring(model.getIsRecurringDaily());
        schedule.setScheduleDate(model.getStartTime().atZoneSimilarLocal(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern(BaseSchedule.DATE_FORMAT)));
        return schedule;
    }

}
