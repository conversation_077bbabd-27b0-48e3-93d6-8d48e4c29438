package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.ArrayList;

/**
 * Message for harmonic data.
 * Note: The PA110 only supplies up to 7th harmonic.
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterChannelThd {
    /**
     * THD.
     */
    private MeterHarmonicData Thd;

    public final MeterHarmonicData getThd() {
        return Thd;
    }

    public final void setThd(MeterHarmonicData value) {
        Thd = value;
    }

    /**
     * Data for all other measured harmonics.
     */
    private ArrayList<MeterHarmonicData> Harmonics;

    public final ArrayList<MeterHarmonicData> getHarmonics() {
        return Harmonics;
    }

    public final void setHarmonics(ArrayList<MeterHarmonicData> value) {
        Harmonics = value;
    }
}
