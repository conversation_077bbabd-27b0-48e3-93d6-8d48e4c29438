package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class InverterInfoVO {

    /**
     * 序列号
     */
    private String serialNumber;

    private String plantName;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 是否小时在线
     */
    private Boolean isHourlyOnline;

    /**
     * 是否日在线
     */
    private Boolean isDailyOnline;

    /**
     * 逆变器模式
     */
    private String inverterMode;

    /**
     * ross版本号
     */
    private String rossVersion;

    /**
     * 固件版本
     */
    private String firmwareVersion;

    /**
     * 硬件配置
     */
    private String hardwareConfig;

    /**
     * 型号
     */
    private String modelName;

    /**
     * 安装时间
     */
    private String installationDate;

    /**
     * 是否在保修期
     */
    private Boolean isInWarranty;

    /**
     * 保修结束时间
     */
    private String warrantyEndDate;

    /**
     * 最后连接时间
     */
    private String lastConnected;

    /**
     * 0-未处理 1-已处理
     */
    private Integer errorStatus;

    /**
     * 地址
     */
    private String address;

    /**
     * owner联系方式
     */
    private String ownerContact;

    /**
     * owner邮箱
     */
    private String ownerEmail;
    
    private String ownerFirstName;
    
    private String ownerLastName;
    
    private String nmi;

    /**
     * settings
     */
    private InverterSettingsVO settings;

    /**
     * 系统状态
     */
    private SystemStatus systemStatus;

    /**
     * 今日pv统计
     */
    private String todayPvTotal;

    /**
     * 月pv统计
     */
    private String monthPvTotal;

    /**
     * 年pv统计
     */
    private String yearPvTotal;


    /**
     * 总pv统计
     */
    private String allTimePvTotal;

    /**
     * 总输入统计
     */
    private String allTimeTotalImport;

    /**
     * 总输出统计
     */
    private String allTimeTotalExport;

    /**
     * 最后遥测接收时间
     */
    private Timestamp lastSystemStatusReceived;

}