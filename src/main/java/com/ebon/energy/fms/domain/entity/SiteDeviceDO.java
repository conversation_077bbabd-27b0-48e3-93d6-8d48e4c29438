package com.ebon.energy.fms.domain.entity;

import com.ebon.energy.fms.common.enums.BalancingMethods;
import com.ebon.energy.fms.common.enums.Phase;
import com.ebon.energy.fms.common.enums.PhaseRole;
import lombok.Data;

import java.sql.Timestamp;
import java.time.ZonedDateTime;

@Data
public class SiteDeviceDO {

    private String siteId;
    private String serialNumber;
    private String latestSystemStatus;
    private Timestamp lastSystemStatusReceivedUtc;
    private boolean isInWarranty;
    private String bclTimeZoneId;
    private String maintainingInstallerId;
    private Timestamp supportsLoadContributors;
    private boolean hasReports;
    private BalancingMethods energyBalancingMethod;
    private Phase phase;
    private PhaseRole phaseRole;
    private Timestamp installationDateUtc;
}