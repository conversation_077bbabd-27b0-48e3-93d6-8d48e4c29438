package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandSGMeterEnergy3P extends DataBackedBand
{
	public BandSGMeterEnergy3P()
	{
		super(BandForge.<BandSGMeterEnergy3P>getMetadataFor(BandSGMeterEnergy3P.class));
	}



	public BandSGMeterEnergy3P(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterEnergy3P>getMetadataFor(BandSGMeterEnergy3P.class));
	}

	public BandSGMeterEnergy3P(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterEnergy3P>getMetadataFor(BandSGMeterEnergy3P.class));
	}


	
	public final WattHour getActiveEnergySellL1()
	{
		return GetU32(0, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyL1()
	{
		return GetU32(4, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergySellL2()
	{
		return GetU32(8, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyL2()
	{
		return GetU32(12, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergySellL3()
	{
		return GetU32(16, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyL3()
	{
		return GetU32(20, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergySellTotal()
	{
		return GetU32(24, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyTotal()
	{
		return GetU32(28, WattHour.Hecto);
	}
}
