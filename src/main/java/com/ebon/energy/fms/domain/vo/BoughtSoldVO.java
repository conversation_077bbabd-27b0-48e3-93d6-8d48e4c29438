package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.util.Objects;

@Data
public class BoughtSoldVO {
    private String id;
    private SevenDayHistoryVO bought;
    private SevenDayHistoryVO sold;

    public BoughtSoldVO(String id, SevenDayHistoryVO bought, SevenDayHistoryVO sold) {
        this.id = Objects.requireNonNull(id, "id must not be null");
        this.bought = Objects.requireNonNull(bought, "bought must not be null");
        this.sold = Objects.requireNonNull(sold, "sold must not be null");
    }

}