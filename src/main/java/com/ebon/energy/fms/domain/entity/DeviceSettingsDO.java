package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("DeviceSettings")
public class DeviceSettingsDO {

    @TableField(value = "Id")
    private String id;
    @TableField("DeviceId")
    private String deviceId;
    @TableField("ReportedDeviceSettings")
    private String reportedDeviceSettings;
    @TableField("DesiredDeviceSettings")
    private String desiredDeviceSettings;
    @TableField("DeviceSettingsIntent")
    private String deviceSettingsIntent;
    @TableField("ReportedLastUpdated")
    private Timestamp reportedLastUpdated;
    @TableField("DesiredDeviceSettingsPatch")
    private String desiredDeviceSettingsPatch;
    @TableField("DesiredVersion")
    private Integer desiredVersion;
    @TableField("LastModifiedById")
    private String lastModifiedById;
    @TableField("LastModifiedOnUtc")
    private Timestamp lastModifiedOnUtc;
    @TableField("HasBeenApplied")
    private Boolean hasBeenApplied;
}
