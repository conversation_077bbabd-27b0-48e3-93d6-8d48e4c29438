package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.SmartRelayMode;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

@Data
@AllArgsConstructor
public class SchedulesForCtrlPageDto {

    @JsonProperty("InverterOperation")
    private InverterOperationViewModel inverterOperation;

    @JsonProperty("RelayOperation")
    private List<RelayOperationViewModel> relayOperation;

    @JsonProperty("SmartRelayOperation")
    private SmartRelayOperationViewModel smartRelayOperation;
     @Data

     public static class RelayOperationViewModel {
        @JsonProperty("Id")
        private String id;

        @JsonProperty("RelayNumber")
        private Integer relayNumber;

        @JsonProperty("RelayName")
        private String relayName;

        @JsonProperty("Mode")
        private RelayModes mode;

        @JsonProperty("Schedules")
        private List<GenericScheduleItemViewModel> schedules;

        @JsonProperty("Installed")
        private Boolean installed;

        @JsonProperty("BaseState")
        private Boolean baseState;

        @JsonProperty("Override")
        private Boolean overrideValue;
    }

    @Data
    public static class SmartRelayOperationViewModel {
        @JsonProperty("Mode")
        private SmartRelayMode mode;

        @JsonProperty("Schedules")
        private List<SmartRelayScheduleItemViewModel> schedules;
    }

    public enum RelayModes {
        @JsonProperty("Schedule")
        Schedule(0),
        @JsonProperty("On")
        On(1),
        @JsonProperty("Off")
        Off(2),
        @JsonProperty("Auto")
        Auto(3);

        private final int value;
        RelayModes(int value) { this.value = value; }
        public int getValue() { return value; }
    }

    @Data
    public static class GenericScheduleItemViewModel {
        @JsonProperty("Id")
        private String id;

        @JsonProperty("StartTime")
        private OffsetDateTime startTime;

        @JsonProperty("EndTime")
        private OffsetDateTime endTime;

        @JsonProperty("ScheduleDays")
        private ScheduleDays scheduleDays;

        @JsonProperty("Timezone")
        private String timezone ;
    }

    @Data
    public static class InverterScheduleItemViewModel extends GenericScheduleItemViewModel {
        @JsonProperty("Mode")
        private InverterScheduleOperationModeEnum mode;

        @JsonProperty("PowerInWatts")
        private Long powerInWatts;

        @JsonProperty("Priority")
        private Integer priority;

        public InverterScheduleItemViewModel(
                String id,
                OffsetDateTime startTime,
                OffsetDateTime endTime,
                ScheduleDays scheduleDays,
                String timezone,
                InverterScheduleOperationModeEnum mode,
                Long powerInWatts,
                Integer priority) {
            super.setId(id);
            super.setStartTime(startTime);
            super.setEndTime(endTime);
            super.setScheduleDays(scheduleDays);
            super.setTimezone(timezone);
            this.mode = mode;
            this.powerInWatts = powerInWatts;
            this.priority = priority;
        }
    }

    @Data
    public static class SmartRelayScheduleItemViewModel extends GenericScheduleItemViewModel {
        // No extra fields
    }

    public enum InverterOperationModeEnum {
        @JsonProperty("NoMode")
        NoMode(0),
        @JsonProperty("Auto")
        Auto(1),
        @JsonProperty("ChargeBattery")
        ChargeBattery(2),
        @JsonProperty("DischargeBattery")
        DischargeBattery(3),
        @JsonProperty("ImportPower")
        ImportPower(4),
        @JsonProperty("ExportPower")
        ExportPower(5),
        @JsonProperty("Conserve")
        Conserve(6),
        @JsonProperty("Offgrid")
        Offgrid(7),
        @JsonProperty("Hibernate")
        Hibernate(8),
        @JsonProperty("BuyPower")
        BuyPower(9),
        @JsonProperty("SellPower")
        SellPower(10),
        @JsonProperty("ForceChargeBattery")
        ForceChargeBattery(11),
        @JsonProperty("ForceDischargeBattery")
        ForceDischargeBattery(12),
        @JsonProperty("Stop")
        Stop(255);

        private final int value;
        InverterOperationModeEnum(int value) { this.value = value; }
        public int getValue() { return value; }
    }



    @Getter
    public enum InverterScheduleOperationModeEnum {
        @JsonProperty("Auto")
        Auto(1, "Auto"),
        @JsonProperty("ChargeBattery")
        ChargeBattery(2, "Charge Battery"),
        @JsonProperty("DischargeBattery")
        DischargeBattery(3, "Discharge Battery"),
        @JsonProperty("ImportPower")
        ImportPower(4, "Import Power"),
        @JsonProperty("ExportPower")
        ExportPower(5, "Export Power"),
        @JsonProperty("Conserve")
        Conserve(6, "Conserve"),
        @JsonProperty("Offgrid")
        Offgrid(7, "Off Grid"),
        @JsonProperty("Hibernate")
        Hibernate(8, "Hibernate"),
        @JsonProperty("BuyPower")
        BuyPower(9, "Buy Power"),
        @JsonProperty("SellPower")
        SellPower(10, "Sell Power"),
        @JsonProperty("ForceChargeBattery")
        ForceChargeBattery(11, "ForceChargeBattery"),
        @JsonProperty("ForceDischargeBattery")
        ForceDischargeBattery(12, "Force Discharge Battery");

        private final int code;
        private final String displayName;

        InverterScheduleOperationModeEnum(int code, String displayName) {
            this.code = code;
            this.displayName = displayName;
        }
    }


    public enum InverterOperationTypeEnum {
        @JsonProperty("Set")
        Set,
        @JsonProperty("Schedule")
        Schedule,
        @JsonProperty("Optimise")
        Optimise
    }

    @Data
    @AllArgsConstructor
    public static class InverterOperationViewModel {
        @JsonProperty("Type")
        private InverterOperationTypeEnum type;

        @JsonProperty("Mode")
        private InverterOperationModeEnum mode;

        @JsonProperty("PowerInWatts")
        private Long powerInWatts;

        @JsonProperty("Schedules")
        private List<InverterScheduleItemViewModel> schedules;
    }


}
