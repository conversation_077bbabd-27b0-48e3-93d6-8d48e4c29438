package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.DredMode;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;

import static com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility.*;

@JsonAutoDetect(
        fieldVisibility = ANY,
        getterVisibility = NONE,
        setterVisibility = NONE
)
public class DredTelemetry {

    private DredMode Mode = DredMode.NoMode;
    private boolean Subscribed;

    public DredTelemetry() {
        // Default constructor for JSON deserialization
    }

    public DredMode getMode() {
        return Mode;
    }

    public void setMode(DredMode Mode) {
        this.Mode = Mode;
    }

    public boolean getSubscribed() {
        return Subscribed;
    }

    public void setSubscribed(boolean Subscribed) {
        this.Subscribed = Subscribed;
    }

    @Override
    public String toString() {
        return "DredTelemetry{" +
                "Mode=" + Mode +
                ", Subscribed=" + Subscribed +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DredTelemetry that = (DredTelemetry) o;

        if (Subscribed != that.Subscribed) return false;
        return Mode == that.Mode;
    }

    @Override
    public int hashCode() {
        int result = Mode != null ? Mode.hashCode() : 0;
        result = 31 * result + (Subscribed ? 1 : 0);
        return result;
    }
}