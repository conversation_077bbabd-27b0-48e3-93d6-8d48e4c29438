package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class DeviceControlVO {
    public static final String LocalDateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(LocalDateTimeFormat);

    @JsonProperty(index = 1)
    private int DeviceId;
    @JsonProperty(index = 2)
    private String DeviceSerialNumber;
    @JsonProperty(index = 4)
    private String DeviceModel;
    @JsonProperty(index = 5)
    private String DateTimeUpdated;
    @JsonProperty(index = 6)
    private InverterOperationVO InverterOperation;
    @JsonProperty(index = 7)
    private List<DispatchableLoadSettingVO> DispatchableLoads;
    @JsonProperty(index = 10)
    private boolean IsOnDRED;
    @JsonProperty(index = 11)
    private Integer GroupEventId;
    @JsonIgnore
    private Boolean IsRoss2OrAbove;

    public DeviceControlVO() {
        this.DateTimeUpdated = "";
        this.DeviceId = 0;
        this.DeviceSerialNumber = "";
        this.DeviceModel = "";
        this.InverterOperation = new InverterOperationVO();
        this.DispatchableLoads = new ArrayList<>();
        this.IsOnDRED = false;
    }

    public int getDeviceId() {
        return DeviceId;
    }

    public void setDeviceId(int deviceId) {
        DeviceId = deviceId;
    }

    public String getDeviceSerialNumber() {
        return DeviceSerialNumber;
    }

    public void setDeviceSerialNumber(String deviceSerialNumber) {
        DeviceSerialNumber = deviceSerialNumber;
    }

    public String getDeviceModel() {
        return DeviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        DeviceModel = deviceModel;
    }

    public String getDateTimeUpdated() {
        return DateTimeUpdated;
    }

    public void setDateTimeUpdated(String dateTimeUpdated) {
        DateTimeUpdated = dateTimeUpdated;
    }

    public InverterOperationVO getInverterOperation() {
        return InverterOperation;
    }

    public void setInverterOperation(InverterOperationVO inverterOperation) {
        InverterOperation = inverterOperation;
    }

    public List<DispatchableLoadSettingVO> getDispatchableLoads() {
        return DispatchableLoads;
    }

    public void setDispatchableLoads(List<DispatchableLoadSettingVO> dispatchableLoads) {
        DispatchableLoads = dispatchableLoads;
    }

    public boolean isOnDRED() {
        return IsOnDRED;
    }

    public void setOnDRED(boolean onDRED) {
        IsOnDRED = onDRED;
    }

    public Integer getGroupEventId() {
        return GroupEventId;
    }

    public void setGroupEventId(Integer groupEventId) {
        GroupEventId = groupEventId;
    }

    public Boolean getIsRoss2OrAbove() {
        return IsRoss2OrAbove;
    }

    public void setIsRoss2OrAbove(Boolean isRoss2OrAbove) {
        IsRoss2OrAbove = isRoss2OrAbove;
    }

    public void updateInverterOperationTo(InverterOperationVO newInverterOperation) {
        this.InverterOperation = newInverterOperation;
    }

    public Date getDateTimeUpdatedAsDate() {
        if (DateTimeUpdated == null) {
            return null;
        }
        try {
            return DATE_FORMAT.parse(DateTimeUpdated);
        } catch (ParseException e) {
            return null;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        DeviceControlVO other = (DeviceControlVO) obj;
        if (DeviceId != other.DeviceId) {
            return false;
        }
        if (!Objects.equals(DeviceSerialNumber, other.DeviceSerialNumber)) {
            return false;
        }
        if (!Objects.equals(DeviceModel, other.DeviceModel)) {
            return false;
        }
        if (!Objects.equals(DateTimeUpdated, other.DateTimeUpdated)) {
            return false;
        }
        if (!Objects.equals(InverterOperation.getMode(), other.InverterOperation.getMode())) {
            return false;
        }
        if (!Objects.equals(InverterOperation.getType(), other.InverterOperation.getType())) {
            return false;
        }
        if (InverterOperation.getPowerInWatts() != other.InverterOperation.getPowerInWatts()) {
            return false;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            String originalSchedules = mapper.writeValueAsString(InverterOperation.getSchedules());
            String compareToSchedules = mapper.writeValueAsString(other.InverterOperation.getSchedules());
            if (!Objects.equals(originalSchedules, compareToSchedules)) {
                return false;
            }
            String originalLoads = mapper.writeValueAsString(DispatchableLoads);
            String compareToLoads = mapper.writeValueAsString(other.DispatchableLoads);
            if (!Objects.equals(originalLoads, compareToLoads)) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        if (IsOnDRED != other.IsOnDRED) {
            return false;
        }
        return true;
    }

    public String Validate() {
        if (getDateTimeUpdatedAsDate() == null) {
            return String.format("Error parsing field 'DateTimeUpdated'. Incorrect value: '%s'. Example value: '2017-12-07 14:35:53'.", DateTimeUpdated);
        }
        List<InverterScheduleVO> schedules = InverterOperation.getSchedules();
        if (schedules != null) {
            for (InverterScheduleVO schedule : schedules) {
                String error = schedule.validate();
                if (error != null &&!error.isEmpty()) {
                    return error;
                }
            }
        }
        return null;
    }

    public boolean Equals(DeviceControlVO other) {
        return other != null &&
                DeviceId == other.DeviceId &&
                Objects.equals(DeviceSerialNumber, other.DeviceSerialNumber) &&
                Objects.equals(DeviceModel, other.DeviceModel) &&
                Objects.equals(DateTimeUpdated, other.DateTimeUpdated) &&
                Objects.equals(InverterOperation, other.InverterOperation) &&
                Objects.equals(DispatchableLoads, other.DispatchableLoads) &&
                IsOnDRED == other.IsOnDRED &&
                Objects.equals(GroupEventId, other.GroupEventId);
    }
}