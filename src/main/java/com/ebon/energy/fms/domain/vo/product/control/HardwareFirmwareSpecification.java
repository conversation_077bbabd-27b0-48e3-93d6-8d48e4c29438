package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.BoardType;
import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.domain.po.HardwareSpecification;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.VoltAmpsReactive;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.grid.GridProfileSettingIds;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.math.BigDecimal;

@Data
public class HardwareFirmwareSpecification extends HardwareSpecification {

    @JsonProperty("ArmVersion")
    private int armVersion;

    @JsonProperty("MaximumExportPowerInWatts")
    private int maximumExportPowerInWatts;

    @JsonProperty("MinimumExportPowerInWatts")
    private int minimumExportPowerInWatts;

    @JsonProperty("MaxInverterExportPowerPlateRatingW")
    private int maxInverterExportPowerPlateRatingW;

    @JsonProperty("MaxInverterImportPowerPlateRatingW")
    private int maxInverterImportPowerPlateRatingW;

    @JsonProperty("MinSiteExportPowerSoftW")
    private Watt minSiteExportPowerSoftW;

    @JsonProperty("MinimumSiteExportWatts")
    private int minimumSiteExportWatts;

    @JsonProperty("MaximumSiteExportWatts")
    private int maximumSiteExportWatts;

    @JsonProperty("MinSiteExportPowerHardW")
    private Watt minSiteExportPowerHardW;

    @JsonProperty("MaxSiteExportPowerHardW")
    private Watt maxSiteExportPowerHardW;

    @JsonProperty("MinSiteGenerationPowerSoftVA")
    private VoltAmps minSiteGenerationPowerSoftVA;

    @JsonProperty("MaxSiteGenerationPowerSoftVA")
    private VoltAmps maxSiteGenerationPowerSoftVA;

    @JsonProperty("MinSiteGenerationPowerHardVA")
    private VoltAmps minSiteGenerationPowerHardVA;

    @JsonProperty("MaxSiteGenerationPowerHardVA")
    private VoltAmps maxSiteGenerationPowerHardVA;

    @JsonProperty("MaxLaggingPowerFactor")
    private BigDecimal maxLaggingPowerFactor;

    @JsonProperty("MaxLeadingPowerFactor")
    private BigDecimal maxLeadingPowerFactor;

    @JsonProperty("MinOnGridSoCLimit")
    private int minOnGridSoCLimit;

    @JsonProperty("MaxOnGridSoCLimit")
    private int maxOnGridSoCLimit;

    @JsonProperty("MinOffGridSoCLimit")
    private int minOffGridSoCLimit;

    @JsonProperty("MaxOffGridSoCLimit")
    private int maxOffGridSoCLimit;

    @JsonProperty("MinSmartLoadControlTriggerPowerW")
    private Watt minSmartLoadControlTriggerPowerW;

    @JsonProperty("MaxSmartLoadControlTriggerPowerW")
    private Watt maxSmartLoadControlTriggerPowerW;

    @JsonProperty("MaximumBatteryChargePowerInWatts")
    private int maximumBatteryChargePowerInWatts;

    @JsonProperty("MaximumBatteryDischargePowerInWatts")
    private int maximumBatteryDischargePowerInWatts;

    @JsonProperty("MinimumBatteryCount")
    private int minimumBatteryCount;

    @JsonProperty("MaximumBatteryCount")
    private int maximumBatteryCount;

    @JsonProperty("MinimumMaxChargeCurrentAmpere")
    private int minimumMaxChargeCurrentAmpere;

    @JsonProperty("MaximumMaxChargeCurrentAmpere")
    private int maximumMaxChargeCurrentAmpere;

    @JsonProperty("MinimumMaxDischargeCurrentAmpere")
    private int minimumMaxDischargeCurrentAmpere;

    @JsonProperty("MaximumMaxDischargeCurrentAmpere")
    private int maximumMaxDischargeCurrentAmpere;

    @JsonProperty("MinimumWorkPowerModeWatts")
    private int minimumWorkPowerModeWatts;

    @JsonProperty("MaximumWorkPowerModeWatts")
    private int maximumWorkPowerModeWatts;

    @JsonProperty("FirstRelayConfigurable")
    private boolean firstRelayConfigurable;

    @JsonProperty("HasShadowScanFeature")
    private boolean hasShadowScanFeature;

    @JsonProperty("CanToggleMeasuringThirdPartyInverter")
    private boolean canToggleMeasuringThirdPartyInverter;

    @JsonProperty("SupportsConnectedPV")
    private boolean supportsConnectedPV;

    @JsonProperty("SupportsDredSubscribed")
    private boolean supportsDredSubscribed;

    // 构造器重载（仿C#）
    public HardwareFirmwareSpecification(
            HardwareFamilyEnum hardwareFamily,
            HardwareModelEnum hardwareModel
    ) {
        super(hardwareFamily, hardwareModel);
    }

    public HardwareFirmwareSpecification(
            HardwareModelEnum hardwareModel,
            int armVersion,
            int maximumExportPowerInWatts,
            int minimumExportPowerInWatts,
            int maxInverterExportPowerPlateRatingW,
            int maxInverterImportPowerPlateRatingW,
            int maximumBatteryChargePowerInWatts,
            int maximumBatteryDischargePowerInWatts,
            int minimumBatteryCount,
            int maximumBatteryCount,
            int minimumMaxChargeCurrentAmpere,
            int maximumMaxChargeCurrentAmpere,
            int minimumMaxDischargeCurrentAmpere,
            int maximumMaxDischargeCurrentAmpere,
            int minimumWorkPowerModeWatts,
            int maximumWorkPowerModeWatts,
            Watt minSiteExportPowerSoftW,
            int minimumSiteExportWatts,
            int maximumSiteExportWatts,
            Watt minSiteExportPowerHardW,
            Watt maxSiteExportPowerHardW,
            VoltAmps minSiteGenerationPowerSoftVA,
            VoltAmps maxSiteGenerationPowerSoftVA,
            VoltAmps minSiteGenerationPowerHardVA,
            VoltAmps maxSiteGenerationPowerHardVA,
            BigDecimal maxLaggingPowerFactor,
            BigDecimal maxLeadingPowerFactor,
            int minOnGridSoCLimit,
            int maxOnGridSoCLimit,
            int minOffGridSoCLimit,
            int maxOffGridSoCLimit,
            Watt minSmartLoadControlTriggerPowerW,
            Watt maxSmartLoadControlTriggerPowerW,
            boolean firstRelayConfigurable,
            boolean hasShadowScanFeature,
            boolean canToggleMeasuringThirdPartyInverter,
            HardwareFamilyEnum hardwareFamily,
            boolean supportsConnectedPV,
            boolean supportsDredSubscribed
    ) {
        super(hardwareFamily, hardwareModel);
        this.armVersion = armVersion;
        this.maximumExportPowerInWatts = maximumExportPowerInWatts;
        this.minimumExportPowerInWatts = minimumExportPowerInWatts;
        this.maxInverterExportPowerPlateRatingW = maxInverterExportPowerPlateRatingW;
        this.maxInverterImportPowerPlateRatingW = maxInverterImportPowerPlateRatingW;
        this.maximumBatteryChargePowerInWatts = maximumBatteryChargePowerInWatts;
        this.maximumBatteryDischargePowerInWatts = maximumBatteryDischargePowerInWatts;
        this.minimumBatteryCount = minimumBatteryCount;
        this.maximumBatteryCount = maximumBatteryCount;
        this.minimumMaxChargeCurrentAmpere = minimumMaxChargeCurrentAmpere;
        this.maximumMaxChargeCurrentAmpere = maximumMaxChargeCurrentAmpere;
        this.minimumMaxDischargeCurrentAmpere = minimumMaxDischargeCurrentAmpere;
        this.maximumMaxDischargeCurrentAmpere = maximumMaxDischargeCurrentAmpere;
        this.minimumWorkPowerModeWatts = minimumWorkPowerModeWatts;
        this.maximumWorkPowerModeWatts = maximumWorkPowerModeWatts;
        this.minSiteExportPowerSoftW = minSiteExportPowerSoftW;
        this.minimumSiteExportWatts = minimumSiteExportWatts;
        this.maximumSiteExportWatts = maximumSiteExportWatts;
        this.minSiteExportPowerHardW = minSiteExportPowerHardW;
        this.maxSiteExportPowerHardW = maxSiteExportPowerHardW;
        this.minSiteGenerationPowerSoftVA = minSiteGenerationPowerSoftVA;
        this.maxSiteGenerationPowerSoftVA = maxSiteGenerationPowerSoftVA;
        this.minSiteGenerationPowerHardVA = minSiteGenerationPowerHardVA;
        this.maxSiteGenerationPowerHardVA = maxSiteGenerationPowerHardVA;
        this.maxLaggingPowerFactor = maxLaggingPowerFactor;
        this.maxLeadingPowerFactor = maxLeadingPowerFactor;
        this.minOnGridSoCLimit = minOnGridSoCLimit;
        this.maxOnGridSoCLimit = maxOnGridSoCLimit;
        this.minOffGridSoCLimit = minOffGridSoCLimit;
        this.maxOffGridSoCLimit = maxOffGridSoCLimit;
        this.minSmartLoadControlTriggerPowerW = minSmartLoadControlTriggerPowerW;
        this.maxSmartLoadControlTriggerPowerW = maxSmartLoadControlTriggerPowerW;
        this.firstRelayConfigurable = firstRelayConfigurable;
        this.hasShadowScanFeature = hasShadowScanFeature;
        this.canToggleMeasuringThirdPartyInverter = canToggleMeasuringThirdPartyInverter;
        this.supportsConnectedPV = supportsConnectedPV;
        this.supportsDredSubscribed = supportsDredSubscribed;
    }

    // 业务方法实现（部分伪代码，需结合实际Java类型和业务）
    public HardwareModelEnum getProductModel() {
        return getHardwareModel();
    }

    public int getMaximumGroupDisplayExportPowerInWatts() {
        return 4000;
    }

    public boolean isSettingsV2() {
        var settingType = getHardwareModel().getSettingsTypeEnum();
        if (settingType == null) {
            throw new RuntimeException("SettingsType missing for " + getHardwareModel());
        }
        return settingType.isSettingsV2();
    }

    public boolean isGEN3() {
        var boardType = getHardwareModel().getBoardType();
        if (boardType == null) {
            throw new RuntimeException("ModelAttribute missing for " + getHardwareModel());
        }
        return boardType == BoardType.EMSPro;
    }

    public GridProfileSettingIds getGridProfileSettingIds() {
        if (isGEN3()) {
            return GridProfileSettingIds.gen3GridProfileSettingIds();
        } else if (isSettingsV2()) {
            return GridProfileSettingIds.siGridProfileSettingIds();
        } else {
            return GridProfileSettingIds.rossGridProfileSettingIds();
        }
    }

    public VoltAmpsReactive getMaxLaggingReactivePower() {
        // 伪代码：需根据Java实现的单位转换
        double pf = maxLaggingPowerFactor.doubleValue();
        double va = maxSiteGenerationPowerSoftVA.asDecimal(VoltAmps.Unit).doubleValue();
        double result = Math.sqrt((1 - Math.pow(pf, 2)) * Math.pow(va, 2));
        return new VoltAmpsReactive(result);
    }

    public VoltAmpsReactive getMaxLeadingReactivePower() {
        // 伪代码：需根据Java实现的单位转换
        double pf = maxLeadingPowerFactor.doubleValue();
        double va = maxSiteGenerationPowerSoftVA.asDecimal(VoltAmps.Unit).doubleValue();
        double result = Math.sqrt((1 - Math.pow(pf, 2)) * Math.pow(va, 2));
        return new VoltAmpsReactive(result);
    }
}
