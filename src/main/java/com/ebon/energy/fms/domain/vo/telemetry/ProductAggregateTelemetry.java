package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.WattHour;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class ProductAggregateTelemetry {
    private Watt ACLoadDayMaxP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant ACLoadDayMaxPTimestampUtc;

    private Watt BackupLoadDayMaxP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant BackupLoadDayMaxPTimestampUtc;

    private Watt GridDayMaxInputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant GridDayMaxInputPTimestampUtc;

    private Watt GridDayMaxOuputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant GridDayMaxOuputPTimestampUtc;

    private Watt BatteryMeasurementsDayMaxInputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant BatteryMeasurementsDayMaxInputPTimestampUtc;

    private Watt BatteryMeasurementsDayMaxOutputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant BatteryMeasurementsDayMaxOutputPTimestampUtc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant GridDayMaxOuputPTimestampLocal;

    private WattHour ETotalSellForToday;
    private WattHour ETotalBuyForToday;
    private WattHour ETotalLoadForToday;
    private WattHour EPvTotalForToday;
    private WattHour EGridTotalForToday;
    private List<ProductBatteryAggregateTelemetry> Battery;
    private String LogMessage;
    private WattHour ETotalLoad;
    private WattHour ETotalBatteryChargeForToday;
    private WattHour ETotalBatteryDischargeForToday;

   /* public ProductAggregateTelemetry(
            Watt aCLoadDayMaxP,
            Instant aCLoadDayMaxPTimestampUtc,
            Watt backupLoadDayMaxP,
            Instant backupLoadDayMaxPTimestampUtc,
            Watt gridDayMaxInputP,
            Instant gridDayMaxInputPTimestampUtc,
            Watt gridDayMaxOuputP,
            Instant gridDayMaxOuputPTimestampUtc,
            Watt batteryMeasurementsDayMaxInputP,
            Instant batteryMeasurementsDayMaxInputPTimestampUtc,
            Watt batteryMeasurementsDayMaxOutputP,
            Instant batteryMeasurementsDayMaxOutputPTimestampUtc,
            WattHour eTotalSellForToday,
            WattHour eTotalBuyForToday,
            WattHour eTotalLoadForToday,
            WattHour ePvTotalForToday,
            WattHour eGridTotalForToday,
            List<ProductBatteryAggregateTelemetry> battery,
            String logMessage,
            WattHour eTotalLoad,
            WattHour eTotalBatteryChargeForToday,
            WattHour eTotalBatteryDischargeForToday) {

        this.ACLoadDayMaxP = aCLoadDayMaxP;
        this.ACLoadDayMaxPTimestampUtc = aCLoadDayMaxPTimestampUtc;
        this.BackupLoadDayMaxP = backupLoadDayMaxP;
        this.BackupLoadDayMaxPTimestampUtc = backupLoadDayMaxPTimestampUtc;
        this.GridDayMaxInputP = gridDayMaxInputP;
        this.GridDayMaxInputPTimestampUtc = gridDayMaxInputPTimestampUtc;
        this.GridDayMaxOuputP = gridDayMaxOuputP;
        this.GridDayMaxOuputPTimestampUtc = gridDayMaxOuputPTimestampUtc;
        this.BatteryMeasurementsDayMaxInputP = batteryMeasurementsDayMaxInputP;
        this.BatteryMeasurementsDayMaxInputPTimestampUtc = batteryMeasurementsDayMaxInputPTimestampUtc;
        this.BatteryMeasurementsDayMaxOutputP = batteryMeasurementsDayMaxOutputP;
        this.BatteryMeasurementsDayMaxOutputPTimestampUtc = batteryMeasurementsDayMaxOutputPTimestampUtc;
        this.ETotalSellForToday = eTotalSellForToday;
        this.ETotalBuyForToday = eTotalBuyForToday;
        this.ETotalLoadForToday = eTotalLoadForToday;
        this.EPvTotalForToday = ePvTotalForToday;
        this.EGridTotalForToday = eGridTotalForToday;
        this.Battery = battery;
        this.LogMessage = logMessage;
        this.ETotalLoad = eTotalLoad;
        this.ETotalBatteryChargeForToday = eTotalBatteryChargeForToday;
        this.ETotalBatteryDischargeForToday = eTotalBatteryDischargeForToday;
    }*/

   /* public static ProductAggregateTelemetry noSerialNumber() {
        return new ProductAggregateTelemetry(
                null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null, null, null,
                null, null, null, null, null);
    }*/

}