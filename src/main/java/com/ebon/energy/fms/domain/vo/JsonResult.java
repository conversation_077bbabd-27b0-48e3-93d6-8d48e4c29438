package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.BaseErrorCodeEnum;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

public class JsonResult<T> implements Serializable {
    private String code;
    private String message;
    private T data;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Object> errorArgs;

    public JsonResult() {
    }

    public JsonResult(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public JsonResult(String code, String message, T data, List<Object> errorArgs) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.errorArgs = errorArgs;
    }

    public static <T> JsonResult<T> buildSuccess() {
        return new JsonResult(CommonErrorCodeEnum.SUCCESS.getErrorCode(), CommonErrorCodeEnum.SUCCESS.getErrorMsg(), (Object)null);
    }

    public static <T> JsonResult<T> buildSuccess(T data) {
        return new JsonResult(CommonErrorCodeEnum.SUCCESS.getErrorCode(), CommonErrorCodeEnum.SUCCESS.getErrorMsg(), data);
    }

    public static <T> JsonResult<T> buildError(String errorMsg) {
        return new JsonResult(CommonErrorCodeEnum.SYSTEM_UNKNOWN_ERROR.getErrorCode(), errorMsg, (Object)null);
    }

    public static <T> JsonResult<T> buildError(String errorMsg, List<Object> errorArgs) {
        return new JsonResult(CommonErrorCodeEnum.SYSTEM_UNKNOWN_ERROR.getErrorCode(), errorMsg, (Object)null, errorArgs);
    }

    public static <T> JsonResult<T> buildError(String errorCode, String errorMsg) {
        return new JsonResult(errorCode, errorMsg, (Object)null);
    }

    public static <T> JsonResult<T> buildError(String errorCode, String errorMsg, List<Object> errorArgs) {
        return new JsonResult(errorCode, errorMsg, (Object)null, errorArgs);
    }

    public static <T> JsonResult<T> buildError(String errorCode, String errorMsg, T data) {
        return new JsonResult(errorCode, errorMsg, data);
    }

    public static <T> JsonResult<T> buildError(BaseErrorCodeEnum baseErrorCodeEnum) {
        return new JsonResult(baseErrorCodeEnum.getErrorCode(), baseErrorCodeEnum.getErrorMsg(), (Object)null);
    }

    public static <T> JsonResult<T> buildError(BaseErrorCodeEnum baseErrorCodeEnum, List<Object> errorArgs) {
        return new JsonResult(baseErrorCodeEnum.getErrorCode(), baseErrorCodeEnum.getErrorMsg(), (Object)null, errorArgs);
    }

    public boolean onSuccess() {
        return CommonErrorCodeEnum.SUCCESS.getErrorCode().equals(this.getCode());
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public List<Object> getErrorArgs() {
        return this.errorArgs;
    }

    public void setErrorArgs(List<Object> errorArgs) {
        this.errorArgs = errorArgs;
    }
}