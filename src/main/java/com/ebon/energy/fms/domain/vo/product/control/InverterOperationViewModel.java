package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.InverterOperationModeEnum;
import com.ebon.energy.fms.common.enums.InverterOperationTypeEnum;
import com.ebon.energy.fms.domain.vo.product.control.invert.InverterScheduleItemViewModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * View model for inverter operation
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InverterOperationViewModel {
    @JsonProperty("PowerInWatts")
    private long powerInWatts;
    @JsonProperty("Schedules")
    private List<InverterScheduleItemViewModel> schedules = new ArrayList<>();
    @JsonProperty("Type")
    private String type;
    @JsonProperty("Mode")
    private String mode;
    @JsonProperty("InverterTimeZone")
    private String inverterTimeZone;
}
