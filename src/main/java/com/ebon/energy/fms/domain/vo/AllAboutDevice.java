package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.Phase;
import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import lombok.Data;

import java.time.LocalDate;

@Data
public class AllAboutDevice extends BaseAboutDevice {
  
    private LocalDate todaysDateInLocalTime;
    private SiteDeviceVO firstSiteDevice;
    private InstallationSpecification specification;
    private Boolean hasSolar;
    private Boolean hasBatteries;
    private LocalDate supportsLoadContributorsSince;
    private WattHour maximumPossiblePVOutput;
    private Boolean isBatteryMismatchProtectionEnabled;
    private WattHour availableBatteryEnergyOverMinOffgridSoCWh;
}