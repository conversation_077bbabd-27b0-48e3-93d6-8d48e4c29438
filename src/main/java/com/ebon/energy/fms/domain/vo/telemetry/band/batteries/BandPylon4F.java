package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandPylon4F extends DataBackedBand
{
	public BandPylon4F()
	{
		super(BandForge.<BandPylon4F>getMetadataFor(BandPylon4F.class));
	}



	public BandPylon4F(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon4F>getMetadataFor(BandPylon4F.class));
	}

	public BandPylon4F(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon4F>getMetadataFor(BandPylon4F.class));
	}



	public final byte getCommVersion()
	{
		return GetU8(0);
	}
}
