package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户实体类
 */
@Data
@Accessors(chain = true)
@TableName("FleetMonitoringUsers")
public class FleetMonitoringUsersDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签值
     */
    @TableField(value = "Email")
    private String email;

}