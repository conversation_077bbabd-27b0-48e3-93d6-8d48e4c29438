package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;

import java.math.*;

public class BandHVP1 extends DataBackedBand
{
	public BandHVP1()
	{
		super(BandForge.<BandHVP1>getMetadataFor(BandHVP1.class));
	}

	public BandHVP1(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVP1>getMetadataFor(BandHVP1.class));
	}

	public BandHVP1(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVP1>getMetadataFor(BandHVP1.class));
	}


	
	public final Object getBasicStatus()
	{
		return PylonHVBasicStatus.parse(GetU16(0));
	}

	
	public final Object getProtectInformation()
	{
		return PylonHVProtectionStatus.parse(GetU16(2));
	}

	
	public final Object getAlarmInformation()
	{
		return PylonHVAlarmStatus.parse(GetU16(4));
	}

	
	public final Volt getTotalVoltage()
	{
		return GetU16(6, Volt.Deci);
	}

	
	public final Ampere getCurrent()
	{
		return GetS32(8, Ampere.Centi);
	}

	
	public final Celsius getTemperature()
	{
		return GetS16(12, Celsius.Deci);
	}

	
	public final BigDecimal getSOC()
	{
		return new BigDecimal(GetU16(14)).multiply(Percentage._1);
	}

	


	public final int getCycleTime() { return GetU16(16); }

	
	public final Volt getMaxChargeVoltageOfPile()
	{
		return GetU16(18, Volt.Deci);
	}

	
	public final Ampere getMaxChargeCurrent()
	{
		return GetU32(20, Ampere.Centi);
	}

	
	public final Volt getMinDischargeVoltageOfPile()
	{
		return GetU16(24, Volt.Deci);
	}

	
	public final Ampere getMaxDischargeCurrent()
	{
		return GetS32(26, Ampere.Centi);
	}

	


	public final int getSwitchingValue() { return GetU16(30); }

	
	public final Volt getMaxCellVoltage()
	{
		return GetU16(32, Volt.Milli);
	}

	
	public final Volt getMinCellVoltage()
	{
		return GetU16(34, Volt.Milli);
	}

	


	public final int getSerialNumberOfMaxCellVoltage() { return GetU16(36); }

	


	public final int getSerialNumberOfMinCellVoltage() { return GetU16(38); }

	
	public final Celsius getMaxCellTemperature()
	{
		return GetS16(40, Celsius.Deci);
	}

	
	public final Celsius getMinCellTemperature()
	{
		return GetS16(42, Celsius.Deci);
	}

	


	public final int getSerialNumberOfMaxCellTemp() { return GetU16(44); }

	


	public final int getSerialNumberonMinCellTemp() { return GetU16(46); }

	
	public final Volt getMaxModuleVoltage()
	{
		return GetU16(48, Volt.Centi);
	}

	
	public final Volt getMinModuleVoltage()
	{
		return GetU16(50, Volt.Centi);
	}

	


	public final int getSerialNumberOfMaxModuleVoltage() { return GetU16(52); }

	


	public final int getSerialNumberOfMinModuleVoltage() { return GetU16(54); }

	
	public final Celsius getMaxModuleTemperature()
	{
		return GetS16(56, Celsius.Deci);
	}

	
	public final Celsius getMinModuleTemperature()
	{
		return GetS16(58, Celsius.Deci);
	}

	


	public final int getSerialNumberOfMaxModuleTemp() { return GetU16(60); }

	


	public final int getSerialNumberOfMinModuleTemp() { return GetU16(62); }

	
	public final BigDecimal getSOH()
	{
		return new BigDecimal(GetU16(64)).multiply(Percentage._1);
	}

	
	public final WattHour getRemainCapacity()
	{
		return GetU32(66, WattHour.Unit);
	}

	
	public final WattHour getChargeCapacityOfpile()
	{
		return GetU32(70, WattHour.Unit);
	}

	
	public final WattHour getDischargeCapacityOfpile()
	{
		return GetU32(74, WattHour.Unit);
	}

	
	public final WattHour getDailyAccumulateChargeCapacity()
	{
		return GetU32(78, WattHour.Unit);
	}

	
	public final WattHour getDailyAccumulateDischargeCapacity()
	{
		return GetU32(82, WattHour.Unit);
	}

	
	public final WattHour getHistoryAccumulateChargeCapacity()
	{
		return GetU32(86, WattHour.Kilo);
	}

	
	public final WattHour getHistoryAccumulateDischargeCapacity()
	{
		return GetU32(90, WattHour.Kilo);
	}

	
	public final boolean getForceChargeRequestMark()
	{
		return GetBool((int) GetU16(94), 1, 0, false);
	}

	
	public final boolean getBalanceChargeRequestMark1Request()
	{
		return GetBool((int) GetU16(96), 1, 0, false);
	}

	


	public final byte[] getReserved0x1431()
	{
		return GetRaw(98, 2);
	}

	
	public final Object getErrorCode1()
	{
		return PylonHVErrorCode1.parse(GetU32(100));
	}

	
	public final String getErrorCode2()
	{
		return String.valueOf(GetU32(104));
	}



	public final int getModuleNumberInSeries() { return GetU16(108); }

	


	public final int getCellNumberInSeries() { return GetU16(110); }

	
	public final boolean getChargeForbiddenMark()
	{
		return GetBool((int) GetU16(112), 1, 0, false);
	}

	
	public final boolean getDischargeForbiddenMark()
	{
		return GetBool((int) GetU16(114), 1, 0, false);
	}

	


	public final byte[] getReserved0x143A()
	{
		return GetRaw(116, 44);
	}

	
	public final String getSNCode()
	{
		return GetBufS(160, 32, StringProcessors.PylonHVDecode);
	}
}
