package com.ebon.energy.fms.domain.vo;

public class DesiredAndReportedVO {

    private RossDesiredSettingsVO desired;
    private RossReportedSettingsVO reported;

    public DesiredAndReportedVO() {
    }

    public DesiredAndReportedVO(
            RossReportedSettingsVO reported,
            RossDesiredSettingsVO desired) {
        this.reported = reported;
        this.desired = desired;
    }

    public static DesiredAndReportedVO getDefault() {
        return new DesiredAndReportedVO(
                new RossReportedSettingsVO(),
                new RossDesiredSettingsVO(null, BatteryManagerDesiredSettingsVO.getDefault())
        );
    }

    public RossDesiredSettingsVO getDesired() {
        return desired;
    }

    public void setDesired(RossDesiredSettingsVO desired) {
        this.desired = desired;
    }

    public RossReportedSettingsVO getReported() {
        return reported;
    }

    public void setReported(RossReportedSettingsVO reported) {
        this.reported = reported;
    }
}
