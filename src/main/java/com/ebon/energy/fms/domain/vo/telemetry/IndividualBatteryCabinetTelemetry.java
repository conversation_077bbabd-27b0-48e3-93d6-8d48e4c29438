package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class IndividualBatteryCabinetTelemetry {
    private BatteryCabinetBandsTelemetry Bands;
    private BatteryCabinetRawBandsTelemetry RawBands;

    public IndividualBatteryCabinetTelemetry() {}

    public IndividualBatteryCabinetTelemetry(
            BatteryCabinetBandsTelemetry Bands,
            BatteryCabinetRawBandsTelemetry RawBands)
    {
        this.Bands = Bands;
        this.RawBands = RawBands;
    }

    public BatteryCabinetBandsTelemetry getBands() {
        return Bands;
    }

    public BatteryCabinetRawBandsTelemetry getRawBands() {
        return RawBands;
    }
}