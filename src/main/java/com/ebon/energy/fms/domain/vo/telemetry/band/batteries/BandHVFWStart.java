package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandHVFWStart extends DataBackedBand
{
	public BandHVFWStart()
	{
		super(BandForge.<BandHVFWStart>getMetadataFor(BandHVFWStart.class));
	}



	public BandHVFWStart(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVFWStart>getMetadataFor(BandHVFWStart.class));
	}

	public BandHVFWStart(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVFWStart>getMetadataFor(BandHVFWStart.class));
	}
}
