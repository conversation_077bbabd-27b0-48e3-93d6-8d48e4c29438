package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;





public class BandETK extends DataBackedBand
{
	public BandETK()
	{
		super(BandForge.<BandETK>getMetadataFor(BandETK.class));
	}



	public BandETK(byte[] bytes)
	{
		super(bytes, BandForge.<BandETK>getMetadataFor(BandETK.class));
	}

	public BandETK(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETK>getMetadataFor(BandETK.class));
	}


	


	public final int getModbusAddress() { return GetU16(0); }

	
	public final String getManufacturer()
	{
		return GetBufS(2, 8, StringProcessors.GoodweDecode);
	}

	


	public final int getRS485ModbusBaudrate()
	{
		return GetU32(10);
	}
}
