package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.SettingsProtocol;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RossReportedSettings {

    public static final String PROTOCOL_SETTING_NAME = "protocol";
    public static final String SETTINGS_V2_SECTION_NAME = "v2";
    public static final String ID_OF_CORRESPONDING_DESIRED_SECTION_NAME = "idOfCorrespondingDesired";

    private SettingsProtocol protocol = SettingsProtocol.Ross;


    @JsonProperty("inverter")
    private InverterReportedSettings inverterSettings;

    @JsonProperty("v2")
    private SettingsV2Reported settingsV2;

    private boolean measuringThirdPartyInverter;

    @JsonProperty("batteryManager")
    private BatteryManagerReportedSettings batteryManager;

}
