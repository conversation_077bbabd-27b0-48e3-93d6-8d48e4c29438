package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public class SiteSettingsChangeRequestVO {
    @JsonProperty("modbusServerEnable")
    private Boolean ModbusServerEnable;

    @JsonProperty("inverterRole")
    private String InverterRole;

    @JsonProperty("executionInterval")
    private String ExecutionInterval;

    @JsonProperty("peers")
    private Map<String, PeerInfoVO> Peers;

    public Boolean isModbusServerEnable() {
        return ModbusServerEnable;
    }

    public void setModbusServerEnable(Boolean modbusServerEnable) {
        ModbusServerEnable = modbusServerEnable;
    }

    public String getInverterRole() {
        return InverterRole;
    }

    public void setInverterRole(String inverterRole) {
        InverterRole = inverterRole;
    }

    public String getExecutionInterval() {
        return ExecutionInterval;
    }

    public void setExecutionInterval(String executionInterval) {
        ExecutionInterval = executionInterval;
    }

    public Map<String, PeerInfoVO> getPeers() {
        return Peers;
    }

    public void setPeers(Map<String, PeerInfoVO> peers) {
        Peers = peers;
    }
}

class PeerInfoVO {
    private String Hostname;

    public String getHostname() {
        return Hostname;
    }

    public void setHostname(String hostname) {
        Hostname = hostname;
    }
}