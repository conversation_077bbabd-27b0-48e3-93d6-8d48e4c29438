package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class BatteryCommsUnitTelemetry {
    private List<TelemetryFrame> RawDataBands;
    private List<IDataFrame<DataBackedBand>> DataBands;
    private Map<String, BatteryAggregatesTelemetry> Aggregates;

    public BatteryCommsUnitTelemetry() {
        this.RawDataBands = Collections.emptyList();
        this.DataBands = Collections.emptyList();
        this.Aggregates = Collections.emptyMap();
    }

    public BatteryCommsUnitTelemetry(
            List<TelemetryFrame> RawDataBands,
            Map<String, BatteryAggregatesTelemetry> Aggregates) {

        this.RawDataBands = RawDataBands != null ?
                Collections.unmodifiableList(RawDataBands) :
                Collections.emptyList();
        this.DataBands = Collections.emptyList();
        this.Aggregates = Aggregates != null ?
                Collections.unmodifiableMap(Aggregates) :
                Collections.emptyMap();
    }

}