package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor()
public class EmsSettingsV2Desired {

    @JsonProperty("WifiSSID")
    private String wifiSsid;

    private String cellularControl;
    private String gridProfileId;
    private String gridProfileCorrelationId;
    private Boolean relay1Installed;
    private String relay1Name;
    private Integer bandIDUpdateRate;
    private Integer bandIRUpdateRate;
    private Integer bandEMUpdateRate;
    private Integer bandMDUpdateRate;
    private Integer bandM3UpdateRate;
    private Integer bandFWDUpdateRate;
    private Integer telemetryPeriodEndEpochS;
    private Integer telemetryRate;
    private Integer telemetryRateBleConnected;

    public static EmsSettingsV2Desired getDefault() {
        return EmsSettingsV2Desired.builder().build();
    }

    // equals and hashCode are generated by Lombok's @Data
}
