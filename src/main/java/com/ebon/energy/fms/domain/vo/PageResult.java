package com.ebon.energy.fms.domain.vo;

import java.io.Serializable;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PageResult<T> implements Serializable {
    private int current = 1;
    private int pageSize = 20;
    private Long total;
    private List<T> list;

    public PageResult() {
    }

    public PageResult(Long total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public PageResult(int current, int pageSize, Long total, List<T> list) {
        this.current = current;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
    }

    public static <T> PageResult toResponse(List<T> list, Long total, Integer current, Integer pageSize) {
        PageResult pageResult = new PageResult();
        pageResult.setCurrent(current);
        pageResult.setPageSize(pageSize);
        pageResult.setTotal(total);
        pageResult.setList(list);
        return pageResult;
    }

    public static <T, U> PageResult<U> toResponseConverted(List<T> dataList, Function<T, U> converter, Long total, Integer current, Integer pageSize) {
        List convertedList = (List) dataList.stream().map(converter).collect(Collectors.toList());
        return new PageResult(current, pageSize, total, convertedList);
    }

    public int getCurrent() {
        return this.current;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    public Long getTotal() {
        return this.total;
    }

    public List<T> getList() {
        return this.list;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

}