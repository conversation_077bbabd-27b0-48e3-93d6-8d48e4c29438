package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.PVStatusValue;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class PVStatus implements Cloneable {

    private Double allTimeTotalE;

    private Double dayTotalE;

    private List<PV> PVs = new ArrayList<>();

    public PVStatus() {
    }

    public PVStatus(List<PV> pVs, Double allTimeTotalE, Double dayTotalE) {
        this.PVs = pVs;
        this.allTimeTotalE = allTimeTotalE;
        this.dayTotalE = dayTotalE;
    }

    @JsonProperty("P")
    public Double getP() {
        if (PVs.isEmpty()) {
            return null;
        }

        return PVs.stream()
                .map(pv -> Optional.ofNullable(pv.getP())
                        .map(BigDecimal::new)
                        .orElse(BigDecimal.ZERO)
                ).reduce(BigDecimal.ZERO, BigDecimal::add).doubleValue();
    }

    @JsonProperty("AllTimeTotalE")
    public Double getAllTimeTotalE() {
        if (allTimeTotalE != null) {
            return allTimeTotalE;
        }

        return PVs.stream()
                .map(pv -> Optional.ofNullable(pv.getAllTimeTotalE())
                        .map(BigDecimal::new)
                        .orElse(BigDecimal.ZERO)
                ).reduce(BigDecimal.ZERO, BigDecimal::add).doubleValue();
    }

    public void setAllTimeTotalE(Double allTimeTotalE) {
        this.allTimeTotalE = allTimeTotalE;
    }

    @JsonProperty("DayTotalE")
    public Double getDayTotalE() {
        if (dayTotalE != null) {
            return dayTotalE;
        }
        if (PVs.stream().anyMatch(pv -> pv.getDayTotalE() != null)) {
            return PVs.stream()
                    .mapToDouble(pv -> Optional.ofNullable(pv.getDayTotalE()).orElse(0.0))
                    .sum();
        }
        return null;
    }

    public void setDayTotalE(Double dayTotalE) {
        this.dayTotalE = dayTotalE;
    }

    @JsonProperty("Status")
    public PVStatusValue getStatus() {
        if (PVs.stream().allMatch(pv -> pv.getStatus() == PVStatusValue.NotConnected)) {
            return PVStatusValue.NotConnected;
        }
        return PVs.stream().anyMatch(pv -> pv.getStatus() == PVStatusValue.Generating)
                ? PVStatusValue.Generating
                : PVStatusValue.NotGenerating;
    }

    @JsonProperty("PVs")
    public List<PV> getPVs() {
        return PVs;
    }

    public void setPVs(List<PV> pVs) {
        PVs = pVs;
    }

    @Override
    public PVStatus clone() {
        try {
            PVStatus clone = (PVStatus) super.clone();
            clone.PVs = new ArrayList<>(this.PVs);
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}