// Copyright (c) Redback Technologies. All rights reserved.

package com.ebon.energy.fms.domain.vo.setting.path;

import com.ebon.energy.fms.domain.vo.product.control.SmartRelaySettingsV2Desired;
import com.ebon.energy.fms.domain.vo.setting.provider.SettingPaths;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

/**
 * SGSettingPaths类，继承自SettingPaths
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SGSettingPaths extends SettingPaths {
    
    // 常量字段
    public static final String POWER_FACTOR_MINUS1_TO1_KEY = "PowerFactorSetting";
    public static final String POWER_FACTOR_SETTING = "v2.inverter." + POWER_FACTOR_MINUS1_TO1_KEY;
    public static final String DRED_SUBSCRIBED_KEY = "DREDSubscribed";
    public static final String DRED_SUBSCRIBED_SETTING = "v2.inverter." + DRED_SUBSCRIBED_KEY;
    public static final String RELAY_KEY = "v2.relaySettings";
    public static final String POWER_RAMP_GENERATE_KEY = "PowerRampGenerate_WgraPos";
    public static final String INVERTER_SOFTWARE_VERSION = "v2.emsFirmware.version";
    public static final String INVERTER_SOFTWARE_URL = "v2.emsFirmware.url";
    public static final String INVERTER_SOFTWARE_MAX_ATTEMPTS = "v2.emsFirmware.maxAttempts";
    public static final String INVERTER_SOFTWARE_ATTEMPT_INTERVAL = "v2.emsFirmware.attemptInterval";
    public static final String ENABLE_SHADOW_SCAN_SETTING = "v2.inverter.MPPTShadowScanningEnable";
    public static final String SCHEDULES_SCHEDULES_SETTING = "v2.schedules.schedules";
    
    public static final String INVERTER_CONTROL_NAME = "InverterControl";
    public static final String INVERTER_CONTROL = "v2.inverter." + INVERTER_CONTROL_NAME;
    public static final boolean INVERTER_CONTROL_DEFAULT = false;
    
    /**
     * @deprecated Do NOT use, as apart from not changing anything on the inverter, 
     * this causes InverterSettingsUnknownKey errors in telemetry.
     */
    @Deprecated
    public static final String POWER_CHANGE_RATE_KEY = "PowerChangeRate";
    
    // 静态列表
    public static final List<String> BAND_UPDATE_RATES = Arrays.asList(
        // "v2.ems.BandIDUpdateRate", // Only use telemetry rate for now due to issues around band expiration 
        // "v2.ems.BandIRUpdateRate", // Only use telemetry rate for now due to issues around band expiration
        // "v2.ems.BandEMUpdateRate", // Only use telemetry rate for now due to issues around band expiration
        // "v2.ems.BandMDUpdateRate", // Only use telemetry rate for now due to issues around band expiration
        // "v2.ems.BandM3UpdateRate", // Only use telemetry rate for now due to issues around band expiration
        // "v2.ems.BandFWDUpdateRate", // Only use telemetry rate for now due to issues around band expiration
        "v2.ems.TelemetryRate"
    );
    
    // 实例字段
    private final String ctFlipSetting;
    private final String telemetryPeriodEndUtc;
    private final String serialNumber;
    private final String gridReferenceCT;
    private final String modelName;
    private final String inverterFirmware;
    private final String exportLimitControlEnName;
    private final String exportLimitControlHardLimName;
    private final String exportLimitControlSoftLimName;
    private final String genLimitControlEnName;
    private final String genLimitControlHardLimName;
    private final String genLimitControlSoftLimName;
    
    /**
     * 构造函数
     */
    public SGSettingPaths() {
        super(
            "v2.inverter.ExportLimitControlEn", // enableSiteExportLimitSetting
            "v2.inverter.ExportLimitControlSoftLim", // siteExportLimitSetting
            "v2.ems.GridProfileId", // gridProfileIdSetting
            "v2.ems.GridProfileCorrelationId", // gridProfileCorrelationIdSetting
            null, // batteryManufacturerSetting
            null, // batteryCountSetting
            null, // batteryMaxChargeCurrentSetting
            null, // batteryMaxDischargeCurrentSetting
            null, // batteryMinSocSetting
            null, // batteryMinOffgridSocSetting
            null, // thirdPartyExportCt
            "v2.ems.Relay{0}Name", // relayNameSettingTemplate
            "v2.ems.Relay{0}Installed", // relayInstalledSettingTemplate
            "v2.inverter.Relay{0}Active", // relayActiveSettingTemplate
            "site" // siteSetting
        );
        
        this.ctFlipSetting = "v2.inverter.CurrentDirection";
        this.telemetryPeriodEndUtc = "v2.ems.TelemetryPeriodEndEpochS";
        this.gridReferenceCT = "v2.inverter.GridReferenceCT";
        this.modelName = "v2.inverter.InverterModel";
        this.serialNumber = "v2.inverter.InverterSN";
        this.inverterFirmware = "v2.inverter.InverterFirmwareVersion";
        
        // Please note these are used by SiteConstraintService
        // and if they ever change SiteConstraintService would have to be updated as well
        this.exportLimitControlEnName = "ExportLimitControlEn";
        this.exportLimitControlHardLimName = "ExportLimitControlHardLim";
        this.exportLimitControlSoftLimName = "ExportLimitControlSoftLim";
        this.genLimitControlEnName = "GenLimitControlEn";
        this.genLimitControlHardLimName = "GenLimitControlHardLim";
        this.genLimitControlSoftLimName = "GenLimitControlSoftLim";
    }
    
    // Getter方法（通过属性计算得出的值）
    public String getExportLimitControlEn() {
        return "v2.inverter." + exportLimitControlEnName;
    }
    
    public String getExportLimitControlHardLim() {
        return "v2.inverter." + exportLimitControlHardLimName;
    }
    
    public String getExportLimitControlSoftLim() {
        return "v2.inverter." + exportLimitControlSoftLimName;
    }
    
    public String getGenLimitControlEn() {
        return "v2.inverter." + genLimitControlEnName;
    }
    
    public String getGenLimitControlHardLim() {
        return "v2.inverter." + genLimitControlHardLimName;
    }
    
    public String getGenLimitControlSoftLim() {
        return "v2.inverter." + genLimitControlSoftLimName;
    }
    
    public String getPowerRampGeneratePath() {
        return "v2.inverter." + POWER_RAMP_GENERATE_KEY;
    }
    
    /**
     * @deprecated Do NOT use, as apart from not changing anything on the inverter, 
     * this causes InverterSettingsUnknownKey errors in telemetry.
     */
    @Deprecated
    public String getPowerChangeRatePath() {
        return "v2.inverter." + POWER_CHANGE_RATE_KEY;
    }
    
    public String getSmartRelayName() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.LOAD_CONTROL_NAME);
    }
    
    public String getSmartRelayInstalled() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.LOAD_CONTROL_INSTALLED_NAME);
    }
    
    public String getSmartRelayLoadType() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_TYPE_NAME);
    }
    
    public String getSmartRelayEnabled() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_ENABLED_NAME);
    }
    
    public String getSmartRelaySource() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_SOURCE_NAME);
    }
    
    public String getSmartRelayOnTrigger() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_ON_TRIGGER_NAME);
    }
    
    public String getSmartRelayOnDelay() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_ON_DELAY_NAME);
    }
    
    public String getSmartRelayOffTrigger() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME);
    }
    
    public String getSmartRelayOffDelay() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_OFF_DELAY_NAME);
    }
    
    public String getSmartRelayMinRunTime() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME);
    }
    
    public String getSmartRelayMinOffTime() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME);
    }
    
    public String getSmartRelayRunTimeTarget() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME);
    }
    
    public String getSmartRelayRunTimeCompletionTime() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME);
    }
    
    public String getSmartRelayRunTimeUseExcessPower() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME);
    }
    
    public String getSmartRelayActiveHigh() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME);
    }
    
    public String getSmartRelayControlMode() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.LOAD_CONTROL_MODE_NAME);
    }
    
    public String getSmartRelayTimeZone() {
        return String.format("%s.%s", RELAY_KEY, SmartRelaySettingsV2Desired.LOAD_CONTROL_TIME_ZONE_NAME);
    }
    
    public String getTimeZoneAlias() {
        return "v2.TzAlias";
    }
}
