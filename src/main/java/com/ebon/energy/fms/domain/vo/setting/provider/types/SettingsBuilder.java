package com.ebon.energy.fms.domain.vo.setting.provider.types;// Copyright (c) Redback Technologies. All Rights Reserved.

import java.time.*;
import java.util.*;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.common.utils.HardwareModelHelpers;
import com.ebon.energy.fms.common.utils.ModelInfo;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.provider.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public abstract class SettingsBuilder<TR extends ICommonSettingsReader, TP extends SettingPaths> implements ICommonSettingsBuilder {
    
    private final DeviceInfoAndSettings deviceSettings;
    private final Instant nowInUtc;
    private final TP settingPaths;
    private final TR settingsReader;
    private final ObjectNode desiredPatch;
    private final ObjectMapper objectMapper;

    public SettingsBuilder(DeviceInfoAndSettings deviceSettings, Instant nowInUtc, TR reader, TP paths) {
        this.deviceSettings = deviceSettings;
        this.nowInUtc = nowInUtc;
        this.settingPaths = paths;
        this.settingsReader = reader;
        this.objectMapper = new ObjectMapper();
        this.desiredPatch = objectMapper.createObjectNode();
    }

    public HardwareModelEnum getHardwareModel() {
        // Use desired > reported > telemetry > SerialNumber
        String modelName = null;
        
        if (deviceSettings != null && deviceSettings.getDesired() != null && 
            deviceSettings.getDesired().getInverter() != null &&
            !isNullOrWhiteSpace(deviceSettings.getDesired().getInverter().getModelName())) {
            modelName = deviceSettings.getDesired().getInverter().getModelName();
        } else if (deviceSettings != null && deviceSettings.getReported() != null && 
                   deviceSettings.getReported().getInverterSettings() != null &&
                   !isNullOrWhiteSpace(deviceSettings.getReported().getInverterSettings().getModelName())) {
            modelName = deviceSettings.getReported().getInverterSettings().getModelName();
        } else if (deviceSettings != null && deviceSettings.getIdentityCard() != null) {
            modelName = deviceSettings.getIdentityCard().getModelName();
        }

        HardwareModelEnum hardwareModel = HardwareModelHelpers.parseModelName(modelName);
        if (hardwareModel == HardwareModelEnum.Unknown) {
            // No desired or reported modelname and no valid system status received yet
            // Parse the serial number to identify the Model Designation if system hasn't be used yet
            hardwareModel = HardwareModelHelpers.determineFromSerialNumber(getSerialNumber());
        }

        return hardwareModel;
    }

    public HardwareFirmwareSpecification getProductModelDefaults() {
        String firmwareVersion = null;
        if (deviceSettings != null && deviceSettings.getIdentityCard() != null) {
            firmwareVersion = deviceSettings.getIdentityCard().getFirmwareVersion();
        }
        
        ModelInfo modelInfo = new ModelInfo(getHardwareModel(), firmwareVersion);
        return SpecificationFactory.get(modelInfo);
    }

    public TR getSettingsReader() {
        return settingsReader;
    }

    protected TP getSettingPaths() {
        return settingPaths;
    }

    protected ObjectNode getDesiredPatch() {
        return desiredPatch;
    }

    protected String getSerialNumber() {
        if (deviceSettings != null && deviceSettings.getIdentityCard() != null) {
            return deviceSettings.getIdentityCard().getSerialNumber();
        }
        return null;
    }

    protected DeviceInfoAndSettings getDeviceSettings() {
        return deviceSettings;
    }

    protected Instant getNowInUtc() {
        return nowInUtc;
    }

    public abstract ICommonSettingsBuilder patchManagedInverterSetting(
            String settingName,
            JsonNode value,
            String settingIndex,
            ManagedInverterSettingDesired.InverterSettingValueType settingType,
            ManagedInverterSettingDesired.InverterSettingExecutionType executionType,
            ManagedInverterSettingDesired.InverterSettingSource source,
            String uniqueId);

    public abstract ICommonSettingsBuilder buildInverterPatch(
            RossDesiredSettings desired,
            List<ManagedInverterSettingDesired> expectedSettings,
            Map<String, Object> toPatch);

    public abstract ICommonSettingsBuilder addShadowScan(boolean enableShadowScan);

    public abstract ICommonSettingsBuilder addACCoupled(boolean enableACCoupledMode);

    public abstract ICommonSettingsBuilder addPowerFactor(Double powerFactorMinus1To1);

    public abstract ICommonSettingsBuilder addSoftSiteExportLimit(Watt limit);

    public abstract ICommonSettingsBuilder addSoftAndHardSiteExportLimit(Watt soft, Watt hard);

    public abstract ICommonSettingsBuilder addSoftSiteGenerationLimit(VoltAmps generationLimit);

    public abstract ICommonSettingsBuilder addSiteExportLimit(boolean enabled, Watt power);

    public abstract ICommonSettingsBuilder overrideDesiredPowerRampRateLimit(Duration rampTime, String settingId);

    public abstract ICommonSettingsBuilder addBatterySettings(
            ManufacturerEnum manufacturer,
            Integer batteryCount,
            Integer maxChargeCurrent,
            Integer maxDischargeCurrent,
            Integer minSoc,
            Integer minOffgridSoc);


    public abstract ICommonSettingsBuilder addCtFlipSettings(Boolean flipCt1, Boolean flipCt2);

    public abstract ICommonSettingsBuilder addDefaultTelemetryPeriod();

    public abstract ICommonSettingsBuilder addTelemetryPeriod(Duration period, Instant endDateUtc);

    public ICommonSettingsBuilder patchArbitrarySetting(String settingPath, JsonNode value) {
        addValue(desiredPatch, settingPath, value);
        return this;
    }

    public ICommonSettingsBuilder addPatch(JsonNode patch) {
        // Merge patch into desiredPatch
        mergeJsonNodes(desiredPatch, patch);
        return this;
    }

    public SettingsPatchResult toPatch() {
        JsonNode settingsPatch = desiredPatch.deepCopy();
        return new SettingsPatchResult(settingsPatch, deviceSettings.getIntent());
    }

    public SettingsPatchStringResult toPatchString() {
        JsonNode settingsPatch = desiredPatch.deepCopy();
        try {
            String json = objectMapper.writeValueAsString(toPatch().getSettingsPatch());
            return new SettingsPatchStringResult(json, deviceSettings.getIntent());
        } catch (Exception e) {
            throw new RuntimeException("Failed to serialize patch", e);
        }
    }


    public abstract ICommonSettingsBuilder addDredSettings(Boolean dredSubscribed);

    /**
     * {@inheritDoc}
     */
    public ICommonSettingsBuilder addSiteLimits(Watt softExportLimit, Watt hardExportLimit, VoltAmps generationLimit) {
        addSoftAndHardSiteExportLimit(softExportLimit, hardExportLimit);
        addSoftSiteGenerationLimit(generationLimit);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    public ICommonSettingsBuilder addSoftSiteLimits(Watt exportLimit, VoltAmps generationLimit) {
        addSoftSiteExportLimit(exportLimit);
        addSoftSiteGenerationLimit(generationLimit);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    public ICommonSettingsBuilder addExportSiteLimits(Watt softExportLimit, Watt hardExportLimit) {
        addSoftAndHardSiteExportLimit(softExportLimit, hardExportLimit);
        return this;
    }

    public abstract ICommonSettingsBuilder removeSchedule(String scheduleId);

    public abstract ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, LocalDateTime endTimeUtc);


    public abstract ICommonSettingsBuilder addMeterCheckModeSchedule(
            String id,
            LocalDateTime startTimeUtc,
            Duration duration,
            Watt maxTestPower);

    public abstract SchedulePriority getSchedulePriority();


    public abstract void deleteRelaySchedule(int relayNumber1Based, String id, Map<String, Object> patch);

    public abstract ICommonSettingsBuilder disableAgeingMode();

    protected static void addValue(JsonNode subsection, V2Setting setting, JsonNode value) {
        addValue(subsection, "v2." + toString(setting.getSection()) + "." + setting.getKey(), value);
    }

    protected static void addValue(JsonNode subsection, V2Section section, String settingName, JsonNode value) {
        addValue(subsection, "v2." + toString(section) + "." + settingName, value);
    }

    public static void addValue(JsonNode subsection, String settingPath, JsonNode value) {
        SettingsBuilderHelper.addValue(subsection, settingPath, value);
    }

    protected static void addValueIfNotSet(DeviceInfoAndSettings deviceSettings, boolean isDesired, JsonNode subsection, V2Setting setting, JsonNode value) {
        Object existingValue = SettingsReader.readObject(deviceSettings, isDesired, setting.getSection(), setting.getKey());

        if (existingValue == null) {
            addValue(subsection, setting, value);
        }
    }

    protected static void addValueIfNotSet(DeviceInfoAndSettings deviceSettings, boolean isDesired, JsonNode subsection, String settingPath, JsonNode value) {
        JsonNode existingValue = SettingsReader.readObjectByPath(deviceSettings, isDesired, settingPath, JsonNode.class);

        if (existingValue == null) {
            addValue(subsection, settingPath, value);
        }
    }


    protected void updateIntentExportLimitTimestamps() {
        // This method is here to ensure that 
        //    once the export limit is set using one of the too many methods available
        //    the GetResponseForSetting(UniversalSettingId.AS4777_2_ExportLimits) returns Configured. 
        
        deviceSettings.getIntent().setExportLimitsModifiedDateUtc(nowInUtc);
        deviceSettings.getIntent().setSiteExportModifiedDateUtc(nowInUtc);
    }

    private static String toString(V2Section section) {
        switch (section) {
            case Inverter:
                return SettingsV2DesiredBase.INVERTER_SETTINGS_NAME;
            case Meter:
                return SettingsV2Desired.METER_SETTINGS_NAME;
            case BatteryManager:
                return SettingsV2DesiredBase.BATTERY_MANAGER_SETTINGS_NAME;
            case BatteryStack:
                throw new UnsupportedOperationException("Writing battery stack settings is not supported");
            case InverterControl:
                return SettingsV2DesiredBase.INVERTER_CONTROL_SETTINGS_NAME;
            case Site:
                return SettingsV2DesiredBase.SITE_SETTINGS_NAME;
            case Constraints:
                return SettingsV2DesiredBase.CONSTRAINTS_NAME;
            default:
                throw new IllegalArgumentException("Unknown section: " + section);
        }
    }

    private boolean isNullOrWhiteSpace(String str) {
        return str == null || str.trim().isEmpty();
    }

    private void mergeJsonNodes(ObjectNode target, JsonNode source) {
        if (source.isObject()) {
            source.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                
                if (target.has(key) && target.get(key).isObject() && value.isObject()) {
                    mergeJsonNodes((ObjectNode) target.get(key), value);
                } else {
                    target.set(key, value);
                }
            });
        }
    }

}


