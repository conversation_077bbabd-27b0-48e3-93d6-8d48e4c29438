package com.ebon.energy.fms.domain.vo.product.control.invert;

import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.RossReportedSettings;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;

@Data
public class DesiredAndReported {

    @JsonProperty("Desired")
    private final RossDesiredSettings desired;

    @JsonProperty("Reported")
    private final RossReportedSettings reported;

    // 构造函数：直接传对象
    public DesiredAndReported(
            @JsonProperty("Desired") RossDesiredSettings desired,
            @JsonProperty("Reported") RossReportedSettings reported) {
        this.desired = desired;
        this.reported = reported;
    }


}
