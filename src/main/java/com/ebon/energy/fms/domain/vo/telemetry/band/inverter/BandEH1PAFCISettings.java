package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.AFCIArcFault;
import com.ebon.energy.fms.common.enums.AFCIModuleFault;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandEH1PAFCISettings extends DataBackedBand {
    public BandEH1PAFCISettings() {
        super(BandForge.<BandEH1PAFCISettings>getMetadataFor(BandEH1PAFCISettings.class));
    }


    public BandEH1PAFCISettings(byte[] bytes) {
        super(bytes, BandForge.<BandEH1PAFCISettings>getMetadataFor(BandEH1PAFCISettings.class));
    }

    public BandEH1PAFCISettings(String encodedBytes) {
        super(encodedBytes, BandForge.<BandEH1PAFCISettings>getMetadataFor(BandEH1PAFCISettings.class));
    }


    public final int getAFCISoftVer() {
        return GetU16(0);
    }


    public final Object getAFCIModuleFaultStatus() {
        return AFCIModuleFault.parse(GetU16(2));
    }


    public final Object getAFCIArcFault() {
        return AFCIArcFault.parse(GetU16(4));
    }
}
