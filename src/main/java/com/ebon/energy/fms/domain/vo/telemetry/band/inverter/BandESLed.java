package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;








/** 
 This is an automatically generated class that defines BandESLed.
	 Base Register Address: 0600
	 Total Length In Bytes: 2
 
*/


public class BandESLed extends DataBackedBand
{
	public BandESLed()
	{
		super(BandForge.<BandESLed>getMetadataFor(BandESLed.class));
	}



	public BandESLed(byte[] bytes)
	{
		super(bytes, BandForge.<BandESLed>getMetadataFor(BandESLed.class));
	}

	public BandESLed(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESLed>getMetadataFor(BandESLed.class));
	}
}
