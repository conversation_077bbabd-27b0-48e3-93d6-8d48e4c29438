package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;
import com.ebon.energy.fms.domain.vo.telemetry.IDataFrame;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import java.time.Duration;
import java.util.*;

public interface IInverterBridge
{
	/** 
	 Returns the names of the configured list of setup Bands for this inverter.
	 
	 Implements IInverterAdapter.ListBandNames()
	 @return List of Band names
	*/
	ArrayList<String> ListSetupBands();

	/** 
	 Returns the names of the configured list of supported Bands for this inverter.
	 
	 Implements IInverterAdapter.ListBandNames()
	 @return List of Band names
	*/
	ArrayList<Class> ListBandTypes();


	<T extends DataBackedBand> void SetupBand();



	<T extends DataBackedBand> void SetupBand(boolean writeOnlyBand);

	<T1 extends DataBackedBand, T2 extends DataBackedBand> void OverrideBand();

	<T1 extends DataBackedBand, T2 extends DataBackedBand> void OverrideBand(boolean writeOnlyBand);

	/** 
	 Provides an indication of whether all known bands have been successfully read from the inverter.
	 
	 @return True if has read all known bands.
	*/
	boolean HasReadAllKnownBands();

	<T extends IBand> T GetLatestBandDataFor();


	<T extends IBand> T GetLatestBandDataFor(boolean includeStale);

	<T extends IBand> IDataFrame<T> GetLatestFrameFor();


	<T extends IBand> IDataFrame<T> GetLatestFrameFor(boolean includeStale);

	/** 
	 Returns all available frames for the specified Band type from the InverterContext bounded by age.
	 
	 <typeparam name="T">IBand</typeparam>
	 @param sinceDuration Maximum age to include, or all if null
	 @return Enumeration of frames for Band type
	*/


	<T extends IBand> Iterable<IDataFrame<T>> GetAllFramesFor(Duration sinceDuration);

	/** 
	 Returns the latest frame for each unique band type current held.
	 
	 @return Enumeration of latest frame for each known band
	*/


	Iterable<IDataFrame<IBand>> GetAllLatestFrames();

///#nullable disable

	/** 
	 Checks for any recent frames read within the given duration.
	 
	 @param sinceDuration How far back to consider recent.
	 @return True if there are recent frames read within the given duration.
	*/
	boolean HasRecentFrames(Duration sinceDuration);

	/** 
	 Returns the SerialNumber from the inverter context.
	 
	 Implements IInverterAdapter.GetSerialNumber()
	 @return string: SerialNumber
	*/
	String GetSerialNumber();

	/** 
	 Returns the ModelName from the inverter context.
	 
	 Implements IInverterAdapter.GetModelName()
	 @return string: ModelName
	*/
	String GetModelName();

	/** 
	 Returns the Firmware Version from the inverter context.
	 
	 Implements IInverterAdapter.GetFirmwareVersionString()
	 @return string: FirmwareVersion
	*/
	String GetFirmwareVersionString();
}
