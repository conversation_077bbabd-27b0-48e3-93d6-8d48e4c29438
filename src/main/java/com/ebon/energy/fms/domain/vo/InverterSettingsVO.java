package com.ebon.energy.fms.domain.vo;

import lombok.Data;

@Data
public class InverterSettingsVO {

    private String batteryModel;
    private Integer maxChargeCurrent;
    private Integer maxDischargeCurrent;
    private Integer minSoCPercent;
    private Integer minOffgridSoCPercent;

    private Boolean allowGridCharge;
    private Long powerInWatts;
    private String chargeTime;
    private String dischargeTime;

    private String forceChargePowerLimit;
    private String batteryAutoAwakenTimeSet;
    private String batteryAwakenVolSet;
    private String batteryAwakenTimeSet;

    private String meterType;

}
