package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import java.math.*;


public interface IBandESC extends IBand
{
	Volt getActiveCurveVolt();

	int getAppModeIndex();

	Object getAS4777_2Parameters();

	boolean getAutoStartBackup();

	boolean getBackUpEnable();

	int getBackupStartDelayMinutes();

	TimeSpan getBatActivePeriod();

	BigDecimal getBatBMSCurrLmtCoff();

	BigDecimal getBatChargePowerMax();

	BigDecimal getBatDisPowerSet();

	Volt getBattAvgChagVolt();

	TimeSpan getBattAvgChgHours();

	Ampere getBattChargeCurrMax();

	Volt getBattChargeVoltMax();

	Ampere getBattDisChargeCurrMax();

	Ampere getBattFloatCurr();

	Volt getBattFloatVolt();

	BigDecimal getBattOfflineSOCUnderMin0to1();

	Volt getBattOfflineVoltUnderMin();

	BigDecimal getBattSOCUnderMin();

	TimeSpan getBattToFloatTime();

	Object getBattTypeIndex();

	Volt getBattVoltUnderMin();

	Object getBMSProtocolCode();

	int getChargerTimeEnd();

	int getChargerTimeStart();

	boolean getDCVoltOutput();

	Volt getDesactiveCurveVolt();

	int getDisChargerTimeEnd();

	int getDisChargerTimeStart();

	boolean getDisChgWithPVEnable();

	boolean getEnableCurve();

	boolean getEnableMPPT4Shadow();

	boolean getFeedPowerEnable();

	Watt getFeedPowerPara();

	Object getFunctionStatus();

	Frequency getGridFreqHighS1();

	TimeSpan getGridFreqHighS1Time();

	Frequency getGridFreqHighS2();

	TimeSpan getGridFreqHighS2Time();

	Frequency getGridFreqLowS1();

	TimeSpan getGridFreqLowS1Time();

	Frequency getGridFreqLowS2();

	TimeSpan getGridFreqLowS2Time();

	Frequency getGridFreqRecoverHigh();

	Frequency getGridFreqRecoverLow();

	TimeSpan getGridFreqRecoverTime();

	int getGridLimitByVolSlope();

	BigDecimal getGridLimitByVolStartPer();

	Volt getGridLimitByVolStartVol();

	Volt getGridVoltHighS1();

	TimeSpan getGridVoltHighS1Time();

	Volt getGridVoltHighS2();

	TimeSpan getGridVoltHighS2Time();

	Volt getGridVoltLowS1();

	TimeSpan getGridVoltLowS1Time();

	Volt getGridVoltLowS2();

	TimeSpan getGridVoltLowS2Time();

	Volt getGridVoltQuality();

	Volt getGridVoltRecoverHigh();

	Volt getGridVoltRecoverLow();

	TimeSpan getGridVoltRecoverTime();

	Object getGridWaveCheckLevel();

	Ohm getIsoLimit();

	Capacity getLeadBatCapacity();

	int getManufacturerCode();

	Watt getMeterCheckValue();

	boolean getMeterConnectCheckFlag();

	Object getMeterConnectStatus();

	boolean getNoGridChargeEnable();

	boolean getOffGridAutoCharge();

	boolean getOnlyNightDischarge();

	BigDecimal getPointBValue();

	BigDecimal getPointCValue();

	boolean getRapidCutOff();

	int getRecoverTimeEE();

	short getRPControlPara();

	Object getSafetyCountry();

	boolean getStopSocProtect();

	int getTBDx0570();

	int getTBDx0573();

	Object getUpsStdVoltType();

	Object getWgPowerMode();

	Watt getWgPowerSet();
}
