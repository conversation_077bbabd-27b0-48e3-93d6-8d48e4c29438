package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public abstract class BaseScheduleVO {
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIME_FORMAT = "hh:mmaa";

    @JsonProperty(index = 1)
    protected String StartTime;

    @JsonProperty(index = 2)
    protected String EndTime;

    @JsonProperty(index = 3)
    protected boolean Recurring;

    @JsonProperty(index = 4)
    protected String ScheduleDate;

    public void convertScheduleDate(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT, Locale.ENGLISH);
        ScheduleDate = sdf.format(d);
    }

    public void convertStartTime(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.ENGLISH);
        StartTime = sdf.format(d);
    }

    public void convertEndTime(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.ENGLISH);
        EndTime = sdf.format(d);
    }

    public Date getStartDate() {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.ENGLISH);
        try {
            return sdf.parse(StartTime);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid start time format", e);
        }
    }

    public Date getEndDate() {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.ENGLISH);
        try {
            return sdf.parse(EndTime);
        } catch (ParseException e) {
            throw new IllegalArgumentException("Invalid end time format", e);
        }
    }

    public void FixStartAndEndTimesOrThrow() {
        try {
            convertStartTime(FixTimeOrThrow(StartTime));
            convertEndTime(FixTimeOrThrow(EndTime));
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse time", e);
        }
    }

    public static Date FixTimeOrThrow(String timeString) {
        String[] cultures = {"en-AU", "mi-NZ", "zh"};
        for (String culture : cultures) {
            SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT, Locale.forLanguageTag(culture));
            sdf.setTimeZone(TimeZone.getDefault());
            try {
                return sdf.parse(timeString);
            } catch (ParseException ignored) {
            }
        }
        throw new IllegalArgumentException("Unable to parse time '" + timeString + "'");
    }

    public String getScheduleDate() {
       return ScheduleDate;
    }

    public String validate() {
        try {
            getStartDate();
            getEndDate();
            return null;
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    public String getStartTime() {
        return StartTime;
    }

    public void setStartTime(String startTime) {
        StartTime = startTime;
    }

    public String getEndTime() {
        return EndTime;
    }

    public void setEndTime(String endTime) {
        EndTime = endTime;
    }

    public boolean isRecurring() {
        return Recurring;
    }

    public void setRecurring(boolean recurring) {
        Recurring = recurring;
    }

    public String getScheduleDateStr() {
        return ScheduleDate;
    }

    public void setScheduleDate(String scheduleDate) {
        ScheduleDate = scheduleDate;
    }
}