package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.BMSAlarmCodeET;
import com.ebon.energy.fms.common.enums.BMSStatusCodeET;
import com.ebon.energy.fms.common.enums.BMSWarningCodeET;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandESGBattControl extends DataBackedBand
{
	public BandESGBattControl()
	{
		super(BandForge.<BandESGBattControl>getMetadataFor(BandESGBattControl.class));
	}



	public BandESGBattControl(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGBattControl>getMetadataFor(BandESGBattControl.class));
	}

	public BandESGBattControl(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGBattControl>getMetadataFor(BandESGBattControl.class));
	}


	


	public final int getBMSVersion() { return GetU16(0); }

	


	public final int getBattStringsW() { return GetU16(2); }

	
	public final Volt getWBMSBatChargeVMax()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Ampere getWBMSBatChargeIMax()
	{
		return GetU16(6, Ampere.Deci);
	}

	
	public final Volt getWBMSBatDisChargeVMin()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Ampere getWBMSBatDisChargeIMax()
	{
		return GetU16(10, Ampere.Deci);
	}

	
	public final Volt getWBMSBatVoltage()
	{
		return GetU16(12, Volt.Deci);
	}

	
	public final Ampere getWBMSBatCurrent()
	{
		return GetU16(14, Ampere.Deci);
	}

	
	public final BigDecimal getWBMSBatSOC()
	{
		return new BigDecimal(GetU16(16)).multiply(Percentage._1);
	}

	
	public final BigDecimal getWBMSBatSOH()
	{
		return new BigDecimal(GetU16(18)).multiply(Percentage._1);
	}

	
	public final Celsius getWBMSBatTemperature()
	{
		return GetS16(20, Celsius.Deci);
	}

	
	public final Object getBMSWarningCode()
	{
		return BMSWarningCodeET.parse(GetU32(22));
	}

	
	public final Object getBMSAlarmCode()
	{
		return BMSAlarmCodeET.parse(GetU32(26));
	}

	
	public final Object getBMSStatusW()
	{
		return BMSStatusCodeET.parse(GetU16(30));
	}

	
	public final boolean getEBMSCommLossDisable()
	{
		return GetBool((int) GetU16(32), 1, 0, false);
	}

	
	public final Volt getBMSBatteryStringRateVoltage()
	{
		return GetU16(34, Volt.Deci);
	}
}
