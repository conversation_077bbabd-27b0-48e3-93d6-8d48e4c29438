package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;









public class BandHVFWBlock extends DataBackedBand
{
	public BandHVFWBlock()
	{
		super(BandForge.<BandHVFWBlock>getMetadataFor(BandHVFWBlock.class));
	}



	public BandHVFWBlock(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVFWBlock>getMetadataFor(BandHVFWBlock.class));
	}

	public BandHVFWBlock(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVFWBlock>getMetadataFor(BandHVFWBlock.class));
	}


	


	public final int getBlockNumber() { return GetU16(0); }

	


	public final byte[] getBlockData()
	{
		return GetRaw(2, 128);
	}

	


	public final int getBlockCRC() { return GetU16(130); }
}
