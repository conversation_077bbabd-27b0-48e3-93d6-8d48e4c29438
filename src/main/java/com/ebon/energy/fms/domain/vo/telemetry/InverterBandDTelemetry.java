package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.math.*;

@JsonAutoDetect(
		fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
		getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
		setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class InverterBandDTelemetry implements ITelemetryBand
{
	private final TimeSpan StartTime1;
	private final TimeSpan EndTime1;
	private final double BatPower0to1_1;
	private final WorkWeek WorkWeek1;
	private final TimeSpan StartTime2;
	private final TimeSpan EndTime2;
	private final double BatPower0to1_2;
	private final WorkWeek WorkWeek2;
	private final TimeSpan StartTime3;
	private final TimeSpan EndTime3;
	private final double BatPower0to1_3;
	private final WorkWeek WorkWeek3;
	private final TimeSpan StartTime4;
	private final TimeSpan EndTime4;
	private final double BatPower0to1_4;
	private final WorkWeek WorkWeek4;

	public InverterBandDTelemetry(
			TimeSpan StartTime1,
			TimeSpan EndTime1,
			double BatPower0to1_1,
			WorkWeek WorkWeek1,
			TimeSpan StartTime2,
			TimeSpan EndTime2,
			double BatPower0to1_2,
			WorkWeek WorkWeek2,
			TimeSpan StartTime3,
			TimeSpan EndTime3,
			double BatPower0to1_3,
			WorkWeek WorkWeek3,
			TimeSpan StartTime4,
			TimeSpan EndTime4,
			double BatPower0to1_4,
			WorkWeek WorkWeek4) {

		this.StartTime1 = StartTime1;
		this.EndTime1 = EndTime1;
		this.BatPower0to1_1 = BatPower0to1_1;
		this.WorkWeek1 = WorkWeek1;
		this.StartTime2 = StartTime2;
		this.EndTime2 = EndTime2;
		this.BatPower0to1_2 = BatPower0to1_2;
		this.WorkWeek2 = WorkWeek2;
		this.StartTime3 = StartTime3;
		this.EndTime3 = EndTime3;
		this.BatPower0to1_3 = BatPower0to1_3;
		this.WorkWeek3 = WorkWeek3;
		this.StartTime4 = StartTime4;
		this.EndTime4 = EndTime4;
		this.BatPower0to1_4 = BatPower0to1_4;
		this.WorkWeek4 = WorkWeek4;
	}

	public static class WorkWeek {
		public final boolean Item1;
		public final byte Item2;

		public WorkWeek(boolean Item1, byte Item2) {
			this.Item1 = Item1;
			this.Item2 = Item2;
		}
	}
}
