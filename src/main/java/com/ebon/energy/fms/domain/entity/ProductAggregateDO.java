package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@TableName("ProductAggregate")
public class ProductAggregateDO {

    @TableField(value = "Id")
    private String id;

    @TableField(value = "SerialNumber")
    private String serialNumber;

    @TableField(value = "DateLocal")
    private Timestamp dateLocal;

    @TableField(value = "EPvTotalForToday")
    private BigDecimal epvTotalForToday;

}