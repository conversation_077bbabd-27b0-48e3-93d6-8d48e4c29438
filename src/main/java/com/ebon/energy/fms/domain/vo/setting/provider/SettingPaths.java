// Copyright (c) Redback Technologies. All rights reserved.

package com.ebon.energy.fms.domain.vo.setting.provider;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
@AllArgsConstructor
public class SettingPaths {
    @JsonProperty("EnableSiteExportLimitSetting")
    private final String enableSiteExportLimitSetting;

    @JsonProperty("SiteExportLimitSetting")
    private final String siteExportLimitSetting;

    @JsonProperty("GridProfileIdSetting")
    private final String gridProfileIdSetting;

    @JsonProperty("GridProfileCorrelationIdSetting")
    private final String gridProfileCorrelationIdSetting;

    @JsonProperty("BatteryManufacturerSetting")
    private final String batteryManufacturerSetting;

    @JsonProperty("BatteryCountSetting")
    private final String batteryCountSetting;

    @JsonProperty("BatteryMaxChargeCurrentSetting")
    private final String batteryMaxChargeCurrentSetting;

    @JsonProperty("BatteryMaxDischargeCurrentSetting")
    private final String batteryMaxDischargeCurrentSetting;

    @JsonProperty("BatteryMinSocSetting")
    private final String batteryMinSocSetting;

    @JsonProperty("BatteryMinOffgridSocSetting")
    private final String batteryMinOffgridSocSetting;

    @JsonProperty("RelayNameSettingTemplate")
    private final String relayNameSettingTemplate;

    @JsonProperty("RelayInstalledSettingTemplate")
    private final String relayInstalledSettingTemplate;

    @JsonProperty("RelayActiveSettingTemplate")
    private final String relayActiveSettingTemplate;

    @JsonProperty("ThirdPartyExportCt")
    private final String thirdPartyExportCt;

    @JsonProperty("SiteSetting")
    private final String siteSetting;
}
