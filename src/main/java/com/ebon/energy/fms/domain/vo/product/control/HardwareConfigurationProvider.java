package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.HardwareTypeEnum;
import com.ebon.energy.fms.config.SupportedHardwareConfigurations;
import com.ebon.energy.fms.domain.vo.HardwareSettingsVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class HardwareConfigurationProvider implements IHardwareConfigurationProvider {
    private final Map<String, HardwareSettingsVO> supportedTypes;

    // 默认构造器，使用默认配置
    public HardwareConfigurationProvider() {
        this.supportedTypes = SupportedHardwareConfigurations.getCurrent();
    }

    // 构造器，传入自定义配置
    public HardwareConfigurationProvider(Map<String, HardwareSettingsVO> supportedTypes) {
        this.supportedTypes = supportedTypes;
    }

    // 获取支持的类型（优先使用传入的，否则用默认）
    private Map<String, HardwareSettingsVO> getSupportedTypes() {
        return supportedTypes != null ? supportedTypes : SupportedHardwareConfigurations.getCurrent();
    }

    public HardwareSettingsVO getHardwareSettingsFor(String hardwareConfig) {
        HardwareSettingsVO value = getSupportedTypes().get(hardwareConfig);
        if (value != null) {
            return value;
        }
        throw new IllegalStateException("Unable to find hardware family for '" + hardwareConfig + "'");
    }

    /**
     * Returns the possible hardware device settings required for model identification.
     */
    @Override
    public List<HardwareSettingsVO> tryIdentifyDevice(
            HardwareTypeEnum hardwareType,
            HardwareModelEnum hardwareModel,
            int firmwareVersion
    ) {
        // 1. 过滤类型
        List<HardwareSettingsVO> targetDevices = getSupportedTypes().values().stream()
                .filter(h -> h.getHardwareType() == hardwareType)
                .collect(Collectors.toList());

        // 2. 过滤型号
        if (hardwareModel != HardwareModelEnum.Unknown) {
            targetDevices = targetDevices.stream()
                    .filter(h -> h.supportsHardwareModel(hardwareModel))
                    .collect(Collectors.toList());

            // 3. 过滤固件版本
            if (firmwareVersion > 0) {
                targetDevices = targetDevices.stream()
                        .filter(h -> firmwareVersion >= h.getMinFirmwareVersion() && firmwareVersion <= h.getMaxFirmwareVersion())
                        .collect(Collectors.toList());
            }
        }
        return targetDevices;
    }

    public HardwareFamilyEnum tryIdentifyFamily(
            HardwareTypeEnum hardwareType,
            HardwareModelEnum hardwareModel
    ) {
        List<HardwareSettingsVO> targetDevices = getSupportedTypes().values().stream()
                .filter(h -> h.getHardwareType() == hardwareType)
                .collect(Collectors.toList());

        if (hardwareModel == HardwareModelEnum.Unknown) {
            return HardwareFamilyEnum.Unknown;
        }

        return targetDevices.stream()
                .filter(h -> h.supportsHardwareModel(hardwareModel))
                .findFirst()
                .map(HardwareSettingsVO::getHardwareFamily)
                .orElse(HardwareFamilyEnum.Unknown);
    }

    public HardwareFamilyEnum identifyFamily(
            HardwareTypeEnum hardwareType,
            HardwareModelEnum hardwareModel
    ) {
        List<HardwareSettingsVO> targetDevices = getSupportedTypes().values().stream()
                .filter(h -> h.getHardwareType() == hardwareType)
                .collect(Collectors.toList());

        if (hardwareModel == HardwareModelEnum.Unknown) {
            throw new IllegalArgumentException("Unable to provide hardware family for Unknown model");
        }

        return targetDevices.stream()
                .filter(h -> h.supportsHardwareModel(hardwareModel))
                .findFirst()
                .map(HardwareSettingsVO::getHardwareFamily)
                .orElseThrow(() -> new IllegalStateException(
                        "Unable to provide hardware family for '" + hardwareModel + "/" + hardwareType + "'"
                ));
    }

    public HardwareFamilyEnum tryIdentifyFamily(HardwareModelEnum hardwareModel) {
        if (hardwareModel == HardwareModelEnum.Unknown) {
            return HardwareFamilyEnum.Unknown;
        }

        return getSupportedTypes().values().stream()
                .filter(h -> h.supportsHardwareModel(hardwareModel))
                .findFirst()
                .map(HardwareSettingsVO::getHardwareFamily)
                .orElse(HardwareFamilyEnum.Unknown);
    }
}