package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonSerialNumberDto extends PylonDto {
    private String SerialNumber;

    public PylonSerialNumberDto(
            String rawMessage,
            String serialNumber) {

        super(rawMessage);
        this.SerialNumber = serialNumber;
    }

    public String getSerialNumber() {
        return SerialNumber;
    }
}