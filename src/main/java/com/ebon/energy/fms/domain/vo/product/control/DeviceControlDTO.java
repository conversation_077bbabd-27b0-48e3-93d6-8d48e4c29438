package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.domain.entity.DeviceControl;
import lombok.Data;

/**
 * Device control data transfer object
 */
@Data
public class DeviceControlDTO {
    private DeviceControl deviceControl;
    private boolean isReadOnly;
    private boolean canUserControlOptimisation;
    private boolean isOptInForOptimization;
    private boolean productCanBeOptimised;
    private String appliedTariffId;
    private String inverterTimeZoneId;
    
    /**
     * Default constructor
     */
    public DeviceControlDTO() {
        isReadOnly = false;
        canUserControlOptimisation = true;
        isOptInForOptimization = false;
        productCanBeOptimised = false;
    }
}
