package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;


public class BandHVEI10 extends DataBackedBand implements IBandHVEI
{
	public BandHVEI10()
	{
		super(BandForge.<BandHVEI10>getMetadataFor(BandHVEI10.class));
	}



	public BandHVEI10(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVEI10>getMetadataFor(BandHVEI10.class));
	}

	public BandHVEI10(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVEI10>getMetadataFor(BandHVEI10.class));
	}


	
	public final String getManufacturersName()
	{
		return GetBufS(0, 10, StringProcessors.PylonHVDecode);
	}

	
	public final String getDeviceCode()
	{
		return GetBufS(10, 10, StringProcessors.PylonHVDecode);
	}

	


	public final int getVersionNumber() { return GetU16(20); }

	


	public final int getInternalVersionNumber() { return GetU16(22); }

	


	public final int getNumberOfPiles() { return GetU16(24); }

	
	public final Capacity getRatedBatteryCapacity()
	{
		return GetU16(26, Capacity.Unit);
	}
}
