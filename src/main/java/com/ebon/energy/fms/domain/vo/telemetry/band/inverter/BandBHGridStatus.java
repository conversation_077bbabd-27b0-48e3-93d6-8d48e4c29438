package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.ExtGridDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandBHGridStatus extends DataBackedBand
{
	public BandBHGridStatus()
	{
		super(BandForge.<BandBHGridStatus>getMetadataFor(BandBHGridStatus.class));
	}



	public BandBHGridStatus(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHGridStatus>getMetadataFor(BandBHGridStatus.class));
	}

	public BandBHGridStatus(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHGridStatus>getMetadataFor(BandBHGridStatus.class));
	}


	
	public final Object getExtGridDetailedErr()
	{
		return ExtGridDetailedErr.parse(GetU64(0));
	}

	
	public final Object getExtInvDetailedErr()
	{
		return ExtInvDetailedErr.parse(GetU64(8));
	}

	
	public final Object getExtInvDetailedStatus()
	{
		return ExtInvDetailedStatus.parse(GetU64(16));
	}
}
