package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.Volt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.time.Instant;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PVVFaultTelemetry {
    public Volt Vpv1;
    public Volt Vpv2;
    public Instant TimestampUtc;

    public PVVFaultTelemetry(Volt vpv1, Volt vpv2, Instant timestampUtc) {
        this.Vpv1 = vpv1;
        this.Vpv2 = vpv2;
        this.TimestampUtc = timestampUtc;
    }
}