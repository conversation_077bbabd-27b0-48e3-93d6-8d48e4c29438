package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;



public class BandBHD extends DataBackedBand
{
	public BandBHD()
	{
		super(BandForge.<BandBHD>getMetadataFor(BandBHD.class));
	}



	public BandBHD(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHD>getMetadataFor(BandBHD.class));
	}

	public BandBHD(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHD>getMetadataFor(BandBHD.class));
	}



	public final Object getDRMStatus()
	{
		return ETDRMStatus.parse(GetU16(0));
	}


	public final Object getBattTypeIndex()
	{
		return FloatBattType.parse(GetU16(2));
	}


	public final Object getBMSStatus()
	{
		return BMSStatusMasks.parse(GetU16(4));
	}


	public final Celsius getBMSPackTemperature()
	{
		return GetU16(6, Celsius.Deci);
	}


	public final Ampere getBMSChargeImax()
	{
		return GetU16(8, Ampere.Unit);
	}


	public final Ampere getBMSDischargeImax()
	{
		return GetU16(10, Ampere.Unit);
	}


	public final Object getBMSErrorCodeL()
	{
		return BMSAlarmCodeET.parse(GetU16(12));
	}


	public final BigDecimal getSOC()
	{
		return new BigDecimal(GetU16(14)).multiply(Percentage._1);
	}


	public final BigDecimal getBMSSOH()
	{
		return new BigDecimal(GetU16(16)).multiply(Percentage._1);
	}




	public final int getBMSBatteryStrings() { return GetU16(18); }


	public final Object getBMSWarningCodeL()
	{
		return BMSWarningCodeET.parse(GetU16(20));
	}


	public final Object getBMSProtocolCode()
	{
		return BMSProtocolCode.parse(GetU16(22));
	}




	public final int getBMSErrorCodeH() { return GetU16(24); }




	public final int getBMSWarningCodeH() { return GetU16(26); }
}
