package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandBHGridSafetyB extends DataBackedBand
{
	public BandBHGridSafetyB()
	{
		super(BandForge.<BandBHGridSafetyB>getMetadataFor(BandBHGridSafetyB.class));
	}



	public BandBHGridSafetyB(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHGridSafetyB>getMetadataFor(BandBHGridSafetyB.class));
	}

	public BandBHGridSafetyB(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHGridSafetyB>getMetadataFor(BandBHGridSafetyB.class));
	}


	
	public final Frequency getPowerFreqFStopChg()
	{
		return GetU16(0, Frequency.Centi);
	}

	
	public final Frequency getPowerFreqFStopTransition()
	{
		return GetU16(2, Frequency.Centi);
	}

	
	public final boolean getPowerAndFrequencyCurveEnabled2()
	{
		return GetBool((int) GetU16(4), 1, 0, true);
	}
}
