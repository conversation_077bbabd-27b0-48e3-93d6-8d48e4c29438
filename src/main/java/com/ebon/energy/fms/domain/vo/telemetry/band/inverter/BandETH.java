package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.LEDState;
import com.ebon.energy.fms.common.enums.MeterConnectStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandETH extends DataBackedBand implements IBandETH
{
	public BandETH()
	{
		super(BandForge.<BandETH>getMetadataFor(BandETH.class));
	}



	public BandETH(byte[] bytes)
	{
		super(bytes, BandForge.<BandETH>getMetadataFor(BandETH.class));
	}

	public BandETH(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETH>getMetadataFor(BandETH.class));
	}





	public final int getAppModeIndex() { return GetU16(0); }


	public final Object getMeterCheckValueL1()
	{
		return MeterConnectStatus.parse(GetMaskedU16(2,  (short) 3,   (short)0));
	}


	public final Object getMeterCheckValueL2()
	{
		return MeterConnectStatus.parse(GetMaskedU16(2, (short) 48, (short) 4));
	}


	public final Object getMeterCheckValueL3()
	{
		return MeterConnectStatus.parse(GetMaskedU16(2, (short) 768, (short) 8));
	}


	public final boolean getMeterConnectCheckFlag()
	{
		return GetBool((int) GetU16(4), 1, 0, false);
	}


	public final Watt getSimulateMeterPower()
	{
		return GetU16(6, Watt.Unit);
	}


	public final boolean getBreezeOnOff()
	{
		return GetBool((int) GetU16(8), 1, 0, false);
	}


	public final boolean getLogDataEnable()
	{
		return GetBool((int) GetU16(10), 1, 0, false);
	}


	public final TimeSpan getDataSendInterval()
	{
		return GetU16(12, TimeSpan.fromSeconds(1));
	}


	public final int getDREDcmd() { return GetU16(14); }


	public final boolean getLedtestflag()
	{
		return GetBool((int) GetU16(16), 1, 0, false);
	}


	public final int getWiFiOrLANSwitch() { return GetU16(18); }


	public final boolean getDredOffGridCheck()
	{
		return GetBool((int) GetU16(20), 1, 0, true);
	}


	public final TimeSpan getCommsTimeout()
	{
		return GetU16(24, TimeSpan.fromSeconds(1));
	}


	public final Object getWiFiLEDState()
	{
		return LEDState.parse(GetU16(26));
	}


	public final Object getComLEDState()
	{
		return LEDState.parse(GetU16(28));
	}
}
