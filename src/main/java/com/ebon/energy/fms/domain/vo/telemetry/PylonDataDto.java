package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.PylonUserDefined;
import com.ebon.energy.fms.domain.vo.Ampere;
import com.ebon.energy.fms.domain.vo.Capacity;
import com.ebon.energy.fms.domain.vo.Kelvin;
import com.ebon.energy.fms.domain.vo.Volt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.Collections;
import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonDataDto extends PylonDto {
    private final int PackIndex;
    private final List<Volt> CellVoltages;
    private final List<Kelvin> TempReadings;
    private final Ampere PackCurrent;
    private final Volt PackVoltage;
    private final Capacity PackRemains;
    private final PylonUserDefined UserDefined;
    private final Capacity PackTotal;
    private final short PackCycle;

    public PylonDataDto(
            String rawMessage,
            int PackIndex,
            List<Volt> CellVoltages,
            List<Kelvin> TempReadings,
            Ampere PackCurrent,
            Volt PackVoltage,
            Capacity PackRemains,
            PylonUserDefined UserDefined,
            Capacity PackTotal,
            short PackCycle) {

        super(rawMessage);
        this.PackIndex = PackIndex;
        this.CellVoltages = CellVoltages != null ?
                Collections.unmodifiableList(CellVoltages) :
                Collections.emptyList();
        this.TempReadings = TempReadings != null ?
                Collections.unmodifiableList(TempReadings) :
                Collections.emptyList();
        this.PackCurrent = PackCurrent;
        this.PackVoltage = PackVoltage;
        this.PackRemains = PackRemains;
        this.UserDefined = UserDefined;
        this.PackTotal = PackTotal;
        this.PackCycle = PackCycle;
    }

    public int getPackIndex() { return PackIndex; }
    public List<Volt> getCellVoltages() { return CellVoltages; }
    public List<Kelvin> getTempReadings() { return TempReadings; }
    public Ampere getPackCurrent() { return PackCurrent; }
    public Volt getPackVoltage() { return PackVoltage; }
    public Capacity getPackRemains() { return PackRemains; }
    public PylonUserDefined getUserDefined() { return UserDefined; }
    public Capacity getPackTotal() { return PackTotal; }
    public short getPackCycle() { return PackCycle; }
}