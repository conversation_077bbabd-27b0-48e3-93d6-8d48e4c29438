package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;







public class BandHVP1ModuleTemperature extends DataBackedBand
{
	public BandHVP1ModuleTemperature()
	{
		super(BandForge.<BandHVP1ModuleTemperature>getMetadataFor(BandHVP1ModuleTemperature.class));
	}



	public BandHVP1ModuleTemperature(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVP1ModuleTemperature>getMetadataFor(BandHVP1ModuleTemperature.class));
	}

	public BandHVP1ModuleTemperature(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVP1ModuleTemperature>getMetadataFor(BandHVP1ModuleTemperature.class));
	}



	public final Celsius getTemperatureOfModuleNumber00()
	{
		return GetS16(0, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber01()
	{
		return GetS16(2, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber02()
	{
		return GetS16(4, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber03()
	{
		return GetS16(6, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber04()
	{
		return GetS16(8, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber05()
	{
		return GetS16(10, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber06()
	{
		return GetS16(12, Celsius.Deci);
	}


	public final Celsius getTemperatureOfModuleNumber07()
	{
		return GetS16(14, Celsius.Deci);
	}
}
