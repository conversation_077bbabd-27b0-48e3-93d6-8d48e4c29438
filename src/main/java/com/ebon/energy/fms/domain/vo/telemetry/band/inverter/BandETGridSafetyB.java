package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandETGridSafetyB extends DataBackedBand
{
	public BandETGridSafetyB()
	{
		super(BandForge.<BandETGridSafetyB>getMetadataFor(BandETGridSafetyB.class));
	}



	public BandETGridSafetyB(byte[] bytes)
	{
		super(bytes, BandForge.<BandETGridSafetyB>getMetadataFor(BandETGridSafetyB.class));
	}

	public BandETGridSafetyB(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETGridSafetyB>getMetadataFor(BandETGridSafetyB.class));
	}



	public final Frequency getPowerFreqFStopChg()
	{
		return GetU16(0, Frequency.Centi);
	}


	public final Frequency getPowerFreqFStopTransition()
	{
		return GetU16(2, Frequency.Centi);
	}


	public final boolean getPowerAndFrequencyCurveEnabled2()
	{
		return GetBool((int) GetU16(4), 1, 0, true);
	}
}
