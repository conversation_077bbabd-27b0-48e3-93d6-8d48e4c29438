package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ProductBatteryAggregateTelemetry
{
    public ProductBatteryAggregateTelemetry(int index, Watt dayMaxOutputP, ZonedDateTime dayMaxOutputPTimestampUtc, Watt dayMaxInputP, ZonedDateTime dayMaxInputPTimestampUtc, Volt highestMinCellVoltageWhileIdle)
    {
        setIndex(index);
        setDayMaxOutputP(dayMaxOutputP);
        setDayMaxOutputPTimestampUtc(dayMaxOutputPTimestampUtc);
        setDayMaxInputP(dayMaxInputP);
        setDayMaxInputPTimestampUtc(dayMaxInputPTimestampUtc);
        setHighestMinCellVoltageWhileIdle(highestMinCellVoltageWhileIdle);
    }

    private int Index;
    public final int getIndex()
    {
        return Index;
    }
    public final void setIndex(int value)
    {
        Index = value;
    }

    private Watt DayMaxOutputP = null;
    public final Watt getDayMaxOutputP()
    {
        return DayMaxOutputP;
    }
    private void setDayMaxOutputP(Watt value)
    {
        DayMaxOutputP = value;
    }

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime DayMaxOutputPTimestampUtc = null;
    public final ZonedDateTime getDayMaxOutputPTimestampUtc()
    {
        return DayMaxOutputPTimestampUtc;
    }
    private void setDayMaxOutputPTimestampUtc(ZonedDateTime value)
    {
        DayMaxOutputPTimestampUtc = value;
    }

    private Watt DayMaxInputP = null;
    public final Watt getDayMaxInputP()
    {
        return DayMaxInputP;
    }
    private void setDayMaxInputP(Watt value)
    {
        DayMaxInputP = value;
    }

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime DayMaxInputPTimestampUtc = null;
    public final ZonedDateTime getDayMaxInputPTimestampUtc()
    {
        return DayMaxInputPTimestampUtc;
    }
    private void setDayMaxInputPTimestampUtc(ZonedDateTime value)
    {
        DayMaxInputPTimestampUtc = value;
    }

    private Volt HighestMinCellVoltageWhileIdle = null;
    public final Volt getHighestMinCellVoltageWhileIdle()
    {
        return HighestMinCellVoltageWhileIdle;
    }
    private void setHighestMinCellVoltageWhileIdle(Volt value)
    {
        HighestMinCellVoltageWhileIdle = value;
    }
}