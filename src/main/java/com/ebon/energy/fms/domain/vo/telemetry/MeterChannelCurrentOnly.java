package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

/**
 * Telemetry for a current-only measurement (typically used on the neutral).
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterChannelCurrentOnly {
    /**
     * Channel identifier (0=Total, 1=L1, 2=L2, 3=L3, 4=N).
     */
    private int ChannelId;

    public final int getChannelId() {
        return ChannelId;
    }

    public final void setChannelId(int value) {
        ChannelId = value;
    }

    /**
     * Current details (A).
     */
    private AvgMinMax Current;

    public final AvgMinMax getCurrent() {
        return Current;
    }

    public final void setCurrent(AvgMinMax value) {
        Current = value;
    }
}
