package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.SGErrorCode;
import com.ebon.energy.fms.common.enums.SGInverterMode;
import com.ebon.energy.fms.common.enums.SGWarningCode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandSGInverterWarningInfo extends DataBackedBand {
    public BandSGInverterWarningInfo() {
        super(BandForge.<BandSGInverterWarningInfo>getMetadataFor(BandSGInverterWarningInfo.class));
    }


    public BandSGInverterWarningInfo(byte[] bytes) {
        super(bytes, BandForge.<BandSGInverterWarningInfo>getMetadataFor(BandSGInverterWarningInfo.class));
    }

    public BandSGInverterWarningInfo(String encodedBytes) {
        super(encodedBytes, BandForge.<BandSGInverterWarningInfo>getMetadataFor(BandSGInverterWarningInfo.class));
    }

    public final int getFaultTimeYear() { return GetU16(0); }

    public final int getFaultTimeMonthDay() { return GetU16(2); }

    public final int getFaultTimeHourMinute() { return GetU16(4); }


    public final int getFaultTimeSecond() { return GetU16(6); }


    public final Volt getFaultPhaseL1Voltage() {
        return GetU16(8, Volt.Deci);
    }


    public final Ampere getFaultPhaseL1Current() {
        return GetU16(10, Ampere.Centi);
    }


    public final Frequency getFaultPhaseL1Frequency() {
        return GetU16(12, Frequency.Centi);
    }


    public final Volt getFaultPhaseL2Voltage() {
        return GetU16(14, Volt.Deci);
    }


    public final Ampere getFaultPhaseL2Current() {
        return GetU16(16, Ampere.Centi);
    }


    public final Volt getFaultPhaseL3Voltage() {
        return GetU16(18, Volt.Deci);
    }


    public final Ampere getFaultPhaseL3Current() {
        return GetU16(20, Ampere.Centi);
    }


    public final Volt getFaultPV1Voltage() {
        return GetU16(22, Volt.Deci);
    }


    public final Ampere getFaultPV1Current() {
        return GetU16(24, Ampere.Centi);
    }


    public final Volt getFaultPV2Voltage() {
        return GetU16(26, Volt.Deci);
    }


    public final Ampere getFaultPV2Current() {
        return GetU16(28, Ampere.Centi);
    }


    public final Volt getFaultPV3Voltage() {
        return GetU16(30, Volt.Deci);
    }


    public final Ampere getFaultPV3Current() {
        return GetU16(32, Ampere.Centi);
    }


    public final Volt getFaultPV4Voltage() {
        return GetU16(34, Volt.Deci);
    }


    public final Ampere getFaultPV4Current() {
        return GetU16(36, Ampere.Centi);
    }


    public final Celsius getFaultInnerTemperature() {
        return GetS16(38, Celsius.Unit);
    }


    public final Celsius getFaultAmbientTemp() {
        return GetS16(40, Celsius.Unit);
    }


    public final Object getFaultInverterMode() {
        return SGInverterMode.parse(GetU16(42));
    }


    public final Object getFaultErrorCode() {
        return SGErrorCode.parse(GetU32(44));
    }


    public final Object getFaultErrorMessage3() {
        return SGWarningCode.parse(GetU16(48));
    }


    public final Watt getFaultTotalActivePower() {
        return GetU32(50, Watt.Deci);
    }


    public final VoltAmpsReactive getFaultTotalReactivePower() {
        return GetU32(54, VoltAmpsReactive.Deci);
    }


    public final BigDecimal getFaultPowerFactor() {
        return new BigDecimal(GetS16(58)).multiply(Percentage.Tenth);
    }


    public final Volt getFaultBusVoltagePos() {
        return GetU16(60, Volt.Deci);
    }


    public final Volt getFaultBusVoltageNeg() {
        return GetU16(62, Volt.Deci);
    }


    public final Watt getFaultInverterPowerLimit() {
        return GetU16(64, Watt.Hecto);
    }
}
