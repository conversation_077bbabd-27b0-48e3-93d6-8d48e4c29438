package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("ErrorMapping")
public class ErrorMappingDO {

    @TableField(value = "Id")
    private Integer id;

    @TableField(value = "Name")
    private String name;

    @TableField(value = "Description")
    private String description;

    @TableField(value = "LevelRb")
    private Integer levelRb;

    @TableField(value = "LevelInstaller")
    private Integer levelInstaller;

    @TableField(value = "LevelHomeuser")
    private Integer levelHomeuser;

    @TableField(value = "LastModifiedById")
    private String lastModifiedById;

    @TableField(value = "LastModifiedOnUtc")
    private Timestamp lastModifiedOnUtc;

    @TableField(value = "CreatedById")
    private String createdById;

    @TableField(value = "CreatedOnUtc")
    private Timestamp createdOnUtc;

    @TableField(value = "RebootAction")
    private Boolean rebootAction;

    @TableField(value = "EmailAction")
    private Boolean emailAction;
}