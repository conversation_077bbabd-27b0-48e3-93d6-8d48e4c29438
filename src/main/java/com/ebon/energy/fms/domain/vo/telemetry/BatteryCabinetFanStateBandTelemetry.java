package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryCabinetFanStateBandTelemetry {
    private final boolean IsFanOn;

    public BatteryCabinetFanStateBandTelemetry(boolean IsFanOn) {
        this.IsFanOn = IsFanOn;
    }

    public boolean getIsFanOn() {
        return IsFanOn;
    }
}