package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;



public class BandESGInverterBMS extends DataBackedBand
{
	public BandESGInverterBMS()
	{
		super(BandForge.<BandESGInverterBMS>getMetadataFor(BandESGInverterBMS.class));
	}



	public BandESGInverterBMS(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGInverterBMS>getMetadataFor(BandESGInverterBMS.class));
	}

	public BandESGInverterBMS(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGInverterBMS>getMetadataFor(BandESGInverterBMS.class));
	}



	public final Object getDRMStatus()
	{
		return ETDRMStatus.parse(GetU16(0));
	}


	public final Object getReserved0x9089()
	{
		return FloatBattType.parse(GetU16(2));
	}


	public final Object getBMSStatus()
	{
		return BMSStatusMasks.parse(GetU16(4));
	}


	public final Celsius getBMSPackTemperature()
	{
		return GetU16(6, Celsius.Deci);
	}


	public final Ampere getBMSChargeImax()
	{
		return GetU16(8, Ampere.Unit);
	}


	public final Ampere getBMSDischargeImax()
	{
		return GetU16(10, Ampere.Unit);
	}


	public final Object getBMSErrorCodeL()
	{
		return BMSAlarmCodeET.parse(GetU16(12));
	}


	public final BigDecimal getSOC()
	{
		return new BigDecimal(GetU16(14)).multiply(Percentage._1);
	}


	public final BigDecimal getBMSSOH()
	{
		return new BigDecimal(GetU16(16)).multiply(Percentage._1);
	}




	public final int getBMSBatteryStrings() { return GetU16(18); }


	public final Object getBMSWarningCodeL()
	{
		return BMSWarningCodeET.parse(GetU16(20));
	}


	public final Object getBMSProtocolCode()
	{
		return BMSProtocolCode.parse(GetU16(22));
	}




	public final int getBMSErrorCodeH() { return GetU16(24); }




	public final int getBMSWarningCodeH() { return GetU16(26); }




	public final int getBMSSoftwareVersion() { return GetU16(28); }




	public final int getBatteryHardwareVersion() { return GetU16(30); }




	public final int getMaximumCellTempID() { return GetU16(32); }




	public final int getMinimumCellTempID() { return GetU16(34); }




	public final int getMaximumCellVoltageID() { return GetU16(36); }




	public final int getMinimumCellVoltageID() { return GetU16(38); }


	public final Celsius getMaximumCellTemp()
	{
		return GetU16(40, Celsius.Deci);
	}


	public final Celsius getMinimumCellTemp()
	{
		return GetU16(42, Celsius.Deci);
	}


	public final Volt getMaximumCellVoltage()
	{
		return GetU16(44, Volt.Milli);
	}


	public final Volt getMinimumCellVoltage()
	{
		return GetU16(46, Volt.Milli);
	}




	public final byte[] getPassInformation1to32()
	{
		return GetRaw(48, 64);
	}
}
