package com.ebon.energy.fms.domain.vo.product.control.invert;

import com.ebon.energy.fms.common.enums.InverterScheduleOperationModeEnum;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleDays;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.*;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class InverterScheduleItemViewModel extends GenericScheduleItemViewModel {

    @JsonProperty("Mode")
    private InverterScheduleOperationModeEnum mode;

    @JsonProperty("PowerInWatts")
    private Long powerInWatts;

    @JsonProperty("CreatedBy")
    private String createdBy;

    @JsonProperty("Priority")
    private Integer priority;

    // 只读属性可用 getter
    public String getModeDisplay() {
        return mode != null ? mode.getDisplayName() : null;
    }

    public InverterScheduleItemViewModel(
            String id,
            Integer priority,
            Boolean isRecurringDaily,
            ScheduleDays scheduleDays,
            OffsetDateTime startTime,
            OffsetDateTime endTime,
            InverterScheduleOperationModeEnum mode,
            Long powerInWatts,
            String timezone,
            String createdBy,
            Boolean hideDelete) {
        // 父类构造器参数顺序：id, startTime, endTime, isRecurringDaily, scheduleDays, timezone, hideDelete
        super(id, startTime, endTime, isRecurringDaily, scheduleDays, timezone, hideDelete);
        this.mode = mode;
        this.powerInWatts = powerInWatts;
        this.createdBy = createdBy;
        this.priority = priority;
    }


}
