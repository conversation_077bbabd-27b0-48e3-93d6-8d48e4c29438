package com.ebon.energy.fms.domain.vo.telemetry.band;

import com.ebon.energy.fms.common.enums.BandReadMethod;
import com.ebon.energy.fms.common.enums.Endianness;
import com.ebon.energy.fms.domain.vo.telemetry.Frame;
import com.ebon.energy.fms.domain.vo.telemetry.IDataFrame;
import com.ebon.energy.fms.domain.vo.telemetry.TelemetryFrame;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.time.Duration;
import java.time.Instant;
import java.util.Base64;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class BandMetadata {
    Class<? extends DataBackedBand> BandClass;
    private byte DeviceAddress;
    private BandReadMethod ReadMethod;
    private String SourceIdentifier;
    private short FileNumber;
    private short BaseAddress;
    private short LengthInBytes;
    private byte BytesPerRegister;
    private Endianness MsgEndianness;
    private boolean HasReadRegisters;
    private boolean HasWriteRegisters;
    private Duration TimeToLive;
    private Duration FreshDuration;
    private Duration ReadPeriod;

    public BandMetadata(
            Class<? extends DataBackedBand> BandClass,
            int DeviceAddress,
            BandReadMethod ReadMethod,
            String SourceIdentifier,
            int FileNumber,
            int BaseAddress,
            int LengthInBytes,
            int BytesPerRegister,
            Endianness endianness,
            boolean HasReadRegisters,
            boolean HasWriteRegisters,
            Duration TimeToLive,
            Duration FreshDuration,
            Duration ReadPeriod) {
        this.BandClass = BandClass;
        this.DeviceAddress = (byte) DeviceAddress;
        this.ReadMethod = ReadMethod;
        this.SourceIdentifier = SourceIdentifier;
        this.FileNumber = (short) FileNumber;
        this.BaseAddress = (short) BaseAddress;
        this.LengthInBytes = (short) LengthInBytes;
        this.BytesPerRegister = (byte) BytesPerRegister;
        this.MsgEndianness = endianness;
        this.HasReadRegisters = HasReadRegisters;
        this.HasWriteRegisters = HasWriteRegisters;
        this.TimeToLive = TimeToLive;
        this.FreshDuration = FreshDuration;
        this.ReadPeriod = ReadPeriod;
    }

    public BandMetadata(
            Class<? extends DataBackedBand> BandClass,
            byte DeviceAddress,
            BandReadMethod ReadMethod,
            String SourceIdentifier,
            short FileNumber,
            short BaseAddress,
            short LengthInBytes,
            byte BytesPerRegister,
            Endianness endianness,
            boolean HasReadRegisters,
            boolean HasWriteRegisters,
            Duration TimeToLive,
            Duration FreshDuration,
            Duration ReadPeriod) {

        this.BandClass = BandClass;
        this.DeviceAddress = DeviceAddress;
        this.ReadMethod = ReadMethod;
        this.SourceIdentifier = SourceIdentifier;
        this.FileNumber = FileNumber;
        this.BaseAddress = BaseAddress;
        this.LengthInBytes = LengthInBytes;
        this.BytesPerRegister = BytesPerRegister;
        this.MsgEndianness = endianness;
        this.HasReadRegisters = HasReadRegisters;
        this.HasWriteRegisters = HasWriteRegisters;
        this.TimeToLive = TimeToLive;
        this.FreshDuration = FreshDuration;
        this.ReadPeriod = ReadPeriod;
    }

    public <TBand extends DataBackedBand> IDataFrame<TBand> RehydrateFrameFor(TelemetryFrame telemetryFrame, Instant rossStartTime)
    {
        Instant frameCreation = telemetryFrame.getTimestampUtc();
        return new Frame(frameCreation, CreateBand(telemetryFrame.getData())
                , Duration.between(rossStartTime, frameCreation));
    }

    public <TBand extends DataBackedBand> TBand CreateBand(byte[] bytes) {
        try {
            Class<?> bandClass = getBandClass();
            if (!DataBackedBand.class.isAssignableFrom(bandClass)) {
                throw new IllegalStateException("Invalid band class");
            }
            TBand band = ((Class<TBand>) bandClass).getDeclaredConstructor().newInstance();
            band.setBytes(bytes);
            return band;
        } catch (Exception e) {
            throw new RuntimeException("Band creation failed", e);
        }
    }

    public <TBand extends DataBackedBand> TBand CreateBand(String dataAsBase64) {
        byte[] bytes = Base64.getDecoder().decode(dataAsBase64);
        return CreateBand(bytes);
    }
}