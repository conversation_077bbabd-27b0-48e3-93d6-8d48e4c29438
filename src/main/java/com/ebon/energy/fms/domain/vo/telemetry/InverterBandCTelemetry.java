package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.enums.Country;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.math.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class InverterBandCTelemetry implements ITelemetryBand {
    private final TimeSpan ChargerTimeStart;
    private final TimeSpan ChargerTimeEnd;
    private final double BatChargePowerMax;
    private final TimeSpan DisChargerTimeStart;
    private final TimeSpan DisChargerTimeEnd;
    private final double BatDisPowerSet;
    private final boolean BackUpEnable;
    private final boolean OffGridAutoCharge;
    private final boolean EnableMPPT4Shadow;
    private final boolean FeedPowerEnable;
    private final int ManufacturerCode;
    private final Capacity LeadBatCapacity;
    private final Volt BattChargeVoltMax;
    private final Ampere BattChargeCurrMax;
    private final Ampere BattDisChargeCurrMax;
    private final Volt BattVoltUnderMin;
    private final double BattSOCUnderMin;
    private final TimeSpan BatActivePeriod;
    private final double RPControlPara;
    private final Volt BattFloatVolt;
    private final Ampere BattFloatCurr;
    private final TimeSpan BattToFloatTime;
    private final FloatBattType BattTypeIndex;
    private final Watt FeedPowerPara;
    private final boolean AutoStartBackup;
    private final boolean StopSocProtect;
    private final boolean DCVoltOutput;
    private final Volt BattAvgChagVolt;
    private final TimeSpan BattAvgChgHours;
    private final AS477Parameters AS4777_2Parameters;
    private final InverterModeValue WgPowerMode;
    private final Watt WgPowerSet;
    private final boolean NoGridChargeEnable;
    private final boolean DisChgWithPVEnable;
    private final int AppModeIndex;
    private final GridWaveQualityLevelCheck GridWaveCheckLevel;
    private final Watt MeterCheckValue;
    private final boolean RapidCutOff;
    private final Volt GridVoltQuality;
    private final Volt GridVoltHighS2;
    private final Volt GridVoltLowS2;
    private final TimeSpan GridVoltHighS2Time;
    private final TimeSpan GridVoltLowS2Time;
    private final Volt GridVoltHighS1;
    private final Volt GridVoltLowS1;
    private final TimeSpan GridVoltHighS1Time;
    private final TimeSpan GridVoltLowS1Time;
    private final Frequency GridFreqHighS2;
    private final Frequency GridFreqLowS2;
    private final TimeSpan GridFreqHighS2Time;
    private final TimeSpan GridFreqLowS2Time;
    private final Frequency GridFreqHighS1;
    private final Frequency GridFreqLowS1;
    private final TimeSpan GridFreqHighS1Time;
    private final TimeSpan GridFreqLowS1Time;
    private final Volt GridVoltRecoverHigh;
    private final Volt GridVoltRecoverLow;
    private final TimeSpan GridVoltRecoverTime;
    private final Frequency GridFreqRecoverHigh;
    private final Frequency GridFreqRecoverLow;
    private final TimeSpan GridFreqRecoverTime;
    private final double PointBValue;
    private final double PointCValue;
    private final Volt GridLimitByVolStartVol;
    private final double GridLimitByVolStartPer;
    private final int GridLimitByVolSlope;
    private final Volt ActiveCurveVolt;
    private final Volt DesactiveCurveVolt;
    private final boolean EnableCurve;
    private final TimeSpan BackupStartDly;
    private final int RecoverTimeEE;
    private final Country SafetyCountry;
    private final Ohm IsoLimit;
    private final double BatBMSCurrLmtCoff;
    private final boolean MeterConnectCheckFlag;
    private final MeterConnectStatus MeterConnectStatus;
    private final UpsStdVoltType UpsStdVoltType;
    private final FunctionStatus FunctionStatus;
    private final Volt BattOfflineVoltUnderMin;
    private final double BattOfflineSOCUnderMin;
    private final Boolean OnlyNightDischarge;
    private final BMSProtocolCode BMSProtocolCode;

    public InverterBandCTelemetry(
            TimeSpan ChargerTimeStart,
            TimeSpan ChargerTimeEnd,
            double BatChargePowerMax,
            TimeSpan DisChargerTimeStart,
            TimeSpan DisChargerTimeEnd,
            double BatDisPowerSet,
            boolean BackUpEnable,
            boolean OffGridAutoCharge,
            boolean EnableMPPT4Shadow,
            boolean FeedPowerEnable,
            int ManufacturerCode,
            Capacity LeadBatCapacity,
            Volt BattChargeVoltMax,
            Ampere BattChargeCurrMax,
            Ampere BattDisChargeCurrMax,
            Volt BattVoltUnderMin,
            double BattSOCUnderMin,
            TimeSpan BatActivePeriod,
            double RPControlPara,
            Volt BattFloatVolt,
            Ampere BattFloatCurr,
            TimeSpan BattToFloatTime,
            FloatBattType BattTypeIndex,
            Watt FeedPowerPara,
            boolean AutoStartBackup,
            boolean StopSocProtect,
            boolean DCVoltOutput,
            Volt BattAvgChagVolt,
            TimeSpan BattAvgChgHours,
            AS477Parameters AS4777_2Parameters,
            InverterModeValue WgPowerMode,
            Watt WgPowerSet,
            boolean NoGridChargeEnable,
            boolean DisChgWithPVEnable,
            int AppModeIndex,
            GridWaveQualityLevelCheck GridWaveCheckLevel,
            Watt MeterCheckValue,
            boolean RapidCutOff,
            Volt GridVoltQuality,
            Volt GridVoltHighS2,
            Volt GridVoltLowS2,
            TimeSpan GridVoltHighS2Time,
            TimeSpan GridVoltLowS2Time,
            Volt GridVoltHighS1,
            Volt GridVoltLowS1,
            TimeSpan GridVoltHighS1Time,
            TimeSpan GridVoltLowS1Time,
            Frequency GridFreqHighS2,
            Frequency GridFreqLowS2,
            TimeSpan GridFreqHighS2Time,
            TimeSpan GridFreqLowS2Time,
            Frequency GridFreqHighS1,
            Frequency GridFreqLowS1,
            TimeSpan GridFreqHighS1Time,
            TimeSpan GridFreqLowS1Time,
            Volt GridVoltRecoverHigh,
            Volt GridVoltRecoverLow,
            TimeSpan GridVoltRecoverTime,
            Frequency GridFreqRecoverHigh,
            Frequency GridFreqRecoverLow,
            TimeSpan GridFreqRecoverTime,
            double PointBValue,
            double PointCValue,
            Volt GridLimitByVolStartVol,
            double GridLimitByVolStartPer,
            int GridLimitByVolSlope,
            Volt ActiveCurveVolt,
            Volt DesactiveCurveVolt,
            boolean EnableCurve,
            TimeSpan BackupStartDly,
            int RecoverTimeEE,
            Country SafetyCountry,
            Ohm IsoLimit,
            double BatBMSCurrLmtCoff,
            boolean MeterConnectCheckFlag,
            MeterConnectStatus MeterConnectStatus,
            UpsStdVoltType UpsStdVoltType,
            FunctionStatus FunctionStatus,
            Volt BattOfflineVoltUnderMin,
            double BattOfflineSOCUnderMin,
            Boolean OnlyNightDischarge,
            BMSProtocolCode BMSProtocolCode) {

        this.ChargerTimeStart = ChargerTimeStart;
        this.ChargerTimeEnd = ChargerTimeEnd;
        this.BatChargePowerMax = BatChargePowerMax;
        this.DisChargerTimeStart = DisChargerTimeStart;
        this.DisChargerTimeEnd = DisChargerTimeEnd;
        this.BatDisPowerSet = BatDisPowerSet;
        this.BackUpEnable = BackUpEnable;
        this.OffGridAutoCharge = OffGridAutoCharge;
        this.EnableMPPT4Shadow = EnableMPPT4Shadow;
        this.FeedPowerEnable = FeedPowerEnable;
        this.ManufacturerCode = ManufacturerCode;
        this.LeadBatCapacity = LeadBatCapacity;
        this.BattChargeVoltMax = BattChargeVoltMax;
        this.BattChargeCurrMax = BattChargeCurrMax;
        this.BattDisChargeCurrMax = BattDisChargeCurrMax;
        this.BattVoltUnderMin = BattVoltUnderMin;
        this.BattSOCUnderMin = BattSOCUnderMin;
        this.BatActivePeriod = BatActivePeriod;
        this.RPControlPara = RPControlPara;
        this.BattFloatVolt = BattFloatVolt;
        this.BattFloatCurr = BattFloatCurr;
        this.BattToFloatTime = BattToFloatTime;
        this.BattTypeIndex = BattTypeIndex;
        this.FeedPowerPara = FeedPowerPara;
        this.AutoStartBackup = AutoStartBackup;
        this.StopSocProtect = StopSocProtect;
        this.DCVoltOutput = DCVoltOutput;
        this.BattAvgChagVolt = BattAvgChagVolt;
        this.BattAvgChgHours = BattAvgChgHours;
        this.AS4777_2Parameters = AS4777_2Parameters;
        this.WgPowerMode = WgPowerMode;
        this.WgPowerSet = WgPowerSet;
        this.NoGridChargeEnable = NoGridChargeEnable;
        this.DisChgWithPVEnable = DisChgWithPVEnable;
        this.AppModeIndex = AppModeIndex;
        this.GridWaveCheckLevel = GridWaveCheckLevel;
        this.MeterCheckValue = MeterCheckValue;
        this.RapidCutOff = RapidCutOff;
        this.GridVoltQuality = GridVoltQuality;
        this.GridVoltHighS2 = GridVoltHighS2;
        this.GridVoltLowS2 = GridVoltLowS2;
        this.GridVoltHighS2Time = GridVoltHighS2Time;
        this.GridVoltLowS2Time = GridVoltLowS2Time;
        this.GridVoltHighS1 = GridVoltHighS1;
        this.GridVoltLowS1 = GridVoltLowS1;
        this.GridVoltHighS1Time = GridVoltHighS1Time;
        this.GridVoltLowS1Time = GridVoltLowS1Time;
        this.GridFreqHighS2 = GridFreqHighS2;
        this.GridFreqLowS2 = GridFreqLowS2;
        this.GridFreqHighS2Time = GridFreqHighS2Time;
        this.GridFreqLowS2Time = GridFreqLowS2Time;
        this.GridFreqHighS1 = GridFreqHighS1;
        this.GridFreqLowS1 = GridFreqLowS1;
        this.GridFreqHighS1Time = GridFreqHighS1Time;
        this.GridFreqLowS1Time = GridFreqLowS1Time;
        this.GridVoltRecoverHigh = GridVoltRecoverHigh;
        this.GridVoltRecoverLow = GridVoltRecoverLow;
        this.GridVoltRecoverTime = GridVoltRecoverTime;
        this.GridFreqRecoverHigh = GridFreqRecoverHigh;
        this.GridFreqRecoverLow = GridFreqRecoverLow;
        this.GridFreqRecoverTime = GridFreqRecoverTime;
        this.PointBValue = PointBValue;
        this.PointCValue = PointCValue;
        this.GridLimitByVolStartVol = GridLimitByVolStartVol;
        this.GridLimitByVolStartPer = GridLimitByVolStartPer;
        this.GridLimitByVolSlope = GridLimitByVolSlope;
        this.ActiveCurveVolt = ActiveCurveVolt;
        this.DesactiveCurveVolt = DesactiveCurveVolt;
        this.EnableCurve = EnableCurve;
        this.BackupStartDly = BackupStartDly;
        this.RecoverTimeEE = RecoverTimeEE;
        this.SafetyCountry = SafetyCountry;
        this.IsoLimit = IsoLimit;
        this.BatBMSCurrLmtCoff = BatBMSCurrLmtCoff;
        this.MeterConnectCheckFlag = MeterConnectCheckFlag;
        this.MeterConnectStatus = MeterConnectStatus;
        this.UpsStdVoltType = UpsStdVoltType;
        this.FunctionStatus = FunctionStatus;
        this.BattOfflineVoltUnderMin = BattOfflineVoltUnderMin;
        this.BattOfflineSOCUnderMin = BattOfflineSOCUnderMin;
        this.OnlyNightDischarge = OnlyNightDischarge;
        this.BMSProtocolCode = BMSProtocolCode;
    }
}
