package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.SGRebootCode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.time.*;


public class BandSGInverterRebootReason extends DataBackedBand
{
	public BandSGInverterRebootReason()
	{
		super(BandForge.<BandSGInverterRebootReason>getMetadataFor(BandSGInverterRebootReason.class));
	}



	public BandSGInverterRebootReason(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterRebootReason>getMetadataFor(BandSGInverterRebootReason.class));
	}

	public BandSGInverterRebootReason(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterRebootReason>getMetadataFor(BandSGInverterRebootReason.class));
	}


	
	public final SGRebootCode getInverterRebootReasonLog1()
	{
		return SGRebootCode.forValue(GetU16(0));
	}

	
	public final SGRebootCode getInverterRebootReasonLog2()
	{
		return SGRebootCode.forValue(GetU16(2));
	}

	
	public final SGRebootCode getInverterRebootReasonLog3()
	{
		return SGRebootCode.forValue(GetU16(4));
	}

	
	public final SGRebootCode getInverterRebootReasonLog4()
	{
		return SGRebootCode.forValue(GetU16(6));
	}

	
	public final SGRebootCode getInverterRebootReasonLog5()
	{
		return SGRebootCode.forValue(GetU16(8));
	}

	
	public final SGRebootCode getInverterRebootReasonLog6()
	{
		return SGRebootCode.forValue(GetU16(10));
	}

	
	public final SGRebootCode getInverterRebootReasonLog7()
	{
		return SGRebootCode.forValue(GetU16(12));
	}

	
	public final SGRebootCode getInverterRebootReasonLog8()
	{
		return SGRebootCode.forValue(GetU16(14));
	}

	
	public final SGRebootCode getInverterRebootReasonLog9()
	{
		return SGRebootCode.forValue(GetU16(16));
	}

	
	public final SGRebootCode getInverterRebootReasonLog10()
	{
		return SGRebootCode.forValue(GetU16(18));
	}

	
	public final LocalDateTime getInverterRebootTimeLog1()
	{
		return ConversionHelpers.convertToEpoch(GetU32(20));
	}

	
	public final LocalDateTime getInverterRebootTimeLog2()
	{
		return ConversionHelpers.convertToEpoch(GetU32(24));
	}

	
	public final LocalDateTime getInverterRebootTimeLog3()
	{
		return ConversionHelpers.convertToEpoch(GetU32(28));
	}

	
	public final LocalDateTime getInverterRebootTimeLog4()
	{
		return ConversionHelpers.convertToEpoch(GetU32(32));
	}

	
	public final LocalDateTime getInverterRebootTimeLog5()
	{
		return ConversionHelpers.convertToEpoch(GetU32(36));
	}

	
	public final LocalDateTime getInverterRebootTimeLog6()
	{
		return ConversionHelpers.convertToEpoch(GetU32(40));
	}

	
	public final LocalDateTime getInverterRebootTimeLog7()
	{
		return ConversionHelpers.convertToEpoch(GetU32(44));
	}

	
	public final LocalDateTime getInverterRebootTimeLog8()
	{
		return ConversionHelpers.convertToEpoch(GetU32(48));
	}

	
	public final LocalDateTime getInverterRebootTimeLog9()
	{
		return ConversionHelpers.convertToEpoch(GetU32(52));
	}

	
	public final LocalDateTime getInverterRebootTimeLog10()
	{
		return ConversionHelpers.convertToEpoch(GetU32(56));
	}
}
