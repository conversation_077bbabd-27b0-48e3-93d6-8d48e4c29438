package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandEH1PInverterDC extends DataBackedBand
{
	public BandEH1PInverterDC()
	{
		super(BandForge.<BandEH1PInverterDC>getMetadataFor(BandEH1PInverterDC.class));
	}



	public BandEH1PInverterDC(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterDC>getMetadataFor(BandEH1PInverterDC.class));
	}

	public BandEH1PInverterDC(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterDC>getMetadataFor(BandEH1PInverterDC.class));
	}


	
	public final Volt getVpv1()
	{
		return GetU16(0, Volt.Deci);
	}

	
	public final Ampere getIpv1()
	{
		return GetU16(2, Ampere.Deci);
	}

	
	public final Volt getVpv2()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Ampere getIpv2()
	{
		return GetU16(6, Ampere.Deci);
	}

	
	public final Volt getVpv3()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Ampere getIpv3()
	{
		return GetU16(10, Ampere.Deci);
	}

	
	public final Volt getVpv4()
	{
		return GetU16(12, Volt.Deci);
	}

	
	public final Ampere getIpv4()
	{
		return GetU16(14, Ampere.Deci);
	}

	
	public final Watt getPpvTotal()
	{
		return GetU32(16, Watt.Unit);
	}
}
