package com.ebon.energy.fms.domain.vo.product.control.report;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatteryReportedSettings {

    @JsonProperty("maxOffgridDischargeCurrent")
    private Integer maxOffgridDischargeCurrent;

    @JsonProperty("maxChargeCurrent")
    private Integer maxChargeCurrent;

    @JsonProperty("maxDischargeCurrent")
    private Integer maxDischargeCurrent;

    @JsonProperty("minStateOfChargePercent")
    private Integer minSoCPercent;

    @JsonProperty("minOffgridSoCPercent")
    private Integer minOffgridSoCPercent;

    /**
     * Gets Max Charge Voltage. Only used for ageing testing. Should only be set when Manufacturer is Custom
     */
    @JsonProperty("maxChargeVoltage")
    private BigDecimal maxChargeVoltage;

    /**
     * Gets Max Discharge Charge Voltage. Only used for ageing testing. Should only be set when Manufacturer is Custom
     */
    @JsonProperty("minDischargeVoltage")
    private BigDecimal minDischargeVoltage;

    @JsonProperty("chargeVMaxOverride")
    private BigDecimal chargeVMaxOverride;

    @JsonProperty("chargeIMaxOverride")
    private BigDecimal chargeIMaxOverride;

    @JsonProperty("dischargeVMinOverride")
    private BigDecimal dischargeVMinOverride;

    @JsonProperty("dischargeIMaxOverride")
    private BigDecimal dischargeIMaxOverride;

    @JsonProperty("soC0to1Override")
    private BigDecimal soC0to1Override;

    @JsonProperty("warningCodeOverride")
    private Integer warningCodeOverride;

    @JsonProperty("alarmCodeOverride")
    private Integer alarmCodeOverride;

    @JsonProperty("statusOverride")
    private Integer statusOverride;

    @JsonProperty("highSoCProtection0to1")
    private BigDecimal highSoCProtection0to1;

    @JsonProperty("highSoCProtectionIA")
    private BigDecimal highSoCProtectionIA;

    // equals, hashCode, and toString are generated by Lombok (@Data)

    // 如果需要和 BatteryDesiredSettings 比较差异，可以添加如下方法（伪代码，需根据实际类实现）：
    /*
    public List<ConfigurationDifference> getDifferences(BatteryDesiredSettings desired) {
        // 实现与C#类似的差异比较逻辑
    }
    */
}
