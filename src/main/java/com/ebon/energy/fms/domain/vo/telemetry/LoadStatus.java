package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class LoadStatus implements Cloneable {
    public LoadStatus() { }

    /**
     * Please consider using the constructor with typed units instead.
     */
    public LoadStatus(
            BigDecimal AllTimeTotalE,
            ZonedDateTime AllTimeEUtc,
            ZonedDateTime AllTimeELocal) {
        this.AllTimeTotalE = AllTimeTotalE;
        this.AllTimeEUtc = AllTimeEUtc;
        this.AllTimeELocal = AllTimeELocal;
    }

   /* public LoadStatus(
            WattHour AllTimeTotalE,
            ZonedDateTime AllTimeEUtc,
            ZonedDateTime AllTimeELocal) {
        this.AllTimeTotalE = AllTimeTotalE != null ?
                AllTimeTotalE.asDecimal(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) :
                null;
        this.AllTimeEUtc = AllTimeEUtc;
        this.AllTimeELocal = AllTimeELocal;
    }*/

    /**
     * Cumulative energy of loads in kWh. In normal circumstances this value should always grow.
     * New in 2.10. Please note that this value is taken from running totals, not from the telemetry.
     * Units: kWh
     */
    // @SystemStatus(minValue = 0, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "kWh")
    // @Display(name = "All time total combined load", description = "Cumulative load energy")
    private BigDecimal AllTimeTotalE;

    // @SystemStatus(logLevel = LogLevels.LEVEL3, role = Roles.SYSTEM_ADMIN,
    //               visibility = Visibilities.DETAILED)
    // @Display(name = "All time total combined load (timestamp Utc)")
    // @JsonConverter(UtcTimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime AllTimeEUtc;

    // @SystemStatus(logLevel = LogLevels.LEVEL3, role = Roles.SYSTEM_ADMIN,
    //               visibility = Visibilities.DETAILED)
    // @Display(name = "All time total combined load (timestamp local)")
    // @JsonConverter(SystemStatusLocalTimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private ZonedDateTime AllTimeELocal;

    // Getters and Setters with original casing
    public BigDecimal getAllTimeTotalE() {
        return AllTimeTotalE;
    }

    public void setAllTimeTotalE(BigDecimal AllTimeTotalE) {
        this.AllTimeTotalE = AllTimeTotalE;
    }

    public ZonedDateTime getAllTimeEUtc() {
        return AllTimeEUtc;
    }

    public void setAllTimeEUtc(ZonedDateTime AllTimeEUtc) {
        this.AllTimeEUtc = AllTimeEUtc;
    }

    public ZonedDateTime getAllTimeELocal() {
        return AllTimeELocal;
    }

    public void setAllTimeELocal(ZonedDateTime AllTimeELocal) {
        this.AllTimeELocal = AllTimeELocal;
    }

    @Override
    public LoadStatus clone() {
        try {
            return (LoadStatus) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // Can't happen
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LoadStatus that = (LoadStatus) o;
        return Objects.equals(AllTimeTotalE, that.AllTimeTotalE) &&
                Objects.equals(AllTimeEUtc, that.AllTimeEUtc) &&
                Objects.equals(AllTimeELocal, that.AllTimeELocal);
    }

    @Override
    public int hashCode() {
        return Objects.hash(AllTimeTotalE, AllTimeEUtc, AllTimeELocal);
    }

    @Override
    public String toString() {
        return "LoadStatus{" +
                "AllTimeTotalE=" + AllTimeTotalE +
                ", AllTimeEUtc=" + AllTimeEUtc +
                ", AllTimeELocal=" + AllTimeELocal +
                '}';
    }
}