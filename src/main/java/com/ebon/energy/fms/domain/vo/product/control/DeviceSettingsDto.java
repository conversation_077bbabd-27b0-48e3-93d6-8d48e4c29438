package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;


/**
 * Java equivalent of the C# DeviceSettingsDto class
 */

@Data
public class DeviceSettingsDto extends DeviceInfoAndSettings {

    public DeviceSettingsDto(RossReportedSettings reported, RossDesiredSettings desired, DeviceSettingsIntentVO intent, InverterIdentityCard identityCard, ExplicitSettings explicitSettings) {
        super(reported, desired, intent, identityCard, explicitSettings);
    }
}
