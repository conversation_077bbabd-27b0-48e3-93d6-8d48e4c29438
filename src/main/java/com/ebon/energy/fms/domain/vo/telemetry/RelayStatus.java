package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class RelayStatus {
    private RelayID ID;
    private String Name;
    private Status Status;
    private RelayWorkingMode Mode;
    private Double DayMinutesRun;

    public RelayStatus() {
    }

    public RelayStatus(RelayID iD, String name, Status status, RelayWorkingMode mode, Double dayMinutesRun) {
        ID = iD;
        Name = name;
        Status = status;
        Mode = mode;
        DayMinutesRun = dayMinutesRun;
    }

    public RelayID getID() {
        return ID;
    }

    public void setID(RelayID iD) {
        ID = iD;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public Status getStatus() {
        return Status;
    }

    public void setStatus(Status status) {
        Status = status;
    }

    public RelayWorkingMode getMode() {
        return Mode;
    }

    public void setMode(RelayWorkingMode mode) {
        Mode = mode;
    }

    public Double getDayMinutesRun() {
        return DayMinutesRun;
    }

    public void setDayMinutesRun(Double dayMinutesRun) {
        DayMinutesRun = dayMinutesRun;
    }
}