package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.InverterOperationModeType;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@JsonSerialize
public class InverterOperationVO {

    public InverterOperationVO() {
        Type = "Set";
        Mode = "Auto";
        PowerInWatts = 0L;
        Schedules = new ArrayList<>();
    }

    @JsonProperty(index = 1)
    private String Type;

    @JsonProperty(index = 2)
    private String Mode;

    @JsonProperty(index = 3)
    private long PowerInWatts;

    @JsonProperty(index = 4)
    private List<InverterScheduleVO> Schedules;

    public static InverterOperationVO Default() {
        return new InverterOperationVO();
    }

    public static InverterOperationVO CreateManualInverterSchedule(List<InverterScheduleVO> schedules) {
        InverterOperationVO operation = new InverterOperationVO();
        operation.Type = InverterOperationModeType.Schedule.toString();
        operation.Schedules = schedules;
        return operation;
    }

    public boolean IsTheSameForOptimizaton(InverterOperationVO other) {
        if (Schedules.size() == other.Schedules.size()) {
            for (int i = 0; i < other.Schedules.size(); i++) {
                if (!Schedules.get(i).equals(other.Schedules.get(i))) {
                    return false;
                }
            }
        } else if (Schedules.size() == other.Schedules.size() + 1) {
            for (int i = 0; i < other.Schedules.size(); i++) {
                if (!Schedules.get(i + 1).equals(other.Schedules.get(i))) {
                    return false;
                }
            }
        } else {
            return false;
        }

        return Objects.equals(Type, other.Type)
                && Objects.equals(Mode, other.Mode)
                && PowerInWatts == other.PowerInWatts;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getMode() {
        return Mode;
    }

    public void setMode(String mode) {
        Mode = mode;
    }

    public long getPowerInWatts() {
        return PowerInWatts;
    }

    public void setPowerInWatts(long powerInWatts) {
        PowerInWatts = powerInWatts;
    }

    public List<InverterScheduleVO> getSchedules() {
        return Schedules;
    }

    public void setSchedules(List<InverterScheduleVO> schedules) {
        Schedules = schedules;
    }
}
