package com.ebon.energy.fms.domain.vo.setting.provider;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class SettingsBuilderHelper {
    public static void addValue(JsonNode subsection, String settingPath, JsonNode value) {
        int indexOfNextDot = settingPath.indexOf('.');
        if (indexOfNextDot == -1) {
            if (subsection instanceof ObjectNode) {
                ((ObjectNode) subsection).set(settingPath, value);
            }
            return;
        }

        String nextPath = settingPath.substring(0, indexOfNextDot);
        if (!subsection.has(nextPath) || subsection.get(nextPath).isNull()) {
            if (subsection instanceof ObjectNode) {
                ((ObjectNode) subsection).set(nextPath, new ObjectMapper().createObjectNode());
            }
        }

        addValue(subsection.get(nextPath), settingPath.substring(indexOfNextDot + 1), value);
    }
}
