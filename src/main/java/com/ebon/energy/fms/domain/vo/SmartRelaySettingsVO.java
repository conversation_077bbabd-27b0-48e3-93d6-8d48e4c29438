package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.SmartRelayLoadType;
import com.ebon.energy.fms.common.enums.SmartRelayMode;
import com.ebon.energy.fms.common.enums.SmartRelaySource;

import java.time.Duration;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class SmartRelaySettingsVO {
    private String Name;
    private Boolean Installed;
    private SmartRelayLoadType LoadType;
    private SmartRelayMode Mode;
    private Boolean Enabled;
    private SmartRelaySource Source;
    private Duration OnDelay;
    private Integer OnTrigger;
    private Duration OffDelay;
    private Integer OffTrigger;
    private Duration MinRunTime;
    private Duration MinOffTime;
    private Duration DailyRunTimeTarget;
    private Duration DailyRunTimeCompletionTime;
    private Boolean DailyRunTimeUseExcessPower;
    private Boolean RelayActiveHigh;
    private String TimeZoneAlias;
    private Integer MinTriggerPowerW;
    private Integer MaxTriggerPowerW;
    private Integer NominatedRelay;
    private Boolean HasMultipleRelays;
    private Boolean NominatedRelayStartsFromTwo;

    public SmartRelaySettingsVO(
            String Name,
            Boolean Installed,
            SmartRelayLoadType LoadType,
            SmartRelayMode Mode,
            Boolean Enabled,
            SmartRelaySource Source,
            Integer OnTrigger,
            Duration OnDelay,
            Integer OffTrigger,
            Duration OffDelay,
            Duration MinRunTime,
            Duration MinOffTime,
            Duration DailyRunTimeTarget,
            Duration DailyRunTimeCompletionTime,
            Boolean DailyRunTimeUseExcessPower,
            Boolean RelayActiveHigh,
            String TimeZoneAlias,
            Integer MinTriggerPowerW,
            Integer MaxTriggerPowerW,
            Integer NominatedRelay,
            Boolean HasMultipleRelays,
            Boolean NominatedRelayStartsFromTwo) {
        this.Name = Name;
        this.Installed = Installed;
        this.LoadType = LoadType;
        this.Mode = Mode;
        this.Enabled = Enabled;
        this.Source = Source;
        this.OnTrigger = OnTrigger;
        this.OnDelay = OnDelay;
        this.OffTrigger = OffTrigger;
        this.OffDelay = OffDelay;
        this.MinRunTime = MinRunTime;
        this.MinOffTime = MinOffTime;
        this.DailyRunTimeTarget = DailyRunTimeTarget;
        this.DailyRunTimeCompletionTime = DailyRunTimeCompletionTime;
        this.DailyRunTimeUseExcessPower = DailyRunTimeUseExcessPower;
        this.RelayActiveHigh = RelayActiveHigh;
        this.TimeZoneAlias = TimeZoneAlias;
        this.MinTriggerPowerW = MinTriggerPowerW;
        this.MaxTriggerPowerW = MaxTriggerPowerW;
        this.NominatedRelay = NominatedRelay;
        this.HasMultipleRelays = HasMultipleRelays;
        this.NominatedRelayStartsFromTwo = NominatedRelayStartsFromTwo;
    }

    public SmartRelaySettingsVO() {
    }

    public SmartRelaySettingsVO(
            SmartRelayMode Mode,
            Boolean Enabled,
            SmartRelaySource Source,
            Integer OnTrigger,
            Duration OnDelay,
            Integer OffTrigger,
            Duration OffDelay,
            Duration MinRunTime,
            Duration MinOffTime,
            Duration DailyRunTimeTarget,
            Duration DailyRunTimeCompletionTime,
            Boolean DailyRunTimeUseExcessPower,
            Boolean RelayActiveHigh,
            String TimeZoneAlias,
            Integer NominatedRelay,
            Boolean HasMultipleRelays,
            Boolean NominatedRelayStartsFromTwo) {
        this.Mode = Mode;
        this.Enabled = Enabled;
        this.Source = Source;
        this.OnTrigger = OnTrigger;
        this.OnDelay = OnDelay;
        this.OffTrigger = OffTrigger;
        this.OffDelay = OffDelay;
        this.MinRunTime = MinRunTime;
        this.MinOffTime = MinOffTime;
        this.DailyRunTimeTarget = DailyRunTimeTarget;
        this.DailyRunTimeCompletionTime = DailyRunTimeCompletionTime;
        this.DailyRunTimeUseExcessPower = DailyRunTimeUseExcessPower;
        this.RelayActiveHigh = RelayActiveHigh;
        this.TimeZoneAlias = TimeZoneAlias;
        this.NominatedRelay = NominatedRelay;
        this.HasMultipleRelays = HasMultipleRelays;
        this.NominatedRelayStartsFromTwo = NominatedRelayStartsFromTwo;
    }

    public String getName() {
        return Name;
    }

    public void setName(String Name) {
        this.Name = Name;
    }

    public Boolean getInstalled() {
        return Installed;
    }

    public void setInstalled(Boolean Installed) {
        this.Installed = Installed;
    }

    public SmartRelayLoadType getLoadType() {
        return LoadType;
    }

    public void setLoadType(SmartRelayLoadType LoadType) {
        this.LoadType = LoadType;
    }

    public SmartRelayMode getMode() {
        return Mode;
    }

    public Boolean getEnabled() {
        return Enabled;
    }

    public SmartRelaySource getSource() {
        return Source;
    }

    public Duration getOnDelay() {
        return OnDelay;
    }

    public Integer getOnTrigger() {
        return OnTrigger;
    }

    public Duration getOffDelay() {
        return OffDelay;
    }

    public Integer getOffTrigger() {
        return OffTrigger;
    }

    public Duration getMinRunTime() {
        return MinRunTime;
    }

    public Duration getMinOffTime() {
        return MinOffTime;
    }

    public Duration getDailyRunTimeTarget() {
        return DailyRunTimeTarget;
    }

    public Duration getDailyRunTimeCompletionTime() {
        return DailyRunTimeCompletionTime;
    }

    public Boolean getDailyRunTimeUseExcessPower() {
        return DailyRunTimeUseExcessPower;
    }

    public Boolean getRelayActiveHigh() {
        return RelayActiveHigh;
    }

    public String getTimeZoneAlias() {
        return TimeZoneAlias;
    }

    public Integer getMinTriggerPowerW() {
        return MinTriggerPowerW;
    }

    public Integer getMaxTriggerPowerW() {
        return MaxTriggerPowerW;
    }

    public Integer getNominatedRelay() {
        return NominatedRelay;
    }

    public Boolean isHasMultipleRelays() {
        return HasMultipleRelays;
    }

    public Boolean isNominatedRelayStartsFromTwo() {
        return NominatedRelayStartsFromTwo;
    }

    @JsonIgnore
    public SmartRelaySettingsVO getActualDesiredSettingsDto() {
        return new SmartRelaySettingsVO(
                this.Name,
                this.Installed,
                this.LoadType,
                this.Mode,
                this.Enabled,
                this.Source,
                this.OnTrigger,
                this.OnDelay,
                this.OffTrigger,
                this.OffDelay,
                this.MinRunTime,
                this.MinOffTime,
                this.DailyRunTimeTarget,
                this.DailyRunTimeCompletionTime,
                this.DailyRunTimeUseExcessPower,
                this.RelayActiveHigh,
                this.TimeZoneAlias,
                null,
                null,
                null,
                false,
                false
        );
    }
}