package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;





public class BandHVSN2 extends DataBackedBand
{
	public BandHVSN2()
	{
		super(BandForge.<BandHVSN2>getMetadataFor(BandHVSN2.class));
	}



	public BandHVSN2(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVSN2>getMetadataFor(BandHVSN2.class));
	}

	public BandHVSN2(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVSN2>getMetadataFor(BandHVSN2.class));
	}


	
	public final String getBatteryModule4SerialNumber()
	{
		return GetBufS(0, 32, StringProcessors.PylonHVDecode);
	}

	
	public final String getBatteryModule5SerialNumber()
	{
		return GetBufS(32, 32, StringProcessors.PylonHVDecode);
	}

	
	public final String getBatteryModule6SerialNumber()
	{
		return GetBufS(64, 32, StringProcessors.PylonHVDecode);
	}

	
	public final String getBatteryModule7SerialNumber()
	{
		return GetBufS(96, 32, StringProcessors.PylonHVDecode);
	}
}
