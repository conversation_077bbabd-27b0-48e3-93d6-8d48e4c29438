package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;

import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandHVRC extends DataBackedBand
{
	public BandHVRC()
	{
		super(BandForge.<BandHVRC>getMetadataFor(BandHVRC.class));
	}



	public BandHVRC(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVRC>getMetadataFor(BandHVRC.class));
	}

	public BandHVRC(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVRC>getMetadataFor(BandHVRC.class));
	}
}
