package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class FirmwareUpdateTelemetry {

    private final List<IndividualFirmwareUpdateTelemetry> ARM;
    private final List<IndividualFirmwareUpdateTelemetry> DSP;
    private final Map<String, Boolean> Criteria;
    private final String Status;
    private final String ConfigurationId;
    private final LocalDateTime StartedAt;
    private final LocalDateTime CompletedAt;

    public FirmwareUpdateTelemetry() {
        this(null, null, null, null, null, null, null);
    }

    public FirmwareUpdateTelemetry(
            List<IndividualFirmwareUpdateTelemetry> ARM,
            List<IndividualFirmwareUpdateTelemetry> DSP,
            Map<String, Boolean> Criteria,
            String Status,
            String ConfigurationId,
            LocalDateTime StartedAt,
            LocalDateTime CompletedAt) {
        this.ARM = ARM;
        this.DSP = DSP;
        this.Criteria = Criteria;
        this.Status = Status;
        this.ConfigurationId = ConfigurationId;
        this.StartedAt = StartedAt;
        this.CompletedAt = CompletedAt;
    }

    public List<IndividualFirmwareUpdateTelemetry> getARM() {
        return ARM;
    }

    public List<IndividualFirmwareUpdateTelemetry> getDSP() {
        return DSP;
    }

    public Map<String, Boolean> getCriteria() {
        return Criteria;
    }

    public String getStatus() {
        return Status;
    }

    public String getConfigurationId() {
        return ConfigurationId;
    }

    public LocalDateTime getStartedAt() {
        return StartedAt;
    }

    public LocalDateTime getCompletedAt() {
        return CompletedAt;
    }
}