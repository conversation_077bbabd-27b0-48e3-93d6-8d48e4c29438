package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandSGInverterPowerSystem extends DataBackedBand
{
	public BandSGInverterPowerSystem()
	{
		super(BandForge.<BandSGInverterPowerSystem>getMetadataFor(BandSGInverterPowerSystem.class));
	}



	public BandSGInverterPowerSystem(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterPowerSystem>getMetadataFor(BandSGInverterPowerSystem.class));
	}

	public BandSGInverterPowerSystem(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterPowerSystem>getMetadataFor(BandSGInverterPowerSystem.class));
	}


	
	public final Watt getGridActivePowerL1()
	{
		return GetS32(0, Watt.Deci);
	}

	
	public final Watt getGridActivePowerL2()
	{
		return GetS32(4, Watt.Deci);
	}

	
	public final Watt getGridActivePowerL3()
	{
		return GetS32(8, Watt.Deci);
	}

	
	public final WattHour getTotalBuyEnergyInverter()
	{
		return GetU32(12, WattHour.Deca);
	}

	
	public final WattHour getTotalSellEnergyInverter()
	{
		return GetU32(16, WattHour.Deca);
	}

	
	public final Watt getActivePowerLoadL1()
	{
		return GetS32(20, Watt.Deci);
	}

	
	public final Watt getActivePowerLoadL2()
	{
		return GetS32(24, Watt.Deci);
	}

	
	public final Watt getActivePowerLoadL3()
	{
		return GetS32(28, Watt.Deci);
	}

	
	public final WattHour getTotalLoadEnergy()
	{
		return GetU32(32, WattHour.Deca);
	}

	
	public final Watt getInverterActivePowerL1AllInverters()
	{
		return GetS32(36, Watt.Deci);
	}

	
	public final Watt getInverterActivePowerL2AllInverters()
	{
		return GetS32(40, Watt.Deci);
	}

	
	public final Watt getInverterActivePowerL3AllInverters()
	{
		return GetS32(44, Watt.Deci);
	}

	
	public final WattHour getTotalEnergyAllInverters()
	{
		return GetU32(48, WattHour.Deca);
	}

	
	public final Volt getGridVoltageL1Inverter()
	{
		return GetU16(52, Volt.Deci);
	}

	
	public final Volt getGridVoltageL2Inverter()
	{
		return GetU16(54, Volt.Deci);
	}

	
	public final Volt getGridVoltageL3Inverter()
	{
		return GetU16(56, Volt.Deci);
	}

	
	public final Ampere getGridCurrentL1Inverter()
	{
		return GetS32(58, Ampere.Centi);
	}

	
	public final Ampere getGridCurrentL2Inverter()
	{
		return GetS32(62, Ampere.Centi);
	}

	
	public final Ampere getGridCurrentL3Inverter()
	{
		return GetS32(66, Ampere.Centi);
	}

	
	public final Volt getLoadVoltageL1Inverter()
	{
		return GetU16(70, Volt.Deci);
	}

	
	public final Volt getLoadVoltageL2Inverter()
	{
		return GetU16(72, Volt.Deci);
	}

	
	public final Volt getLoadVoltageL3Inverter()
	{
		return GetU16(74, Volt.Deci);
	}

	
	public final Ampere getLoadCurrentL1()
	{
		return GetS32(76, Ampere.Centi);
	}

	
	public final Ampere getLoadCurrentL2()
	{
		return GetS32(80, Ampere.Centi);
	}

	
	public final Ampere getLoadCurrentL3()
	{
		return GetS32(84, Ampere.Centi);
	}

	
	public final Ampere getInverterCurrentL1AllInverters()
	{
		return GetS32(88, Ampere.Centi);
	}

	
	public final Ampere getInverterCurrentL2AllInverters()
	{
		return GetS32(92, Ampere.Centi);
	}

	
	public final Ampere getInverterCurrentL3AllInverters()
	{
		return GetS32(96, Ampere.Centi);
	}
}
