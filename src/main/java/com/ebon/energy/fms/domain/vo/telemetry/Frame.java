package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.time.Duration;

public class Frame<T extends IBand> implements IDataFrame<T> {

    private Instant TimestampUtc;
    private T Data;
    private Duration CreationUptime;

    public Frame(Instant timestampUtc, T data, Duration creationUptime) {
        if (data == null) {
            throw new IllegalArgumentException("Data cannot be null");
        }
        this.TimestampUtc = timestampUtc;
        this.Data = data;
        this.CreationUptime = creationUptime;
    }

    @JsonProperty("Name")
    public String getName() {
        return Data.getClass().getSimpleName();
    }

    @JsonProperty("TimestampUtc")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public Instant getTimestampUtc() {
        return TimestampUtc;
    }

    @JsonProperty("Data")
    public T getData() {
        return Data;
    }

    @JsonIgnore
    public Duration getCreationUptime() {
        return CreationUptime;
    }

    @JsonIgnore
    public Duration getTimeToLive() {
        return Data.getTimeToLive();
    }

    @JsonIgnore
    public Duration getFreshDuration() {
        return Data.getFreshDuration();
    }

    @JsonIgnore
    public Duration getReadPeriod() {
        return Data.getReadPeriod();
    }

    @JsonIgnore
    public Duration getRefreshAt() {
        return CreationUptime.plus(getReadPeriod());
    }

    @JsonIgnore
    public Duration getStaleAt() {
        return CreationUptime.plus(getFreshDuration());
    }

    @JsonIgnore
    public Duration getExpiresAt() {
        return CreationUptime.plus(getTimeToLive());
    }
}
