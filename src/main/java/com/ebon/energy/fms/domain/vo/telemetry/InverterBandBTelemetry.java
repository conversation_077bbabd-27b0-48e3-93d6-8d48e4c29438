package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandBTelemetry implements ITelemetryBand
{
    public InverterBandBTelemetry(Volt vpv1, Ampere ipv1, PvMode pV1Mode, Volt vpv2, Ampere ipv2, PvMode pV2Mode, Volt vBattery1, BMSStatusMasks bMSStatus, Celsius bMSPackTemp, Ampere iBattery1, Ampere bMSChargeIMax, Ampere bMSDischargeIMax, BatteryErrorMasks bMSErrorCode, BigDecimal sOC, short inverterWarningCode, BigDecimal bMS_SoH, BattMode batteryWorkMode, BatteryErrorMasks bMSWarningCode, boolean meterStatus, Volt vGrid, Ampere iGrid, Watt pGrid, Frequency fGrid, GridMode gridMode, Volt vLoad, Ampere iLoad, Watt onGridLoadPower, Frequency fLoad, LoadMode loadMode, InverterWorkMode workMode, Celsius temperature, InverterErrorMode errorMessage, WattHour eGridTotal, TimeSpan hGridTotal, WattHour eGridDay, WattHour eLoadDay, WattHour eTotalLoad, Watt totalPower, WattHour ePvTotal, GridInOutFlag gridInOutFlag, Watt backupLoadPower, BigDecimal meterPowerFactor, DiagStatus diagStatus, DRMStatus dRMStatus, float eTotalSellF, float eTotalBuyF, Volt vpv3, Ampere ipv3, PvMode pV3Mode, Volt vGridUo, Ampere iGridUo, Volt vGridWo, Ampere iGridWo, WattHour eBatteryCharge, WattHour eBatteryDischarge, Watt ppv1, Watt ppv2, Watt ppv3, Watt batteryPower, WattHour eTotalSellI, WattHour eTotalBuyI, WattHour eBatChargeToday, WattHour eBatDischargeToday)
    {
        setVpv1(vpv1);
        setIpv1(ipv1);
        setPV1Mode(pV1Mode);
        setVpv2(vpv2);
        setIpv2(ipv2);
        setPV2Mode(pV2Mode);
        setVBattery1(vBattery1);
        setBMSStatus(bMSStatus);
        setBMSPackTemp(bMSPackTemp);
        setIBattery1(iBattery1);
        setBMSChargeIMax(bMSChargeIMax);
        setBMSDischargeIMax(bMSDischargeIMax);
        setBMSErrorCode(bMSErrorCode);
        setSOC(sOC);
        setInverterWarningCode(inverterWarningCode);
        setBMSSoH(bMS_SoH);
        setBatteryWorkMode(batteryWorkMode);
        setBMSWarningCode(bMSWarningCode);
        setMeterStatus(meterStatus);
        setVGrid(vGrid);
        setIGrid(iGrid);
        setPGrid(pGrid);
        setFGrid(fGrid);
        setGridMode(gridMode);
        setVLoad(vLoad);
        setILoad(iLoad);
        setOnGridLoadPower(onGridLoadPower);
        setFLoad(fLoad);
        setLoadMode(loadMode);
        setWorkMode(workMode);
        setTemperature(temperature);
        setErrorMessage(errorMessage);
        setEGridTotal(eGridTotal);
        setHGridTotal(hGridTotal);
        setEGridDay(eGridDay);
        setELoadDay(eLoadDay);
        setETotalLoad(eTotalLoad);
        setTotalPower(totalPower);
        setEPvTotal(ePvTotal);
        setGridInOutFlag(gridInOutFlag);
        setBackupLoadPower(backupLoadPower);
        setMeterPowerFactor(meterPowerFactor);
        setDiagStatus(diagStatus);
        setDRMStatus(dRMStatus);
        setETotalSellF(eTotalSellF);
        setETotalBuyF(eTotalBuyF);
        setVpv3(vpv3);
        setIpv3(ipv3);
        setPV3Mode(pV3Mode);
        setVGridUo(vGridUo);
        setIGridUo(iGridUo);
        setVGridWo(vGridWo);
        setIGridWo(iGridWo);
        setEBatteryCharge(eBatteryCharge);
        setEBatteryDischarge(eBatteryDischarge);
        setPpv1(ppv1);
        setPpv2(ppv2);
        setPpv3(ppv3);
        setBatteryPower(batteryPower);
        setETotalSellI(eTotalSellI);
        setETotalBuyI(eTotalBuyI);
        setEBatChargeToday(eBatChargeToday);
        setEBatDischargeToday(eBatDischargeToday);
    }

    private Volt Vpv1;
    public final Volt getVpv1()
    {
        return Vpv1;
    }
    private void setVpv1(Volt value)
    {
        Vpv1 = value;
    }

    private Ampere Ipv1;
    public final Ampere getIpv1()
    {
        return Ipv1;
    }
    private void setIpv1(Ampere value)
    {
        Ipv1 = value;
    }

    private PvMode PV1Mode;
    public final Object getPV1Mode()
    {
        return PV1Mode;
    }
    private void setPV1Mode(PvMode value)
    {
        PV1Mode = value;
    }

    private Volt Vpv2;
    public final Volt getVpv2()
    {
        return Vpv2;
    }
    private void setVpv2(Volt value)
    {
        Vpv2 = value;
    }

    private Ampere Ipv2;
    public final Ampere getIpv2()
    {
        return Ipv2;
    }
    private void setIpv2(Ampere value)
    {
        Ipv2 = value;
    }

    private PvMode PV2Mode;
    public final Object getPV2Mode()
    {
        return PV2Mode;
    }
    private void setPV2Mode(PvMode value)
    {
        PV2Mode = value;
    }

    private Volt VBattery1;
    public final Volt getVBattery1()
    {
        return VBattery1;
    }
    private void setVBattery1(Volt value)
    {
        VBattery1 = value;
    }

    private BMSStatusMasks BMSStatus;
    public final BMSStatusMasks getBMSStatus()
    {
        return BMSStatus;
    }
    private void setBMSStatus(BMSStatusMasks value)
    {
        BMSStatus = value;
    }

    private Celsius BMSPackTemp;
    public final Celsius getBMSPackTemp()
    {
        return BMSPackTemp;
    }
    private void setBMSPackTemp(Celsius value)
    {
        BMSPackTemp = value;
    }

    private Ampere IBattery1;
    public final Ampere getIBattery1()
    {
        return IBattery1;
    }
    private void setIBattery1(Ampere value)
    {
        IBattery1 = value;
    }

    private Ampere BMSChargeIMax;
    public final Ampere getBMSChargeIMax()
    {
        return BMSChargeIMax;
    }
    private void setBMSChargeIMax(Ampere value)
    {
        BMSChargeIMax = value;
    }

    private Ampere BMSDischargeIMax;
    public final Ampere getBMSDischargeIMax()
    {
        return BMSDischargeIMax;
    }
    private void setBMSDischargeIMax(Ampere value)
    {
        BMSDischargeIMax = value;
    }

    private BatteryErrorMasks BMSErrorCode;
    public final BatteryErrorMasks getBMSErrorCode()
    {
        return BMSErrorCode;
    }
    private void setBMSErrorCode(BatteryErrorMasks value)
    {
        BMSErrorCode = value;
    }

    /**
     Gets SoC as 0 to 1
     */
    private BigDecimal SOC = new BigDecimal(0);
    public final BigDecimal getSOC()
    {
        return SOC;
    }
    private void setSOC(BigDecimal value)
    {
        SOC = value;
    }

    // C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
// ORIGINAL LINE: private ushort InverterWarningCode;
    private short InverterWarningCode;
    // C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
// ORIGINAL LINE: public ushort getInverterWarningCode()
    public final short getInverterWarningCode()
    {
        return InverterWarningCode;
    }
    // C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
// ORIGINAL LINE: private void setInverterWarningCode(ushort value)
    private void setInverterWarningCode(short value)
    {
        InverterWarningCode = value;
    }

    private BigDecimal BMS_SoH = new BigDecimal(0);
    public final BigDecimal getBMSSoH()
    {
        return BMS_SoH;
    }
    private void setBMSSoH(BigDecimal value)
    {
        BMS_SoH = value;
    }

    private BattMode BatteryWorkMode;
    public final BattMode getBatteryWorkMode()
    {
        return BatteryWorkMode;
    }
    private void setBatteryWorkMode(BattMode value)
    {
        BatteryWorkMode = value;
    }

    private BatteryErrorMasks BMSWarningCode;
    public final BatteryErrorMasks getBMSWarningCode()
    {
        return BMSWarningCode;
    }
    private void setBMSWarningCode(BatteryErrorMasks value)
    {
        BMSWarningCode = value;
    }

    private boolean MeterStatus;
    public final boolean getMeterStatus()
    {
        return MeterStatus;
    }
    private void setMeterStatus(boolean value)
    {
        MeterStatus = value;
    }

    private Volt VGrid;
    public final Volt getVGrid()
    {
        return VGrid;
    }
    private void setVGrid(Volt value)
    {
        VGrid = value;
    }

    private Ampere IGrid;
    public final Ampere getIGrid()
    {
        return IGrid;
    }
    private void setIGrid(Ampere value)
    {
        IGrid = value;
    }

    private Watt PGrid;
    public final Watt getPGrid()
    {
        return PGrid;
    }
    private void setPGrid(Watt value)
    {
        PGrid = value;
    }

    private Frequency FGrid;
    public final Frequency getFGrid()
    {
        return FGrid;
    }
    private void setFGrid(Frequency value)
    {
        FGrid = value;
    }

    private GridMode GridMode;
    public final GridMode getGridMode()
    {
        return GridMode;
    }
    private void setGridMode(GridMode value)
    {
        GridMode = value;
    }

    private Volt VLoad;
    public final Volt getVLoad()
    {
        return VLoad;
    }
    private void setVLoad(Volt value)
    {
        VLoad = value;
    }

    private Ampere ILoad;
    public final Ampere getILoad()
    {
        return ILoad;
    }
    private void setILoad(Ampere value)
    {
        ILoad = value;
    }

    private Watt OnGridLoadPower;
    public final Watt getOnGridLoadPower()
    {
        return OnGridLoadPower;
    }
    private void setOnGridLoadPower(Watt value)
    {
        OnGridLoadPower = value;
    }

    private Frequency FLoad;
    public final Frequency getFLoad()
    {
        return FLoad;
    }
    private void setFLoad(Frequency value)
    {
        FLoad = value;
    }

    private LoadMode LoadMode;
    public final LoadMode getLoadMode()
    {
        return LoadMode;
    }
    private void setLoadMode(LoadMode value)
    {
        LoadMode = value;
    }

    private InverterWorkMode WorkMode;
    public final InverterWorkMode getWorkMode()
    {
        return WorkMode;
    }
    private void setWorkMode(InverterWorkMode value)
    {
        WorkMode = value;
    }

    private Celsius Temperature;
    public final Celsius getTemperature()
    {
        return Temperature;
    }
    private void setTemperature(Celsius value)
    {
        Temperature = value;
    }

    private InverterErrorMode ErrorMessage;
    public final InverterErrorMode getErrorMessage()
    {
        return ErrorMessage;
    }
    private void setErrorMessage(InverterErrorMode value)
    {
        ErrorMessage = value;
    }

    private WattHour EGridTotal;
    public final WattHour getEGridTotal()
    {
        return EGridTotal;
    }
    private void setEGridTotal(WattHour value)
    {
        EGridTotal = value;
    }

    private TimeSpan HGridTotal;
    public final TimeSpan getHGridTotal()
    {
        return HGridTotal;
    }
    private void setHGridTotal(TimeSpan value)
    {
        HGridTotal = value;
    }

    private WattHour EGridDay;
    public final WattHour getEGridDay()
    {
        return EGridDay;
    }
    private void setEGridDay(WattHour value)
    {
        EGridDay = value;
    }

    private WattHour ELoadDay;
    public final WattHour getELoadDay()
    {
        return ELoadDay;
    }
    private void setELoadDay(WattHour value)
    {
        ELoadDay = value;
    }

    private WattHour ETotalLoad;
    public final WattHour getETotalLoad()
    {
        return ETotalLoad;
    }
    private void setETotalLoad(WattHour value)
    {
        ETotalLoad = value;
    }

    private Watt TotalPower;
    public final Watt getTotalPower()
    {
        return TotalPower;
    }
    private void setTotalPower(Watt value)
    {
        TotalPower = value;
    }

    private WattHour EPvTotal;
    public final WattHour getEPvTotal()
    {
        return EPvTotal;
    }
    private void setEPvTotal(WattHour value)
    {
        EPvTotal = value;
    }

    private GridInOutFlag GridInOutFlag;
    public final GridInOutFlag getGridInOutFlag()
    {
        return GridInOutFlag;
    }
    private void setGridInOutFlag(GridInOutFlag value)
    {
        GridInOutFlag = value;
    }

    private Watt BackupLoadPower;
    public final Watt getBackupLoadPower()
    {
        return BackupLoadPower;
    }
    private void setBackupLoadPower(Watt value)
    {
        BackupLoadPower = value;
    }

    private BigDecimal MeterPowerFactor = new BigDecimal(0);
    public final BigDecimal getMeterPowerFactor()
    {
        return MeterPowerFactor;
    }
    private void setMeterPowerFactor(BigDecimal value)
    {
        MeterPowerFactor = value;
    }

    private DiagStatus DiagStatus;
    public final DiagStatus getDiagStatus()
    {
        return DiagStatus;
    }
    private void setDiagStatus(DiagStatus value)
    {
        DiagStatus = value;
    }

    private DRMStatus DRMStatus;
    public final DRMStatus getDRMStatus()
    {
        return DRMStatus;
    }
    private void setDRMStatus(DRMStatus value)
    {
        DRMStatus = value;
    }

    private float ETotalSellF;
    public final float getETotalSellF()
    {
        return ETotalSellF;
    }
    private void setETotalSellF(float value)
    {
        ETotalSellF = value;
    }

    private float ETotalBuyF;
    public final float getETotalBuyF()
    {
        return ETotalBuyF;
    }
    private void setETotalBuyF(float value)
    {
        ETotalBuyF = value;
    }

    private Volt Vpv3;
    public final Volt getVpv3()
    {
        return Vpv3;
    }
    private void setVpv3(Volt value)
    {
        Vpv3 = value;
    }

    private Ampere Ipv3;
    public final Ampere getIpv3()
    {
        return Ipv3;
    }
    private void setIpv3(Ampere value)
    {
        Ipv3 = value;
    }

    private PvMode PV3Mode;
    public final PvMode getPV3Mode()
    {
        return PV3Mode;
    }
    private void setPV3Mode(PvMode value)
    {
        PV3Mode = value;
    }

    private Volt VGridUo;
    public final Volt getVGridUo()
    {
        return VGridUo;
    }
    private void setVGridUo(Volt value)
    {
        VGridUo = value;
    }

    private Ampere IGridUo;
    public final Ampere getIGridUo()
    {
        return IGridUo;
    }
    private void setIGridUo(Ampere value)
    {
        IGridUo = value;
    }

    private Volt VGridWo;
    public final Volt getVGridWo()
    {
        return VGridWo;
    }
    private void setVGridWo(Volt value)
    {
        VGridWo = value;
    }

    private Ampere IGridWo;
    public final Ampere getIGridWo()
    {
        return IGridWo;
    }
    private void setIGridWo(Ampere value)
    {
        IGridWo = value;
    }

    private WattHour EBatteryCharge;
    public final WattHour getEBatteryCharge()
    {
        return EBatteryCharge;
    }
    private void setEBatteryCharge(WattHour value)
    {
        EBatteryCharge = value;
    }

    private WattHour EBatteryDischarge;
    public final WattHour getEBatteryDischarge()
    {
        return EBatteryDischarge;
    }
    private void setEBatteryDischarge(WattHour value)
    {
        EBatteryDischarge = value;
    }

    private Watt Ppv1;
    public final Watt getPpv1()
    {
        return Ppv1;
    }
    private void setPpv1(Watt value)
    {
        Ppv1 = value;
    }

    private Watt Ppv2;
    public final Watt getPpv2()
    {
        return Ppv2;
    }
    private void setPpv2(Watt value)
    {
        Ppv2 = value;
    }

    private Watt Ppv3;
    public final Watt getPpv3()
    {
        return Ppv3;
    }
    private void setPpv3(Watt value)
    {
        Ppv3 = value;
    }

    private Watt BatteryPower;
    public final Watt getBatteryPower()
    {
        return BatteryPower;
    }
    private void setBatteryPower(Watt value)
    {
        BatteryPower = value;
    }

    private WattHour ETotalSellI;
    public final WattHour getETotalSellI()
    {
        return ETotalSellI;
    }
    private void setETotalSellI(WattHour value)
    {
        ETotalSellI = value;
    }

    private WattHour ETotalBuyI;
    public final WattHour getETotalBuyI()
    {
        return ETotalBuyI;
    }
    private void setETotalBuyI(WattHour value)
    {
        ETotalBuyI = value;
    }

    private WattHour EBatChargeToday;
    public final WattHour getEBatChargeToday()
    {
        return EBatChargeToday;
    }
    private void setEBatChargeToday(WattHour value)
    {
        EBatChargeToday = value;
    }

    private WattHour EBatDischargeToday;
    public final WattHour getEBatDischargeToday()
    {
        return EBatDischargeToday;
    }
    private void setEBatDischargeToday(WattHour value)
    {
        EBatDischargeToday = value;
    }
}

