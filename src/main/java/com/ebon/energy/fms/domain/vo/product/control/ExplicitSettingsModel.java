package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExplicitSettingsModel {

    @JsonProperty("MinOnGridSoC0to100")
    private Integer minOnGridSoC0to100;

    @JsonProperty("MaxOnGridSoC0to100")
    private Integer maxOnGridSoC0to100;

    @JsonProperty("MinOffGridSoC0to100")
    private Integer minOffGridSoC0to100;

    @JsonProperty("MaxOffGridSoC0to100")
    private Integer maxOffGridSoC0to100;

    /**
     * 请注意，这个机制的目的是确保随着 ExplicitSettingsModel 的增长，
     * 所有创建 ExplicitSettings 的地方都能及时更新，避免遗漏新字段。
     */
    public ExplicitSettings asExplicitSettings() {
        return new ExplicitSettings(
            minOnGridSoC0to100,
            maxOnGridSoC0to100,
            minOffGridSoC0to100,
            maxOffGridSoC0to100
        );
    }
}
