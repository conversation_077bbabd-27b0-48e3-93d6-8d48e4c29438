package com.ebon.energy.fms.domain.po;

import com.ebon.energy.fms.common.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ModifyAppVersionPO {

    @NotNull
    private Long id;

    private String releaseNotes;

    private String downloadUrl;

    private String upgradeMethod;

    private String status;

    public void checkParams() {
        if (StringUtils.isNotBlank(status)) {
            if (!"effective".equalsIgnoreCase(status) && !"invalid".equalsIgnoreCase(status)) {
                throw new BizException("status must be effective or invalid");
            }
        }
    }
}
