package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.util.Objects;

@Data
public class DayPowerPairVO {
    private String label;
    private int watts;
    private Boolean accurate;

    public DayPowerPairVO(String label, int watts, boolean accurate) {
        this.label = Objects.requireNonNull(label, "not must  be null");
        this.watts = watts;
        this.accurate = accurate;
    }

}
