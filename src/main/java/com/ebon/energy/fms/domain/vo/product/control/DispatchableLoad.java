package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Dispatchable load setting entity
 */
@Data
public class DispatchableLoad {
    @JsonProperty(index = 1)
    private String name;
    
    @JsonProperty(index = 2)
    private String id;
    
    @JsonProperty(index = 3)
    private String type;
    
    @JsonProperty(index = 4)
    private DispatchableLoadScheduleAuto autoSchedule;
    
    @JsonProperty(index = 5)
    private List<DispatchableLoadSchedule> manualSchedules;
    
    /**
     * Default constructor
     */
    public DispatchableLoad() {
        id = "";
        name = "";
        type = "Auto";
        autoSchedule = new DispatchableLoadScheduleAuto();
        manualSchedules = new ArrayList<>();
    }
}
