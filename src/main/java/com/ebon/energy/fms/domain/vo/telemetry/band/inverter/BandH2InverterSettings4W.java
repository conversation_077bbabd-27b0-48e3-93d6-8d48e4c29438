package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;








/** 
 This is an automatically generated class that defines BandH2InverterSettings4W.
	 Base Register Address: 8015
	 Total Length In Bytes: 16
 
*/


public class BandH2InverterSettings4W extends DataBackedBand
{
	public BandH2InverterSettings4W()
	{
		super(BandForge.<BandH2InverterSettings4W>getMetadataFor(BandH2InverterSettings4W.class));
	}



	public BandH2InverterSettings4W(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterSettings4W>getMetadataFor(BandH2InverterSettings4W.class));
	}

	public BandH2InverterSettings4W(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterSettings4W>getMetadataFor(BandH2InverterSettings4W.class));
	}
}
