package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.BMSFlags;
import com.ebon.energy.fms.common.enums.InverterFlags;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import static com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility.*;

@JsonAutoDetect(
        fieldVisibility = ANY,
        getterVisibility = NONE,
        setterVisibility = NONE
)
public class FlagsTelemetry {

    private final BMSFlags BMS;
    private final InverterFlags Inverter;

    public FlagsTelemetry(BMSFlags BMS, InverterFlags Inverter) {
        this.BMS = BMS;
        this.Inverter = Inverter;
    }

    public BMSFlags getBMS() {
        return BMS;
    }

    public InverterFlags getInverter() {
        return Inverter;
    }
}