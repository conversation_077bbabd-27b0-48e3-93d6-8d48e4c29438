package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandH2InverterPVData extends DataBackedBand
{
	public BandH2InverterPVData()
	{
		super(BandForge.<BandH2InverterPVData>getMetadataFor(BandH2InverterPVData.class));
	}



	public BandH2InverterPVData(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterPVData>getMetadataFor(BandH2InverterPVData.class));
	}

	public BandH2InverterPVData(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterPVData>getMetadataFor(BandH2InverterPVData.class));
	}



	public final Volt getPV1Voltage()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Ampere getPV1Current()
	{
		return GetU16(2, Ampere.Centi);
	}


	public final Watt getPV1Power()
	{
		return GetU16(4, Watt.Unit);
	}


	public final Volt getPV2Voltage()
	{
		return GetU16(6, Volt.Deci);
	}


	public final Ampere getPV2Current()
	{
		return GetU16(8, Ampere.Centi);
	}


	public final Watt getPV2Power()
	{
		return GetU16(10, Watt.Unit);
	}


	public final Volt getPV3Voltage()
	{
		return GetU16(12, Volt.Deci);
	}


	public final Ampere getPV3Current()
	{
		return GetU16(14, Ampere.Centi);
	}


	public final Watt getPV3Power()
	{
		return GetU16(16, Watt.Unit);
	}
}
