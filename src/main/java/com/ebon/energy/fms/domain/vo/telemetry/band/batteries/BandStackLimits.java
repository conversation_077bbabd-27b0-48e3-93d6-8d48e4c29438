package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandStackLimits extends DataBackedBand
{
	public BandStackLimits()
	{
		super(BandForge.<BandStackLimits>getMetadataFor(BandStackLimits.class));
	}



	public BandStackLimits(byte[] bytes)
	{
		super(bytes, BandForge.<BandStackLimits>getMetadataFor(BandStackLimits.class));
	}

	public BandStackLimits(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandStackLimits>getMetadataFor(BandStackLimits.class));
	}


	public final Volt getStackChargeVoltageLimit()
	{
		return GetU16(0, Volt.Milli);
	}

	public final Volt getStackDischargeVoltageLimit()
	{
		return GetU16(2, Volt.Milli);
	}

	public final Ampere getStackChargeCurrentLimit()
	{
		return GetU16(4, Ampere.Deci);
	}

	public final Ampere getStackDischargeCurrentLimit()
	{
		return GetS16(6, Ampere.Deci);
	}

	public final String getStackChargeDischargeStatus()
	{
		return PylonChargeStatus.parse(GetU8(8));
	}
}
