package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@TableName("RedbackUserDetails")
public class RedbackUserDetailsDO {

    @TableField(value = "RedbackUserId")
    private String redbackUserId;

    @TableField(value = "Name")
    private String name;

    @TableField(value = "RedbackProductSn")
    private String redbackProductSn;

    @TableField(value = "Value")
    private String value;

}