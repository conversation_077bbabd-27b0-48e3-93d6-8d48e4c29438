package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.PylonUSAlarm1;
import com.ebon.energy.fms.common.enums.PylonUSAlarm2;
import com.ebon.energy.fms.common.enums.PylonUSProtect1;
import com.ebon.energy.fms.common.enums.PylonUSProtect2;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandPylonUSStackAlarms extends DataBackedBand
{
	public BandPylonUSStackAlarms()
	{
		super(BandForge.<BandPylonUSStackAlarms>getMetadataFor(BandPylonUSStackAlarms.class));
	}



	public BandPylonUSStackAlarms(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylonUSStackAlarms>getMetadataFor(BandPylonUSStackAlarms.class));
	}

	public BandPylonUSStackAlarms(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylonUSStackAlarms>getMetadataFor(BandPylonUSStackAlarms.class));
	}


	
	public final Object getAlarm1()
	{
		return PylonUSAlarm1.parse(GetU8(0));
	}

	
	public final Object getAlarm2()
	{
		return PylonUSAlarm2.parse(GetU8(1));
	}

	
	public final Object getProtect1()
	{
		return PylonUSProtect1.parse(GetU8(2));
	}

	
	public final Object getProtect2()
	{
		return PylonUSProtect2.parse(GetU8(3));
	}
}
