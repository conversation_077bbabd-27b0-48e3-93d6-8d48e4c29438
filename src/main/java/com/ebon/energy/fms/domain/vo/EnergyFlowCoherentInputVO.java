package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.EnergyFlowIssue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class EnergyFlowCoherentInputVO {
    public static final int PowerConsideredIdleW = 100;
    public static final int SignificantImbalanceThreshold = 600;

    public Watt ACLoadW;
    public Watt BackupLoadW;
    public Watt PVW;
    public Watt ThirdPartyW;
    public Watt GridNegativeIsImportW;
    public Watt BatteryNegativeIsChargingW;
    public Boolean HasBatteries;
    public BigDecimal BatterySoC0to1;
    public Boolean CtComms;
    public List<EnergyFlowIssue> Issues;

    public EnergyFlowCoherentInputVO(Watt aCLoadW, Watt backupLoadW, Watt pVW, Watt thirdPartyW,
                                   Watt gridNegativeIsImportW, Watt batteryNegativeIsChargingW,
                                   Boolean configuredWithBatteries, BigDecimal batterySoC0to1,
                                   boolean ctComms, List<EnergyFlowIssue> issues) {
        CtComms = ctComms;
        Issues = issues != null ? issues : new ArrayList<>();

        BigDecimal soc0to1 = batterySoC0to1;
        if (soc0to1 != null) {
            if (soc0to1.compareTo(BigDecimal.ONE) > 0) {
                Issues.add(EnergyFlowIssue.SoCOutOfBounds);
                soc0to1 = BigDecimal.ONE;
            }
            if (soc0to1.compareTo(BigDecimal.ZERO) < 0) {
                Issues.add(EnergyFlowIssue.SoCOutOfBounds);
                soc0to1 = BigDecimal.ZERO;
            }
        }

        ACLoadW = roundAbs(aCLoadW, Issues, EnergyFlowIssue.NegativeMainLoad, EnergyFlowIssue.LowMainLoadValueMayBeInacurrate);
        BackupLoadW = roundAbs(backupLoadW, Issues, EnergyFlowIssue.NegativeBackupLoad, EnergyFlowIssue.LowBackupLoadValueMayBeInacurrate);
        balanceLoads(aCLoadW, backupLoadW);

        PVW = roundAbs(pVW, Issues, EnergyFlowIssue.NegativePV, EnergyFlowIssue.LowPVValueMayBeInacurrate);
        ThirdPartyW = roundAbs(thirdPartyW, Issues, EnergyFlowIssue.Negative3rdParty, EnergyFlowIssue.LowThirdPartyWValueMayBeInacurrate);
        balancePV(pVW, thirdPartyW);

        GridNegativeIsImportW = round(gridNegativeIsImportW, Issues, EnergyFlowIssue.LowGridValueMayBeInacurrate);

        HasBatteries = (configuredWithBatteries != null && configuredWithBatteries) || (batteryNegativeIsChargingW != null && batteryNegativeIsChargingW.getValue() != null);
        BatteryNegativeIsChargingW = round(batteryNegativeIsChargingW, Issues, EnergyFlowIssue.LowBatteryValueMayBeInacurrate);
        BatterySoC0to1 = soc0to1;
    }

    public boolean isOnGrid() {
        return GridNegativeIsImportW != null;
    }

    public boolean hasBatteryData() {
        return BatteryNegativeIsChargingW != null;
    }

    public Watt getTotalLoadW() {
        return ACLoadW.add(BackupLoadW);
    }

    public Watt getTotalSourcePVW() {
        Watt pv = Watt.getValueOrZero(PVW);
        Watt thirdParty = Watt.getValueOrZero(ThirdPartyW);
        return pv.add(thirdParty);
    }

    public Watt getCalculatedGridW() {
        Watt battery = Watt.getValueOrZero(BatteryNegativeIsChargingW);
        return getTotalSourcePVW().add(battery).subtract(getTotalLoadW());
    }

    public Watt getGridImbalanceW() {
        if (GridNegativeIsImportW == null || GridNegativeIsImportW.getValue() == null) {
            return new Watt(BigDecimal.ZERO);
        }
        return GridNegativeIsImportW.subtract(getCalculatedGridW());
    }

    public void balanceLoads(Watt aCLoadW, Watt backupLoadW) {
        // 计算总负载的四舍五入值
        Watt totalLoadsRounded = roundNearestHundred(Watt.add(aCLoadW, backupLoadW));
        // 计算负载差距
        Watt loadGap = Watt.subtract(totalLoadsRounded, getTotalLoadW());

        if (loadGap.getValue().compareTo(BigDecimal.ZERO) < 0) {
            // 从较小值中减去多余部分
            if (aCLoadW.getValue().compareTo(backupLoadW.getValue()) <= 0 && ACLoadW.getValue().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal remainder = BigDecimal.valueOf(Math.min(0, ACLoadW.getValue().add(loadGap.getValue()).doubleValue()));
                ACLoadW.setValue(BigDecimal.valueOf(Math.max(0, ACLoadW.getValue().add(loadGap.getValue()).doubleValue())));
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToACLoad);
                if (remainder.compareTo(BigDecimal.ZERO) < 0) {
                    BackupLoadW = BackupLoadW.add(new Watt(remainder));
                    Issues.add(EnergyFlowIssue.BalancingAdjustmentToBackupLoad);
                }
            } else {
                BigDecimal remainder = BigDecimal.valueOf(Math.min(0, BackupLoadW.getValue().add(loadGap.getValue()).doubleValue()));
                BackupLoadW.setValue(BigDecimal.valueOf(Math.max(0, BackupLoadW.getValue().add(loadGap.getValue()).doubleValue())));
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToBackupLoad);
                if (remainder.compareTo(BigDecimal.ZERO) < 0) {
                    ACLoadW = ACLoadW.add(new Watt(remainder));
                    Issues.add(EnergyFlowIssue.BalancingAdjustmentToACLoad);
                }
            }
        } else if (loadGap.getValue().compareTo(BigDecimal.ZERO) > 0) {
            // 向较大值中添加差距
            if (aCLoadW.getValue().compareTo(backupLoadW.getValue()) >= 0) {
                ACLoadW = ACLoadW.add(loadGap);
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToACLoad);
            } else {
                BackupLoadW = BackupLoadW.add(loadGap);
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToBackupLoad);
            }
        }
    }

    // 平衡光伏电量
    public void balancePV(Watt pVW, Watt thirdPartyW) {
        // 计算总光伏电量的四舍五入值
        Watt totalPVRounded = roundNearestHundred(Watt.add(Watt.getValueOrZero(pVW), Watt.getValueOrZero(thirdPartyW)));
        // 计算光伏电量差距
        Watt pvGap = Watt.subtract(totalPVRounded, getTotalSourcePVW());

        if (pvGap.getValue().compareTo(BigDecimal.ZERO) < 0) {
            // 从较小值中减去多余部分
            if (Watt.getValueOrZero(pVW).getValue().compareTo(Watt.getValueOrZero(thirdPartyW).getValue()) <= 0 && Watt.getValueOrZero(PVW).getValue().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal remainder = BigDecimal.valueOf(Math.min(0, PVW.getValue().add(pvGap.getValue()).doubleValue()));
                PVW.setValue(BigDecimal.valueOf(Math.max(0, PVW.getValue().add(pvGap.getValue()).doubleValue())));
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToPV);
                if (remainder.compareTo(BigDecimal.ZERO) < 0) {
                    ThirdPartyW = thirdPartyW.add(new Watt(remainder));
                    Issues.add(EnergyFlowIssue.BalancingAdjustmentToThirdPartyPV);
                }
            } else {
                BigDecimal remainder = BigDecimal.valueOf(Math.min(0, ThirdPartyW.getValue().add(pvGap.getValue()).doubleValue()));
                ThirdPartyW.setValue(BigDecimal.valueOf(Math.max(0, ThirdPartyW.getValue().add(pvGap.getValue()).doubleValue())));
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToThirdPartyPV);
                if (remainder.compareTo(BigDecimal.ZERO) < 0) {
                    PVW = PVW.add(new Watt(remainder));
                    Issues.add(EnergyFlowIssue.BalancingAdjustmentToPV);
                }
            }
        } else if (pvGap.getValue().compareTo(BigDecimal.ZERO) > 0) {
            // 向较大值中添加差距
            if (Watt.getValueOrZero(pVW).getValue().compareTo(Watt.getValueOrZero(thirdPartyW).getValue()) >= 0) {
                PVW = PVW.add(pvGap);
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToPV);
            } else {
                ThirdPartyW = thirdPartyW.add(pvGap);
                Issues.add(EnergyFlowIssue.BalancingAdjustmentToThirdPartyPV);
            }
        }
    }

    private static Watt roundAbs(Watt watt, List<EnergyFlowIssue> issues, EnergyFlowIssue ifNegative, EnergyFlowIssue ifLowValue) {
        if (watt == null) {
            return null;
        }

        if (watt.lessThan(new Watt(BigDecimal.ZERO))) {
            issues.add(ifNegative);
            watt = new Watt(watt.getValue().negate());
        }
        return round(watt, issues, ifLowValue);
    }

    private static Watt round(Watt watt, List<EnergyFlowIssue> issues, EnergyFlowIssue ifLowValue) {
        if (watt == null || watt.getValue() == null) {
            return null;
        }

        if (low(watt)) {
            issues.add(ifLowValue);
        }
        return roundNearestHundred(watt);
    }

    private static boolean low(Watt power) {
        return power != null && power.abs().greaterThan(new Watt(BigDecimal.ZERO)) && power.abs().lessThan(new Watt(BigDecimal.valueOf(PowerConsideredIdleW)));
    }

    private static Watt roundNearestHundred(Watt value) {
        BigDecimal dividedValue = value.getValue().divide(new BigDecimal("100"));
        // 按照远离零的方式进行四舍五入到整数
        BigDecimal roundedValue;
        if (dividedValue.compareTo(BigDecimal.ZERO) >= 0) {
            // 正数或零，使用 HALF_UP 模式
            roundedValue = dividedValue.setScale(0, RoundingMode.HALF_UP);
        } else {
            // 负数，使用 HALF_DOWN 模式，HALF_DOWN 对于负数会向远离零的方向舍入
            roundedValue = dividedValue.setScale(0, RoundingMode.HALF_DOWN);
        }
        // 乘以 100
        return new Watt(roundedValue.multiply(new BigDecimal("100")));
    }

    private static Watt min(Watt a, Watt b) {
        return a.lessThan(b) ? a : b;
    }

    private static Watt max(Watt a, Watt b) {
        return a.greaterThan(b) ? a : b;
    }
}    