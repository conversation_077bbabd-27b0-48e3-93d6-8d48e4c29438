package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.LEDState;
import com.ebon.energy.fms.common.enums.MeterConnectStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public interface IBandETH extends IBand
{


	int getAppModeIndex();

	boolean getBreezeOnOff();

	Object getComLEDState();

	TimeSpan getDataSendInterval();



	int getDREDcmd();

	boolean getDredOffGridCheck();

	TimeSpan getCommsTimeout();

	boolean getLedtestflag();

	boolean getLogDataEnable();

	Object getMeterCheckValueL1();

	Object getMeterCheckValueL2();

	Object getMeterCheckValueL3();

	boolean getMeterConnectCheckFlag();

	Watt getSimulateMeterPower();

	Object getWiFiLEDState();



	int getWiFiOrLANSwitch();
}
