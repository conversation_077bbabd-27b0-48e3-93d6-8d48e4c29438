package com.ebon.energy.fms.domain.vo.product.control.report;

import java.util.ArrayList;
import java.util.List;

// SettingsComparer 工具类
public class SettingsComparer {
    public static List<ConfigurationDifference> getDifferences(InverterRelayCheckDesiredSettings desired, InverterRelayCheckReportedSettings reported) {
        List<ConfigurationDifference> differences = new ArrayList<>();
        ConfigurationDifference.add(differences, "Enabled", desired != null ? desired.getEnabled() : null, reported != null ? reported.getEnabled() : null);
        ConfigurationDifference.add(differences, "MaximumAttempts", desired != null ? desired.getMaximumAttempts() : null, reported != null ? reported.getMaximumAttempts() : null);
        ConfigurationDifference.add(differences, "LowPVAttempts", desired != null ? desired.getLowPVAttempts() : null, reported != null ? reported.getLowPVAttempts() : null);
        ConfigurationDifference.add(differences, "PVVoltageMinimum", desired != null ? desired.getPvVoltageMinimum() : null, reported != null ? reported.getPvVoltageMinimum() : null);
        ConfigurationDifference.add(differences, "FailureStateDuration", desired != null ? desired.getFailureStateDuration() : null, reported != null ? reported.getFailureStateDuration() : null);
        ConfigurationDifference.add(differences, "SuccessStateDuration", desired != null ? desired.getSuccessStateDuration() : null, reported != null ? reported.getSuccessStateDuration() : null);
        ConfigurationDifference.add(differences, "RebootGracePeriod", desired != null ? desired.getRebootGracePeriod() : null, reported != null ? reported.getRebootGracePeriod() : null);
        return differences;
    }
}