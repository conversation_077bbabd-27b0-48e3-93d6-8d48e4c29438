package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.InverterModeValue;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.common.utils.path.EH1PSettingPaths;
import com.ebon.energy.fms.common.utils.path.ESGSettingPaths;
import com.ebon.energy.fms.domain.vo.BatterySettingsDto;
import com.ebon.energy.fms.domain.vo.GetInverterModeSettingsDto;
import com.ebon.energy.fms.domain.vo.InverterModePair;
import com.ebon.energy.fms.domain.vo.MinMaxValueDto;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.domain.vo.product.control.ESGSettingsReader.toManufacturer;

public class HVH3PSettingsReader extends SettingsReader {


    public HVH3PSettingsReader(DeviceInfoAndSettings deviceSettings) {
        super(deviceSettings);
    }


    public static HVH3PSettingPaths.BatteryConfig fromManufacturer(ManufacturerEnum manufacturer) {
        if (manufacturer == null) {
            return new HVH3PSettingPaths.BatteryConfig("None", null);
        }

        // Comment from the spec as of 2.20:
        // For HV Batteries this will need to be updated.
        if (manufacturer == ManufacturerEnum.None) {
            return new HVH3PSettingPaths.BatteryConfig("None", "Parallel");  // Even for "None" we want to return a valid architecture; User Story 84542: GEN3 Battery Default Values
        }

        // Temporarily disable, wait for device support of 2025.03.11
        // if (manufacturer == ManufacturerEnum.Redback) {
        //     return new BatteryConfig("RED-R1-5000LV", "Redback");
        // }

        return new HVH3PSettingPaths.BatteryConfig("PylonUS", "Parallel");
    }


    @Override
    public GetInverterModeSettingsDto getDesiredPowerModeSchedulesForPortal() {
        Integer inverterModeNumber = readInt(deviceSettings, true, new V2Setting(V2Section.InverterControl, HVH3PSettingPaths.InverterModeSettingName));
        Integer inverterModePowerW = Objects.requireNonNullElse(readInt(deviceSettings, true, new V2Setting(V2Section.InverterControl, HVH3PSettingPaths.InverterModePowerSettingName)), 0);

        Map<String, ScheduleV2Dto> currentInverterModeSchedules = null;
        if (deviceSettings != null &&
                deviceSettings.getDesired() != null &&
                deviceSettings.getDesired().getSettingsV2() != null &&
                deviceSettings.getDesired().getSettingsV2().getScheduleSettings() != null) {

            currentInverterModeSchedules = deviceSettings.getDesired().getSettingsV2().getScheduleSettings().getSchedules()
                    .entrySet().stream()
                    .filter(x -> x.getValue() != null &&
                            x.getValue().getDesiredScheduledSettings() != null &&
                            x.getValue().getDesiredScheduledSettings().getInverterControlSettings() != null &&
                            !x.getValue().getDesiredScheduledSettings().getInverterControlSettings().isEmpty())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        return new GetInverterModeSettingsDto(new InverterModePair(inverterModeNumber != null ?
                FromTwinWorkMode(inverterModeNumber) :
                InverterModeValue.Auto, inverterModePowerW), currentInverterModeSchedules == null ? new HashMap<>() : Map(currentInverterModeSchedules));
    }

    private static Map<String, ScheduleInfoDto> Map(Map<String, ScheduleV2Dto> schedules) {
        Map<String, ScheduleInfoDto> transformed = new HashMap<>();
        for (Map.Entry<String, ScheduleV2Dto> schedule : schedules.entrySet()) {
            try {
                ScheduleV2Dto scheduleV2 = schedule.getValue();

                Map<String, Object> settings = scheduleV2.getDesiredScheduledSettings().getInverterControlSettings();

                Integer inverterModeNumber = readInt(settings, HVH3PSettingPaths.InverterModeSettingName);

                Integer inverterModePowerW = Objects.requireNonNullElse(readInt(settings, HVH3PSettingPaths.InverterModePowerSettingName), 0);

                ScheduleInfoDto s = new ScheduleInfoDto(
                        scheduleV2.getPriority(),
                        scheduleV2.getStartAtUtc(),
                        scheduleV2.getEndAtUtc(),
                        scheduleV2.getStartTimeOfDay(),
                        scheduleV2.getDuration(),
                        scheduleV2.getDaysOfWeekActive() != null ? ScheduleDays.fromValue(scheduleV2.getDaysOfWeekActive()) : null,
                        EncodeToScheduleAction(
                                inverterModeNumber != null
                                        ? FromTwinWorkMode(inverterModeNumber)
                                        : InverterModeValue.Auto),
                        inverterModePowerW);

                transformed.put(schedule.getKey(), s);
            } catch (Exception e) {
                // Failed to translate the schedule,
                // As of 2.20 we ignore bad schedules
            }
        }

        return transformed;
    }

    public static String EncodeToScheduleAction(InverterModeValue inverterModeValue) {
        switch (inverterModeValue) {
            case NoMode:
                return "NoMode";
            case ChargeBattery:
                return "ChargeB";
            case DischargeBattery:
                return "DischargeB";
            case Auto:
                return "Auto";
            case ImportPower:
                return "Import";
            case ExportPower:
                return "Export";
            case Conserve:
                return "Conserve";
            case BuyPower:
                return "BuyPower";
            case ForceChargeBattery:
                return "ForceChargeBattery";
            case ForceDischargeBattery:
                return "ForceDischargeBattery";
            case Hibernate:
                return "Hibernate";
            case Offgrid:
                return "Offgrid";
            case SellPower:
                return "SellPower";
            case Stop:
                return "Stop";
            default:
                return inverterModeValue.name();
        }
    }


    public static InverterModeValue FromTwinWorkMode(int inTwin) {
        return InverterModeValue.fromValue(inTwin);
    }

    @Override
    public ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source) {
        if (source == null) {
            source = UniversalSettingSource.DESIRED;
        }
        switch (source) {
            case INTENT:
                // Assuming DeviceSettings and Intent may be null, handle safely
                Boolean enable = null;
                if (deviceSettings != null && deviceSettings.getIntent() != null) {
                    enable = deviceSettings.getIntent().getEnableACCoupledMode();
                }
                return new ACCoupledSettingsDto(enable);
            case REPORTED:
                return new ACCoupledSettingsDto(readBool(false, ESGSettingPaths.ACCoupledOnOff));
            case DESIRED:
            default:
                return new ACCoupledSettingsDto(readBool(true, ESGSettingPaths.ACCoupledOnOff));
        }
    }

    @Override
    public List<UniversalSettingId> getSupportedUniveralSettings(Integer version) {
        List<UniversalSettingId> supportedUniversalSettings = new ArrayList<>();

        supportedUniversalSettings.add(UniversalSettingId.BATTERY_SETTINGS);
        supportedUniversalSettings.add(UniversalSettingId.CT_FLIP);

        return supportedUniversalSettings;
    }

    @Override
    public BatterySettingsDto getBatterySettings(UniversalSettingSource source) {
        var minOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOnGridSoCLimit()).orElse(null));
        var maxOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOnGridSoCLimit()).orElse(null));
        var minOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOffGridSoCLimit()).orElse(null));
        var maxOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOffGridSoCLimit()).orElse(null));

        return getBatterySettings(source == UniversalSettingSource.DESIRED,
                minOnGridSoC0to100,
                maxOnGridSoC0to100,
                minOffGridSoC0to100,
                maxOffGridSoC0to100);
    }

    private BatterySettingsDto getBatterySettings(boolean isDesired,
                                                  int minOnGridSoC0to100,
                                                  int maxOnGridSoC0to100,
                                                  int minOffGridSoC0to100,
                                                  int maxOffGridSoC0to100
    ) {
        return BatterySettingsDto.builder()
                .manufacturer(toManufacturer(readString(isDesired, EH1PSettingPaths.BatteryProtocol)))
                .batteryCount(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumBatteryCount())
                        .max(getProductModelDefaults().getMaximumBatteryCount())
                        .value(getBatteryCount(isDesired))
                        .build())
                .maxChargeCurrent(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumMaxChargeCurrentAmpere())
                        .max(getProductModelDefaults().getMaximumMaxChargeCurrentAmpere())
                        .value(readInt(isDesired, ESGSettingPaths.BatteryMaxChargeCurrent))
                        .build())
                .maxChargeVoltageV(readDecimal(isDesired, ESGSettingPaths.BatteryMaxChargeVoltage))
                .maxDischargeCurrent(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumMaxDischargeCurrentAmpere())
                        .max(getProductModelDefaults().getMaximumMaxDischargeCurrentAmpere())
                        .value(readInt(isDesired, ESGSettingPaths.BatteryMaxDischargeCurrent))
                        .build())
                .minSoc(MinMaxValueDto.builder()
                        .min(minOnGridSoC0to100)
                        .max(maxOnGridSoC0to100)
                        .value((int) (readDecimal(isDesired, ESGSettingPaths.BatteryMinSoc0to1) == null ? null :
                                readDecimal(isDesired, ESGSettingPaths.BatteryMinSoc0to1).multiply(new BigDecimal(100)).intValue()))
                        .build())
                .minOffgridSoc(MinMaxValueDto.builder()
                        .min(minOffGridSoC0to100)
                        .max(maxOffGridSoC0to100)
                        .value((int) (readDecimal(isDesired, ESGSettingPaths.BatteryMinOffgridSoc0to1) == null ? null :
                                readDecimal(isDesired, ESGSettingPaths.BatteryMinOffgridSoc0to1).multiply(new BigDecimal(100)).intValue()))
                        .build())
                .totalCapacityAh(null)
                .build();
    }

    private int getBatteryCount(boolean isDesired) {
        if (isDesired) {
            return Optional.ofNullable(readInt(isDesired, ESGSettingPaths.BatteryCount)).orElse(0);
        } else {
            var x = readIntByPath(isDesired, "settings/v2/batteryStack/batteryCount");
            if (x != null) {
                return x;
            } else {
                return Optional.ofNullable(readInt(isDesired, ESGSettingPaths.BatteryCount)).orElse(0);
            }
        }
    }


}
