package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.time.LocalDateTime;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterStatus implements Cloneable {
    private Double DayMaxInputP;
    private Long DayMaxInputPEpoch;
    private Double DayMaxOutputP;
    private Long DayMaxOutputPEpoch;
    private List<Error> Errors;
    private ESWorkModeValue ESWorkMode;
    private String FirmwareVersion;
    private String InverterSN;
    private Double GFCIFaultLevel;
    private Long GFCIFaultEpoch;
    private InverterModeValue InverterMode;
    private Double InverterModePower;
    private InverterModeValue RequestedInverterMode;
    private Double RequestedInverterModePower;
    private Double RequestedInverterSoCLimit;
    private Double ImportPower;
    private Double ExportPower;
    private Double InverterP;
    private Double InverterQ;
    private Double InverterS;
    private String ModelName;
    private Double EnergyGeneratedToday;
    private Double EnergyGeneratedTotal;
    private Double Temperature;
    private Double TmpFaultLevel;
    private Long TmpFaultEpoch;
    private UpdateStatus updateStatus;
    private Integer updatePct;
    private double ImportLifeTimeAverage;
    private double ImportFiveMinAverage;
    private double ExportLifeTimeAverage;
    private double ExportFiveMinAverage;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private ZonedDateTime InverterTime;
    private BatteryStatus BatteryMeasurements;
    private BackupLoadStatus BackupLoad;

    public InverterStatus() {
    }

    public InverterStatus(Double dayMaxInputP, Long dayMaxInputPEpoch,
                          Double dayMaxOutputP, Long dayMaxOutputPEpoch,
                          List<Error> errors, ESWorkModeValue eSWorkMode,
                          String firmwareVersion, String inverterSN,
                          Double gFCIFaultLevel, Long gFCIFaultEpoch,
                          InverterModeValue inverterMode, Double inverterModePower,
                          InverterModeValue requestedInverterMode, Double requestedInverterModePower,
                          Double requestedInverterSoCLimit, Double importPower,
                          Double exportPower, Double inverterP, Double inverterQ,
                          Double inverterS, String modelName, Double energyGeneratedToday,
                          Double energyGeneratedTotal, Double temperature,
                          Double tmpFaultLevel, Long tmpFaultEpoch,
                          UpdateStatus updateStatus, Integer updatePct,
                          double importLifeTimeAverage, double importFiveMinAverage,
                          double exportLifeTimeAverage, double exportFiveMinAverage,
                          ZonedDateTime inverterTime, BatteryStatus batteryMeasurements,
                          BackupLoadStatus backupLoad) {
        this.DayMaxInputP = dayMaxInputP;
        this.DayMaxInputPEpoch = dayMaxInputPEpoch;
        this.DayMaxOutputP = dayMaxOutputP;
        this.DayMaxOutputPEpoch = dayMaxOutputPEpoch;
        this.Errors = errors;
        this.ESWorkMode = eSWorkMode;
        this.FirmwareVersion = firmwareVersion;
        this.InverterSN = inverterSN;
        this.GFCIFaultLevel = gFCIFaultLevel;
        this.GFCIFaultEpoch = gFCIFaultEpoch;
        this.InverterMode = inverterMode;
        this.InverterModePower = inverterModePower;
        this.RequestedInverterMode = requestedInverterMode;
        this.RequestedInverterModePower = requestedInverterModePower;
        this.RequestedInverterSoCLimit = requestedInverterSoCLimit;
        this.ImportPower = importPower;
        this.ExportPower = exportPower;
        this.InverterP = inverterP;
        this.InverterQ = inverterQ;
        this.InverterS = inverterS;
        this.ModelName = modelName;
        this.EnergyGeneratedToday = energyGeneratedToday;
        this.EnergyGeneratedTotal = energyGeneratedTotal;
        this.Temperature = temperature;
        this.TmpFaultLevel = tmpFaultLevel;
        this.TmpFaultEpoch = tmpFaultEpoch;
        this.updateStatus = updateStatus;
        this.updatePct = updatePct;
        this.ImportLifeTimeAverage = importLifeTimeAverage;
        this.ImportFiveMinAverage = importFiveMinAverage;
        this.ExportLifeTimeAverage = exportLifeTimeAverage;
        this.ExportFiveMinAverage = exportFiveMinAverage;
        this.InverterTime = inverterTime;
        this.BatteryMeasurements = batteryMeasurements;
        this.BackupLoad = backupLoad;
    }

    public Double getDayMaxInputP() {
        return DayMaxInputP;
    }

    public void setDayMaxInputP(Double dayMaxInputP) {
        DayMaxInputP = dayMaxInputP;
    }

    public Long getDayMaxInputPEpoch() {
        return DayMaxInputPEpoch;
    }

    public void setDayMaxInputPEpoch(Long dayMaxInputPEpoch) {
        DayMaxInputPEpoch = dayMaxInputPEpoch;
    }

    public Double getDayMaxOutputP() {
        return DayMaxOutputP;
    }

    public void setDayMaxOutputP(Double dayMaxOutputP) {
        DayMaxOutputP = dayMaxOutputP;
    }

    public Long getDayMaxOutputPEpoch() {
        return DayMaxOutputPEpoch;
    }

    public void setDayMaxOutputPEpoch(Long dayMaxOutputPEpoch) {
        DayMaxOutputPEpoch = dayMaxOutputPEpoch;
    }

    public List<Error> getErrors() {
        return Errors;
    }

    public void setErrors(List<Error> errors) {
        Errors = errors;
    }

    public ESWorkModeValue getESWorkMode() {
        return ESWorkMode;
    }

    public void setESWorkMode(ESWorkModeValue eSWorkMode) {
        ESWorkMode = eSWorkMode;
    }

    public String getFirmwareVersion() {
        return FirmwareVersion;
    }

    public void setFirmwareVersion(String firmwareVersion) {
        FirmwareVersion = firmwareVersion;
    }

    public String getInverterSN() {
        return InverterSN;
    }

    public void setInverterSN(String inverterSN) {
        InverterSN = inverterSN;
    }

    public Double getGFCIFaultLevel() {
        return GFCIFaultLevel;
    }

    public void setGFCIFaultLevel(Double gFCIFaultLevel) {
        GFCIFaultLevel = gFCIFaultLevel;
    }

    public Long getGFCIFaultEpoch() {
        return GFCIFaultEpoch;
    }

    public void setGFCIFaultEpoch(Long gFCIFaultEpoch) {
        GFCIFaultEpoch = gFCIFaultEpoch;
    }

    public InverterModeValue getInverterMode() {
        return InverterMode;
    }

    public void setInverterMode(InverterModeValue inverterMode) {
        InverterMode = inverterMode;
    }

    public Double getInverterModePower() {
        return InverterModePower;
    }

    public void setInverterModePower(Double inverterModePower) {
        InverterModePower = inverterModePower;
    }

    public InverterModeValue getRequestedInverterMode() {
        return RequestedInverterMode;
    }

    public void setRequestedInverterMode(InverterModeValue requestedInverterMode) {
        RequestedInverterMode = requestedInverterMode;
    }

    public Double getRequestedInverterModePower() {
        return RequestedInverterModePower;
    }

    public void setRequestedInverterModePower(Double requestedInverterModePower) {
        RequestedInverterModePower = requestedInverterModePower;
    }

    public Double getRequestedInverterSoCLimit() {
        return RequestedInverterSoCLimit;
    }

    public void setRequestedInverterSoCLimit(Double requestedInverterSoCLimit) {
        RequestedInverterSoCLimit = requestedInverterSoCLimit;
    }

    public Double getImportPower() {
        return ImportPower;
    }

    public void setImportPower(Double importPower) {
        ImportPower = importPower;
    }

    public Double getExportPower() {
        return ExportPower;
    }

    public void setExportPower(Double exportPower) {
        ExportPower = exportPower;
    }

    public Double getInverterP() {
        return InverterP;
    }

    public void setInverterP(Double inverterP) {
        InverterP = inverterP;
    }

    public Double getInverterQ() {
        return InverterQ;
    }

    public void setInverterQ(Double inverterQ) {
        InverterQ = inverterQ;
    }

    public Double getInverterS() {
        return InverterS;
    }

    public void setInverterS(Double inverterS) {
        InverterS = inverterS;
    }

    public String getModelName() {
        return ModelName;
    }

    public void setModelName(String modelName) {
        ModelName = modelName;
    }

    public Double getEnergyGeneratedToday() {
        return EnergyGeneratedToday;
    }

    public void setEnergyGeneratedToday(Double energyGeneratedToday) {
        EnergyGeneratedToday = energyGeneratedToday;
    }

    public Double getEnergyGeneratedTotal() {
        return EnergyGeneratedTotal;
    }

    public void setEnergyGeneratedTotal(Double energyGeneratedTotal) {
        EnergyGeneratedTotal = energyGeneratedTotal;
    }

    public Double getTemperature() {
        return Temperature;
    }

    public void setTemperature(Double temperature) {
        Temperature = temperature;
    }

    public Double getTmpFaultLevel() {
        return TmpFaultLevel;
    }

    public void setTmpFaultLevel(Double tmpFaultLevel) {
        TmpFaultLevel = tmpFaultLevel;
    }

    public Long getTmpFaultEpoch() {
        return TmpFaultEpoch;
    }

    public void setTmpFaultEpoch(Long tmpFaultEpoch) {
        TmpFaultEpoch = tmpFaultEpoch;
    }

    public UpdateStatus getUpdateStatus() {
        return updateStatus;
    }

    public void setUpdateStatus(UpdateStatus updateStatus) {
        this.updateStatus = updateStatus;
    }

    public Integer getUpdatePct() {
        return updatePct;
    }

    public void setUpdatePct(Integer updatePct) {
        this.updatePct = updatePct;
    }

    public double getImportLifeTimeAverage() {
        return ImportLifeTimeAverage;
    }

    public void setImportLifeTimeAverage(double importLifeTimeAverage) {
        ImportLifeTimeAverage = importLifeTimeAverage;
    }

    public double getImportFiveMinAverage() {
        return ImportFiveMinAverage;
    }

    public void setImportFiveMinAverage(double importFiveMinAverage) {
        ImportFiveMinAverage = importFiveMinAverage;
    }

    public double getExportLifeTimeAverage() {
        return ExportLifeTimeAverage;
    }

    public void setExportLifeTimeAverage(double exportLifeTimeAverage) {
        ExportLifeTimeAverage = exportLifeTimeAverage;
    }

    public double getExportFiveMinAverage() {
        return ExportFiveMinAverage;
    }

    public void setExportFiveMinAverage(double exportFiveMinAverage) {
        ExportFiveMinAverage = exportFiveMinAverage;
    }

    public ZonedDateTime getInverterTime() {
        return InverterTime;
    }

    public void setInverterTime(ZonedDateTime inverterTime) {
        InverterTime = inverterTime;
    }

    public BatteryStatus getBatteryMeasurements() {
        return BatteryMeasurements;
    }

    public void setBatteryMeasurements(BatteryStatus batteryMeasurements) {
        BatteryMeasurements = batteryMeasurements;
    }

    public BackupLoadStatus getBackupLoad() {
        return BackupLoad;
    }

    public void setBackupLoad(BackupLoadStatus backupLoad) {
        BackupLoad = backupLoad;
    }

    @Override
    public InverterStatus clone() {
        try {
            InverterStatus clone = (InverterStatus) super.clone();
            if (this.Errors != null) {
                clone.Errors = new ArrayList<>(this.Errors);
            }
            if (this.InverterTime != null) {
                clone.InverterTime = this.InverterTime;
            }
            if (this.BatteryMeasurements != null) {
                clone.BatteryMeasurements = (BatteryStatus) this.BatteryMeasurements.clone();
            }
            if (this.BackupLoad != null) {
                clone.BackupLoad = (BackupLoadStatus) this.BackupLoad.clone();
            }
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}