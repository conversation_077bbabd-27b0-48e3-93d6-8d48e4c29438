package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.EquipmentClassFaultCode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandEH1PInverterEquipmentFault extends DataBackedBand
{
	public BandEH1PInverterEquipmentFault()
	{
		super(BandForge.<BandEH1PInverterEquipmentFault>getMetadataFor(BandEH1PInverterEquipmentFault.class));
	}



	public BandEH1PInverterEquipmentFault(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterEquipmentFault>getMetadataFor(BandEH1PInverterEquipmentFault.class));
	}

	public BandEH1PInverterEquipmentFault(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterEquipmentFault>getMetadataFor(BandEH1PInverterEquipmentFault.class));
	}


	
	public final Object getEquipmentClassFault()
	{
		return EquipmentClassFaultCode.parse(GetU16(0));
	}
}
