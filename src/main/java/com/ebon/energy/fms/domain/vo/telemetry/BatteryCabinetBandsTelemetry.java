package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryCabinetBandsTelemetry {
    private TelemetryBandFrame<BatteryCabinetTemperatureBandTelemetry> TemperatureBand;
    private TelemetryBandFrame<BatteryCabinetFanStateBandTelemetry> FanStateBand;

    public BatteryCabinetBandsTelemetry() {}

    public BatteryCabinetBandsTelemetry(
            TelemetryBandFrame<BatteryCabinetTemperatureBandTelemetry> TemperatureBand,
            TelemetryBandFrame<BatteryCabinetFanStateBandTelemetry> FanStateBand)
    {
        this.TemperatureBand = TemperatureBand;
        this.FanStateBand = FanStateBand;
    }

    public TelemetryBandFrame<BatteryCabinetTemperatureBandTelemetry> getTemperatureBand() {
        return TemperatureBand;
    }

    public TelemetryBandFrame<BatteryCabinetFanStateBandTelemetry> getFanStateBand() {
        return FanStateBand;
    }
}
