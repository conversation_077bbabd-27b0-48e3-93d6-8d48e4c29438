package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.BMSStatusMasks;
import com.ebon.energy.fms.common.enums.BatteryErrorMasks;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandESJ extends DataBackedBand
{
	public BandESJ()
	{
		super(BandForge.<BandESJ>getMetadataFor(BandESJ.class));
	}


	public BandESJ(byte[] bytes)
	{
		super(bytes, BandForge.<BandESJ>getMetadataFor(BandESJ.class));
	}

	public BandESJ(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESJ>getMetadataFor(BandESJ.class));
	}


	public final int getBMSBattVersion() { return GetU16(0); }


	public final int getBMSBattStrings() { return GetU16(2); }

	
	public final Volt getBMSBattChargeVMax()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Ampere getBMSBattChargeIMax()
	{
		return GetU16(6, Ampere.Unit);
	}

	
	public final Volt getBMSBattDischargeVMin()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Ampere getBMSBattDischargeIMax()
	{
		return GetU16(10, Ampere.Unit);
	}

	
	public final Volt getBMSBattVoltage()
	{
		return GetU16(12, Volt.Deci);
	}

	
	public final Ampere getBMSBattCurrent()
	{
		return GetU16(14, Ampere.Deci);
	}

	
	public final BigDecimal getBMSBattSoC_0to1()
	{
		return new BigDecimal(GetU16(16)).multiply(Percentage._01);
	}

	
	public final BigDecimal getBMSBattSoH_0to1()
	{
		return new BigDecimal(GetU16(18)).multiply(Percentage._01);
	}

	
	public final Celsius getBMSBattTemperature()
	{
		return GetS16(20, Celsius.Deci);
	}

	
	public final Object getBMSBattWarningCode()
	{
		return BatteryErrorMasks.parse((short) GetU32(22));
	}

	
	public final Object getBMSBattAlarmCode()
	{
		return BatteryErrorMasks.parse((short) GetU32(26));
	}

	
	public final Object getBMSBattStatus()
	{
		return BMSStatusMasks.parse(GetU16(30));
	}
}
