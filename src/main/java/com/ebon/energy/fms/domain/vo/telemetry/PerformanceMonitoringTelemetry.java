package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PerformanceMonitoringTelemetry {
    private Integer Uptime;
    private BigDecimal Cpu1;
    private BigDecimal Cpu2;
    private Integer DefaultHeapFreeBytes;
    private Integer InternalHeapFreeBytes;
    private BigDecimal Temperature;

    public PerformanceMonitoringTelemetry() {}

    public PerformanceMonitoringTelemetry(
            Integer Uptime,
            BigDecimal Cpu1,
            BigDecimal Cpu2,
            Integer DefaultHeapFreeBytes,
            Integer InternalHeapFreeBytes,
            BigDecimal Temperature)
    {
        this.Uptime = Uptime;
        this.Cpu1 = Cpu1;
        this.Cpu2 = Cpu2;
        this.DefaultHeapFreeBytes = DefaultHeapFreeBytes;
        this.InternalHeapFreeBytes = InternalHeapFreeBytes;
        this.Temperature = Temperature;
    }

    public Integer getUptime() {
        return Uptime;
    }

    public void setUptime(Integer Uptime) {
        this.Uptime = Uptime;
    }

    // 其他getter/setter方法...
    public BigDecimal getCpu1() {
        return Cpu1;
    }

    public void setCpu1(BigDecimal Cpu1) {
        this.Cpu1 = Cpu1;
    }

    public BigDecimal getCpu2() {
        return Cpu2;
    }

    public void setCpu2(BigDecimal Cpu2) {
        this.Cpu2 = Cpu2;
    }

    public Integer getDefaultHeapFreeBytes() {
        return DefaultHeapFreeBytes;
    }

    public void setDefaultHeapFreeBytes(Integer DefaultHeapFreeBytes) {
        this.DefaultHeapFreeBytes = DefaultHeapFreeBytes;
    }

    public Integer getInternalHeapFreeBytes() {
        return InternalHeapFreeBytes;
    }

    public void setInternalHeapFreeBytes(Integer InternalHeapFreeBytes) {
        this.InternalHeapFreeBytes = InternalHeapFreeBytes;
    }

    public BigDecimal getTemperature() {
        return Temperature;
    }

    public void setTemperature(BigDecimal Temperature) {
        this.Temperature = Temperature;
    }
}