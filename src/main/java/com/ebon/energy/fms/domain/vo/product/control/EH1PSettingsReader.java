package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.InverterModeValue;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.common.utils.path.EH1PSettingPaths;
import com.ebon.energy.fms.common.utils.path.ESGSettingPaths;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class EH1PSettingsReader extends SettingsReader {

    // Constructor that calls the parent constructor
    public EH1PSettingsReader(DeviceInfoAndSettings deviceSettings) {
        super(deviceSettings);
    }

    @Override
    public ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source) {
        if (source == null) {
            source = UniversalSettingSource.DESIRED;
        }
        switch (source) {
            case INTENT:
                // Assuming DeviceSettings and Intent may be null, handle safely
                Boolean enable = null;
                if (deviceSettings != null && deviceSettings.getIntent() != null) {
                    enable = deviceSettings.getIntent().getEnableACCoupledMode();
                }
                return new ACCoupledSettingsDto(enable);
            case REPORTED:
                return new ACCoupledSettingsDto(readBool(false, ESGSettingPaths.ACCoupledOnOff));
            case DESIRED:
            default:
                return new ACCoupledSettingsDto(readBool(true, ESGSettingPaths.ACCoupledOnOff));
        }
    }

    @Override
    public List<UniversalSettingId> getSupportedUniveralSettings(Integer version) {
        List<UniversalSettingId> supportedUniversalSettings = new ArrayList<>();

        supportedUniversalSettings.add(UniversalSettingId.BATTERY_SETTINGS);
        supportedUniversalSettings.add(UniversalSettingId.CT_FLIP);

//        if (getProductModelDefaults().isHasShadowScanFeature()) {
//            supportedUniversalSettings.add(UniversalSettingId.SHADOW_SCAN);
//        }
//
//        if (supportsACCoupledWithAnotherInverter()) {
//            supportedUniversalSettings.add(UniversalSettingId.AC_COUPLED);
//        }
//
//        if (deviceSettings != null &&
//            deviceSettings.getIdentityCard() != null &&
//            deviceSettings.getIdentityCard().getSofterVersion() != null &&
//            deviceSettings.getIdentityCard().getSofterVersion().softwareSupportsGoodweSiteLimits()) {
//            supportedUniversalSettings.add(UniversalSettingId.AS4777_2_GOOD_WE_EXPORT_LIMIT);
//        }
//
//        if (deviceSettings != null &&
//            deviceSettings.getIdentityCard() != null &&
//            deviceSettings.getIdentityCard().getSofterVersion() != null &&
//            deviceSettings.getIdentityCard().getSofterVersion().supportsDred()) {
//            supportedUniversalSettings.add(UniversalSettingId.DRED_SETTINGS);
//        }
//
//        if (supportsRelays()) {
//            supportedUniversalSettings.add(UniversalSettingId.SMART_RELAY_SETTINGS);
//            supportedUniversalSettings.add(UniversalSettingId.TIME_ZONE_ALIAS_SETTINGS);
//        }
//
//        if (supportsSmartLoadControl()) {
//            supportedUniversalSettings.add(UniversalSettingId.SMART_LOAD_CONTROL);
//        }

        return supportedUniversalSettings;
    }

    @Override
    public BatterySettingsDto getBatterySettings(UniversalSettingSource source) {
        var minOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOnGridSoCLimit()).orElse(null));
        var maxOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOnGridSoCLimit()).orElse(null));
        var minOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOffGridSoCLimit()).orElse(null));
        var maxOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOffGridSoCLimit()).orElse(null));

        return getBatterySettings(source == UniversalSettingSource.DESIRED,
                minOnGridSoC0to100,
                maxOnGridSoC0to100,
                minOffGridSoC0to100,
                maxOffGridSoC0to100);
    }

    private BatterySettingsDto getBatterySettings(boolean isDesired,
                                                  int minOnGridSoC0to100,
                                                  int maxOnGridSoC0to100,
                                                  int minOffGridSoC0to100,
                                                  int maxOffGridSoC0to100
    ) {
        return BatterySettingsDto.builder()
                .batteryType(readString(isDesired, EH1PSettingPaths.InverterMode))
                .manufacturer(toManufacturer(readString(isDesired, EH1PSettingPaths.BatteryProtocol)))
                .batteryCount(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumBatteryCount())
                        .max(getProductModelDefaults().getMaximumBatteryCount())
                        .value(getBatteryCount(isDesired))
                        .build())
                .maxChargeCurrent(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumMaxChargeCurrentAmpere())
                        .max(getProductModelDefaults().getMaximumMaxChargeCurrentAmpere())
                        .value(readInt(isDesired, ESGSettingPaths.BatteryMaxChargeCurrent))
                        .build())
                .maxChargeVoltageV(readDecimal(isDesired, ESGSettingPaths.BatteryMaxChargeVoltage))
                .maxDischargeCurrent(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumMaxDischargeCurrentAmpere())
                        .max(getProductModelDefaults().getMaximumMaxDischargeCurrentAmpere())
                        .value(readInt(isDesired, ESGSettingPaths.BatteryMaxDischargeCurrent))
                        .build())
                .minSoc(MinMaxValueDto.builder()
                        .min(minOnGridSoC0to100)
                        .max(maxOnGridSoC0to100)
                        .value((readDecimal(isDesired, ESGSettingPaths.BatteryMinSoc0to1) == null ? null :
                                readDecimal(isDesired, ESGSettingPaths.BatteryMinSoc0to1).multiply(new BigDecimal(100)).intValue()))
                        .build())
                .minOffgridSoc(MinMaxValueDto.builder()
                        .min(minOffGridSoC0to100)
                        .max(maxOffGridSoC0to100)
                        .value( (readDecimal(isDesired, ESGSettingPaths.BatteryMinOffgridSoc0to1) == null ? null :
                                readDecimal(isDesired, ESGSettingPaths.BatteryMinOffgridSoc0to1).multiply(new BigDecimal(100)).intValue()))
                        .build())
                .totalCapacityAh(null)
                .build();
    }

    private boolean supportsACCoupledWithAnotherInverter() {
        return deviceSettings != null &&
                deviceSettings.getIdentityCard() != null &&
                deviceSettings.getIdentityCard().getSofterVersion() != null &&
                deviceSettings.getIdentityCard().getSofterVersion().supportsAcCoupledMode();
    }

    private boolean supportsRelays() {
        return deviceSettings != null &&
                deviceSettings.getIdentityCard() != null &&
                deviceSettings.getIdentityCard().getSofterVersion() != null &&
                deviceSettings.getIdentityCard().getSofterVersion().supportsSmartRelaySettings();
    }

    private boolean supportsSmartLoadControl() {
        return deviceSettings != null &&
                deviceSettings.getIdentityCard() != null &&
                deviceSettings.getIdentityCard().getSofterVersion() != null &&
                deviceSettings.getIdentityCard().getSofterVersion().supportsSmartLoadControl();
    }

    @Override
    public GetInverterModeSettingsDto getDesiredPowerModeSchedulesForPortal() {
        Integer inverterModeNumber = ReadInt(deviceSettings, true, new V2SettingVO(V2Section.InverterControl, ESGSettingPaths.InverterModeSettingName));
        Integer inverterModePowerW = Objects.requireNonNullElse(
                ReadInt(deviceSettings, true, new V2SettingVO(V2Section.InverterControl, ESGSettingPaths.InverterModePowerSettingName)),
                0);

        Map<String, ScheduleV2Dto> currentInverterModeSchedules = null;
        if (deviceSettings != null &&
                deviceSettings.getDesired() != null &&
                deviceSettings.getDesired().getSettingsV2() != null &&
                deviceSettings.getDesired().getSettingsV2().getScheduleSettings() != null) {

            currentInverterModeSchedules = deviceSettings.getDesired().getSettingsV2().getScheduleSettings().getSchedules()
                    .entrySet().stream()
                    .filter(x -> x.getValue() != null &&
                            x.getValue().getDesiredScheduledSettings() != null &&
                            x.getValue().getDesiredScheduledSettings().getInverterControlSettings() != null &&
                            !x.getValue().getDesiredScheduledSettings().getInverterControlSettings().isEmpty())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        return new GetInverterModeSettingsDto(
                new InverterModePair(
                        inverterModeNumber != null
                                ? FromTwinWorkMode(inverterModeNumber)
                                : InverterModeValue.Auto,
                        inverterModePowerW),
                currentInverterModeSchedules == null
                        ? new HashMap<>()
                        : Map(currentInverterModeSchedules));
    }

    private static Map<String, ScheduleInfoDto> Map(Map<String, ScheduleV2Dto> schedules) {
        Map<String, ScheduleInfoDto> transformed = new HashMap<>();
        for (Map.Entry<String, ScheduleV2Dto> schedule : schedules.entrySet()) {
            try {
                ScheduleV2Dto scheduleV2 = schedule.getValue();

                Map<String, Object> settings = scheduleV2.getDesiredScheduledSettings().getInverterControlSettings();

                Integer inverterModeNumber = ReadInt(settings, ESGSettingPaths.InverterModeSettingName);

                Integer inverterModePowerW = Objects.requireNonNullElse(
                        ReadInt(settings, ESGSettingPaths.InverterModePowerSettingName),
                        0);

                ScheduleInfoDto s = new ScheduleInfoDto(
                        scheduleV2.getPriority(),
                        scheduleV2.getStartAtUtc(),
                        scheduleV2.getEndAtUtc(),
                        scheduleV2.getStartTimeOfDay(),
                        scheduleV2.getDuration(),
                        ScheduleDays.fromValue(scheduleV2.getDaysOfWeekActive()),
                        EncodeToScheduleAction(
                                inverterModeNumber != null
                                        ? FromTwinWorkMode(inverterModeNumber)
                                        : InverterModeValue.Auto),
                        inverterModePowerW);

                transformed.put(schedule.getKey(), s);
            } catch (Exception e) {
                // Failed to translate the schedule,
                // As of 2.20 we ignore bad schedules
            }
        }

        return transformed;
    }

    // Utility methods
    private static Integer ReadInt(DeviceInfoAndSettings deviceSettings, boolean isDesired, V2SettingVO setting) {
        // Implementation depends on how settings are stored in DeviceSettingsDto
        if (deviceSettings == null) return null;

        try {
            if (isDesired) {
                // Access desired settings
                if (deviceSettings.getDesired() == null ||
                        deviceSettings.getDesired().getSettingsV2() == null) {
                    return null;
                }

                // Here you would navigate to the specific setting based on section and key
                // This implementation should be adjusted based on the actual data structure
                if (setting.getSection() == V2Section.InverterControl &&
                        deviceSettings.getDesired().getSettingsV2().getInverterControlSettings() != null) {
                    return ReadInt(deviceSettings.getDesired().getSettingsV2().getInverterControlSettings(), setting.getKey());
                }

                return null;
            } else {
                // Access reported settings
                // Similar implementation for reported settings
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    private static Integer ReadInt(Map<String, Object> settings, String key) {
        if (settings == null || !settings.containsKey(key)) return null;

        Object value = settings.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    private static InverterModeValue FromTwinWorkMode(int workMode) {
        // Convert from twin work mode value to InverterModeValue
        return InverterModeValue.fromValue(workMode);
    }

    private static String EncodeToScheduleAction(InverterModeValue mode) {
        // Convert InverterModeValue to schedule action string
        return mode.name();
    }


    private int getBatteryCount(boolean isDesired) {
        if (isDesired) {
            return Optional.ofNullable(readInt(isDesired, ESGSettingPaths.BatteryCount)).orElse(0);
        } else {
            var x = readIntByPath(isDesired, "settings/v2/batteryStack/batteryCount");
            if (x != null) {
                return x;
            } else {
                return Optional.ofNullable(readInt(isDesired, ESGSettingPaths.BatteryCount)).orElse(0);
            }
        }
    }


    public static String toManufacturer(String protocol) {
        if (StringUtils.isEmpty(protocol)) {
            // The default, like for ROSS
            return "None";
        }
        // Comment from the spec as of 2.20:
        // For HV Batteries this will need to be updated.
        if (protocol.toLowerCase().equals("none")) {
            return "None";
        }
        // Temporarily disable, wait for device support of 2025.03.11
        // if (protocol.toLowerCase().equals("red-r1-5000lv")) {
        //     return "Redback";
        // }
        return "Pylon";
    }

}
