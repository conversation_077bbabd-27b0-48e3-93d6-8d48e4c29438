package com.ebon.energy.fms.domain.entity;

import com.ebon.energy.fms.util.AddressFormatter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.time.*;

import static com.ebon.energy.fms.util.AddressFormatter.getAddressPart;

@Data
public class ProductWithOwnerDO {
    
    private String SerialNumber;

    private Timestamp DateCreated;

    private Timestamp LastSystemStatusReceived;

    private String OwnerId;

    private String OriginalInstallerCompanyId;

    private String OwnerFirstName;

    private String OwnerLastName;

    private String CompanyName;

    private String PublicSiteId;

    private String Nmi;

    private String SiteId;

    private String OwnerContact;

    private String OwnerEmail;

    private String AddressLineOne;

    private String AddressLineTwo;

    private String Suburb;

    private String State;

    private String Postcode;

    private String Country;

    public final String getAddressForUI() {
        String addressStr = getAddressPart(getAddressLineOne()) +
                getAddressPart(getAddressLineTwo(), true) +
                getAddressPart(getSuburb()) +
                getAddressPart(getState()) +
                getAddressPart(getPostcode()) +
                getAddressPart(getCountry(), true);
        return StringUtils.isNoneBlank(addressStr) ? addressStr.trim() : "Not found";
    }
}