package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandSGCurrentInverterAnalytics extends DataBackedBand
{
	public BandSGCurrentInverterAnalytics()
	{
		super(BandForge.<BandSGCurrentInverterAnalytics>getMetadataFor(BandSGCurrentInverterAnalytics.class));
	}



	public BandSGCurrentInverterAnalytics(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGCurrentInverterAnalytics>getMetadataFor(BandSGCurrentInverterAnalytics.class));
	}

	public BandSGCurrentInverterAnalytics(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGCurrentInverterAnalytics>getMetadataFor(BandSGCurrentInverterAnalytics.class));
	}



	public final Ampere getPV1CurrentMax()
	{
		return GetU16(0, Ampere.Centi);
	}


	public final Ampere getPV1CurrentMin()
	{
		return GetU16(2, Ampere.Centi);
	}


	public final Ampere getPV1CurrentAvg()
	{
		return GetU16(4, Ampere.Centi);
	}


	public final Ampere getPV2CurrentMax()
	{
		return GetU16(6, Ampere.Centi);
	}


	public final Ampere getPV2CurrentMin()
	{
		return GetU16(8, Ampere.Centi);
	}


	public final Ampere getPV2CurrentAvg()
	{
		return GetU16(10, Ampere.Centi);
	}
}
