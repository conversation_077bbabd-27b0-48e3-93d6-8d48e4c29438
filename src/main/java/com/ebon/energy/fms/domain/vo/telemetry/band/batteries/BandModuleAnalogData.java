package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.PylonInfoFlagState;
import com.ebon.energy.fms.common.enums.PylonUserDefined;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import java.math.BigDecimal;


public class BandModuleAnalogData extends DataBackedBand {
    public BandModuleAnalogData() {
        super(BandForge.<BandModuleAnalogData>getMetadataFor(BandModuleAnalogData.class));
    }


    public BandModuleAnalogData(byte[] bytes) {
        super(bytes, BandForge.<BandModuleAnalogData>getMetadataFor(BandModuleAnalogData.class));
    }

    public BandModuleAnalogData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandModuleAnalogData>getMetadataFor(BandModuleAnalogData.class));
    }


    public final Object getInfoFlag42() {
        return PylonInfoFlagState.parse(GetU8(0));
    }


    public final byte getCommandValue42() {
        return GetU8(1);
    }


    public final byte getNumberOfCells42() {
        return GetU8(2);
    }


    public final Volt getCell1Voltage() {
        return GetS16(3, Volt.Milli);
    }


    public final Volt getCell2Voltage() {
        return GetS16(5, Volt.Milli);
    }


    public final Volt getCell3Voltage() {
        return GetS16(7, Volt.Milli);
    }


    public final Volt getCell4Voltage() {
        return GetS16(9, Volt.Milli);
    }


    public final Volt getCell5Voltage() {
        return GetS16(11, Volt.Milli);
    }


    public final Volt getCell6Voltage() {
        return GetS16(13, Volt.Milli);
    }


    public final Volt getCell7Voltage() {
        return GetS16(15, Volt.Milli);
    }


    public final Volt getCell8Voltage() {
        return GetS16(17, Volt.Milli);
    }


    public final Volt getCell9Voltage() {
        return GetS16(19, Volt.Milli);
    }


    public final Volt getCell10Voltage() {
        return GetS16(21, Volt.Milli);
    }


    public final Volt getCell11Voltage() {
        return GetS16(23, Volt.Milli);
    }


    public final Volt getCell12Voltage() {
        return GetS16(25, Volt.Milli);
    }


    public final Volt getCell13Voltage() {
        return GetS16(27, Volt.Milli);
    }


    public final Volt getCell14Voltage() {
        return GetS16(29, Volt.Milli);
    }


    public final Volt getCell15Voltage() {
        return GetS16(31, Volt.Milli);
    }


    public final Volt getCell16Voltage() {
        return getNumberOfCells42() == 16 ? GetS16(33, Volt.Milli) : null;
    }


    public final byte getNumberOfTempSensors() {
        return GetU8(3 + getNumberOfCells42() * 2);
    }


    public final Celsius getTempBMSBoard() {
        return getNumberOfTempSensors() >= 1 ? GetS16(4 + getNumberOfCells42() * 2, Kelvin.Deci).ToCelsius() : null;
    }


    public final Celsius getCellTempAvgGroup1() {
        return getNumberOfTempSensors() >= 3 ? GetS16(6 + getNumberOfCells42() * 2, Kelvin.Deci).ToCelsius() : null;
    }

    public final Celsius getCellTempAvgGroup2() {
        return getNumberOfTempSensors() >= 4 ? GetS16(8 + getNumberOfCells42() * 2, Kelvin.Deci).ToCelsius() : null;
    }

    public final Celsius getCellTempAvgGroup3() {
        return getNumberOfTempSensors() >= 5 ? GetS16(10 + getNumberOfCells42() * 2, Kelvin.Deci).ToCelsius() : null;
    }

    public final Celsius getCellTempAvgGroup4() {
        return getNumberOfTempSensors() >= 6 ? GetS16(12 + getNumberOfCells42() * 2, Kelvin.Deci).ToCelsius() : null;
    }


    public final Celsius getMOSFETTemp() {
        return getNumberOfTempSensors() >= 2 ? GetS16(2 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2, Kelvin.Deci).ToCelsius() : null;
    }


    public final Ampere getModuleCurrent() {
        return GetS16(4 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2, Ampere.Deci);
    }

    public final Volt getModuleVoltage() {
        return GetU16(6 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2, Volt.Milli);
    }

    public final Capacity getRemainingCapacitySmallBatt() {
        return GetU16(8 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2, Capacity.Milli);
    }

    public final PylonUserDefined getUserDefined() {
        return PylonUserDefined.fromValue(GetU8(10 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2));
    }


    public final Capacity getTotalCapacitySmallBatt() {
        return GetU16(11 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2, Capacity.Milli);
    }


    public final int getCycleNumber() {
        return GetU16(13 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2);
    }

    public final Capacity getRemainingCapacity() {
        return Capacity.operatorMultiply(new Capacity(GetU24(15 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2)), Capacity.Milli);
    }

    public final Capacity getTotalCapacity() {
        return Capacity.operatorMultiply(new Capacity(GetU24(18 + (getNumberOfCells42() + getNumberOfTempSensors()) * 2)), Capacity.Milli);
    }
}
