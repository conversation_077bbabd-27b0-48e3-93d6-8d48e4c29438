package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.ETDRMStatus;
import com.ebon.energy.fms.common.enums.SGLEDMode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandSGInverterBooleans extends DataBackedBand
{
	public BandSGInverterBooleans()
	{
		super(BandForge.<BandSGInverterBooleans>getMetadataFor(BandSGInverterBooleans.class));
	}



	public BandSGInverterBooleans(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterBooleans>getMetadataFor(BandSGInverterBooleans.class));
	}

	public BandSGInverterBooleans(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterBooleans>getMetadataFor(BandSGInverterBooleans.class));
	}


	
	public final Object getDRMStatus()
	{
		return ETDRMStatus.parse(GetU16(0));
	}

	
	public final Object getLEDMode()
	{
		return SGLEDMode.parse(GetU16(2));
	}
}
