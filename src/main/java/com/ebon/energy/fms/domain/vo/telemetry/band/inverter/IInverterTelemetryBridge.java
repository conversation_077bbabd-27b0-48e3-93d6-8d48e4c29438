package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.InverterCommsStatus;
import com.ebon.energy.fms.domain.vo.telemetry.ProductLocalTimeTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.RossDeviceTelemetry;

import java.time.*;


public interface IInverterTelemetryBridge extends IInverterBridge
{
	RossDeviceTelemetry getRossDeviceTelemetry();

	ProductLocalTimeTelemetry getTelemetryLocalTime();

	//IClock getClock();

	/** 
	 Gets a value indicating the parsed inverter communication status from inverter telemetry.
	*/
	InverterCommsStatus getInverterCommunicationStatus();

	/** 
	 The IoT Hub device Id for the Ross that produced this telemetry.
	 
	 @return IoT Hub Device Id.
	*/
	String GetRossDeviceId();

	/** 
	 Converts a utc datetime into the local time for the inverter.
	 
	 @param itemTimestampUtc UTC Timestamp to convert.
	 @return Local datetime.
	*/
	LocalDateTime GetLocalTimeFromUtcTimestamp(LocalDateTime itemTimestampUtc);
}
