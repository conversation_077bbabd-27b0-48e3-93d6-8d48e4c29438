package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.PylonInfoFlagState;
import com.ebon.energy.fms.common.enums.PylonUserDefined;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandPylonUSModuleAnalogData extends DataBackedBand
{
	public BandPylonUSModuleAnalogData()
	{
		super(BandForge.<BandPylonUSModuleAnalogData>getMetadataFor(BandPylonUSModuleAnalogData.class));
	}



	public BandPylonUSModuleAnalogData(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylonUSModuleAnalogData>getMetadataFor(BandPylonUSModuleAnalogData.class));
	}

	public BandPylonUSModuleAnalogData(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylonUSModuleAnalogData>getMetadataFor(BandPylonUSModuleAnalogData.class));
	}


	public final Object getInfoFlag42()
	{
		return PylonInfoFlagState.parse(GetU8(0));
	}


	public final byte getCommandValue42()
	{
		return GetU8(1);
	}




	public final byte getNumberOfCells42()
	{
		return GetU8(2);
	}


	public final Volt getCell1Voltage()
	{
		return GetS16(3, Volt.Milli);
	}


	public final Volt getCell2Voltage()
	{
		return GetS16(5, Volt.Milli);
	}


	public final Volt getCell3Voltage()
	{
		return GetS16(7, Volt.Milli);
	}


	public final Volt getCell4Voltage()
	{
		return GetS16(9, Volt.Milli);
	}


	public final Volt getCell5Voltage()
	{
		return GetS16(11, Volt.Milli);
	}


	public final Volt getCell6Voltage()
	{
		return GetS16(13, Volt.Milli);
	}


	public final Volt getCell7Voltage()
	{
		return GetS16(15, Volt.Milli);
	}


	public final Volt getCell8Voltage()
	{
		return GetS16(17, Volt.Milli);
	}


	public final Volt getCell9Voltage()
	{
		return GetS16(19, Volt.Milli);
	}


	public final Volt getCell10Voltage()
	{
		return GetS16(21, Volt.Milli);
	}


	public final Volt getCell11Voltage()
	{
		return GetS16(23, Volt.Milli);
	}


	public final Volt getCell12Voltage()
	{
		return GetS16(25, Volt.Milli);
	}


	public final Volt getCell13Voltage()
	{
		return GetS16(27, Volt.Milli);
	}


	public final Volt getCell14Voltage()
	{
		return GetS16(29, Volt.Milli);
	}


	public final Volt getCell15Voltage()
	{
		return GetS16(31, Volt.Milli);
	}




	public final byte getNumberOfTempSensors()
	{
		return GetU8(33);
	}


	public final Celsius getTempBMSBoard()
	{
		return GetS16(34,Kelvin.Deci).ToCelsius();
	}


	public final Celsius getCellTempAvgGroup1()
	{
		return GetS16(36,Kelvin.Deci).ToCelsius();
	}


	public final Celsius getCellTempAvgGroup2()
	{
		return GetS16(38,Kelvin.Deci).ToCelsius();
	}


	public final Celsius getCellTempAvgGroup3()
	{
		return GetS16(40,Kelvin.Deci).ToCelsius();
	}


	public final Celsius getCellTempAvgGroup4()
	{
		return GetS16(42,Kelvin.Deci).ToCelsius();
	}


	public final Celsius getMOSFETTemp()
	{
		return GetS16(44,Kelvin.Deci).ToCelsius();
	}


	public final Ampere getModuleCurrent()
	{
		return GetS16(46, Ampere.Deci);
	}


	public final Volt getModuleVoltage()
	{
		return GetU16(48, Volt.Milli);
	}


	public final Capacity getRemainingCapacitySmallBatt()
	{
		return GetU16(50, Capacity.Milli);
	}


	public final PylonUserDefined getUserDefined()
	{
		return PylonUserDefined.fromValue(GetU8(52));
	}


	public final Capacity getTotalCapacitySmallBatt()
	{
		return GetU16(53, Capacity.Milli);
	}




	public final int getCycleNumber() { return GetU16(55); }


	public final Capacity getRemainingCapacity()
	{
		return GetU24(57, Capacity.Milli);
	}


	public final Capacity getTotalCapacity()
	{
		return GetU24(60, Capacity.Milli);
	}
}
