package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ACCoupledSettingsDto {

    @JsonProperty("IsACCoupledEnabled")
    private Boolean isACCoupledEnabled;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ACCoupledSettingsDto)) return false;
        ACCoupledSettingsDto that = (ACCoupledSettingsDto) o;
        return Objects.equals(isACCoupledEnabled, that.isACCoupledEnabled);
    }

    @Override
    public int hashCode() {
        return Objects.hash(isACCoupledEnabled);
    }
}
