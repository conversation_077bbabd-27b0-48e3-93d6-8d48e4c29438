package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.FPSetting;
import com.ebon.energy.fms.common.enums.VRTMode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandETG extends DataBackedBand implements IBandETG
{
	public BandETG()
	{
		super(BandForge.<BandETG>getMetadataFor(BandETG.class));
	}



	public BandETG(byte[] bytes)
	{
		super(bytes, BandForge.<BandETG>getMetadataFor(BandETG.class));
	}

	public BandETG(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETG>getMetadataFor(BandETG.class));
	}


	
	public final Volt getGridVoltHighS1()
	{
		return GetU16(0, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltHighS1Time()
	{
		return GetU16(2, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltLowS1()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltLowS1Time()
	{
		return GetU16(6, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltHighS2()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltHighS2Time()
	{
		return GetU16(10, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltLowS2()
	{
		return GetU16(12, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltLowS2Time()
	{
		return GetU16(14, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltQuality()
	{
		return GetU16(16, Volt.Deci);
	}

	
	public final Frequency getGridFreqHighS1()
	{
		return GetU16(18, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqHighS1Time()
	{
		return GetU16(20, TimeSpan.fromSeconds(0.02));
	}

	
	public final Frequency getGridFreqLowS1()
	{
		return GetU16(22, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqLowS1Time()
	{
		return GetU16(24, TimeSpan.fromSeconds(0.02));
	}

	
	public final Frequency getGridFreqHighS2()
	{
		return GetU16(26, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqHighS2Time()
	{
		return GetU16(28, TimeSpan.fromSeconds(0.02));
	}

	
	public final Frequency getGridFreqLowS2()
	{
		return GetU16(30, Frequency.Centi);
	}

	
	public final TimeSpan getGridFreqLowS2Time()
	{
		return GetU16(32, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltHigh()
	{
		return GetU16(34, Volt.Deci);
	}

	
	public final Volt getGridVoltLow()
	{
		return GetU16(36, Volt.Deci);
	}

	
	public final Frequency getGridFreqHigh()
	{
		return GetU16(38, Frequency.Centi);
	}

	
	public final Frequency getGridFreqLow()
	{
		return GetU16(40, Frequency.Centi);
	}

	
	public final TimeSpan getGridStartTime()
	{
		return GetU16(42, TimeSpan.fromSeconds(1));
	}

	
	public final Volt getGridVoltRecoverHigh()
	{
		return GetU16(44, Volt.Deci);
	}

	
	public final Volt getGridVoltRecoverLow()
	{
		return GetU16(46, Volt.Deci);
	}

	
	public final Frequency getGridFreqRecoverHigh()
	{
		return GetU16(48, Frequency.Centi);
	}

	
	public final Frequency getGridFreqRecoverLow()
	{
		return GetU16(50, Frequency.Centi);
	}

	
	public final TimeSpan getGridRecoverTime()
	{
		return GetU16(52, TimeSpan.fromSeconds(1));
	}

	


	public final int getReserved0xB173() { return GetU16(54); }

	
	public final TimeSpan getPowerRateLimitGenerate()
	{
		return GetU16(56, TimeSpan.fromSeconds(1));
	}

	
	public final TimeSpan getPowerRateLimitReconnect()
	{
		return GetU16(58, TimeSpan.fromSeconds(1));
	}

	
	public final TimeSpan getPowerRateLimitReduction()
	{
		return GetU16(60, TimeSpan.fromSeconds(1));
	}

	
	public final boolean getGridProtect()
	{
		return GetBool((int) GetU16(62), 1, 0, true);
	}

	
	public final boolean getPowerSlopeEnable()
	{
		return GetBool((int) GetU16(64), 1, 0, true);
	}

	
	public final boolean getCosPhiEnableCurve()
	{
		return GetBool((int) GetU16(66), 1, 0, false);
	}

	
	public final BigDecimal getCosPhiPointAPowerPercent()
	{
		return new BigDecimal(GetU16(68)).multiply(Percentage._01);
	}

	
	public final BigDecimal getCosPhiPointAPowerFactor()
	{
		return new BigDecimal(GetU16(70)).multiply(Percentage._1);
	}

	
	public final BigDecimal getCosPhiPointBPowerPercent()
	{
		return new BigDecimal(GetU16(72)).multiply(Percentage._01);
	}

	
	public final BigDecimal getCosPhiPointBPowerFactor()
	{
		return new BigDecimal(GetU16(74)).multiply(Percentage._1);
	}

	
	public final BigDecimal getCosPhiPointCPowerPercent()
	{
		return new BigDecimal(GetU16(76)).multiply(Percentage._01);
	}

	
	public final BigDecimal getCosPhiPointCPowerFactor()
	{
		return new BigDecimal(GetU16(78)).multiply(Percentage._1);
	}

	
	public final Volt getCosPhiLockinVoltage()
	{
		return GetU16(80, Volt.Deci);
	}

	
	public final Volt getCosPhiLockoutVoltage()
	{
		return GetU16(82, Volt.Deci);
	}

	
	public final BigDecimal getCosPhiLockoutPowerPercent()
	{
		return new BigDecimal(GetS16(84)).multiply(Percentage._01);
	}

	
	public final boolean getPowerAndFrequencyCurveEnabled()
	{
		return GetBool((int) GetU16(86), 1, 0, true);
	}

	
	public final Frequency getFfrozenDCHFrequencyOfPm()
	{
		return GetU16(88, Frequency.Centi);
	}

	
	public final Frequency getFfrozenCHFrequencyOfPm()
	{
		return GetU16(90, Frequency.Centi);
	}

	
	public final Frequency getFstopDCH()
	{
		return GetU16(92, Frequency.Centi);
	}

	
	public final Frequency getFstopCH()
	{
		return GetU16(94, Frequency.Centi);
	}

	
	public final TimeSpan getRecoveryWaitingTime()
	{
		return GetU16(96, TimeSpan.fromSeconds(1));
	}

	
	public final Frequency getRecoveryFrequency1()
	{
		return GetU16(98, Frequency.Centi);
	}

	
	public final Frequency getRecoveryFrequency2()
	{
		return GetU16(100, Frequency.Centi);
	}

	
	public final TimeSpan getRecoveryHoldTime()
	{
		return GetU16(102, TimeSpan.fromSeconds(1));
	}

	
	public final Object getFPSetting()
	{
		return FPSetting.parse(GetU16(104));
	}

	
	public final BigDecimal getOverFrequencySlopePercHz()
	{
		return new BigDecimal(GetS16(106)).multiply(Percentage._01);
	}

	
	public final BigDecimal getUnderFrequencySlopePercHz()
	{
		return new BigDecimal(GetS16(108)).multiply(Percentage._01);
	}

	
	public final BigDecimal getFPrecoverPowerPerc()
	{
		return new BigDecimal(GetS16(110)).multiply(Percentage._01);
	}

	
	public final boolean getQUCurveEnable()
	{
		return GetBool((int) GetU16(112), 1, 0, false);
	}

	
	public final BigDecimal getQULockInPowerPerc()
	{
		return new BigDecimal(GetU16(114)).multiply(Percentage._01);
	}

	
	public final BigDecimal getQULockOutPowerPerc()
	{
		return new BigDecimal(GetU16(116)).multiply(Percentage._01);
	}

	
	public final Volt getQUV1Voltage()
	{
		return GetU16(118, Volt.Deci);
	}

	
	public final BigDecimal getQUV1ValueVarPercentageRatedVA()
	{
		return new BigDecimal(GetS16(120)).multiply(Percentage._01);
	}

	
	public final Volt getQUV2Voltage()
	{
		return GetU16(122, Volt.Deci);
	}

	
	public final BigDecimal getQUV2ValuevarPercentageRatedVA()
	{
		return new BigDecimal(GetS16(124)).multiply(Percentage._01);
	}

	
	public final Volt getQUV3Voltage()
	{
		return GetU16(126, Volt.Deci);
	}

	
	public final BigDecimal getQUV3ValuevarPercentageRatedVA()
	{
		return new BigDecimal(GetS16(128)).multiply(Percentage._01);
	}

	
	public final Volt getQUV4Voltage()
	{
		return GetU16(130, Volt.Deci);
	}

	
	public final BigDecimal getQUV4ValuevarPercentageRatedVA()
	{
		return new BigDecimal(GetS16(132)).multiply(Percentage._01);
	}

	
	public final short getQUKValue()
	{
		return GetS16(134);
	}

	


	public final int getQUTimeConstant() { return GetU16(136); }

	


	public final int getMiscellaneous() { return GetU16(138); }

	


	public final int getRatedVoltageKorea() { return GetU16(140); }

	


	public final int getResponseTimeKorea() { return GetU16(142); }

	
	public final boolean getPUcurveEnabled()
	{
		return GetBool((int) GetU16(144), 1, 0, true);
	}

	
	public final TimeSpan getPowerchangerate()
	{
		return GetU16(146, TimeSpan.fromSeconds(1));
	}

	
	public final Volt getPUV1Voltage()
	{
		return GetU16(148, Volt.Deci);
	}

	
	public final BigDecimal getPUV1ValuePDividedByPn()
	{
		return new BigDecimal(GetS16(150)).multiply(Percentage._01);
	}

	
	public final Volt getPUV2Voltage()
	{
		return GetU16(152, Volt.Deci);
	}

	
	public final BigDecimal getPUV2ValuePDividedByPn()
	{
		return new BigDecimal(GetS16(154)).multiply(Percentage._01);
	}

	
	public final Volt getPUV3Voltage()
	{
		return GetU16(156, Volt.Deci);
	}

	
	public final BigDecimal getPUV3ValuePDividedByPn()
	{
		return new BigDecimal(GetS16(158)).multiply(Percentage._01);
	}

	
	public final Volt getPUV4Voltage()
	{
		return GetU16(160, Volt.Deci);
	}

	
	public final BigDecimal getPUV4ValuePDividedByPn()
	{
		return new BigDecimal(GetS16(162)).multiply(Percentage._01);
	}

	
	public final BigDecimal getFixedPowerFactor()
	{
		return ConversionHelpers.convertToPowerFactor(GetU16(164));
	}

	
	public final VoltAmpsReactive getFixedReactivePower()
	{
		return GetS16(166, VoltAmpsReactive.Unit);
	}

	
	public final BigDecimal getFixedActivePower()
	{
		return new BigDecimal(GetU16(168)).multiply(Percentage._01);
	}

	
	public final Volt getGridLimitByVolStartVol()
	{
		return GetU16(170, Volt.Deci);
	}

	
	public final BigDecimal getGridLimitByVolStartPer()
	{
		return new BigDecimal(GetU16(172)).multiply(Percentage._1);
	}

	


	public final int getGridLimitByVolSlope() { return GetU16(174); }

	
	public final boolean getAutotestEnable()
	{
		return GetBool((int) GetU16(176), 1, 0, false);
	}

	


	public final int getAutoTeststep() { return GetU16(178); }

	


	public final int getUwItalyFreqMode() { return GetU16(180); }

	
	public final boolean getAllPowerCurveDisable()
	{
		return GetBool((int) GetU16(182), 1, 0, false);
	}

	
	public final BigDecimal getL1phasefixedactivePower()
	{
		return new BigDecimal(GetU16(184)).multiply(Percentage._01);
	}

	
	public final BigDecimal getL2phasefixedactivePower()
	{
		return new BigDecimal(GetU16(186)).multiply(Percentage._01);
	}

	
	public final BigDecimal getL3phasefixedactivePower()
	{
		return new BigDecimal(GetU16(188)).multiply(Percentage._01);
	}

	
	public final Volt getGridVoltHighS3()
	{
		return GetU16(190, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltHighS3Time()
	{
		return GetU16(192, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getGridVoltLowS3()
	{
		return GetU16(194, Volt.Deci);
	}

	
	public final TimeSpan getGridVoltLowS3Time()
	{
		return GetU16(196, TimeSpan.fromSeconds(0.02));
	}

	
	public final Object getZvrtConfig()
	{
		return VRTMode.parse(GetU16(198));
	}

	
	public final Volt getLvrtStartVolt()
	{
		return GetU16(200, Volt.Deci);
	}

	
	public final Volt getLvrtEndVolt()
	{
		return GetU16(202, Volt.Deci);
	}

	
	public final TimeSpan getLvrtStartTripTime()
	{
		return GetU16(204, TimeSpan.fromSeconds(0.02));
	}

	
	public final TimeSpan getLvrtEndTripTime()
	{
		return GetU16(206, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getLvrtTripLimitVolt()
	{
		return GetU16(208, Volt.Deci);
	}

	
	public final Volt getHvrtStartVolt()
	{
		return GetU16(210, Volt.Deci);
	}

	
	public final Volt getHvrtEndVolt()
	{
		return GetU16(212, Volt.Deci);
	}

	
	public final TimeSpan getHvrtStartTripTime()
	{
		return GetU16(214, TimeSpan.fromSeconds(0.02));
	}

	
	public final TimeSpan getHvrtEndTripTime()
	{
		return GetU16(216, TimeSpan.fromSeconds(0.02));
	}

	
	public final Volt getHvrtTripLimitVolt()
	{
		return GetU16(218, Volt.Deci);
	}
}
