package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.ebon.energy.fms.common.enums.LoadActive;
import com.ebon.energy.fms.common.enums.AutoLoadControlState;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class SmartRelayTelemetry {
    private String OverallRunTime;
    private LoadActive LoadActive;
    private AutoLoadControlState AutoLoadControlState;

    public SmartRelayTelemetry() {}

    public String getOverallRunTime() {
        return OverallRunTime;
    }

    public void setOverallRunTime(String OverallRunTime) {
        this.OverallRunTime = OverallRunTime;
    }

    public LoadActive getLoadActive() {
        return LoadActive;
    }

    public void setLoadActive(LoadActive LoadActive) {
        this.LoadActive = LoadActive;
    }

    public AutoLoadControlState getAutoLoadControlState() {
        return AutoLoadControlState;
    }

    public void setAutoLoadControlState(AutoLoadControlState AutoLoadControlState) {
        this.AutoLoadControlState = AutoLoadControlState;
    }
}