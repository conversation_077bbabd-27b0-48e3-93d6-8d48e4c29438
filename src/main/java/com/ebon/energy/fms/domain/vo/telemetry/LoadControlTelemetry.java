package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.ControlledLoadState;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.math.BigDecimal;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class LoadControlTelemetry {
    private Integer Id;
    private Boolean RelayState;
    private Boolean LoadState;
    private BigDecimal DailyRunTime;
    private ControlledLoadState SmartLoadControlState;
    private BigDecimal LoadPower;

}