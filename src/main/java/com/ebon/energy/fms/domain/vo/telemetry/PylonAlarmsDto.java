package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.List;
import java.util.Collections;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonAlarmsDto extends PylonDto {
    private Integer PackIndex;
    private List<PylonAlarmState> CellVoltageState;
    private List<PylonAlarmState> TempSensorState;
    private PylonAlarmState ChargeCurrentState;
    private PylonAlarmState PackVoltageState;
    private PylonAlarmState DischargeCurrentState;
    private PylonPackStatus1 Status1;
    private PylonPackStatus2 Status2;
    private PylonPackStatus3 Status3;
    private PylonPackStatus4 Status4;
    private PylonPackStatus5 Status5;

    public PylonAlarmsDto(
            String rawMessage,
            Integer packIndex,
            List<PylonAlarmState> cellVoltageState,
            List<PylonAlarmState> tempSensorState,
            PylonAlarmState chargeCurrentState,
            PylonAlarmState packVoltageState,
            PylonAlarmState dischargeCurrentState,
            PylonPackStatus1 status1,
            PylonPackStatus2 status2,
            PylonPackStatus3 status3,
            PylonPackStatus4 status4,
            PylonPackStatus5 status5) {

        super(rawMessage);
        this.PackIndex = packIndex;
        this.CellVoltageState = cellVoltageState != null ?
                Collections.unmodifiableList(cellVoltageState) :
                Collections.emptyList();
        this.TempSensorState = tempSensorState != null ?
                Collections.unmodifiableList(tempSensorState) :
                Collections.emptyList();
        this.ChargeCurrentState = chargeCurrentState;
        this.PackVoltageState = packVoltageState;
        this.DischargeCurrentState = dischargeCurrentState;
        this.Status1 = status1;
        this.Status2 = status2;
        this.Status3 = status3;
        this.Status4 = status4;
        this.Status5 = status5;
    }

    public Integer getPackIndex() { return PackIndex; }
    public List<PylonAlarmState> getCellVoltageState() { return CellVoltageState; }
    public List<PylonAlarmState> getTempSensorState() { return TempSensorState; }
    public PylonAlarmState getChargeCurrentState() { return ChargeCurrentState; }
    public PylonAlarmState getPackVoltageState() { return PackVoltageState; }
    public PylonAlarmState getDischargeCurrentState() { return DischargeCurrentState; }
    public PylonPackStatus1 getStatus1() { return Status1; }
    public PylonPackStatus2 getStatus2() { return Status2; }
    public PylonPackStatus3 getStatus3() { return Status3; }
    public PylonPackStatus4 getStatus4() { return Status4; }
    public PylonPackStatus5 getStatus5() { return Status5; }
}