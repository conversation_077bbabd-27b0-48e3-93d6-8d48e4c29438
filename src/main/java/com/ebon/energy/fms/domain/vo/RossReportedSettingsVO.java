package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.SettingsProtocol;

public class RossReportedSettingsVO {

    private SettingsProtocol Protocol;

    private SettingsV2ReportedVO v2;

    private InverterReportedSettingsVO inverter;

    public SettingsProtocol getProtocol() {
        return Protocol;
    }

    public void setProtocol(SettingsProtocol protocol) {
        Protocol = protocol;
    }

    public SettingsV2ReportedVO getV2() {
        return v2;
    }

    public void setV2(SettingsV2ReportedVO v2) {
        this.v2 = v2;
    }

    public InverterReportedSettingsVO getInverter() {
        return inverter;
    }

    public void setInverter(InverterReportedSettingsVO inverter) {
        this.inverter = inverter;
    }
}