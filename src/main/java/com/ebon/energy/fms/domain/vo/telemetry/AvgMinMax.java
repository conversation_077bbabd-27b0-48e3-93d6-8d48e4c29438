package com.ebon.energy.fms.domain.vo.telemetry;

import java.math.BigDecimal;

/**
 * Message type for min/max/avg values.
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
public class AvgMinMax {
    /**
     * Average over time period.
     */
    public BigDecimal Average = null;

    public final BigDecimal getAverage() {
        return Average;
    }

    public final void setAverage(BigDecimal value) {
        Average = value;
    }

    /**
     * Min over time period.
     */
    public BigDecimal Min = null;

    public final BigDecimal getMin() {
        return Min;
    }

    public final void setMin(BigDecimal value) {
        Min = value;
    }

    /**
     * Max over time period.
     */
    public BigDecimal Max = null;

    public final BigDecimal getMax() {
        return Max;
    }

    public final void setMax(BigDecimal value) {
        Max = value;
    }

    /**
     * Instantaneous.
     */
    public BigDecimal Instantaneous = null;

    public final BigDecimal getInstantaneous() {
        return Instantaneous;
    }

    public final void setInstantaneous(BigDecimal value) {
        Instantaneous = value;
    }
}
