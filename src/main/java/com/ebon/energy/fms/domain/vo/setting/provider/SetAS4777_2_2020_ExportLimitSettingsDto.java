package com.ebon.energy.fms.domain.vo.setting.provider;

import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SetAS4777_2_2020_ExportLimitSettingsDto implements ISetAS4777_2_2020_ExportLimitSettingsDto {
    @JsonProperty("ExportHardLimitEnable")
    private final Boolean exportHardLimitEnable;

    @JsonProperty("ExportHardLimitW")
    private final Watt exportHardLimitW;

    @JsonProperty("ExportSoftLimitEnable")
    private final Boolean exportSoftLimitEnable;

    @JsonProperty("ExportSoftLimitW")
    private final Watt exportSoftLimitW;
}