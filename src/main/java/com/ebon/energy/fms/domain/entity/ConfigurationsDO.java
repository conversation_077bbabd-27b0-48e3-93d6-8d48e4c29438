package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("Configurations")
public class ConfigurationsDO {

    // 对应表中的 Id 字段，自增长主键
    @TableId("Id")
    private Integer id;

    @TableField("RedbackProductSn")
    private String redbackProductSn;

    @TableField("ConfigurationType")
    private int configurationType;

    @TableField("Configurations")
    private String configurations;

    @TableField("ModifiedDateTime")
    private Timestamp modifiedDateTime;

    @TableField("ConfigurationsOnDevice")
    private String configurationsOnDevice;

    // 对应表中的 RowVersion 字段，这里使用 byte[] 来表示 timestamp 类型，具体根据实际情况调整
    @TableField("RowVersion")
    private byte[] rowVersion;

    @TableField("LastModifiedById")
    private String lastModifiedById;

}