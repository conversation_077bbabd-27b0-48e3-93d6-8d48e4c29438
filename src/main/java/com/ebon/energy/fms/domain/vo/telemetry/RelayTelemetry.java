package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.RelayReason;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class RelayTelemetry {
    private Boolean State;
    private RelayReason Reason;

    public RelayTelemetry(Boolean State, RelayReason Reason) {
        this.State = State;
        this.Reason = Reason;
    }

    public boolean getState() {
        return State;
    }

    public void setState(Boolean State) {
        this.State = State;
    }

    public RelayReason getReason() {
        return Reason;
    }

    public void setReason(RelayReason Reason) {
        this.Reason = Reason;
    }
}