package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandJTelemetry implements ITelemetryBand {
    public InverterBandJTelemetry(short bMSBattVersion, short bMSBattStrings, Volt bMSBattChargeVMax, Ampere bMSBattChargeIMax, Volt bMSBattDischargeVMin, Ampere bMSBattDischargeIMax, Volt bMSBattVoltage, Ampere bMSBattCurrent, BigDecimal bMSBattSoC_0to1, BigDecimal bMSBattSoH_0to1, Celsius bMSBattTemperature, BatteryErrorMasks bMSBattWarningCode, BatteryErrorMasks bMSBattAlarmCode, BMSStatusMasks bMSBattStatus) {
        BMSBattVersion = bMSBattVersion;
        BMSBattStrings = bMSBattStrings;
        BMSBattChargeVMax = bMSBattChargeVMax;
        BMSBattChargeIMax = bMSBattChargeIMax;
        BMSBattDischargeVMin = bMSBattDischargeVMin;
        BMSBattDischargeIMax = bMSBattDischargeIMax;
        BMSBattVoltage = bMSBattVoltage;
        BMSBattCurrent = bMSBattCurrent;
        BMSBattSoC_0to1 = bMSBattSoC_0to1;
        BMSBattSoH_0to1 = bMSBattSoH_0to1;
        BMSBattTemperature = bMSBattTemperature;
        BMSBattWarningCode = bMSBattWarningCode;
        BMSBattAlarmCode = bMSBattAlarmCode;
        BMSBattStatus = bMSBattStatus;
    }

    private final short BMSBattVersion;

    public final short getBMSBattVersion() {
        return BMSBattVersion;
    }

    private final short BMSBattStrings;

    public final short getBMSBattStrings() {
        return BMSBattStrings;
    }

    private final Volt BMSBattChargeVMax;

    public final Volt getBMSBattChargeVMax() {
        return BMSBattChargeVMax;
    }

    private final Ampere BMSBattChargeIMax;

    public final Ampere getBMSBattChargeIMax() {
        return BMSBattChargeIMax;
    }

    private final Volt BMSBattDischargeVMin;

    public final Volt getBMSBattDischargeVMin() {
        return BMSBattDischargeVMin;
    }

    private final Ampere BMSBattDischargeIMax;

    public final Ampere getBMSBattDischargeIMax() {
        return BMSBattDischargeIMax;
    }

    private final Volt BMSBattVoltage;

    public final Volt getBMSBattVoltage() {
        return BMSBattVoltage;
    }

    private final Ampere BMSBattCurrent;

    public final Ampere getBMSBattCurrent() {
        return BMSBattCurrent;
    }

    private final BigDecimal BMSBattSoC_0to1;

    public final BigDecimal getBMSBattSoC_0to1() {
        return BMSBattSoC_0to1;
    }

    private final BigDecimal BMSBattSoH_0to1;

    public final BigDecimal getBMSBattSoH_0to1() {
        return BMSBattSoH_0to1;
    }

    private final Celsius BMSBattTemperature;

    public final Celsius getBMSBattTemperature() {
        return BMSBattTemperature;
    }

    private final BatteryErrorMasks BMSBattWarningCode;

    public final BatteryErrorMasks getBMSBattWarningCode() {
        return BMSBattWarningCode;
    }

    private final BatteryErrorMasks BMSBattAlarmCode;

    public final BatteryErrorMasks getBMSBattAlarmCode() {
        return BMSBattAlarmCode;
    }

    private final BMSStatusMasks BMSBattStatus;

    public final BMSStatusMasks getBMSBattStatus() {
        return BMSBattStatus;
    }
}
