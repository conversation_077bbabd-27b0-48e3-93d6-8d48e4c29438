package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class SiteLimits implements Cloneable {

    private Integer SoftExportLimit;
    private Integer HardExportLimit;
    private Integer SoftGenerationLimitVA;
    private Integer HardGenerationLimitVA;
    private Integer MaxBatteryChargeW;
    private Integer MaxBatteryDischargeW;

    public SiteLimits() {
    }

    public SiteLimits(Integer SoftExportLimit, Integer HardExportLimit,
                      Integer SoftGenerationLimitVA, Integer HardGenerationLimitVA,
                      Integer MaxBatteryChargeW, Integer MaxBatteryDischargeW) {
        this.SoftExportLimit = SoftExportLimit;
        this.HardExportLimit = HardExportLimit;
        this.SoftGenerationLimitVA = SoftGenerationLimitVA;
        this.HardGenerationLimitVA = HardGenerationLimitVA;
        this.MaxBatteryChargeW = MaxBatteryChargeW;
        this.MaxBatteryDischargeW = MaxBatteryDischargeW;
    }

    public Integer getSoftExportLimit() {
        return SoftExportLimit;
    }

    public void setSoftExportLimit(Integer SoftExportLimit) {
        this.SoftExportLimit = SoftExportLimit;
    }

    public Integer getHardExportLimit() {
        return HardExportLimit;
    }

    public void setHardExportLimit(Integer HardExportLimit) {
        this.HardExportLimit = HardExportLimit;
    }

    public Integer getSoftGenerationLimitVA() {
        return SoftGenerationLimitVA;
    }

    public void setSoftGenerationLimitVA(Integer SoftGenerationLimitVA) {
        this.SoftGenerationLimitVA = SoftGenerationLimitVA;
    }

    public Integer getHardGenerationLimitVA() {
        return HardGenerationLimitVA;
    }

    public void setHardGenerationLimitVA(Integer HardGenerationLimitVA) {
        this.HardGenerationLimitVA = HardGenerationLimitVA;
    }

    public Integer getMaxBatteryChargeW() {
        return MaxBatteryChargeW;
    }

    public void setMaxBatteryChargeW(Integer MaxBatteryChargeW) {
        this.MaxBatteryChargeW = MaxBatteryChargeW;
    }

    public Integer getMaxBatteryDischargeW() {
        return MaxBatteryDischargeW;
    }

    public void setMaxBatteryDischargeW(Integer MaxBatteryDischargeW) {
        this.MaxBatteryDischargeW = MaxBatteryDischargeW;
    }

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }

    public Integer getFunctionalSiteExportLimitW() {
        if (HardExportLimit != null || SoftExportLimit != null) {
            if (HardExportLimit != null && SoftExportLimit != null) {
                return Math.min(SoftExportLimit, HardExportLimit);
            } else if (HardExportLimit != null) {
                return HardExportLimit;
            } else {
                return SoftExportLimit;
            }
        }
        return null;
    }

    public Integer getFunctionalGenerationLimitVA() {
        if (HardGenerationLimitVA != null || SoftGenerationLimitVA != null) {
            if (HardGenerationLimitVA != null && SoftGenerationLimitVA != null) {
                return Math.min(SoftGenerationLimitVA, HardGenerationLimitVA);
            } else if (HardGenerationLimitVA != null) {
                return HardGenerationLimitVA;
            } else {
                return SoftGenerationLimitVA;
            }
        }
        return null;
    }
}