package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.SystemStatusError;
import com.ebon.energy.fms.common.enums.TelemetryErrorCodeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.ZonedDateTime;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class Error {
    public Error() { }

    public Error(
            SystemStatusError type,
            String description,
            Integer errorCode,
            ZonedDateTime firstUtc,
            ZonedDateTime latestUtc,
            Long count) {
        this.Type = type;
        this.Description = description;
        this.ErrorCode = errorCode;
        this.FirstUtc = firstUtc;
        this.LatestUtc = latestUtc;
        this.Count = count;
    }

    public Error(
            SystemStatusError type,
            String description,
            Integer errorCode) {
        this.Type = type;
        this.Description = description;
        this.ErrorCode = errorCode;
    }

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "Type")
    private SystemStatusError Type;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "Description")
    private String Description;

    private String ErrorDescription;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "Code")
    private Integer ErrorCode = null;

    /**
     * Parsed error code
     * Please be aware that this may incorrectly parse Ross1 errors
     */
    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "Code (Parsed)")
    @JsonProperty("ErrorCodeParsed")
    public String getErrorCodeParsed() {
        return ErrorCode != null ? TelemetryErrorCodeEnum.getErrorCodeNameOrNull(ErrorCode) : null;
    }

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "FirstUtc")
    // @JsonConverter(UtcTimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime FirstUtc;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "LatestUtc")
    // @JsonConverter(UtcTimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime LatestUtc;

    /**
     * Gets the number of time an error happened since FirstUtc
     * 1. long will be ok for over 500 billion years if the error is reported 1/second.
     *    (Long.MAX_VALUE / 60 / 60 / 24 / 365)
     */
    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "Count")
    private Long Count;

    // Getters and Setters
    public SystemStatusError getType() {
        return Type;
    }

    public void setType(SystemStatusError type) {
        Type = type;
    }

    public String getDescription() {
        return Description;
    }

    public void setDescription(String description) {
        Description = description;
    }

    public Integer getErrorCode() {
        return ErrorCode;
    }

    public void setErrorCode(Integer errorCode) {
        ErrorCode = errorCode;
    }

    public ZonedDateTime getFirstUtc() {
        return FirstUtc;
    }

    public void setFirstUtc(ZonedDateTime firstUtc) {
        FirstUtc = firstUtc;
    }

    public ZonedDateTime getLatestUtc() {
        return LatestUtc;
    }

    public void setLatestUtc(ZonedDateTime latestUtc) {
        LatestUtc = latestUtc;
    }

    public Long getCount() {
        return Count;
    }

    public void setCount(Long count) {
        Count = count;
    }

    public String getErrorDescription() {
        return this.ErrorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.ErrorDescription = errorDescription;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Error error = (Error) o;
        return Type == error.Type &&
                Objects.equals(Description, error.Description) &&
                Objects.equals(ErrorCode, error.ErrorCode) &&
                Objects.equals(FirstUtc, error.FirstUtc) &&
                Objects.equals(LatestUtc, error.LatestUtc) &&
                Objects.equals(Count, error.Count);
    }

    @Override
    public int hashCode() {
        // Using same algorithm as original C# implementation
        int hash = 17;
        hash = hash * 23 + (Type != null ? Type.hashCode() : 0);
        hash = hash * 23 + (Description != null ? Description.hashCode() : 0);
        hash = hash * 23 + (ErrorCode != null ? ErrorCode.hashCode() : 0);
        hash = hash * 23 + (FirstUtc != null ? FirstUtc.hashCode() : 0);
        hash = hash * 23 + (LatestUtc != null ? LatestUtc.hashCode() : 0);
        hash = hash * 23 + (Count != null ? Count.hashCode() : 0);
        return hash;
    }

    @Override
    public String toString() {
        return "Error{" +
                "Type=" + Type +
                ", Description='" + Description + '\'' +
                ", ErrorCode=" + ErrorCode +
                ", ErrorCodeParsed='" + getErrorCodeParsed() + '\'' +
                ", FirstUtc=" + FirstUtc +
                ", LatestUtc=" + LatestUtc +
                ", Count=" + Count +
                '}';
    }
}