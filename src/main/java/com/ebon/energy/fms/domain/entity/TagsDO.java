package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 标签实体类
 */
@Data
@Accessors(chain = true)
@TableName("Tags")
public class TagsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID (主键，自增长)
     */
    @TableId(value = "TagId", type = IdType.AUTO)
    private Integer tagId;

    /**
     * 标签值
     */
    @TableField(value = "TagValue")
    private String tagValue;

}