package com.ebon.energy.fms.domain.entity;

import com.ebon.energy.fms.domain.vo.InverterOperationVO;
import com.ebon.energy.fms.domain.vo.InverterScheduleVO;
import com.ebon.energy.fms.domain.vo.product.control.DispatchableLoad;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Device control entity
 */
@Data
public class DeviceControl {
    public static final String LOCAL_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    @JsonProperty(index = 1)
    private int deviceId;
    
    @JsonProperty(index = 2)
    private String deviceSerialNumber;
    
    @JsonProperty(index = 4)
    private String deviceModel;
    
    @JsonProperty(index = 5)
    private String dateTimeUpdated;
    
    @JsonProperty(index = 6)
    private InverterOperationVO inverterOperation;
    
    @JsonProperty(index = 7)
    private List<DispatchableLoad> dispatchableLoads;
    
    @JsonProperty(index = 10)
    private boolean onDRED;
    
    @JsonProperty(index = 11)
    private Integer groupEventId;
    
    @JsonIgnore
    private Boolean isRoss2OrAbove;
    
    public DeviceControl() {
        dateTimeUpdated = "";
        deviceId = 0;
        deviceSerialNumber = "";
        deviceModel = "";
        inverterOperation = new InverterOperationVO();
        dispatchableLoads = new ArrayList<>();
        onDRED = false;
    }
    
    /**
     * Updates the inverter operation to the new inverter operation
     * 
     * @param newInverterOperation New inverter operation
     */
    public void updateInverterOperationTo(InverterOperationVO newInverterOperation) {
        this.inverterOperation = newInverterOperation;
    }
    
    /**
     * Get the date time updated as a Date object
     * 
     * @return Date time updated or null if invalid
     */
    public Date getDateTimeUpdated() {
        if (dateTimeUpdated == null) {
            return null;
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(LOCAL_DATE_TIME_FORMAT);
            return sdf.parse(dateTimeUpdated);
        } catch (ParseException e) {
            return null;
        }
    }
    
    /**
     * Validate the object and reports the 1st problem found
     * 
     * @return Null if no validation issues, otherwise a human readable error message
     */
    public String validate() {
        if (getDateTimeUpdated() == null) {
            return String.format("Error parsing field 'DateTimeUpdated'. Incorrect value: '%s'. Example value: '2017-12-07 14:35:53'.", dateTimeUpdated);
        }
        
        List<InverterScheduleVO> schedules = inverterOperation != null ? inverterOperation.getSchedules() : null;
        if (schedules != null) {
            for (InverterScheduleVO schedule : schedules) {
                String error = schedule.validate();
                if (error != null && !error.isEmpty()) {
                    return error;
                }
            }
        }
        
        return null;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        
        if (!(obj instanceof DeviceControl)) {
            return false;
        }
        
        DeviceControl other = (DeviceControl) obj;
        
        if (deviceId != other.deviceId) {
            return false;
        }
        
        if (!(Objects.equals(deviceSerialNumber, other.deviceSerialNumber) || 
                (isEmpty(deviceSerialNumber) && isEmpty(other.deviceSerialNumber)))) {
            return false;
        }
        
        if (!(Objects.equals(deviceModel, other.deviceModel) || 
                (isEmpty(deviceModel) && isEmpty(other.deviceModel)))) {
            return false;
        }
        
        if (!(Objects.equals(dateTimeUpdated, other.dateTimeUpdated) || 
                (isEmpty(dateTimeUpdated) && isEmpty(other.dateTimeUpdated)))) {
            return false;
        }
        
        if (inverterOperation != null && other.inverterOperation != null) {
            if (!(Objects.equals(inverterOperation.getType(), other.inverterOperation.getType()) || 
                    (isEmpty(inverterOperation.getType()) && isEmpty(other.inverterOperation.getType())))) {
                return false;
            }
            
            if (!(Objects.equals(inverterOperation.getMode(), other.inverterOperation.getMode()) || 
                    (isEmpty(inverterOperation.getMode()) && isEmpty(other.inverterOperation.getMode())))) {
                return false;
            }
            
            if (inverterOperation.getPowerInWatts() != other.inverterOperation.getPowerInWatts()) {
                return false;
            }
            
            if (!Objects.equals(inverterOperation.getSchedules(), other.inverterOperation.getSchedules())) {
                return false;
            }
        }
        
        if (!Objects.equals(dispatchableLoads, other.dispatchableLoads)) {
            return false;
        }
        
        if (onDRED != other.onDRED) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(deviceId, deviceSerialNumber, deviceModel, dateTimeUpdated, 
                inverterOperation, dispatchableLoads, onDRED, groupEventId);
    }
    
    private boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
}
