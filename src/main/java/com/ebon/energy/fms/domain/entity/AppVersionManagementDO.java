package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Data
@TableName("AppVersionManagement")
public class AppVersionManagementDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("Product")
    private String product;

    @TableField("OsType")
    private String osType;

    @TableField("Version")
    private String version;

    @TableField("ReleaseNotes")
    private String releaseNotes;

    @TableField("DownloadUrl")
    private String downloadUrl;

    @TableField("UpgradeMethod")
    private String upgradeMethod;

    @TableField("VersionStatus")
    private String status;

    @TableField("CreatedBy")
    private String createdBy;

    @TableField("CreateTime")
    private LocalDateTime createdAt;

    @TableField("UpdatedBy")
    private String updatedBy;

    @TableField("UpdateTime")
    private LocalDateTime updatedAt;
}
