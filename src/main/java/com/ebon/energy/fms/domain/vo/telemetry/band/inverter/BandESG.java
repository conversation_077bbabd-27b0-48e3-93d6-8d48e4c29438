package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.fasterxml.jackson.annotation.JsonFormat;


import java.time.*;


public class BandESG extends DataBackedBand
{
	public BandESG()
	{
		super(BandForge.<BandESG>getMetadataFor(BandESG.class));
	}



	public BandESG(byte[] bytes)
	{
		super(bytes, BandForge.<BandESG>getMetadataFor(BandESG.class));
	}

	public BandESG(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESG>getMetadataFor(BandESG.class));
	}


	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
	public final LocalDateTime getInverterTimeUtc()
	{
		return GetDateTime(0);
	}
}
