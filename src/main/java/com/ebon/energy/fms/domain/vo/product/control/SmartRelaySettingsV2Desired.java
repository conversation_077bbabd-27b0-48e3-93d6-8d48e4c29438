package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.SmartRelayLoadType;
import com.ebon.energy.fms.common.enums.SmartRelayMode;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.time.Duration;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SmartRelaySettingsV2Desired {

    public static final String LOAD_CONTROL_NAME = "loadControlName";
    public static final String LOAD_CONTROL_INSTALLED_NAME = "loadControlInstalled";
    public static final String AUTO_LOAD_CONTROL_TYPE_NAME = "autoLoadControlType";
    public static final String AUTO_LOAD_CONTROL_ENABLED_NAME = "autoLoadControlEnabled";
    public static final String AUTO_LOAD_CONTROL_SOURCE_NAME = "autoLoadControlSource";
    public static final String AUTO_LOAD_CONTROL_ON_TRIGGER_NAME = "autoLoadControlOnTrigger";
    public static final String AUTO_LOAD_CONTROL_ON_DELAY_NAME = "autoLoadControlOnDelay";
    public static final String AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME = "autoLoadControlOffTrigger";
    public static final String AUTO_LOAD_CONTROL_OFF_DELAY_NAME = "autoLoadControlOffDelay";
    public static final String AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME = "autoLoadControlMinRunTime";
    public static final String AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME = "autoLoadControlMinOffTime";
    public static final String AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME = "autoLoadControlDailyRunTimeTarget";
    public static final String AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME = "autoLoadControlDailyRunTimeCompletionTime";
    public static final String AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME = "autoLoadControlDailyRunTimeUseExcessPower";
    public static final String LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME = "loadRelayActiveHigh";
    public static final String LOAD_CONTROL_MODE_NAME = "loadControlMode";
    public static final String LOAD_CONTROL_TIME_ZONE_NAME = "loadControlTimeZone";
    public static final String SMART_LOAD_CONTROL_DEFAULT_NAME = "Relay";

    @JsonProperty(LOAD_CONTROL_NAME)
    private String name;

    @JsonProperty(LOAD_CONTROL_INSTALLED_NAME)
    private Boolean installed;

    @JsonProperty(AUTO_LOAD_CONTROL_TYPE_NAME)
    private SmartRelayLoadType loadType;

    @JsonProperty(AUTO_LOAD_CONTROL_ENABLED_NAME)
    private Boolean enabled;

    @JsonProperty(AUTO_LOAD_CONTROL_SOURCE_NAME)
    private Integer source;

    @JsonProperty(AUTO_LOAD_CONTROL_ON_TRIGGER_NAME)
    private Integer onTrigger;

    @JsonProperty(AUTO_LOAD_CONTROL_ON_DELAY_NAME)
    private String onDelay;

    @JsonProperty(AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME)
    private Integer offTrigger;

    @JsonProperty(AUTO_LOAD_CONTROL_OFF_DELAY_NAME)
    private String offDelay;

    @JsonProperty(AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME)
    private String minRunTime;

    @JsonProperty(AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME)
    private String minOffTime;

    @JsonProperty(AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME)
    private String dailyRunTimeTarget;

    @JsonProperty(AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME)
    private String dailyRunTimeCompletionTime;

    @JsonProperty(AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME)
    private Boolean dailyRunTimeUseExcessPower;

    @JsonProperty(LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME)
    private Boolean relayActiveHigh;

    @JsonProperty(LOAD_CONTROL_MODE_NAME)
    private SmartRelayMode mode;

    @JsonProperty(LOAD_CONTROL_TIME_ZONE_NAME)
    private String timeZoneAlias;

    // 可选：实现 equals 和 hashCode（Lombok 的 @Data 已自动生成）

    // 可选：实现默认实例
    public static SmartRelaySettingsV2Desired defaultInstance() {
        return new SmartRelaySettingsV2Desired();
    }
}
