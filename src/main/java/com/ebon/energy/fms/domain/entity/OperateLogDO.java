package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 操作日志表
 */
@Data
@Accessors
@TableName("OperateLog")
public class OperateLogDO {

    /**
     * 审计ID
     */
    @TableId(value = "LogID", type = IdType.AUTO)
    private Integer logId;

    /**
     * 操作模块
     */
    @TableField(value = "Module")
    private String module;

    /**
     * 用户名
     */
    @TableField(value = "UserName")
    private String userName;

    /**
     * 访问URL
     */
    @TableField(value = "URLAccessed")
    private String urlAccessed;

    /**
     * 访问时间
     */
    @TableField(value = "TimeAccessed")
    private Timestamp timeAccessed;

    /**
     * IP地址
     */
    @TableField(value = "IPAddress")
    private String ipAddress;

    /**
     * 方法
     */
    @TableField(value = "Method")
    private String method;

    /**
     * 内容
     */
    @TableField(value = "Content")
    private String content;

    /**
     * 内容
     */
    @TableField(value = "ResultCode")
    private String resultCode;

    /**
     * 内容
     */
    @TableField(value = "ResultMsg")
    private String resultMsg;
}