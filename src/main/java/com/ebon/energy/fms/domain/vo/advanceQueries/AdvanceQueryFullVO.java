package com.ebon.energy.fms.domain.vo.advanceQueries;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdvanceQueryFullVO {

    /**
     * 查询ID
     */
    private Integer queryId;

    /**
     * 查询名称
     */
    private String queryName;


    /**
     * 查询条件
     */
    private String query;

    /**
     * 创建人
     */
    private String createdBy;


    /**
     * 创建邮箱
     */
    private String createdByEmail;

    /**
     * select-resultColumns
     */
    private List<QueryResultColumnsVO> queryResultColumns;

    /**
     * tags
     */
    private List<TagsVO> tags;

    private Boolean favorite;

    private Boolean dashboard;

}