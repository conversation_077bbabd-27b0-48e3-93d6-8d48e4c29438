package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 


/** 
 This is an automatically generated class that defines BandESEco.
	 Base Register Address: 0700
	 Total Length In Bytes: 2
 
*/


public class BandESEco extends DataBackedBand
{
	public BandESEco()
	{
		super(BandForge.<BandESEco>getMetadataFor(BandESEco.class));
	}



	public BandESEco(byte[] bytes)
	{
		super(bytes, BandForge.<BandESEco>getMetadataFor(BandESEco.class));
	}

	public BandESEco(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESEco>getMetadataFor(BandESEco.class));
	}
}
