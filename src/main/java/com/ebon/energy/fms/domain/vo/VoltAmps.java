package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class VoltAmps implements IUnit {
    public static final VoltAmps Unit = new VoltAmps(new BigDecimal("1.0"));
    public static final VoltAmps Zero = new VoltAmps(new BigDecimal("0.0"));
    public static final VoltAmps Tera = new VoltAmps(new BigDecimal("1000000000000"));
    public static final VoltAmps Giga = new VoltAmps(new BigDecimal("1000000000"));
    public static final VoltAmps Mega = new VoltAmps(new BigDecimal("1000000"));
    public static final VoltAmps Kilo = new VoltAmps(new BigDecimal("1000"));
    public static final VoltAmps Hecto = new VoltAmps(new BigDecimal("100"));
    public static final VoltAmps Deca = new VoltAmps(new BigDecimal("10"));
    public static final VoltAmps Deci = new VoltAmps(new BigDecimal("0.1"));
    public static final VoltAmps Centi = new VoltAmps(new BigDecimal("0.01"));
    public static final VoltAmps Milli = new VoltAmps(new BigDecimal("0.001"));

    public static final String SYMBOL = "VA";

    private BigDecimal value;

    public VoltAmps() {
    }

    public VoltAmps(BigDecimal value) {
        this.value = value;
    }

    public VoltAmps(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public VoltAmps(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public BigDecimal getValue() {
        return value;
    }

    @Override
    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    @Override
    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public BigDecimal asDecimal(VoltAmps unit) {
        return value.divide(unit.value);
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }

    public VoltAmps add(VoltAmps other) {
        return new VoltAmps(value.add(other.value));
    }

    public VoltAmps subtract(VoltAmps other) {
        return new VoltAmps(value.subtract(other.value));
    }

    public VoltAmps multiply(BigDecimal multiplier) {
        return new VoltAmps(value.multiply(multiplier));
    }

    public VoltAmps divide(BigDecimal divisor) {
        return new VoltAmps(value.divide(divisor));
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof VoltAmps)) {
            return false;
        }
        VoltAmps other = (VoltAmps) obj;
        return value.compareTo(other.value) == 0;
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    public int compareTo(VoltAmps other) {
        return value.compareTo(other.value);
    }
}
