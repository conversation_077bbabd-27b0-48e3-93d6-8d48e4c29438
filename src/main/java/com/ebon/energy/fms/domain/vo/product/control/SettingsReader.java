package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.common.utils.HardwareModelHelpers;
import com.ebon.energy.fms.common.utils.ModelInfo;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

@Data
@RequiredArgsConstructor
public abstract class SettingsReader implements ICommonSettingsReader {

    protected final DeviceInfoAndSettings deviceSettings;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public HardwareModelEnum getHardwareModel() {
        String modelName = null;
        if (deviceSettings != null) {
            if (deviceSettings.getDesired() != null && deviceSettings.getDesired().getInverter() != null &&
                !isNullOrBlank(deviceSettings.getDesired().getInverter().getModelName())) {
                modelName = deviceSettings.getDesired().getInverter().getModelName();
            } else if (deviceSettings.getReported() != null && deviceSettings.getReported().getInverterSettings() != null &&
                       !isNullOrBlank(deviceSettings.getReported().getInverterSettings().getModelName())) {
                modelName = deviceSettings.getReported().getInverterSettings().getModelName();
            } else if (deviceSettings.getIdentityCard() != null) {
                modelName = deviceSettings.getIdentityCard().getModelName();
            }
        }

        HardwareModelEnum hardwareModel = HardwareModelHelpers.parseModelName(modelName);
        if (hardwareModel == HardwareModelEnum.Unknown) {
            hardwareModel = HardwareModelHelpers.determineFromSerialNumber(getSerialNumber());
        }
        return hardwareModel;
    }

    public  HardwareFirmwareSpecification getProductModelDefaults()
    {
            var modelInfo = new ModelInfo(getHardwareModel(),getDeviceSettings().getIdentityCard().getFirmwareVersion());
            var hardAndFirm = SpecificationFactory.get(modelInfo);
            return hardAndFirm;
    }


    private static boolean isNullOrBlank(String s) {
        return s == null || s.isBlank(); // Java 11+
        // For older Java: return s == null || s.trim().isEmpty();
    }

    public static Object readObject(DeviceInfoAndSettings deviceSettings, boolean isDesired, V2Section section, String key) {
        Map<String, Object> sectionSettings = null;

        if (isDesired) {
            if (deviceSettings != null && deviceSettings.getDesired() != null && deviceSettings.getDesired().getSettingsV2() != null) {
                SettingsV2Desired desiredV2 = deviceSettings.getDesired().getSettingsV2();
                switch (section) {
                    case Inverter: sectionSettings = desiredV2.getInverterSettings(); break;
                    case Meter: sectionSettings = desiredV2.getMeterSettings(); break;
                    case BatteryManager: sectionSettings = desiredV2.getBatteryManagerSettings(); break;
                    case BatteryStack: sectionSettings = Collections.emptyMap(); break; // No desired for battery stack
                    case InverterControl: sectionSettings = desiredV2.getInverterControlSettings(); break;
                    case Site: sectionSettings = desiredV2.getSiteSettings(); break;
                    case Constraints: sectionSettings = desiredV2.getConstraints(); break;
                }
            }
        } else {
            if (deviceSettings != null && deviceSettings.getReported() != null && deviceSettings.getReported().getSettingsV2() != null) {
                SettingsV2Reported reportedV2 = deviceSettings.getReported().getSettingsV2();
                 switch (section) {
                    case Inverter: sectionSettings = reportedV2.getInverterSettings(); break;
                    case Meter: sectionSettings = reportedV2.getMeterSettings(); break;
                    case BatteryManager: sectionSettings = reportedV2.getBatteryManager(); break; // Note: BatteryManager in C#
                    case BatteryStack: sectionSettings = reportedV2.getBatteryStack(); break;
                    case InverterControl: sectionSettings = Collections.emptyMap(); break; // Not read as of 2.20
                    case Site: sectionSettings = reportedV2.getSiteSettings(); break;
                    case Constraints: sectionSettings = reportedV2.getConstraints(); break;
                }
            }
        }

        if (sectionSettings == null) {
            return null;
        }

        // In C#, TryGetValue returns false if key not found.
        // Java's get returns null if key not found. If null is a valid stored value, check containsKey.
        // The C# logic implies if TryGetValue is false (key not found), return null.
        if (!sectionSettings.containsKey(key)) {
            return null;
        }
        return sectionSettings.get(key);
    }

    public static <T> T readObjectByPath(DeviceInfoAndSettings deviceSettings, boolean isDesired, String path, Class<T> clazz) {
        if (isNullOrBlank(path)) {
            return null;
        }

        String processedPath = path.replace("/", ".").replace("settings.", "");

        Object sourceRootObject = null;
        if (isDesired) {
            if (deviceSettings != null) sourceRootObject = deviceSettings.getDesired();
        } else {
            if (deviceSettings != null) sourceRootObject = deviceSettings.getReported();
        }

        if (sourceRootObject == null) {
            return null;
        }

        JsonNode rootNode = objectMapper.valueToTree(sourceRootObject);
        if (rootNode == null || rootNode.isMissingNode()) {
            return null;
        }

        // Convert dot notation path to JSON Pointer path (e.g., "v2.meters.grid.MeterSerialNumber" -> "/v2/meters/grid/MeterSerialNumber")
        // The C# SelectToken with "$.path" implies root. Jackson's at() with "/path" also starts from root of the node.
        String jsonPointerPath = "/" + processedPath.replace(".", "/");
        JsonNode tokenNode = rootNode.at(jsonPointerPath);

        if (tokenNode == null || tokenNode.isMissingNode() || tokenNode.isNull()) {
            return null; // Mimics C# default(T) for null/missing token
        }

        try {
            return objectMapper.treeToValue(tokenNode, clazz);
        } catch (JsonProcessingException e) {
            // Consider logging the exception
            // e.printStackTrace();
            return null; // Mimics C# default(T) if ToObject<T> fails or returns default
        }
    }

    public static String readStringByPath(DeviceInfoAndSettings deviceSettings, boolean isDesired, String path) {
        return readObjectByPath(deviceSettings, isDesired, path, String.class);
    }

    public static Integer readIntByPath(DeviceInfoAndSettings deviceSettings, boolean isDesired, String path) {
        return readObjectByPath(deviceSettings, isDesired, path, Integer.class);
    }

    public static Boolean readBoolByPath(DeviceInfoAndSettings deviceSettings, boolean isDesired, String path) {
        return readObjectByPath(deviceSettings, isDesired, path, Boolean.class);
    }


    public static Integer readInt(DeviceInfoAndSettings deviceSettings, boolean isDesired, V2Setting setting) {
        return readInt(deviceSettings, isDesired, setting.getSection(), setting.getKey());
    }

    public static Integer readInt(DeviceInfoAndSettings deviceSettings, boolean isDesired, V2Section section, String key) {
        Object value = readObject(deviceSettings, isDesired, section, key);
        return getInt(value, key);
    }

    public static Integer readInt(Map<String, Object> source, String key) {
        if (source == null) {
            return null;
        }
        if (!source.containsKey(key)) {
            return null;
        }
        Object value = source.get(key);
        return getInt(value, key);
    }

    public static Integer getInt(Object o, String key) {
        if (o == null) return null;
        if (o instanceof Boolean) return ((Boolean) o) ? 1 : 0;
        if (o instanceof Double) return ((Double) o).intValue();
        if (o instanceof BigDecimal) return ((BigDecimal) o).intValue();
        if (o instanceof Long) return ((Long) o).intValue(); // Potential data loss if long > Integer.MAX_VALUE
        if (o instanceof Integer) return (Integer) o;
        if (o instanceof String) {
            try {
                return Integer.parseInt((String) o);
            } catch (NumberFormatException e) {
                // Fall through to throw exception, as C# 'when' clause wouldn't match
            }
        }
        throw new RuntimeException(String.format("Unable to read setting '%s', expected number but is '%s'(%s)", key, o, o.getClass().getName()));
    }

    public static Boolean readBool(Map<String, Object> source, String key) {
        if (source == null) {
            return null;
        }
        if (!source.containsKey(key)) {
            return null;
        }
        Object value = source.get(key);
        return getBool(value, key);
    }
    
    public static Boolean getBool(Object o, String key) {
        if (o == null) return null;
        if (o instanceof Boolean) return (Boolean) o;
        if (o instanceof Integer) {
            int i = (Integer) o;
            if (i == 1) return true;
            if (i == 0) return false;
            return null; // C# logic: int not 0 or 1 for bool is null
        }
        if (o instanceof String) {
            // C# bool.TryParse is more lenient than Java's Boolean.parseBoolean for "true"/"false"
            // Java Boolean.parseBoolean("True") is true, others false.
            // C# bool.TryParse("True", out res) is true, res = true. bool.TryParse("1", out res) is false.
            // The original C# is: string str when bool.TryParse(str, out bool parsedStr) => parsedStr
            // This means it only converts if the string is literally "true" or "false" (case-insensitive).
            String s = (String) o;
            if ("true".equalsIgnoreCase(s)) return true;
            if ("false".equalsIgnoreCase(s)) return false;
            // Fall through if not "true" or "false" to the exception
        }
        throw new RuntimeException(String.format("Unable to read setting '%s', expected boolean but is '%s'(%s)", key, o, o.getClass().getName()));
    }

    public static BigDecimal readDecimal(DeviceInfoAndSettings deviceSettings, boolean isDesired, V2Section section, String key) {
        Object value = readObject(deviceSettings, isDesired, section, key);
        if (value == null) return null;
        if (value instanceof Boolean) return ((Boolean) value) ? BigDecimal.ONE : BigDecimal.ZERO;
        if (value instanceof Double) return BigDecimal.valueOf((Double) value);
        if (value instanceof BigDecimal) return (BigDecimal) value;
        if (value instanceof Long) return BigDecimal.valueOf((Long) value);
        if (value instanceof Integer) return BigDecimal.valueOf((Integer) value);
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                // Fall through
            }
        }
        throw new RuntimeException(String.format("Unable to read setting '%s', expected decimal but is '%s'(%s)", key, value, value.getClass().getName()));
    }

    public static Boolean readBool(DeviceInfoAndSettings deviceSettings, boolean isDesired, V2Section section, String key) {
        Object value = readObject(deviceSettings, isDesired, section, key);
        // This C# switch is a bit different from the GetBool specific method
        // bool boolean => (bool)value, -> This is redundant, already handled by GetBool
        // double doublePrecision => doublePrecision > 0,
        // decimal m => m > 0,
        // long doubleword => doubleword > 0,
        // int integer => integer == 1,
        // string str when bool.TryParse(str, out bool parsedStr) => parsedStr,
        // Let's use the more specific GetBool logic and adapt if needed
        // The C# switch here for ReadBool has its own logic:
        if (value == null) return null;
        if (value instanceof Boolean) return (Boolean) value;
        if (value instanceof Double) return ((Double) value) > 0;
        if (value instanceof BigDecimal) return ((BigDecimal) value).compareTo(BigDecimal.ZERO) > 0;
        if (value instanceof Long) return ((Long) value) > 0;
        if (value instanceof Integer) return ((Integer) value) == 1; // Note: C# GetBool has 0 as false, others null. Here only 1 is true.
        if (value instanceof String) {
            String s = (String) value;
            if ("true".equalsIgnoreCase(s)) return true;
            if ("false".equalsIgnoreCase(s)) return false;
            // Fall through if not "true" or "false"
        }
        throw new RuntimeException(String.format("Unable to read setting '%s', expected bool but is '%s'(%s)", key, value, value.getClass().getName()));
    }


    // Instance methods
    public Integer readInt(boolean isDesired, V2Section section, String key) {
        return SettingsReader.readInt(this.deviceSettings, isDesired, section, key);
    }

    public Integer readInt(boolean isDesired, V2Setting setting) {
        return SettingsReader.readInt(this.deviceSettings, isDesired, setting.getSection(), setting.getKey());
    }

    public Integer readIntByPath(boolean isDesired, String path) {
        return SettingsReader.readIntByPath(this.deviceSettings, isDesired, path);
    }

    public BigDecimal readDecimal(boolean isDesired, V2Section section, String key) {
        return SettingsReader.readDecimal(this.deviceSettings, isDesired, section, key);
    }

    public BigDecimal readDecimal(boolean isDesired, V2Setting setting) {
        return SettingsReader.readDecimal(this.deviceSettings, isDesired, setting.getSection(), setting.getKey());
    }

    public Boolean readBool(boolean isDesired, V2Section section, String key) {
        return SettingsReader.readBool(this.deviceSettings, isDesired, section, key);
    }

    public Boolean readBool(boolean isDesired, V2Setting setting) {
        return SettingsReader.readBool(this.deviceSettings, isDesired, setting.getSection(), setting.getKey());
    }

    public Boolean readBoolByPath(boolean isDesired, String path) {
        return SettingsReader.readBoolByPath(this.deviceSettings, isDesired, path);
    }

    public String readString(boolean isDesired, V2Setting setting) {
        Object obj = readObject(this.deviceSettings, isDesired, setting.getSection(), setting.getKey());
        return obj != null ? obj.toString() : null;
    }

    protected String getSerialNumber() {
        return Optional.ofNullable(deviceSettings)
                       .map(x -> x.getIdentityCard())
                       .map(InverterIdentityCard::getSerialNumber)
                       .orElse(null);
    }


   public abstract ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source);


}