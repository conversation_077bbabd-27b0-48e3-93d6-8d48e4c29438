package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.PylonHVFirmwareUpdateState;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandHVFWState extends DataBackedBand
{
	public BandHVFWState()
	{
		super(BandForge.<BandHVFWState>getMetadataFor(BandHVFWState.class));
	}



	public BandHVFWState(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVFWState>getMetadataFor(BandHVFWState.class));
	}

	public BandHVFWState(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVFWState>getMetadataFor(BandHVFWState.class));
	}


	
	public final Object getFWUpdateState()
	{
		return PylonHVFirmwareUpdateState.parse(GetU16(0));
	}
}
