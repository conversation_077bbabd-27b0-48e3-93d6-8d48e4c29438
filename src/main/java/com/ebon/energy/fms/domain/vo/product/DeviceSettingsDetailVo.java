package com.ebon.energy.fms.domain.vo.product;

import lombok.Data;

import java.sql.Timestamp;
import java.time.ZonedDateTime;

/**
 * Device settings detail value object
 */
@Data
public class DeviceSettingsDetailVo {
    
    private Timestamp modifiedDateUtc;
    
    private String reportedDeviceSettings;
    
    private String desiredDeviceSettings;
    
    private String deviceSettingsIntent;
    
    private Timestamp reportedLastUpdated;
    
    private String desiredDeviceSettingsPatch;
    
    private String desiredVersion;
    
    private String latestSystemStatus;
    
    private String hardwareConfig;
    
    private String redbackProductSn;
    
    private Integer minOnGridSoC0to100;
    
    private Integer maxOnGridSoC0to100;
    
    private Integer minOffGridSoC0to100;
    
    private Integer maxOffGridSoC0to100;
}
