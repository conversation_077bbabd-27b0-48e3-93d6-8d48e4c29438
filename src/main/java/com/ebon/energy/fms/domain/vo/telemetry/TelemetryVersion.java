package com.ebon.energy.fms.domain.vo.telemetry;

public class TelemetryVersion {
    private int major;
    private int minor;
    private int patch; // 可选

    public TelemetryVersion(int major, int minor) {
        this(major, minor, 0);
    }

    public TelemetryVersion(int major, int minor, int patch) {
        this.major = major;
        this.minor = minor;
        this.patch = patch;
    }

    public static TelemetryVersion parse(String version) {
        String[] parts = version.split("\\.");
        if (parts.length == 0) {
            throw new IllegalArgumentException("Invalid version format");
        }

        int major = Integer.parseInt(parts[0]);
        int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
        int patch = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;

        return new TelemetryVersion(major, minor, patch);
    }

    // Getters
    public int getMajor() { return major; }
    public int getMinor() { return minor; }
    public int getPatch() { return patch; }

    @Override
    public String toString() {
        return patch == 0 ? major + "." + minor : major + "." + minor + "." + patch;
    }
}