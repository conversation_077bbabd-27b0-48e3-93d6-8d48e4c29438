package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandH2InverterRunningData3 extends DataBackedBand
{
	public BandH2InverterRunningData3()
	{
		super(BandForge.<BandH2InverterRunningData3>getMetadataFor(BandH2InverterRunningData3.class));
	}



	public BandH2InverterRunningData3(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterRunningData3>getMetadataFor(BandH2InverterRunningData3.class));
	}

	public BandH2InverterRunningData3(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterRunningData3>getMetadataFor(BandH2InverterRunningData3.class));
	}



	public final Volt getBackupVoltageL1()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Ampere getBackupCurrentL1()
	{
		return GetU16(2, Ampere.Centi);
	}


	public final Frequency getBackupFrequencyL1()
	{
		return GetU16(4, Frequency.Centi);
	}


	public final Volt getBackupVoltageDCComponentL1()
	{
		return GetS16(6, Volt.Milli);
	}


	public final Watt getBackupActivePowerL1()
	{
		return GetU16(8, Watt.Unit);
	}


	public final VoltAmps getBackupApparentPowerL1()
	{
		return GetU16(10, VoltAmps.Unit);
	}


	public final Volt getBackupVoltageL2()
	{
		return GetU16(12, Volt.Deci);
	}


	public final Ampere getBackupCurrentL2()
	{
		return GetU16(14, Ampere.Centi);
	}


	public final Frequency getBackupFrequencyL2()
	{
		return GetU16(16, Frequency.Centi);
	}


	public final Volt getBackupVoltageDCComponentL2()
	{
		return GetS16(18, Volt.Milli);
	}


	public final Watt getBackupActivePowerL2()
	{
		return GetU16(20, Watt.Unit);
	}


	public final VoltAmps getBackupApparentPowerL2()
	{
		return GetU16(22, VoltAmps.Unit);
	}


	public final Volt getBackupVoltageL3()
	{
		return GetU16(24, Volt.Deci);
	}


	public final Ampere getBackupCurrentL3()
	{
		return GetU16(26, Ampere.Centi);
	}


	public final Frequency getBackupFrequencyL3()
	{
		return GetU16(28, Frequency.Centi);
	}


	public final Volt getBackupVoltageDCComponentL3()
	{
		return GetS16(30, Volt.Milli);
	}


	public final Watt getBackupActivePowerL3()
	{
		return GetU16(32, Watt.Unit);
	}


	public final VoltAmps getBackupApparentPowerL3()
	{
		return GetU16(34, VoltAmps.Unit);
	}


	public final Volt getBusVoltageMaster()
	{
		return GetU16(36, Volt.Deci);
	}


	public final Volt getBusVoltageSlave()
	{
		return GetU16(38, Volt.Deci);
	}


	public final Volt getBatVoltage()
	{
		return GetU16(40, Volt.Deci);
	}


	public final Ampere getBatCurrent()
	{
		return GetS16(42, Ampere.Centi);
	}


	public final Watt getBatPower()
	{
		return GetS16(48, Watt.Unit);
	}


	public final Celsius getBatTemperature()
	{
		return GetS16(50, Celsius.Deci);
	}


	public final BigDecimal getBatSOC()
	{
		return new BigDecimal(GetU16(52)).multiply(Percentage._001);
	}
}
