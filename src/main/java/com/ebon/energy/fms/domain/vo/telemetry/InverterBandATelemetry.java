package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.UpdateResult;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandATelemetry implements ITelemetryBand
{
    public InverterBandATelemetry(String serialNumber, String nominalVpv, String firmwareVersionString, String modelName, String dSPFirmwareVersion, String aRMFirmwareVersion, String manufacturerInfo, short firmwareVersion, UpdateResult armUpdateResult, UpdateResult dspUpdateResult)
    {
        SerialNumber = serialNumber;
        NominalVpv = nominalVpv;
        FirmwareVersionString = firmwareVersionString;
        ModelName = modelName;
        DSPFirmwareVersion = dSPFirmwareVersion;
        ARMFirmwareVersion = aRMFirmwareVersion;
        ManufacturerInfo = manufacturerInfo;
        FirmwareVersion = firmwareVersion;
        ArmUpdateResult = armUpdateResult;
        DspUpdateResult = dspUpdateResult;
    }

    private String SerialNumber;
    public final String getSerialNumber()
    {
        return SerialNumber;
    }
    private void setSerialNumber(String value)
    {
        SerialNumber = value;
    }

    private final String NominalVpv;
    public final String getNominalVpv()
    {
        return NominalVpv;
    }

    private final String FirmwareVersionString;
    public final String getFirmwareVersionString()
    {
        return FirmwareVersionString;
    }

    private final String ModelName;
    public final String getModelName()
    {
        return ModelName;
    }

    private final String DSPFirmwareVersion;
    public final String getDSPFirmwareVersion()
    {
        return DSPFirmwareVersion;
    }

    private final String ARMFirmwareVersion;
    public final String getARMFirmwareVersion()
    {
        return ARMFirmwareVersion;
    }

    private final String ManufacturerInfo;
    public final String getManufacturerInfo()
    {
        return ManufacturerInfo;
    }

    private final short FirmwareVersion;
    public final short getFirmwareVersion()
    {
        return FirmwareVersion;
    }

    private final UpdateResult ArmUpdateResult;
    public final UpdateResult getArmUpdateResult()
    {
        return ArmUpdateResult;
    }

    private final UpdateResult DspUpdateResult;
    public final UpdateResult getDspUpdateResult()
    {
        return DspUpdateResult;
    }
}


