package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.ArrayList;
import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BackupLoadStatus implements Cloneable {
    private Double V;
    private Double I;
    private Double P;
    private Double F;
    private Status Status;
    private Double DayTotalE;
    private Double DayMaxP;
    private Long DayMaxPEpoch;
    private Boolean IsMeterInstalled;
    private List<BackupLoadPhase> Phases;

    public BackupLoadStatus() {
    }

    public BackupLoadStatus(Double v, Double i, Double p, Double f, Status status,
                            Double dayTotalE, Double dayMaxP, Long dayMaxPEpoch,
                            Boolean isMeterInstalled, List<BackupLoadPhase> phases) {
        this.V = v;
        this.I = i;
        this.P = p;
        this.F = f;
        this.Status = status;
        this.DayTotalE = dayTotalE;
        this.DayMaxP = dayMaxP;
        this.DayMaxPEpoch = dayMaxPEpoch;
        this.IsMeterInstalled = isMeterInstalled;
        this.Phases = phases;
    }

    public Double getV() {
        return V;
    }

    public void setV(Double v) {
        V = v;
    }

    public Double getI() {
        return I;
    }

    public void setI(Double i) {
        I = i;
    }

    public Double getP() {
        return P;
    }

    public void setP(Double p) {
        P = p;
    }

    public Double getF() {
        return F;
    }

    public void setF(Double f) {
        F = f;
    }

    public Status getStatus() {
        return Status;
    }

    public void setStatus(Status status) {
        Status = status;
    }

    public Double getDayTotalE() {
        return DayTotalE;
    }

    public void setDayTotalE(Double dayTotalE) {
        DayTotalE = dayTotalE;
    }

    public Double getDayMaxP() {
        return DayMaxP;
    }

    public void setDayMaxP(Double dayMaxP) {
        DayMaxP = dayMaxP;
    }

    public Long getDayMaxPEpoch() {
        return DayMaxPEpoch;
    }

    public void setDayMaxPEpoch(Long dayMaxPEpoch) {
        DayMaxPEpoch = dayMaxPEpoch;
    }

    public Boolean getIsMeterInstalled() {
        return IsMeterInstalled;
    }

    public void setIsMeterInstalled(Boolean isMeterInstalled) {
        IsMeterInstalled = isMeterInstalled;
    }

    public List<BackupLoadPhase> getPhases() {
        return Phases;
    }

    public void setPhases(List<BackupLoadPhase> phases) {
        Phases = phases;
    }

    @Override
    public BackupLoadStatus clone() {
        try {
            BackupLoadStatus clone = (BackupLoadStatus) super.clone();
            if (this.Phases != null) {
                clone.Phases = new ArrayList<>(this.Phases);
            }
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}