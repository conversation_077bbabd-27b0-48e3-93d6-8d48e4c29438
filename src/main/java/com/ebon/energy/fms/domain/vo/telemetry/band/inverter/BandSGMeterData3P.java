package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;




 





public class BandSGMeterData3P extends DataBackedBand
{
	public BandSGMeterData3P()
	{
		super(BandForge.<BandSGMeterData3P>getMetadataFor(BandSGMeterData3P.class));
	}



	public BandSGMeterData3P(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterData3P>getMetadataFor(BandSGMeterData3P.class));
	}

	public BandSGMeterData3P(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterData3P>getMetadataFor(BandSGMeterData3P.class));
	}



	public final Volt getVoltageL1()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Ampere getCurrentL1()
	{
		return GetS32(2, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL1()
	{
		return new BigDecimal(GetS16(6)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL1()
	{
		return GetS32(8, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL1()
	{
		return GetU32(12, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL1()
	{
		return GetS32(16, VoltAmpsReactive.Deci);
	}


	public final Frequency getFrequencyL1()
	{
		return GetU16(20, Frequency.Centi);
	}


	public final Volt getVoltageL2()
	{
		return GetU16(22, Volt.Deci);
	}


	public final Ampere getCurrentL2()
	{
		return GetS32(24, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL2()
	{
		return new BigDecimal(GetS16(28)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL2()
	{
		return GetS32(30, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL2()
	{
		return GetU32(34, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL2()
	{
		return GetS32(38, VoltAmpsReactive.Deci);
	}


	public final Volt getVoltageL3()
	{
		return GetU16(42, Volt.Deci);
	}


	public final Ampere getCurrentL3()
	{
		return GetS32(44, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL3()
	{
		return new BigDecimal(GetS16(48)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL3()
	{
		return GetS32(50, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL3()
	{
		return GetU32(54, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getReactivePowerL3()
	{
		return GetS32(58, VoltAmpsReactive.Deci);
	}


	public final Watt getActivePowerTotal()
	{
		return GetS32(62, Watt.Deci);
	}


	public final VoltAmps getReactivePowerTotal()
	{
		return GetS32(66, VoltAmps.Deci);
	}


	public final VoltAmpsReactive getApparentPowerTotal()
	{
		return GetU32(70, VoltAmpsReactive.Deci);
	}


	public final BigDecimal getPowerFactorTotal()
	{
		return new BigDecimal(GetS16(74)).multiply(Percentage.Tenth);
	}
}
