package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.domain.vo.telemetry.band.IPrimaryBand;
import com.ebon.energy.fms.util.StringProcessors;


public class BandESFallback extends DataBackedBand implements IPrimaryBand
{
	public BandESFallback()
	{
		super(BandForge.<BandESFallback>getMetadataFor(BandESFallback.class));
	}


	public BandESFallback(byte[] bytes)
	{
		super(bytes, BandForge.<BandESFallback>getMetadataFor(BandESFallback.class));
	}

	public BandESFallback(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESFallback>getMetadataFor(BandESFallback.class));
	}


	
	public final String getFirmwareVersionString()
	{
		return GetBufS(0, 5, StringProcessors.GoodweDecode);
	}

	
	public final String getModelName()
	{
		return GetBufS(5, 10, StringProcessors.GoodweDecode);
	}

	
	public final String getManufacturerInfo()
	{
		return GetBufS(15, 16, StringProcessors.GoodweDecode);
	}

	
	public final String getSerialNumber()
	{
		return GetBufS(31, 16, StringProcessors.GoodweDecode);
	}

	
	public final String getNominalVpv()
	{
		return GetBufS(47, 4, StringProcessors.GoodweDecode);
	}

	
	public final String getInternalVersionDSP()
	{
		return GetBufS(51, 12, StringProcessors.GoodweDecode);
	}

	


	public final byte getSafteyCountry()
	{
		return GetU8(63);
	}

	
	public final String getInternalVersionARM()
	{
		return GetBufS(64, 13, StringProcessors.GoodweDecode);
	}


	/** 
	 Gets the FirmwareVersionString register
	 
	 @return 
	 FirmwareVersionString which may be different from ABandES's GetFirmwareVersionString()
	 
	*/
	public final String GetFirmwareVersionString()
	{
		return getFirmwareVersionString();
	}
}
