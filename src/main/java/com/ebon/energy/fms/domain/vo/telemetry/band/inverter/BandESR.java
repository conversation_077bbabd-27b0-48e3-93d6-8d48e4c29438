package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESR extends DataBackedBand
{
	public BandESR()
	{
		super(BandForge.<BandESR>getMetadataFor(BandESR.class));
	}



	public BandESR(byte[] bytes)
	{
		super(bytes, BandForge.<BandESR>getMetadataFor(BandESR.class));
	}

	public BandESR(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESR>getMetadataFor(BandESR.class));
	}


	
	public final boolean getHardwareFeedPowerDisable()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}
}
