package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonDto {
    private String RawMessage;

    public PylonDto(String rawMessage) {
        this.RawMessage = rawMessage;
    }

    public String getRawMessage() {
        return RawMessage;
    }

    public void setRawMessage(String rawMessage) {
        this.RawMessage = rawMessage;
    }
}
