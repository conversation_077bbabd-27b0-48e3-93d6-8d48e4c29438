package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class EnergyFlowInputVO {
    private Watt ACLoadW;
    private Watt BackupLoadW;
    private Boolean SupportsConnectedPV;
    private Watt PVW;
    private Watt ThirdPartyW;
    private GridStatusValue GridStatus;
    private Watt GridNegativeIsImportW;
    private Boolean ConfiguredWithBatteries;
    private Watt BatteryNegativeIsChargingW;
    private BatteryStatusValue BatteryStatus;
    private Double BatterySoC0to100;
    private Boolean CtComms;

    public EnergyFlowInputVO(Watt ACLoadW, Watt BackupLoadW, boolean SupportsConnectedPV, Watt PVW, Watt ThirdPartyW,
                             GridStatusValue GridStatus, Watt GridNegativeIsImportW, Boolean ConfiguredWithBatteries,
                             Watt BatteryNegativeIsChargingW, BatteryStatusValue BatteryStatus, Double BatterySoC0to100,
                             Boolean CtComms) {
        this.ACLoadW = ACLoadW;
        this.BackupLoadW = BackupLoadW;
        this.SupportsConnectedPV = SupportsConnectedPV;
        this.PVW = PVW;
        this.ThirdPartyW = ThirdPartyW;
        this.GridStatus = GridStatus;
        this.GridNegativeIsImportW = GridNegativeIsImportW;
        this.ConfiguredWithBatteries = ConfiguredWithBatteries;
        this.BatteryNegativeIsChargingW = BatteryNegativeIsChargingW;
        this.BatteryStatus = BatteryStatus;
        this.BatterySoC0to100 = BatterySoC0to100;
        this.CtComms = CtComms;
    }

    public EnergyFlowInputVO() {
    }

    public Watt getACLoadW() {
        return ACLoadW;
    }

    public Watt getBackupLoadW() {
        return BackupLoadW;
    }

    public Boolean getSupportsConnectedPV() {
        return SupportsConnectedPV;
    }

    public Watt getPVW() {
        return PVW;
    }

    public Watt getThirdPartyW() {
        return ThirdPartyW;
    }

    public GridStatusValue getGridStatus() {
        return GridStatus;
    }

    public Watt getGridNegativeIsImportW() {
        return GridNegativeIsImportW;
    }

    public Boolean getConfiguredWithBatteries() {
        return ConfiguredWithBatteries;
    }

    public Watt getBatteryNegativeIsChargingW() {
        return BatteryNegativeIsChargingW;
    }

    public BatteryStatusValue getBatteryStatus() {
        return BatteryStatus;
    }

    public Double getBatterySoC0to100() {
        return BatterySoC0to100;
    }

    public Boolean getCtComms() {
        return CtComms;
    }
}