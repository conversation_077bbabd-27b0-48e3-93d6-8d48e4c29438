package com.ebon.energy.fms.domain.vo.telemetry.band;

import com.ebon.energy.fms.domain.vo.IUnit;
import com.ebon.energy.fms.domain.vo.TimeSpan;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;
import com.ebon.energy.fms.util.BytesUtils;
import com.ebon.energy.fms.util.StringHelper;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.function.TriFunction;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Base64;

public abstract class DataBackedBand implements IBand {
    public DataBackedBand(byte[] bytes, BandMetadata recipe) {
        this.BandRecipe = recipe;
        this.setBytes(bytes);

        if (bytes.length != recipe.getLengthInBytes()) {
            throw new IllegalArgumentException(String.format("Unexpected length of data backing band, got %1$s expected %2$s", bytes.length, recipe.getLengthInBytes()));
        }
    }

    public DataBackedBand(String encodedBytes, BandMetadata recipe) {
        if (StringHelper.isNullOrEmpty(encodedBytes)) {
            throw new IllegalArgumentException(String.format("Not a valid encoded bytes string, must be a base64 encoded set of data for this band type."));
        }

        this.BandRecipe = recipe;
        this.setBytes(Base64.getDecoder().decode(encodedBytes));

        if (this.getBytes().length != recipe.getLengthInBytes()) {
            throw new IllegalArgumentException(String.format("Unexpected length of data backing band, got %1$s expected %2$s", this.getBytes().length, recipe.getLengthInBytes()));
        }
    }

    protected DataBackedBand(BandMetadata recipe) {
        this(recipe, false);
    }

    protected DataBackedBand(BandMetadata recipe, boolean createBackingArray) {
        this.BandRecipe = recipe;

        this.setBytes(createBackingArray ? new byte[recipe.getLengthInBytes()] : null);
    }

    @JsonIgnore
    public Duration getTimeToLive() {
        return getBandRecipe().getTimeToLive();
    }

    @JsonIgnore
    public Duration getFreshDuration() {
        return getBandRecipe().getFreshDuration();
    }

    @JsonIgnore
    public Duration getReadPeriod() {
        return getBandRecipe().getReadPeriod();
    }

    @JsonIgnore
    private final BandMetadata BandRecipe;

    @JsonIgnore
    public final BandMetadata getBandRecipe() {
        return BandRecipe;
    }

    private byte[] Bytes;

    protected byte[] getBytes() {
        return Bytes;
    }

    public void setBytes(byte[] value) {
        Bytes = value;
    }

    public final byte[] GetRawData() {

        return (byte[]) getBytes().clone();
    }

    public final String ToBase64String() {
        return Base64.getEncoder().encodeToString(getBytes());
    }


    public final boolean equals(DataBackedBand other) {

        return other != null && Arrays.equals(getBytes(), other.getBytes());
    }

    @Override
    public int hashCode() {
        return Base64.getEncoder().encodeToString(getBytes()).hashCode();
    }

    protected final String GetBandName() {
        return this.getClass().getSimpleName();
    }

    protected final byte[] GetRaw(int start, int length) {
        return  ArrayUtils.subarray(getBytes(),start,start+length);
    }

    protected final String GetBufS(int start, int length, TriFunction<byte[], Integer, Integer, String> decoder) {
        return decoder.apply(getBytes(), start, length);
    }

    protected final byte GetU8(int pos) {
        byte[] bytes = getBytes();
        if (bytes == null) {
            throw new NullPointerException("字节数组未初始化");
        }
        if (pos < 0 || pos >= bytes.length) {
            throw new IndexOutOfBoundsException("位置 " + pos + " 超出数组范围");
        }
        return bytes[pos];
    }

    protected final short GetS16(int start) {
        return BytesUtils.getInt16(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final int GetU16(int start) {
        return BytesUtils.getUInt16(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final int GetU24(int start) {
        return BytesUtils.getUInt24(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final int GetS32(int start) {
        return BytesUtils.getInt32(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final int GetU32(int start) {
        return (int) BytesUtils.getUInt32(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final long GetU48(int start) {
        return BytesUtils.getUInt48(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final long GetU64(int start) {
        return BytesUtils.getUInt64(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final short GetMaskedU16(int start, short mask, short shiftRight) {

        return (short) ((BytesUtils.getUInt16(getBytes(), start, getBandRecipe().getMsgEndianness()) & mask) >> shiftRight);
    }

    protected final int GetMaskedU32(int start, int mask, short shiftRight) {

        return (int) ((BytesUtils.getUInt32(getBytes(), start, getBandRecipe().getMsgEndianness()) & mask) >> shiftRight);
    }

    protected final float GetFloat(int start) {
        return BytesUtils.getSingle(getBytes(), start, getBandRecipe().getMsgEndianness());
    }

    protected final <T extends IUnit> T GetFloat(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetFloat(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetU16(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetU16(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetU24(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetU24(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetU32(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetU32(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetU48(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetU48(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetU64(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetU64(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetS16(int start, T unit)
    {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetS16(start))));
        return tempVar;
    }

    protected final TimeSpan GetU16(int start, TimeSpan unit) {
        return TimeSpan.fromTicks(GetU16(start) * unit.getTicks());
    }

    protected final TimeSpan GetS16(int start, TimeSpan unit) {
        return TimeSpan.fromTicks(GetS16(start) * unit.getTicks());
    }

    protected final TimeSpan GetU32(int start, TimeSpan unit) {
        return TimeSpan.fromTicks(GetU32(start) * unit.getTicks());
    }

    protected final <T extends IUnit> T GetS16(int start, T unit, Class<T> clazz) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetS16(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetS32(int start, T unit) {
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(GetS32(start))));
        return tempVar;
    }

    protected final <T extends IUnit> T GetU16Nullable(int start, T unit, short nullIs) {
        var raw = GetU16(start);
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(raw)));
        return raw == nullIs ? null : tempVar;
    }

    protected final <T extends IUnit> T GetS16Nullable(int start, T unit, short nullIs) {
        var raw = GetS16(start);
        T tempVar = null;
        try {
            tempVar = (T) unit.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tempVar.setValue(unit.getValue().multiply(new BigDecimal(raw)));
        return raw == nullIs ? null : tempVar;
    }

    protected <T extends Comparable<T>> boolean GetBool(T current, T valIfTrue, T valIfFalse, boolean resultIfUnknown) {
        // 如果默认返回 true，检查是否不等于 false 值；否则检查是否等于 true 值
        return resultIfUnknown ? !current.equals(valIfFalse) : current.equals(valIfTrue);
    }

    /**
     * Get datetime for GoodWe inverters
     */
    protected final LocalDateTime GetDateTime(int start) {
        var yearMonth = GetU16(start + 0);
        var dayHour = GetU16(start + 2);
        var minuteSecond = GetU16(start + 4);

        if (yearMonth == 0) {
            return LocalDateTime.MIN;
        }

        var dt = LocalDateTime.of(2000 + GetUpperByte(yearMonth), GetLowerByte(yearMonth),
        GetUpperByte(dayHour), GetLowerByte(dayHour), GetUpperByte(minuteSecond),
        GetLowerByte(minuteSecond));
        return dt;
    }


    /**
     * Get datetime for Solis inverters
     */
    protected final LocalDateTime GetDateTime6BytesSolis(int start) {
        var year = GetU16(start + 0);
        var month = GetU16(start + 2);
        var day = GetU16(start + 4);
        var hour = GetU16(start + 6);
        var min = GetU16(start + 8);
        var sec = GetU16(start + 10);

        if (year == 0) {
            return LocalDateTime.MIN;
        }

        var dt = LocalDateTime.of(2000 + year, month, day, hour, min, sec);
        return dt;
    }

    private byte GetLowerByte(int combined) {
        return (byte) ((combined & 0x00FF) >> 0);
    }

    private byte GetUpperByte(int combined) {
        return (byte) ((combined & 0xFF00) >> 8);
    }
}