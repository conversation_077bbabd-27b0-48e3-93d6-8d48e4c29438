package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class WattHour implements IUnit {

    public static final String SYMBOL = "Wh";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final WattHour Unit = new WattHour(new BigDecimal("1.0"));
    public static final WattHour Zero = new WattHour(new BigDecimal("0.0"));
    public static final WattHour Tera = new WattHour(new BigDecimal("1000000000000"));
    public static final WattHour Giga = new WattHour(new BigDecimal("1000000000"));
    public static final WattHour Mega = new WattHour(new BigDecimal("1000000"));
    public static final WattHour Kilo = new WattHour(new BigDecimal("1000"));
    public static final WattHour Hecto = new WattHour(new BigDecimal("100"));
    public static final WattHour Deca = new WattHour(new BigDecimal("10"));
    public static final WattHour Deci = new WattHour(new BigDecimal("0.1"));
    public static final WattHour Centi = new WattHour(new BigDecimal("0.01"));
    public static final WattHour Milli = new WattHour(new BigDecimal("0.001"));

    public WattHour() {
    }

    public WattHour(BigDecimal value) {
        this.value = value;
    }

    public WattHour(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public WattHour(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public WattHour add(WattHour other) {
        return new WattHour(this.value.add(other.value));
    }

    public WattHour subtract(WattHour other) {
        return new WattHour(value.subtract(other.value));
    }
    
    public WattHour multiply(WattHour other) {
        return new WattHour(this.value.multiply(other.value));
    }

    public static WattHour operatorPlus(WattHour a) {
        return a;
    }

    public static WattHour operatorMinus(WattHour a) {
        return new WattHour(a.value.negate());
    }

    public static WattHour operatorAdd(WattHour a, WattHour b) {
        return new WattHour(a.value.add(b.value));
    }

    public static WattHour operatorSubtract(WattHour a, WattHour b) {
        return new WattHour(a.value.subtract(b.value));
    }

    public static BigDecimal operatorDivide(WattHour a, WattHour b) {
        return a.value.divide(b.value);
    }

    public static WattHour operatorDivide(WattHour a, BigDecimal b) {
        return new WattHour(a.value.divide(b));
    }

    public static WattHour operatorMultiply(WattHour a, BigDecimal value) {
        return new WattHour(a.value.multiply(value));
    }

    public static WattHour operatorMultiply(BigDecimal value, WattHour a) {
        return new WattHour(value.multiply(a.value));
    }

    public static boolean operatorEqual(WattHour a, WattHour b) {
        return a.value.equals(b.value);
    }

    public static boolean operatorNotEqual(WattHour a, WattHour b) {
        return !a.value.equals(b.value);
    }

    public static boolean operatorLessThan(WattHour a, WattHour b) {
        return a.value.compareTo(b.value) < 0;
    }

    public static boolean operatorLessThanOrEqual(WattHour a, WattHour b) {
        return a.value.compareTo(b.value) <= 0;
    }

    public static boolean operatorGreaterThan(WattHour a, WattHour b) {
        return a.value.compareTo(b.value) > 0;
    }

    public static boolean operatorGreaterThanOrEqual(WattHour a, WattHour b) {
        return a.value.compareTo(b.value) >= 0;
    }

    public static WattHour fromBigDecimal(BigDecimal d) {
        return new WattHour(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public int asInt() {
        return value.intValue();
    }

    public BigDecimal asDecimal(WattHour unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(WattHour unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(WattHour unit) {
        return value.divide(unit.value);
    }

    public double asDouble(WattHour unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(WattHour unit) {
        return value.divide(unit.value).longValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        WattHour other = (WattHour) obj;
        return value.equals(other.value);
    }

    public int compareTo(WattHour other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}