package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonStatusDto {
    private long BaudRate;
    private byte CommVersion;
    private PylonManufacturerDto Manufacturer;
    private PylonSystemParameterDto SystemParameter;
    private List<PylonSerialNumberDto> SerialNumbers;
    private List<PylonAlarmsDto> Alarms;
    private List<PylonDataDto> Data;
    private List<PylonManagementInfoDto> MgtInfo;

    public PylonStatusDto(
            long baudRate,
            byte commVersion,
            PylonManufacturerDto manufacturer,
            PylonSystemParameterDto systemParameter,
            List<PylonSerialNumberDto> serialNumbers,
            List<PylonAlarmsDto> alarms,
            List<PylonDataDto> data,
            List<PylonManagementInfoDto> mgtInfo) {

        this.BaudRate = baudRate;
        this.CommVersion = commVersion;
        this.Manufacturer = manufacturer;
        this.SystemParameter = systemParameter;
        this.SerialNumbers = serialNumbers;
        this.Alarms = alarms;
        this.Data = data;
        this.MgtInfo = mgtInfo;
    }

    public long getBaudRate() {
        return BaudRate;
    }

    public byte getCommVersion() {
        return CommVersion;
    }

    public PylonManufacturerDto getManufacturer() {
        return Manufacturer;
    }

    public PylonSystemParameterDto getSystemParameter() {
        return SystemParameter;
    }

    public List<PylonSerialNumberDto> getSerialNumbers() {
        return SerialNumbers;
    }

    public List<PylonAlarmsDto> getAlarms() {
        return Alarms;
    }

    public List<PylonDataDto> getData() {
        return Data;
    }

    public List<PylonManagementInfoDto> getMgtInfo() {
        return MgtInfo;
    }
}