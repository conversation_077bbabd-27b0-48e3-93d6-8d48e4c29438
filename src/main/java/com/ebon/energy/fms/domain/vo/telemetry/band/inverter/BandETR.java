package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandETR extends DataBackedBand
{
	public BandETR()
	{
		super(BandForge.<BandETR>getMetadataFor(BandETR.class));
	}



	public BandETR(byte[] bytes)
	{
		super(bytes, BandForge.<BandETR>getMetadataFor(BandETR.class));
	}

	public BandETR(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETR>getMetadataFor(BandETR.class));
	}


	
	public final boolean getHardwareFeedPowerDisable()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}
}
