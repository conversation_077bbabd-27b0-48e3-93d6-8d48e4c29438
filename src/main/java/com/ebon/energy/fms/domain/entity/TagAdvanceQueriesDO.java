package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 标签与高级查询关联表实体
 */
@Data
@Accessors(chain = true)
@TableName("TagAdvanceQueries")
public class TagAdvanceQueriesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签ID (联合主键部分)
     */
    @TableField("Tag_TagId")
    private Integer tagId;

    /**
     * 高级查询ID (联合主键部分)
     */
    @TableField("AdvanceQuery_QueryId")
    private Integer queryId;

}