package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.LEDState;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandESGPeripherals extends DataBackedBand
{
	public BandESGPeripherals()
	{
		super(BandForge.<BandESGPeripherals>getMetadataFor(BandESGPeripherals.class));
	}



	public BandESGPeripherals(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGPeripherals>getMetadataFor(BandESGPeripherals.class));
	}

	public BandESGPeripherals(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGPeripherals>getMetadataFor(BandESGPeripherals.class));
	}


	
	public final Object getWiFiLEDState()
	{
		return LEDState.parse(GetU16(0));
	}

	
	public final Object getComLEDState()
	{
		return LEDState.parse(GetU16(2));
	}

	
	public final boolean getMeterCT1ReverseEnable()
	{
		return GetBool((int) GetU16(4), 1, 0, true);
	}

	


	public final int getErrorLogReadPage() { return GetU16(6); }

	


	public final int getModbusTCPWithoutInternet() { return GetU16(8); }

	
	public final Object getBackupLED()
	{
		return LEDState.parse(GetU16(10));
	}

	
	public final Object getGridLED()
	{
		return LEDState.parse(GetU16(12));
	}

	
	public final Object getSOCLED1()
	{
		return LEDState.parse(GetU16(14));
	}

	
	public final Object getSOCLED2()
	{
		return LEDState.parse(GetU16(16));
	}

	
	public final Object getSOCLED3()
	{
		return LEDState.parse(GetU16(18));
	}

	
	public final Object getSOCLED4()
	{
		return LEDState.parse(GetU16(20));
	}

	
	public final Object getBatteryLED()
	{
		return LEDState.parse(GetU16(22));
	}

	
	public final Object getSystemLED()
	{
		return LEDState.parse(GetU16(24));
	}

	
	public final Object getFaultLED()
	{
		return LEDState.parse(GetU16(26));
	}

	
	public final Object getEnergyLED()
	{
		return LEDState.parse(GetU16(28));
	}

	
	public final Object getLEDExternalControl()
	{
		return LEDState.parse(GetU16(30));
	}



	public final byte[] getReserved0xB7B5()
	{
		return GetRaw(32, 8);
	}

	
	public final boolean getReserved0xB7B9()
	{
		return GetBool((int) GetU16(40), 1, 0, true);
	}

	
	public final Watt getReserved0xB7BA()
	{
		return GetS16(42, Watt.Unit);
	}

	
	public final VoltAmpsReactive getReserved0xB7BB()
	{
		return GetS16(44, VoltAmpsReactive.Unit);
	}

	
	public final boolean getReserved0xB7BC()
	{
		return GetBool((int) GetU16(46), 1, 0, true);
	}

	
	public final boolean getReserved0xB7BD()
	{
		return GetBool((int) GetU16(48), 1, 0, true);
	}

	
	public final boolean getStopModeSaveEnable()
	{
		return GetBool((int) GetU16(50), 1, 0, true);
	}

	
	public final boolean getReserved0xB7BF()
	{
		return GetBool((int) GetU16(52), 1, 0, true);
	}
}
