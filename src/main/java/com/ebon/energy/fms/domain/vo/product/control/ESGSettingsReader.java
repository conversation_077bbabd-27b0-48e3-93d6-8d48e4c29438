package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.InverterModeValue;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.common.utils.path.EH1PSettingPaths;
import com.ebon.energy.fms.common.utils.path.ESGSettingPaths;
import com.ebon.energy.fms.domain.vo.BatterySettingsDto;
import com.ebon.energy.fms.domain.vo.GetInverterModeSettingsDto;
import com.ebon.energy.fms.domain.vo.InverterModePair;
import com.ebon.energy.fms.domain.vo.MinMaxValueDto;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


public class ESGSettingsReader extends SettingsReader {


    public ESGSettingsReader(DeviceInfoAndSettings deviceSettings) {
        super(deviceSettings);
    }


    public static ESGSettingPaths.BatteryConfig fromManufacturer(ManufacturerEnum manufacturer) {
        if (manufacturer == null) {
            return new ESGSettingPaths.BatteryConfig("None", null);
        }

        // Comment from the spec as of 2.20:
        // For HV Batteries this will need to be updated.
        if (manufacturer == ManufacturerEnum.None) {
            return new ESGSettingPaths.BatteryConfig("None", "Parallel");  // Even for "None" we want to return a valid architecture; User Story 84542: GEN3 Battery Default Values
        }

        // Temporarily disable, wait for device support of 2025.03.11
        // if (manufacturer == ManufacturerEnum.Redback) {
        //     return new BatteryConfig("RED-R1-5000LV", "Redback");
        // }

        return new ESGSettingPaths.BatteryConfig("PylonUS", "Parallel");
    }


    @Override
    public ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source) {
        if (source == null) {
            source = UniversalSettingSource.DESIRED;
        }
        switch (source) {
            case INTENT:
                // Assuming DeviceSettings and Intent may be null, handle safely
                Boolean enable = null;
                if (deviceSettings != null && deviceSettings.getIntent() != null) {
                    enable = deviceSettings.getIntent().getEnableACCoupledMode();
                }
                return new ACCoupledSettingsDto(enable);
            case REPORTED:
                return new ACCoupledSettingsDto(readBool(false, ESGSettingPaths.ACCoupledOnOff));
            case DESIRED:
            default:
                return new ACCoupledSettingsDto(readBool(true, ESGSettingPaths.ACCoupledOnOff));
        }
    }

    @Override
    public List<UniversalSettingId> getSupportedUniveralSettings(Integer version) {
        List<UniversalSettingId> supportedUniversalSettings = new ArrayList<>();

        supportedUniversalSettings.add(UniversalSettingId.BATTERY_SETTINGS);
        supportedUniversalSettings.add(UniversalSettingId.CT_FLIP);

        return supportedUniversalSettings;
    }

    @Override
    public BatterySettingsDto getBatterySettings(UniversalSettingSource source) {
        var minOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOnGridSoCLimit()).orElse(null));
        var maxOnGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOnGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOnGridSoCLimit()).orElse(null));
        var minOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMinOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMinOffGridSoCLimit()).orElse(null));
        var maxOffGridSoC0to100 = Optional.ofNullable(getDeviceSettings()).map(x -> x.getExplicitSettings())
                .map(x -> x.getMaxOffGridSoC0to100()).orElse(Optional.ofNullable(getProductModelDefaults()).map(x -> x.getMaxOffGridSoCLimit()).orElse(null));

        return getBatterySettings(source == UniversalSettingSource.DESIRED, minOnGridSoC0to100, maxOnGridSoC0to100, minOffGridSoC0to100, maxOffGridSoC0to100);
    }


    @Override
    public GetInverterModeSettingsDto getDesiredPowerModeSchedulesForPortal() {
        Integer inverterModeNumber = readInt(deviceSettings, true, new V2Setting(V2Section.InverterControl, ESGSettingPaths.InverterModeSettingName));
        Integer inverterModePowerW = Objects.requireNonNullElse(
                readInt(deviceSettings, true, new V2Setting(V2Section.InverterControl, ESGSettingPaths.InverterModePowerSettingName)),
                0);

        Map<String, ScheduleInfoDto> currentInverterModeSchedules = null;
        if (deviceSettings != null &&
                deviceSettings.getDesired() != null &&
                deviceSettings.getDesired().getSettingsV2() != null &&
                deviceSettings.getDesired().getSettingsV2().getScheduleSettings() != null &&
                deviceSettings.getDesired().getSettingsV2().getScheduleSettings().getSchedules() != null) {

            currentInverterModeSchedules = deviceSettings.getDesired().getSettingsV2().getScheduleSettings().getSchedules()
                    .entrySet().stream()
                    .filter(x -> x.getValue() != null &&
                            x.getValue().getDesiredScheduledSettings() != null &&
                            x.getValue().getDesiredScheduledSettings().getInverterControlSettings() != null &&
                            !x.getValue().getDesiredScheduledSettings().getInverterControlSettings().isEmpty())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> mapScheduleToDto(entry.getValue())
                    ));
        }

        return new GetInverterModeSettingsDto(
                new InverterModePair(
                        inverterModeNumber != null
                                ? FromTwinWorkMode(inverterModeNumber)
                                : InverterModeValue.Auto,
                        inverterModePowerW),
                currentInverterModeSchedules == null ? new HashMap<>() : currentInverterModeSchedules);
    }

    private static com.ebon.energy.fms.domain.vo.product.control.ScheduleInfoDto mapScheduleToDto(ScheduleV2Dto scheduleV2) {
        try {
            Map<String, Object> settings = scheduleV2.getDesiredScheduledSettings().getInverterControlSettings();

            Integer inverterModeNumber = readInt(settings, ESGSettingPaths.InverterModeSettingName);

            Integer inverterModePowerW = Objects.requireNonNullElse(
                    readInt(settings, ESGSettingPaths.InverterModePowerSettingName),
                    0);

            return new ScheduleInfoDto(
                    scheduleV2.getPriority(),
                    scheduleV2.getStartAtUtc(),
                    scheduleV2.getEndAtUtc(),
                    scheduleV2.getStartTimeOfDay(),
                    scheduleV2.getDuration(),
                    ScheduleDays.fromValue(scheduleV2.getDaysOfWeekActive()),
                    EncodeToScheduleAction(
                            inverterModeNumber != null
                                    ? FromTwinWorkMode(inverterModeNumber)
                                    : InverterModeValue.Auto),
                    inverterModePowerW);
        } catch (Exception e) {
            // Failed to translate the schedule,
            // As of 2.20 we ignore bad schedules
            return null;
        }
    }

    private static InverterModeValue FromTwinWorkMode(int workMode) {
        // Convert from twin work mode value to InverterModeValue
        // This is a simplified implementation and may need adjustment
        return InverterModeValue.fromValue(workMode);
    }

    private static String EncodeToScheduleAction(InverterModeValue mode) {
        // Convert InverterModeValue to schedule action string
        // This is a simplified implementation and may need adjustment
        return mode.name();
    }


    private BatterySettingsDto getBatterySettings(boolean isDesired,
                                                  int minOnGridSoC0to100,
                                                  int maxOnGridSoC0to100,
                                                  int minOffGridSoC0to100,
                                                  int maxOffGridSoC0to100
    ) {
        return BatterySettingsDto.builder()
                .manufacturer(Optional.ofNullable(getDeviceSettings()).map(x -> x.getIntent()).map(x -> x.getBatteryManufacturer()).orElse(null))
                .batteryCount(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumBatteryCount())
                        .max(getProductModelDefaults().getMaximumBatteryCount())
                        .value(Optional.ofNullable(getDeviceSettings()).map(x -> x.getIntent()).map(x -> x.getBatteryCount()).orElse(null))
                        .build())
                .maxChargeCurrent(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumMaxChargeCurrentAmpere())
                        .max(getProductModelDefaults().getMaximumMaxChargeCurrentAmpere())
                        .value(Optional.ofNullable(getDeviceSettings()).map(x -> x.getIntent()).map(x -> x.getBatteryMaxChargeCurrent()).orElse(null))
                        .build())
                .maxDischargeCurrent(MinMaxValueDto.builder()
                        .min(getProductModelDefaults().getMinimumMaxDischargeCurrentAmpere())
                        .max(getProductModelDefaults().getMaximumMaxDischargeCurrentAmpere())
                        .value(Optional.ofNullable(getDeviceSettings()).map(x -> x.getIntent()).map(x -> x.getBatteryMaxDischargeCurrent()).orElse(null))
                        .build())
                .minSoc(MinMaxValueDto.builder()
                        .min(minOnGridSoC0to100)
                        .max(maxOnGridSoC0to100)
                        .value(Optional.ofNullable(getDeviceSettings()).map(x -> x.getIntent()).map(x -> x.getBatteryMinSoc()).orElse(null))
                        .build())
                .minOffgridSoc(MinMaxValueDto.builder()
                        .min(minOffGridSoC0to100)
                        .max(maxOffGridSoC0to100)
                        .value(Optional.ofNullable(getDeviceSettings()).map(x -> x.getIntent()).map(x -> x.getBatteryMinOffgridSoc()).orElse(null))
                        .build())
                .totalCapacityAh(null)
                .build();
    }

    private int getBatteryCount(boolean isDesired) {
        if (isDesired) {
            return Optional.ofNullable(readInt(isDesired, ESGSettingPaths.BatteryCount)).orElse(0);
        } else {
            var x = readIntByPath(isDesired, "settings/v2/batteryStack/batteryCount");
            if (x != null) {
                return x;
            } else {
                return Optional.ofNullable(readInt(isDesired, ESGSettingPaths.BatteryCount)).orElse(0);
            }
        }
    }


    public static String toManufacturer(String protocol) {
        if (StringUtils.isEmpty(protocol)) {
            // The default, like for ROSS
            return "None";
        }
        // Comment from the spec as of 2.20:
        // For HV Batteries this will need to be updated.
        if (protocol.toLowerCase().equals("none")) {
            return "None";
        }
        // Temporarily disable, wait for device support of 2025.03.11
        // if (protocol.toLowerCase().equals("red-r1-5000lv")) {
        //     return "Redback";
        // }
        return "Pylon";
    }
}
