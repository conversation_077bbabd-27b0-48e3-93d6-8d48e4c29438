package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor()
@Builder
public class SettingsV2Reported {

    public static final String EMS_SETTINGS_NAME = "ems";
    public static final String INVERTER_SETTINGS_NAME = "inverter";
    public static final String SCHEDULES_SETTINGS_NAME = "schedules";
    public static final String INVERTER_FIRMWARE_SETTINGS_NAME = "inverterFirmware";
    public static final String COMMS_FIRMWARE_SETTINGS_NAME = "commsFirmware";
    public static final String METER_FIRMWARE_SETTINGS_NAME = "meterFirmware";
    public static final String CORRESPONDING_DESIRED_SETTINGS_NAME = "correspondingDesired";
    public static final String SMART_RELAY_SETTINGS_NAME = "relaySettings";
    public static final String TIME_ZONE_ALIAS_SETTINGS_NAME = "TzAlias";
    public static final String BATTERY_MANAGER_SETTINGS_NAME = "batteryManager";
    public static final String BATTERY_STACK_SETTINGS_NAME = "batteryStack";

    @JsonProperty(EMS_SETTINGS_NAME)
    private EmsSettingsV2Reported emsSettings;

    @JsonProperty(INVERTER_SETTINGS_NAME)
    @Builder.Default
    private Map<String, Object> inverterSettings = Collections.emptyMap();

    @JsonProperty(SCHEDULES_SETTINGS_NAME)
    private ScheduleSettingsV2Reported scheduleSettings;

    @JsonProperty(INVERTER_FIRMWARE_SETTINGS_NAME)
    private FirmwareSettingsV2Reported inverterFirmwareSettings;

    @JsonProperty(COMMS_FIRMWARE_SETTINGS_NAME)
    private FirmwareSettingsV2Reported commsFirmwareSettings;

    @JsonProperty(METER_FIRMWARE_SETTINGS_NAME)
    private FirmwareSettingsV2Reported meterFirmwareSettings;

    @JsonProperty(CORRESPONDING_DESIRED_SETTINGS_NAME)
    private Long correspondingDesired;

//    @JsonProperty(SMART_RELAY_SETTINGS_NAME)
//    private SmartRelaySettingsV2Reported smartRelaySettings;

    @JsonProperty(TIME_ZONE_ALIAS_SETTINGS_NAME)
    @Builder.Default
    private Map<String, String> timeZoneAliasSettings = Collections.emptyMap();

    @JsonProperty(BATTERY_MANAGER_SETTINGS_NAME)
    @Builder.Default
    private Map<String, Object> batteryManager = Collections.emptyMap();

    @JsonProperty(BATTERY_STACK_SETTINGS_NAME)
    @Builder.Default
    private Map<String, Object> batteryStack = Collections.emptyMap();

    @JsonProperty(SettingsV2Desired.METER_SETTINGS_NAME)
    @Builder.Default
    private Map<String, Object> meterSettings = Collections.emptyMap();

    @JsonProperty(SettingsV2Desired.METERS_SETTINGS_NAME)
    @Builder.Default
    private Map<String, Object> meters = Collections.emptyMap();

    @JsonProperty(SettingsV2Desired.SITE_SETTINGS_NAME)
    @Builder.Default
    private Map<String, Object> siteSettings = Collections.emptyMap();

    @JsonProperty(SettingsV2Desired.CONSTRAINTS_NAME)
    @Builder.Default
    private Map<String, Object> constraints = Collections.emptyMap();

    // equals 和 hashCode 由 Lombok @Data 自动生成，已包含所有字段的比较

    // 可选：静态方法用于默认实例
    public static SettingsV2Reported defaultInstance() {
        return new SettingsV2Reported();
    }
}
