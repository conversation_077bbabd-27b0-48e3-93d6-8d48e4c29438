package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import java.math.BigDecimal;

public class BandBHL extends DataBackedBand
{
	public BandBHL()
	{
		super(BandForge.<BandBHL>getMetadataFor(BandBHL.class));
	}


	public BandBHL(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHL>getMetadataFor(BandBHL.class));
	}

	public BandBHL(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHL>getMetadataFor(BandBHL.class));
	}

	public final WattHour getActiveETotalSellR()
	{
		return GetU48(0, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellS()
	{
		return GetU48(6, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellT()
	{
		return GetU48(12, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellTotal()
	{
		return GetU48(18, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyR()
	{
		return GetU48(24, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyS()
	{
		return GetU48(30, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyT()
	{
		return GetU48(36, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyTotal()
	{
		return GetU48(42, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
	}
}
