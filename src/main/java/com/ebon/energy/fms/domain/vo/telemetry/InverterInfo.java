package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.TimeSpan;
import com.ebon.energy.fms.util.DateTimeHelper;
import com.ebon.energy.fms.util.TimeUtils;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.TimeUtils.getUtcTimeStr;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterInfo {
    private final String ModelName;
    private final String RossVersion;
    private final String FirmwareVersion;
    private final String HardwareConfig;
    private final List<String> Errors;
    private String RunningSince;
    private String UptimeText;

    public InverterInfo(String modelName, String rossVersion, String firmwareVersion,
                        String hardwareConfig, List<String> errors) {
        this.ModelName = modelName;
        this.RossVersion = rossVersion;
        this.FirmwareVersion = firmwareVersion;
        this.HardwareConfig = hardwareConfig;
        this.Errors = errors;
        this.RunningSince = null;
        this.UptimeText = null;
    }

    public InverterInfo(String modelName, String rossVersion, String firmwareVersion,
                        String hardwareConfig, String runningSince, String uptimeText,
                        List<String> errors) {
        this(modelName, rossVersion, firmwareVersion, hardwareConfig, errors);
        this.RunningSince = runningSince;
        this.UptimeText = uptimeText;
    }

    public String getModelName() {
        return ModelName;
    }

    public String getRossVersion() {
        return RossVersion;
    }

    public String getFirmwareVersion() {
        return FirmwareVersion;
    }

    public String getHardwareConfig() {
        return HardwareConfig;
    }

    public List<String> getErrors() {
        return Errors;
    }

    public String getRunningSince() {
        return RunningSince;
    }

    public String getUptimeText() {
        return UptimeText;
    }

    public static InverterInfo fromRossTelemetry(RossDeviceTelemetry t) {
        Instant telemTime = t != null && t.getTimestampUtc() != null ?
                t.getTimestampUtc() : null;

        TimeSpan uptime = t != null && t.getOuijaBoard() != null &&
                t.getOuijaBoard().getRossUpTimeMilliseconds() != null ?
                TimeSpan.fromMilliseconds(t.getOuijaBoard().getRossUpTimeMilliseconds()) : null;

        String uptimeHuman = uptime != null ?
                TimeUtils.toHumanReadableString(uptime) : null;

        Instant startTime = uptime != null && telemTime != null ?
                Instant.ofEpochMilli(telemTime.toEpochMilli() - (int) uptime.getTotalMilliseconds())
                : null;

        List<String> errors = getErrors(t != null && t.getErrors() != null ?
                t.getErrors().stream()
                        .map(InverterInfo::fromTelemetryError)
                        .collect(Collectors.toList()) : null);

        return new InverterInfo(
                t != null && t.getInverter() != null ? t.getInverter().getModelName() : null,
                t != null && t.getOuijaBoard() != null && t.getOuijaBoard().getRossVersion() != null ?
                        t.getOuijaBoard().getRossVersion().toString() : null,
                t != null && t.getInverter() != null ? t.getInverter().getFirmwareVersion() : null,
                t != null && t.getInverter() != null ? t.getInverter().getHardwareConfig() : null,
                startTime != null ? getUtcTimeStr(startTime.toEpochMilli()) : null,
                uptimeHuman,
                errors
        );
    }

    public static ErrorForGlance fromTelemetryError(ErrorTelemetry e) {
        return new ErrorForGlance(
                e != null ? (e.getErrorCode() != null ? e.getErrorCode().name() : null) : null,
                e != null ? (e.getErrorCode() != null ? e.getErrorCode().getErrorCode() : null) : null,
                e != null ? e.getMessage() : null,
                e != null ? e.getFirstUtc() : null,
                e != null ? e.getLatestUtc() : null,
                e != null ? e.getCount() : null
        );
    }

    public static ErrorForGlance fromSSError(boolean isRoss1, Error e) {
        String errorCode = null;
        Integer errorNumber = 0;
        String message = null;
        ZonedDateTime firstUtc = null;
        ZonedDateTime latestUtc = null;
        Long count = 0L;

        if (e != null) {
            errorCode = isRoss1 ? String.valueOf(e.getErrorCode()) : e.getErrorCodeParsed();
            errorNumber = e.getErrorCode();
            message = e.getDescription();
            firstUtc = e.getFirstUtc();
            latestUtc = e.getLatestUtc();
            count = e.getCount();
        }

        return new ErrorForGlance(errorCode, errorNumber, message, firstUtc, latestUtc, count);
    }

    public static List<String> getErrors(List<ErrorForGlance> errors) {
        try {
            if (errors == null) {
                return null;
            }

            return errors.stream()
                    .sorted(Comparator.comparing(ErrorForGlance::getLatestUtc))
                    .map(e -> {
                        String m = e != null ? e.forGlance() : null;
                        if (m == null || m.isEmpty()) {
                            return "?";
                        }
                        return m.length() > 120 ? m.substring(0, 120) + "..." : m;
                    })
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            List<String> result = new ArrayList<>();
            result.add("Failed to extract errors");
            return result;
        }
    }

    public static class ErrorForGlance {
        private final String errorCode;
        private final Integer errorNumber;
        private final String message;
        private final ZonedDateTime firstUtc;
        private final ZonedDateTime latestUtc;
        private final Long count;

        public ErrorForGlance(String errorCode, Integer errorNumber, String message,
                              ZonedDateTime firstUtc, ZonedDateTime latestUtc, Long count) {
            this.errorCode = errorCode;
            this.errorNumber = errorNumber;
            this.message = message;
            this.firstUtc = firstUtc;
            this.latestUtc = latestUtc;
            this.count = count;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public int getErrorNumber() {
            return errorNumber;
        }

        public String getMessage() {
            return message;
        }

        public ZonedDateTime getFirstUtc() {
            return firstUtc;
        }

        public ZonedDateTime getLatestUtc() {
            return latestUtc;
        }

        public Long getCount() {
            return count;
        }

        public String forGlance() {
            String howLong = (latestUtc != null && firstUtc != null) ?
                    (latestUtc.equals(firstUtc) ? "-" :
                            TimeUtils.toHumanReadableStringShort(TimeSpan.fromMilliseconds(latestUtc.toInstant().toEpochMilli() - firstUtc.toInstant().toEpochMilli()))) :
                    "-";

            String start;
            if (latestUtc != null) {
                if (firstUtc != null) {
                    if (firstUtc.toLocalDate().equals(latestUtc.toLocalDate())) {
                        start = String.format("%s-%s",
                                DateTimeHelper.HUMAN_FRIENDLY_ZULU_TIME_FORMATTER.format(firstUtc),
                                DateTimeHelper.HUMAN_FRIENDLY_ZULU_TIME_FORMATTER.format(latestUtc));
                    } else {
                        start = String.format("%s -> %s",
                                DateTimeHelper.HUMAN_FRIENDLY_FORMATTER.format(firstUtc),
                                DateTimeHelper.HUMAN_FRIENDLY_FORMATTER.format(latestUtc));
                    }
                } else {
                    start = DateTimeHelper.HUMAN_FRIENDLY_ZULU_TIME_FORMATTER.format(latestUtc);
                }
            } else {
                start = "<?,?>";
            }

            List<String> elements = new ArrayList<>();
            elements.add("s:" + start);
            elements.add("n:" + (count != null ? count.toString() : "1"));
            elements.add("d:" + howLong);
            elements.add(errorCode != null ? errorCode : "n/a");
            elements.add(message);

            return elements.stream()
                    .filter(s -> s != null)
                    .collect(Collectors.joining("|"));
        }
    }

}