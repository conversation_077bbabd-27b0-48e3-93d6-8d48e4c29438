package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.ZonedDateTime;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class AtAGlance<T> {
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime AtUtc;

    private String HowOld;

    private T Summary;

    public AtAGlance(ZonedDateTime atUtc, T summary, String howOld) {
        this.AtUtc = atUtc;
        this.Summary = summary;
        this.HowOld = howOld;
    }

    public ZonedDateTime getAtUtc() {
        return AtUtc;
    }

    public T getSummary() {
        return Summary;
    }

    public String getHowOld()
    {
        return HowOld;
    }
}