package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility.ANY;
import static com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility.NONE;

@JsonAutoDetect(
        fieldVisibility = ANY,
        getterVisibility = NONE,
        setterVisibility = NONE
)
public class ScheduleTelemetry {

    public Map<String, String> Active;

    public ScheduleTelemetry() {
        this.Active = new HashMap<>();
    }

    public ScheduleTelemetry(List<String> active) {
        this.Active = new HashMap<>();
        for (String id : active) {
            this.Active.put(id, id);
        }
    }

    public ScheduleTelemetry(Map<String, String> active) {
        this.Active = active;
    }
}