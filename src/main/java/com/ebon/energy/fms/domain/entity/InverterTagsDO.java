package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 逆变器与标签关联表 数据对象
 * (多对多关联表)
 */
@Data
@Accessors(chain = true)
@TableName("InverterTags")
public class InverterTagsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * serialNumber
     */
    @TableField("Inverter_SerialNumber")
    private String serialNumber;

    /**
     * tagId
     */
    @TableField("Tag_TagId")
    private String tagId;

}