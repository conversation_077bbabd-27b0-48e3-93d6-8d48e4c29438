package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.util.*;
import java.math.*;



public class BandESB extends DataBackedBand
{
	public BandESB()
	{
		super(BandForge.<BandESB>getMetadataFor(BandESB.class));
	}



	public BandESB(byte[] bytes)
	{
		super(bytes, BandForge.<BandESB>getMetadataFor(BandESB.class));
	}

	public BandESB(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESB>getMetadataFor(BandESB.class));
	}



	public final Volt getVpv1()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Ampere getIpv1()
	{
		return GetS16(2, Ampere.Deci);
	}


	public final Object getPV1Mode()
	{
		return PvMode.parse((byte) GetU16(4));
	}


	public final Volt getVpv2()
	{
		return GetU16(6, Volt.Deci);
	}


	public final Ampere getIpv2()
	{
		return GetS16(8, Ampere.Deci);
	}


	public final Object getPV2Mode()
	{
		return PvMode.parse((byte) GetU16(10));
	}


	public final Volt getVBattery1()
	{
		return GetU16(12, Volt.Deci);
	}




	public final int getTBDx0507() { return GetU16(14); }


	public final Object getBMSStatus()
	{
		return BMSStatusMasks.parse(GetU16(16));
	}


	public final Celsius getBMSPackTemp()
	{
		return GetS16(18, Celsius.Deci);
	}


	public final Ampere getIBattery1()
	{
		return GetS16(20, Ampere.Deci);
	}


	public final Ampere getBMSChargeIMax()
	{
		return GetS16(22, Ampere.Unit);
	}


	public final Ampere getBMSDischargeIMax()
	{
		return GetS16(24, Ampere.Unit);
	}


	public final Object getBMSErrorCode()
	{
		return BatteryErrorMasks.parse(GetU16(26));
	}


	public final BigDecimal getSOC()
	{
		return new BigDecimal(GetU16(28)).multiply(Percentage._1);
	}


	public final int getInverterWarningCode() { return GetU16(30); }




	public final int getTBDx0510() { return GetU16(32); }


	public final BigDecimal getBMS_SoH()
	{
		return new BigDecimal(GetU16(34)).multiply(Percentage._1);
	}


	public final Object getBatteryWorkMode()
	{
		return BattMode.parse((byte) GetU16(36));
	}


	public final Object getBMSWarningCode()
	{
		return BatteryErrorMasks.parse((short) GetU32(38));
	}


	public final boolean getMeterStatus()
	{
		return GetBool((int) GetU16(42), 1, 0, true);
	}


	public final Volt getVGrid()
	{
		return GetU16(44, Volt.Deci);
	}


	public final Ampere getIGrid()
	{
		return GetS16(46, Ampere.Deci);
	}


	public final Watt getPGrid()
	{
		return GetS16(48, Watt.Unit);
	}


	public final Frequency getFGrid()
	{
		return GetU16(50, Frequency.Centi);
	}


	public final Object getGridMode()
	{
		return GridMode.parse((byte) GetU16(52));
	}


	public final Volt getVLoad()
	{
		return GetU16(54, Volt.Deci);
	}


	public final Ampere getILoad()
	{
		return GetS16(56, Ampere.Deci);
	}


	public final Watt getOnGridLoadPower()
	{
		return GetU16(58, Watt.Unit);
	}


	public final Frequency getFLoad()
	{
		return GetU16(60, Frequency.Centi);
	}


	public final Object getLoadMode()
	{
		return LoadMode.parse(GetU16(62));
	}


	public final Object getWorkMode()
	{
		return InverterWorkMode.parse(GetU16(64));
	}


	public final Celsius getTemperature()
	{
		return GetS16(66, Celsius.Deci);
	}


	public final Object getErrorMessage()
	{
		return InverterErrorMode.parse(GetU32(68));
	}


	public final WattHour getEGridTotal()
	{
		return GetU32(72, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final TimeSpan getHGridTotal()
	{
		return GetU32(76, TimeSpan.fromHours(1));
	}


	public final WattHour getEGridDay()
	{
		return GetU16(80, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final WattHour getELoadDay()
	{
		return GetU16(82, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final WattHour getETotalLoad()
	{
		return GetU32(84, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final Watt getTotalPower()
	{
		return GetS16(88, Watt.Unit);
	}


	public final WattHour getEPvTotal()
	{
		return GetU32(90, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final Object getGridInOutFlag()
	{
		return GridInOutFlag.parse((byte) GetU16(94));
	}


	public final Watt getBackupLoadPower()
	{
		return GetU16(96, Watt.Unit);
	}


	public final short getMeterPowerFactor()
	{
		return GetS16(98);
	}


	public final Object getDiagStatus()
	{
		return DiagStatus.parse(GetU32(100));
	}


	public final Object getDRMStatus()
	{
		return DRMStatus.parse(GetU16(104));
	}


	public final float getETotalSellF()
	{
		return GetFloat(106);
	}


	public final float getETotalBuyF()
	{
		return GetFloat(110);
	}


	public final Volt getVpv3()
	{
		return GetU16(114, Volt.Deci);
	}


	public final Ampere getIpv3()
	{
		return GetU16(116, Ampere.Deci);
	}


	public final Object getPV3Mode()
	{
		return PvMode.parse((byte) GetU16(118));
	}


	public final Volt getVGridUo()
	{
		return GetU16(120, Volt.Deci);
	}


	public final Ampere getIGridUo()
	{
		return GetS16(122, Ampere.Deci);
	}


	public final Volt getVGridWo()
	{
		return GetU16(124, Volt.Deci);
	}


	public final Ampere getIGridWo()
	{
		return GetS16(126, Ampere.Deci);
	}


	public final WattHour getEBatteryCharge()
	{
		return GetU32(128, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final WattHour getEBatteryDischarge()
	{
		return GetU32(132, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final Watt getPpv1()
	{
		return GetU16(136, Watt.Unit);
	}


	public final Watt getPpv2()
	{
		return GetU16(138, Watt.Unit);
	}


	public final Watt getPpv3()
	{
		return GetU16(140, Watt.Unit);
	}


	public final Watt getBatteryPower()
	{
		return GetU16(142, Watt.Unit);
	}


	public final WattHour getETotalSellI()
	{
		return GetU32(144, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final WattHour getETotalBuyI()
	{
		return GetU32(148, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final WattHour getEBatChargeToday()
	{
		return GetU16(152, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public final WattHour getEBatDischargeToday()
	{
		return GetU16(154, WattHour.operatorMultiply(new BigDecimal("0.1"),WattHour.Kilo));
	}


	public List<String> getInverterWarningCodeText()
	{
		return processInverterWarningCode(getInverterWarningCode());
	}


	public static List<String> processInverterWarningCode(int inverterWarningCode) {
		List<String> inverterWarningCodes = new ArrayList<>();

		// Lower enum which can be Flags => multiple results
		if (inverterWarningCode <= 64) {
			for (InverterWarningCodeESLower warning : InverterWarningCodeESLower.values()) {
				if ((inverterWarningCode & warning.getValue()) == warning.getValue()) {
					inverterWarningCodes.add(warning.name());
				}
			}
		}
		// OR Upper enum with only one state/result
		else {
			for (InverterWarningCodeESUpper warning : InverterWarningCodeESUpper.values()) {
				if (InverterWarningCodeESUpper.forValue(inverterWarningCode) == warning) {
					inverterWarningCodes.add(warning.name());
					break;
				}
			}

			// Code didn't match any options in Enum
			if (inverterWarningCodes.isEmpty()) {
				inverterWarningCodes.add(String.valueOf(inverterWarningCode));
			}
		}

		return inverterWarningCodes;
	}
}
