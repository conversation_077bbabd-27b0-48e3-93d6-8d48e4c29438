package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 标签实体类
 */
@Data
@Accessors(chain = true)
@TableName("QueryResultColumnAdvanceQueries")
public class QueryResultColumnAdvanceQueriesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签值
     */
    @TableField(value = "QueryResultColumn_Column")
    private String queryResultColumn;

    /**
     * 标签值
     */
    @TableField(value = "AdvanceQuery_QueryId")
    private Integer advanceQueryId;

}