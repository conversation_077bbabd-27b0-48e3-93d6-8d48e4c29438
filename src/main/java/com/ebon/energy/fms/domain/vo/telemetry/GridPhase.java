package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class GridPhase {
    public GridPhase() { }

    /**
     * Please consider using the constructor with typed units instead.
     */
    public GridPhase(
            BigDecimal V,
            BigDecimal I,
            BigDecimal P,
            BigDecimal F,
            BigDecimal PowerFactor,
            BigDecimal Q,
            BigDecimal S) {
        this.V = V;
        this.I = I;
        this.P = P;
        this.F = F;
        this.PowerFactor = PowerFactor;
        this.Q = Q;
        this.S = S;
    }

    /*public GridPhase(
            Volt V,
            Ampere I,
            <PERSON>,
            Frequency F,
            BigDecimal PowerFactor,
            VoltAmpsReactive Q,
            VoltAmps S) {
        this.V = V != null ? V.asDecimal(Volt.UNIT, SystemStatus.VOLT_PRECISION) : null;
        this.I = I != null ? I.asDecimal(Ampere.UNIT, SystemStatus.AMPERE_PRECISION) : null;
        this.P = P != null ? P.asDecimal(Watt.UNIT, SystemStatus.WATT_PRECISION) : null;
        this.F = F != null ? F.asDecimal(Frequency.UNIT, SystemStatus.FREQUENCY_PRECISION) : null;
        this.PowerFactor = PowerFactor;
        this.Q = Q != null ? Q.asDecimal(VoltAmpsReactive.UNIT, SystemStatus.VOLT_AMPS_PRECISION) : null;
        this.S = S != null ? S.asDecimal(VoltAmps.UNIT, SystemStatus.VOLT_AMPS_PRECISION) : null;
    }*/

    // @SystemStatus(minValue = 180, maxValue = 260, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Voltage", description = "Voltage")
    private BigDecimal V;

    // @SystemStatus(minValue = -100, maxValue = 100, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Current", description = "Current")
    private BigDecimal I;

    // @SystemStatus(minValue = -25000, maxValue = 25000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "")
    // @Display(name = "Power", description = "Power flow")
    private BigDecimal P;

    // @SystemStatus(minValue = 40, maxValue = 60, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "Hz")
    // @Display(name = "Frequency", description = "Frequency")
    private BigDecimal F;

    // @SystemStatus(minValue = -1, maxValue = 1, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Power Factor", description = "Power factor")
    private BigDecimal PowerFactor;

    // @SystemStatus(minValue = -25000, maxValue = 25000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "")
    // @Display(name = "Reactive power", description = "Power flow")
    private BigDecimal Q;

    // @SystemStatus(minValue = -25000, maxValue = 25000, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.SIMPLE, units = "")
    // @Display(name = "Apparent power", description = "Power flow")
    private BigDecimal S;

    // Getters and Setters with original casing
    public BigDecimal getV() { return V; }
    public void setV(BigDecimal V) { this.V = V; }

    public BigDecimal getI() { return I; }
    public void setI(BigDecimal I) { this.I = I; }

    public BigDecimal getP() { return P; }
    public void setP(BigDecimal P) { this.P = P; }

    public BigDecimal getF() { return F; }
    public void setF(BigDecimal F) { this.F = F; }

    public BigDecimal getPowerFactor() { return PowerFactor; }
    public void setPowerFactor(BigDecimal PowerFactor) { this.PowerFactor = PowerFactor; }

    public BigDecimal getQ() { return Q; }
    public void setQ(BigDecimal Q) { this.Q = Q; }

    public BigDecimal getS() { return S; }
    public void setS(BigDecimal S) { this.S = S; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GridPhase gridPhase = (GridPhase) o;
        return Objects.equals(V, gridPhase.V) &&
                Objects.equals(I, gridPhase.I) &&
                Objects.equals(P, gridPhase.P) &&
                Objects.equals(F, gridPhase.F) &&
                Objects.equals(PowerFactor, gridPhase.PowerFactor) &&
                Objects.equals(Q, gridPhase.Q) &&
                Objects.equals(S, gridPhase.S);
    }

    @Override
    public int hashCode() {
        return Objects.hash(V, I, P, F, PowerFactor, Q, S);
    }

    @Override
    public String toString() {
        return "GridPhase{" +
                "V=" + V +
                ", I=" + I +
                ", P=" + P +
                ", F=" + F +
                ", PowerFactor=" + PowerFactor +
                ", Q=" + Q +
                ", S=" + S +
                '}';
    }
}