package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Dispatchable load schedule auto entity
 */
@Data
public class DispatchableLoadScheduleAuto {
    @JsonProperty(index = 1)
    private boolean recurring;
    
    @JsonProperty(index = 2)
    private long periodInMinutes;
    
    @JsonProperty(index = 3)
    private String scheduleDate;
    
    /**
     * Default constructor
     */
    public DispatchableLoadScheduleAuto() {
        periodInMinutes = 0;
        recurring = false;
        
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        scheduleDate = sdf.format(new Date());
    }
}
