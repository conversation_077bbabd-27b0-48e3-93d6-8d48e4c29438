package com.ebon.energy.fms.domain.vo;

public class RossDesiredSettingsVO {
    private SettingsV2DesiredVO v2;

    private BatteryManagerDesiredSettingsVO batteryManager;

    private InverterDesiredSettingsVO inverter;

    public static String  ThirdPartyExportCtName = "thirdPartyExportCt";


    public RossDesiredSettingsVO() {
    }

    public RossDesiredSettingsVO(SettingsV2DesiredVO v2, BatteryManagerDesiredSettingsVO batteryManager) {
        this.v2 = v2;
        this.batteryManager = batteryManager;
    }

    public RossDesiredSettingsVO(SettingsV2DesiredVO v2, BatteryManagerDesiredSettingsVO batteryManager, InverterDesiredSettingsVO inverter) {
        this.v2 = v2;
        this.batteryManager = batteryManager;
        this.inverter = inverter;
    }

    public SettingsV2DesiredVO getV2() {
        return v2;
    }

    public void setV2(SettingsV2DesiredVO v2) {
        this.v2 = v2;
    }

    public BatteryManagerDesiredSettingsVO getBatteryManager() {
        return batteryManager;
    }

    public void setBatteryManager(BatteryManagerDesiredSettingsVO batteryManager) {
        this.batteryManager = batteryManager;
    }

    public InverterDesiredSettingsVO getInverter() {
        return inverter;
    }

    public void setInverter(InverterDesiredSettingsVO inverter) {
        this.inverter = inverter;
    }
}