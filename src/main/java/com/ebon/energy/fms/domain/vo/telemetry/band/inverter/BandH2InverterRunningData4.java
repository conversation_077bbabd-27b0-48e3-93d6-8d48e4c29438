package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;










public class BandH2InverterRunningData4 extends DataBackedBand
{
	public BandH2InverterRunningData4()
	{
		super(BandForge.<BandH2InverterRunningData4>getMetadataFor(BandH2InverterRunningData4.class));
	}



	public BandH2InverterRunningData4(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterRunningData4>getMetadataFor(BandH2InverterRunningData4.class));
	}

	public BandH2InverterRunningData4(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterRunningData4>getMetadataFor(BandH2InverterRunningData4.class));
	}



	public final Watt getSysTotalLoadPower()
	{
		return GetS16(0, Watt.Unit);
	}


	public final Watt getCTGridActivePower()
	{
		return GetS16(2, Watt.Unit);
	}


	public final VoltAmps getCTGridApparentPower()
	{
		return GetS16(4, VoltAmps.Unit);
	}


	public final Watt getCTPVActivePower()
	{
		return GetS16(6, Watt.Unit);
	}


	public final VoltAmps getCTPVApparentPower()
	{
		return GetS16(8, VoltAmps.Unit);
	}


	public final Watt getTotalPVPower()
	{
		return GetS16(10, Watt.Unit);
	}


	public final Watt getTotalBatteryPower()
	{
		return GetS16(12, Watt.Unit);
	}


	public final Watt getTotalGridActivePower()
	{
		return GetS16(14, Watt.Unit);
	}


	public final VoltAmps getTotalGridApparentPower()
	{
		return GetS16(16, VoltAmps.Unit);
	}


	public final Watt getTotalInvActivePower()
	{
		return GetS16(18, Watt.Unit);
	}


	public final VoltAmps getTotalInvApparentPower()
	{
		return GetS16(20, VoltAmps.Unit);
	}


	public final Watt getBackupTotalLoadActivePower()
	{
		return GetU16(22, Watt.Unit);
	}


	public final VoltAmps getBackupTotalLoadApparentPower()
	{
		return GetU16(24, VoltAmps.Unit);
	}


	public final Watt getSysGridActivePower()
	{
		return GetS16(26, Watt.Unit);
	}


	public final BigDecimal getIntelligentLoadPowerL1()
	{
		return new BigDecimal(GetU16(32)).multiply(Percentage._1);
	}


	public final BigDecimal getIntelligentLoadPowerL2()
	{
		return new BigDecimal(GetU16(34)).multiply(Percentage._1);
	}


	public final BigDecimal getIntelligentLoadPowerL3()
	{
		return new BigDecimal(GetU16(36)).multiply(Percentage._1);
	}


	public final Watt getSysTotalLoadPowerH()
	{
		return GetS16(38, Watt.Unit);
	}


	public final Watt getTotalPVPowerH()
	{
		return GetS16(40, Watt.Unit);
	}


	public final Watt getTotalBatteryPowerH()
	{
		return GetS16(42, Watt.Unit);
	}


	public final Watt getSysGridActivePowerH()
	{
		return GetS16(44, Watt.Unit);
	}
}
