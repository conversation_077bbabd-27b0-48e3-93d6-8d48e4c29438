package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import java.util.Map;
import java.util.Objects;

@Data
@NoArgsConstructor
@Builder
public class SettingsV2DesiredBase {

    public static final String EMS_SETTINGS_NAME = "ems";
    public static final String INVERTER_SETTINGS_NAME = "inverter";
    public static final String INVERTER_CONTROL_SETTINGS_NAME = "inverterControl";
    public static final String SMART_RELAY_SETTINGS_NAME = "relaySettings";
    public static final String SITE_SETTINGS_NAME = "site";
    public static final String BATTERY_MANAGER_SETTINGS_NAME = "batteryManager";
    public static final String CONSTRAINTS_NAME = "constraints";

    @JsonProperty(EMS_SETTINGS_NAME)
    private EmsSettingsV2Desired emsSettings;

    @JsonProperty(INVERTER_SETTINGS_NAME)
    private Map<String, Object> inverterSettings;

    @JsonProperty(SMART_RELAY_SETTINGS_NAME)
    private SmartRelaySettingsV2Desired smartRelaySettings;

    @JsonProperty(INVERTER_CONTROL_SETTINGS_NAME)
    private Map<String, Object> inverterControlSettings;

    @JsonProperty(SITE_SETTINGS_NAME)
    private Map<String, Object> siteSettings;

    @JsonProperty(BATTERY_MANAGER_SETTINGS_NAME)
    private Map<String, Object> batteryManagerSettings;

    @JsonProperty(CONSTRAINTS_NAME)
    private Map<String, Object> constraints;

    @JsonCreator
    public SettingsV2DesiredBase(
            @JsonProperty(EMS_SETTINGS_NAME) EmsSettingsV2Desired emsSettings,
            @JsonProperty(INVERTER_SETTINGS_NAME) Map<String, Object> inverterSettings,
            @JsonProperty(SMART_RELAY_SETTINGS_NAME) SmartRelaySettingsV2Desired smartRelaySettings,
            @JsonProperty(INVERTER_CONTROL_SETTINGS_NAME) Map<String, Object> inverterControlSettings,
            @JsonProperty(SITE_SETTINGS_NAME) Map<String, Object> siteSettings,
            @JsonProperty(BATTERY_MANAGER_SETTINGS_NAME) Map<String, Object> batteryManagerSettings,
            @JsonProperty(CONSTRAINTS_NAME) Map<String, Object> constraints
    ) {
        this.emsSettings = emsSettings;
        this.inverterSettings = inverterSettings;
        this.smartRelaySettings = smartRelaySettings;
        this.inverterControlSettings = inverterControlSettings;
        this.siteSettings = siteSettings;
        this.batteryManagerSettings = batteryManagerSettings;
        this.constraints = constraints;
    }

    public static SettingsV2DesiredBase getDefault() {
        return new SettingsV2DesiredBase();
    }
}
