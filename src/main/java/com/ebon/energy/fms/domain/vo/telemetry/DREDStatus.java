package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class DREDStatus implements Cloneable {
    private Boolean IsOn;
    private DREDCommand DREDCommand;
    private String DREDDisplay;

    public DREDStatus() {
        this.DREDDisplay = "";
    }

    public DREDStatus(Boolean IsOn, DREDCommand DREDCommand, String DREDDisplay) {
        this.IsOn = IsOn;
        this.DREDCommand = DREDCommand;
        this.DREDDisplay = DREDDisplay;
    }

    public Boolean getIsOn() {
        return IsOn;
    }

    public void setIsOn(Boolean IsOn) {
        this.IsOn = IsOn;
    }

    public DREDCommand getDREDCommand() {
        return DREDCommand;
    }

    public void setDREDCommand(DREDCommand DREDCommand) {
        this.DREDCommand = DREDCommand;
    }

    public String getDREDDisplay() {
        return DREDDisplay;
    }

    public void setDREDDisplay(String DREDDisplay) {
        this.DREDDisplay = DREDDisplay;
    }

    @Override
    public DREDStatus clone() {
        try {
            return (DREDStatus) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}