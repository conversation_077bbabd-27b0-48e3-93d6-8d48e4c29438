package com.ebon.energy.fms.domain.vo.setting.provider.types;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.utils.path.EH1PSettingPaths;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.EH1PSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.RossSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.SettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.V2Setting;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.provider.ICommonSettingsBuilder;
import com.ebon.energy.fms.domain.vo.setting.provider.RossSettingPaths;
import com.ebon.energy.fms.domain.vo.setting.provider.SchedulePriority;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class EH1PSettingsBuilder extends SettingsBuilder<EH1PSettingsReader, EH1PSettingPaths> {


    public EH1PSettingsBuilder(DeviceInfoAndSettings deviceInfoAndSettings, Instant nowInUtc) {
        super(deviceInfoAndSettings, nowInUtc, new EH1PSettingsReader(deviceInfoAndSettings), new EH1PSettingPaths());
    }

    @Override
    public ICommonSettingsBuilder patchManagedInverterSetting(String settingName, JsonNode value, String settingIndex, ManagedInverterSettingDesired.InverterSettingValueType settingType, ManagedInverterSettingDesired.InverterSettingExecutionType executionType, ManagedInverterSettingDesired.InverterSettingSource source, String uniqueId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder buildInverterPatch(RossDesiredSettings desired, List<ManagedInverterSettingDesired> expectedSettings, Map<String, Object> toPatch) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addShadowScan(boolean enableShadowScan) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addACCoupled(boolean enableACCoupledMode) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(BigDecimal powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(Double powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteExportLimit(Watt limit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftAndHardSiteExportLimit(Watt soft, Watt hard) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteGenerationLimit(VoltAmps generationLimit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSiteExportLimit(boolean enabled, Watt power) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder overrideDesiredPowerRampRateLimit(Duration rampTime, String settingId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addBatterySettings(ManufacturerEnum manufacturer, Integer batteryCount, Integer maxChargeCurrent, Integer maxDischargeCurrent, Integer minSoc, Integer minOffgridSoc) {
        ObjectMapper objectMapper = new ObjectMapper();

        // 确定电池制造商
        ManufacturerEnum batteryManufacturer = ManufacturerEnum.None;
        switch (manufacturer) {
            case Pylon:
                batteryManufacturer = ManufacturerEnum.Pylon;
                getDeviceSettings().getIntent().setBatteryType(ManufacturerEnum.Pylon.toString());
                break;
            case Redback:
                batteryManufacturer = ManufacturerEnum.Pylon;
                getDeviceSettings().getIntent().setBatteryType(ManufacturerEnum.Redback.toString());
                break;
            default:
                batteryManufacturer = manufacturer;
                getDeviceSettings().getIntent().setBatteryType(batteryManufacturer != null ? batteryManufacturer.toString() : null);
                break;
        }

        // 设置 Intent 字段
        getDeviceSettings().getIntent().setBatteryManufacturer(batteryManufacturer != null ? batteryManufacturer.toString() : null);
        getDeviceSettings().getIntent().setBatteryCount(batteryCount);
        getDeviceSettings().getIntent().setBatteryMaxChargeCurrent(maxChargeCurrent);
        getDeviceSettings().getIntent().setBatteryMaxDischargeCurrent(maxDischargeCurrent);
        getDeviceSettings().getIntent().setBatteryMinSoc(minSoc);
        getDeviceSettings().getIntent().setBatteryMinOffgridSoc(minOffgridSoc);
        getDeviceSettings().getIntent().setBatterySettingsModifiedUtc(getNowInUtc());

        // 获取制造商配置
        EH1PSettingPaths.BatteryConfig config = EH1PSettingPaths.fromManufacturer(manufacturer);

        // 添加设置到 DesiredPatch
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryProtocol, objectMapper.valueToTree(config.getProtocol()));
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryType, objectMapper.valueToTree(manufacturer != null ? manufacturer.toString() : null));
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryArchitecture, objectMapper.valueToTree(config.getBatteryArchitecture()));
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryCount, objectMapper.valueToTree(batteryCount));
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryMaxChargeCurrent, objectMapper.valueToTree(maxChargeCurrent));
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryMaxDischargeCurrent, objectMapper.valueToTree(maxDischargeCurrent));

        // 转换 SOC 从百分比到小数 (0-100 -> 0.0-1.0)
        BigDecimal minSocDecimal = minSoc != null ? new BigDecimal(minSoc).divide(new BigDecimal("100")) : null;
        BigDecimal minOffgridSocDecimal = minOffgridSoc != null ? new BigDecimal(minOffgridSoc).divide(new BigDecimal("100")) : null;

        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryMinSoc0to1, objectMapper.valueToTree(minSocDecimal));
        addValue(getDesiredPatch(), EH1PSettingPaths.BatteryMinOffgridSoc0to1, objectMapper.valueToTree(minOffgridSocDecimal));

        // 添加额外的 Gen3 电池设置
        addAdditionalRequiredGen3BatterySettings(manufacturer);

        return this;
    }

    @Override
    public ICommonSettingsBuilder addCtFlipSettings(Boolean flipCt1, Boolean flipCt2) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDefaultTelemetryPeriod() {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addTelemetryPeriod(Duration period, Instant endDateUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDredSettings(Boolean dredSubscribed) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder removeSchedule(String scheduleId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, Instant endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, Instant startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, LocalDateTime endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, LocalDateTime startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public SchedulePriority getSchedulePriority() {
        return null;
    }

    @Override
    public void deleteRelaySchedule(int relayNumber1Based, String id, Map<String, Object> patch) {

    }

    @Override
    public ICommonSettingsBuilder disableAgeingMode() {
        return null;
    }

    /**
     * 为 Gen3 电池添加额外的必需设置
     * 注意：如果将来不同的 Gen 3 型号需要不同的值，部分逻辑需要推回到 SettingChangeService 中。
     *
     * @param batteryManufacturer 电池制造商
     */
    private void addAdditionalRequiredGen3BatterySettings(ManufacturerEnum batteryManufacturer) {
        ObjectMapper objectMapper = new ObjectMapper();

        // 添加基本的电池模块设置
        addValueIfNotSet(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMaxChargeCurrent,
                objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMaxChargeCurrentDefault().asDecimal()));
        addValueIfNotSet(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMaxDischargeCurrent,
                objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMaxDischargeCurrentDefault().asDecimal()));

        // 根据制造商设置不同的电压值
        switch (batteryManufacturer) {
            case Redback:
                addValue(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMaxChargeVoltage,
                        objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMaxChargeVoltageForRedbackDefault().asDecimal()));
                addValue(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMinDischargeVoltage,
                        objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMinDischargeVoltageForRedbackDefault().asDecimal()));
                addValue(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMinDischargeVoltageOffgrid,
                        objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMinDischargeVoltageOffgridForRedbackDefault().asDecimal()));
                break;
            default:
                addValue(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMaxChargeVoltage,
                        objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMaxChargeVoltageDefault().asDecimal()));
                addValue(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMinDischargeVoltage,
                        objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMinDischargeVoltageDefault().asDecimal()));
                addValue(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMinDischargeVoltageOffgrid,
                        objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMinDischargeVoltageOffgridDefault().asDecimal()));
                break;
        }

        // 添加其他电池设置
        addValueIfNotSet(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMinSOC,
                objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMinSOCDefault()));
        addValueIfNotSet(getDesiredPatch(), EH1PSettingPaths.BatteryModuleRatedMinSOCOffgrid,
                objectMapper.valueToTree(EH1PSettingPaths.getBatteryModuleRatedMinSOCOffgridDefault()));
        addValueIfNotSet(getDesiredPatch(), EH1PSettingPaths.BatteryInverterRatedMaxChargeCurrent,
                objectMapper.valueToTree(EH1PSettingPaths.getBatteryInverterRatedMaxChargeCurrentDefault().asDecimal()));
        addValueIfNotSet(getDesiredPatch(), EH1PSettingPaths.BatteryInverterRatedMaxDischargeCurrent,
                objectMapper.valueToTree(EH1PSettingPaths.getBatteryInverterRatedMaxDischargeCurrentDefault().asDecimal()));
    }

    /**
     * 如果设置尚未设置，则添加值
     *
     * @param subsection 子节点
     * @param setting 设置
     * @param value 值
     */
    private void addValueIfNotSet(JsonNode subsection, V2Setting setting, JsonNode value) {
        Object existingValue = SettingsReader.readObject(getDeviceSettings(), true, setting.getSection(), setting.getKey());

        if (existingValue == null) {
            addValue(subsection, setting, value);
        }
    }
}
