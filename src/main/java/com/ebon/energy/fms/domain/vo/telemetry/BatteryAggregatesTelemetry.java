package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.Volt;
import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryAggregatesTelemetry {
    private Watt MaxInputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant MaxInputPTimestampUtc;

    private Watt MaxOutputP;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant MaxOutputPTimestampUtc;

    private Volt HighestMinCellVoltageWhileIdle;

    public Watt getMaxInputP() {
        return MaxInputP;
    }

    public void setMaxInputP(Watt MaxInputP) {
        this.MaxInputP = MaxInputP;
    }

    public Instant getMaxInputPTimestampUtc() {
        return MaxInputPTimestampUtc;
    }

    public void setMaxInputPTimestampUtc(Instant MaxInputPTimestampUtc) {
        this.MaxInputPTimestampUtc = MaxInputPTimestampUtc;
    }

    public Watt getMaxOutputP() {
        return MaxOutputP;
    }

    public void setMaxOutputP(Watt MaxOutputP) {
        this.MaxOutputP = MaxOutputP;
    }

    public Instant getMaxOutputPTimestampUtc() {
        return MaxOutputPTimestampUtc;
    }

    public void setMaxOutputPTimestampUtc(Instant MaxOutputPTimestampUtc) {
        this.MaxOutputPTimestampUtc = MaxOutputPTimestampUtc;
    }

    public Volt getHighestMinCellVoltageWhileIdle() {
        return HighestMinCellVoltageWhileIdle;
    }

    public void setHighestMinCellVoltageWhileIdle(Volt HighestMinCellVoltageWhileIdle) {
        this.HighestMinCellVoltageWhileIdle = HighestMinCellVoltageWhileIdle;
    }
}
