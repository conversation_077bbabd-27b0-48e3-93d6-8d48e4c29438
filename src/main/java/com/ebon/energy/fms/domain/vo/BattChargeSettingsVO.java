package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class BattChargeSettingsVO implements Cloneable {
    // [Volts]
    @JsonProperty("VoltageValue")
    public double getVoltageValue() {
        return Voltage != null ? Voltage : 0d;
    }

    @JsonProperty("CurrentValue")
    // [Amps]
    public double getCurrentValue() {
        return Current != null ? Current : 0d;
    }

    // [Volts]
    public Double Voltage;

    // [Amps]
    public Double Current;

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            // 由于实现了 Cloneable 接口，这里不会抛出异常
            throw new AssertionError();
        }
    }

    @Override
    public int hashCode() {
        return 5 * (int) getVoltageValue() + 7 * (int) getCurrentValue() + 11;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof BattChargeSettingsVO)) {
            return false;
        }
        BattChargeSettingsVO bcs = (BattChargeSettingsVO) obj;
        return Objects.equals(bcs.Voltage, Voltage) && Objects.equals(bcs.Current, Current);
    }
}