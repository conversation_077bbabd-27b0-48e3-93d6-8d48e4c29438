package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;










public class BandESD extends DataBackedBand
{
	public BandESD()
	{
		super(BandForge.<BandESD>getMetadataFor(BandESD.class));
	}



	public BandESD(byte[] bytes)
	{
		super(bytes, BandForge.<BandESD>getMetadataFor(BandESD.class));
	}

	public BandESD(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESD>getMetadataFor(BandESD.class));
	}


	


	public final int getStartTime1() { return GetU16(0); }

	


	public final int getEndTime1() { return GetU16(2); }

	
	public final BigDecimal getBatPower0to1_1()
	{
		return new BigDecimal(GetS16(4)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek1() { return GetU16(6); }

	


	public final int getStartTime2() { return GetU16(8); }

	


	public final int getEndTime2() { return GetU16(10); }

	
	public final BigDecimal getBatPower0to1_2()
	{
		return new BigDecimal(GetS16(12)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek2() { return GetU16(14); }

	


	public final int getStartTime3() { return GetU16(16); }

	


	public final int getEndTime3() { return GetU16(18); }

	
	public final BigDecimal getBatPower0to1_3()
	{
		return new BigDecimal(GetS16(20)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek3() { return GetU16(22); }

	


	public final int getStartTime4() { return GetU16(24); }

	


	public final int getEndTime4() { return GetU16(26); }

	
	public final BigDecimal getBatPower0to1_4()
	{
		return new BigDecimal(GetS16(28)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek4() { return GetU16(30); }
}
