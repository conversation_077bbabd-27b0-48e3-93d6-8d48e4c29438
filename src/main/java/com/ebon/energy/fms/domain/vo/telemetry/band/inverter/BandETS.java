package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandETS extends DataBackedBand
{
	public BandETS()
	{
		super(BandForge.<BandETS>getMetadataFor(BandETS.class));
	}



	public BandETS(byte[] bytes)
	{
		super(bytes, BandForge.<BandETS>getMetadataFor(BandETS.class));
	}

	public BandETS(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETS>getMetadataFor(BandETS.class));
	}


	


	public final int getOldMeterProtocol() { return GetU16(0); }
}
