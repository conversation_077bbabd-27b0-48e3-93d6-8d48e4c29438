package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandH2InverterMeterData extends DataBackedBand
{
	public BandH2InverterMeterData()
	{
		super(BandForge.<BandH2InverterMeterData>getMetadataFor(BandH2InverterMeterData.class));
	}



	public BandH2InverterMeterData(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterMeterData>getMetadataFor(BandH2InverterMeterData.class));
	}

	public BandH2InverterMeterData(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterMeterData>getMetadataFor(BandH2InverterMeterData.class));
	}


	
	public final Volt getMeterVoltageL1()
	{
		return GetU16(0, Volt.Deci);
	}

	
	public final Ampere getMeterCurrentL1()
	{
		return GetU16(2, Ampere.Centi);
	}

	
	public final Frequency getMeterFrequencyL1()
	{
		return GetU16(4, Frequency.Centi);
	}

	
	public final Watt getMeterActivePowerL1()
	{
		return GetU16(6, Watt.Unit);
	}

	
	public final Volt getMeterVoltageL2()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Watt getMeterActivePowerL2()
	{
		return GetU16(10, Watt.Unit);
	}

	
	public final Volt getMeterVoltageL3()
	{
		return GetU16(12, Volt.Deci);
	}

	
	public final Watt getMeterActivePowerL3()
	{
		return GetU16(14, Watt.Unit);
	}
}
