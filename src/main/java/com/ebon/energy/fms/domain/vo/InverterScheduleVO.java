package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.InverterModeValue;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

public class InverterScheduleVO extends BaseScheduleVO {
    private String ScheduleId;
    @JsonProperty(index = 5)
    private String Mode;
    @JsonProperty(index = 6)
    private Long PowerInWatts;

    public InverterScheduleVO() {
        convertStartTime(new Date());
        Date end = new Date(System.currentTimeMillis() + 60 * 1000);
        convertEndTime(end);
        Mode = "Auto";
        Recurring = false;
        PowerInWatts = 0L;
        convertScheduleDate(new Date());
    }

    public InverterScheduleVO(Date startLocal, Date endLocal, long powerW, InverterModeValue mode) {
        if (startLocal == null) {
            throw new IllegalArgumentException("Start must be provided. " + startLocal + " vs " + endLocal);
        }

        if (endLocal == null) {
            throw new IllegalArgumentException("End must be provided. " + startLocal + " vs " + endLocal);
        }

        if (startLocal.getYear() != endLocal.getYear() || startLocal.getMonth() != endLocal.getMonth() || startLocal.getDate() != endLocal.getDate()) {
            throw new IllegalArgumentException("Start and end must be in the same day. " + startLocal + " vs " + endLocal);
        }

        if (startLocal.getTime() >= endLocal.getTime()) {
            throw new IllegalArgumentException("End must be after the start. " + startLocal + " vs " + endLocal);
        }

        Mode = mode.toString();
        PowerInWatts = powerW;
        Recurring = false;

        convertScheduleDate(startLocal);
        convertStartTime(startLocal);
        convertEndTime(endLocal);
    }

    public InverterScheduleVO(
            LocalDate date,
            LocalTime startLocal,
            LocalTime endLocal,
            Watt powerW,
            InverterModeValue mode) {
        if (date == null) {
            throw new IllegalArgumentException("Date must be provided. " + startLocal + " vs " + endLocal);
        }

        if (startLocal.compareTo(endLocal) >= 0) {
            throw new IllegalArgumentException("End must be after the start. " + startLocal + " vs " + endLocal);
        }

        Mode = mode.name();
        PowerInWatts = powerW == null ? null : Long.parseLong(String.valueOf(powerW.getDoubleValue()));
        Recurring = false;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
        ScheduleDate = date.format(formatter);

        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(TIME_FORMAT);
        StartTime = startLocal.format(timeFormatter);
        EndTime = endLocal.format(timeFormatter);
    }

    public String getScheduleId() {
        return ScheduleId;
    }

    public void setScheduleId(String scheduleId) {
        ScheduleId = scheduleId;
    }

    public String getMode() {
        return Mode;
    }

    public void setMode(String mode) {
        Mode = mode;
    }

    public long getPowerInWatts() {
        return PowerInWatts;
    }

    public void setPowerInWatts(long powerInWatts) {
        PowerInWatts = powerInWatts;
    }

    public static boolean operator_equals(InverterScheduleVO left, InverterScheduleVO right) {
        if (left == null || right == null) {
            return Objects.equals(left, right);
        }
        return left.test(right);
    }

    public static boolean operator_notEquals(InverterScheduleVO left, InverterScheduleVO right) {
        if (left == null || right == null) {
            return !Objects.equals(left, right);
        }
        return !(left.test(right));
    }

    public boolean test(InverterScheduleVO other) {
        return Objects.equals(Mode, other.Mode)
                && PowerInWatts == other.PowerInWatts
                && Objects.equals(StartTime, other.StartTime)
                && Objects.equals(EndTime, other.EndTime)
                && Recurring == other.Recurring
                && PowerInWatts == other.PowerInWatts
                && Objects.equals(ScheduleDate, other.ScheduleDate);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof InverterScheduleVO) {
            InverterScheduleVO other = (InverterScheduleVO) obj;
            return test(other);
        }
        return false;
    }

    @Override
    public int hashCode() {
        int hash = 17;
        hash = (hash * 23) + (Mode != null ? Mode.hashCode() : 0);
        hash = (hash * 23) + Long.hashCode(PowerInWatts);
        hash = (hash * 23) + (StartTime != null ? StartTime.hashCode() : 0);
        hash = (hash * 23) + (EndTime != null ? EndTime.hashCode() : 0);
        hash = (hash * 23) + Boolean.hashCode(Recurring);
        hash = (hash * 23) + Long.hashCode(PowerInWatts);
        hash = (hash * 23) + (ScheduleDate != null ? ScheduleDate.hashCode() : 0);
        return hash;
    }

    @Override
    public String validate() {
        String error = super.validate();
        if (error != null && !error.isEmpty()) {
            return error;
        }
        return null;
    }
}