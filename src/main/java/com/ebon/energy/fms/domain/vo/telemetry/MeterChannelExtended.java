package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;
import java.util.Optional;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterChannelExtended {
    public int ChannelId;
    public AvgMinMax Voltage;
    public AvgMinMax Current;
    public AvgMinMax PowerFactor;
    public AvgMinMax Frequency;
    public AvgMinMax ApparentPower;
    public AvgMinMax ActivePower;
    public AvgMinMax ReactivePower;
    public Double AverageImpedance;
    public MeterChannelThd ThdMeasurements;
    public int Index;

    public Optional<Tuple4<Volt, Volt, Volt, Volt>> getVoltage() {
        if (Voltage == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                Voltage.Average != null ? new Volt(Voltage.Average) : null,
                Voltage.Min != null ? new Volt(Voltage.Min) : null,
                Voltage.Max != null ? new Volt(Voltage.Max) : null,
                Voltage.Instantaneous != null ? new Volt(Voltage.Instantaneous) : null
        ));
    }

    public Optional<Tuple4<Ampere, Ampere, Ampere, Ampere>> getCurrent() {
        if (Current == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                Current.Average != null ? new Ampere(Current.Average) : null,
                Current.Min != null ? new Ampere(Current.Min) : null,
                Current.Max != null ? new Ampere(Current.Max) : null,
                Current.Instantaneous != null ? new Ampere(Current.Instantaneous) : null
        ));
    }

    public Optional<Tuple4<BigDecimal, BigDecimal, BigDecimal, BigDecimal>> getPowerFactor() {
        if (PowerFactor == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                PowerFactor.Average,
                PowerFactor.Min,
                PowerFactor.Max,
                PowerFactor.Instantaneous
        ));
    }

    public Optional<Tuple4<Frequency, Frequency, Frequency, Frequency>> getFrequency() {
        if (Frequency == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                Frequency.Average != null ? new Frequency(Frequency.Average) : null,
                Frequency.Min != null ? new Frequency(Frequency.Min) : null,
                Frequency.Max != null ? new Frequency(Frequency.Max) : null,
                Frequency.Instantaneous != null ? new Frequency(Frequency.Instantaneous) : null
        ));
    }

    public Optional<Tuple4<VoltAmps, VoltAmps, VoltAmps, VoltAmps>> getApparentPower() {
        if (ApparentPower == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                ApparentPower.Average != null ? new VoltAmps(ApparentPower.Average) : null,
                ApparentPower.Min != null ? new VoltAmps(ApparentPower.Min) : null,
                ApparentPower.Max != null ? new VoltAmps(ApparentPower.Max) : null,
                ApparentPower.Instantaneous != null ? new VoltAmps(ApparentPower.Instantaneous) : null
        ));
    }

    public Optional<Tuple4<Watt, Watt, Watt, Watt>> getActivePower() {
        if (ActivePower == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                ActivePower.Average != null ? new Watt(ActivePower.Average) : null,
                ActivePower.Min != null ? new Watt(ActivePower.Min) : null,
                ActivePower.Max != null ? new Watt(ActivePower.Max) : null,
                ActivePower.Instantaneous != null ? new Watt(ActivePower.Instantaneous) : null
        ));
    }

    public Optional<Tuple4<VoltAmpsReactive, VoltAmpsReactive, VoltAmpsReactive, VoltAmpsReactive>> getReactivePower() {
        if (ReactivePower == null) {
            return Optional.empty();
        }
        return Optional.of(new Tuple4<>(
                ReactivePower.Average != null ? new VoltAmpsReactive(ReactivePower.Average) : null,
                ReactivePower.Min != null ? new VoltAmpsReactive(ReactivePower.Min) : null,
                ReactivePower.Max != null ? new VoltAmpsReactive(ReactivePower.Max) : null,
                ReactivePower.Instantaneous != null ? new VoltAmpsReactive(ReactivePower.Instantaneous) : null
        ));
    }

    public static class Tuple4<T1, T2, T3, T4> {
        public final T1 Item1;
        public final T2 Item2;
        public final T3 Item3;
        public final T4 Item4;

        public Tuple4(T1 item1, T2 item2, T3 item3, T4 item4) {
            Item1 = item1;
            Item2 = item2;
            Item3 = item3;
            Item4 = item4;
        }
    }
}