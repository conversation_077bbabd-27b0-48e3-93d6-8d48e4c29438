package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 审计表
 */
@Data
@Accessors
@TableName("Audits")
public class AuditsDO {

    /**
     * 审计ID
     */
    @TableId(value = "AuditID", type = IdType.AUTO)
    private Integer auditID;

    /**
     * 用户名
     */
    @TableField(value = "UserName")
    private String userName;

    /**
     * 访问URL
     */
    @TableField(value = "URLAccessed")
    private String urlAccessed;

    /**
     * 访问时间
     */
    @TableField(value = "TimeAccessed")
    private Timestamp timeAccessed;

    /**
     * IP地址
     */
    @TableField(value = "IPAddress")
    private String ipAddress;

    /**
     * 方法
     */
    @TableField(value = "Method")
    private String method;

    /**
     * 内容
     */
    @TableField(value = "Content")
    private String content;

    /**
     * 标签
     */
    @TableField(value = "tag")
    private String tag;

}