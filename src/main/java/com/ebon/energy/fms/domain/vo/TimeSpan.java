package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.config.ToStringSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonSerialize(using = ToStringSerializer.class)
public class TimeSpan {
    private final long milliseconds;

    public long getTicks() {
        return milliseconds * 10000;
    }

    private TimeSpan(long milliseconds) {
        this.milliseconds = milliseconds;
    }

    // 从秒创建 TimeSpan 对象
    public static TimeSpan fromSeconds(long seconds) {
        return new TimeSpan(seconds * 1000);
    }

    public static TimeSpan fromSeconds(double seconds) {
        return new TimeSpan(Math.round(seconds * 1000));
    }

    // 从分钟创建 TimeSpan 对象
    public static TimeSpan fromMinutes(long minutes) {
        return new TimeSpan(minutes * 60 * 1000);
    }

    // 从小时创建 TimeSpan 对象
    public static TimeSpan fromHours(long hours) {
        return new TimeSpan(hours * 60 * 60 * 1000);
    }

    // 从天创建 TimeSpan 对象
    public static TimeSpan fromDays(long days) {
        return new TimeSpan(days * 24 * 60 * 60 * 1000);
    }

    // 从 ticks 创建 TimeSpan 对象
    public static TimeSpan fromTicks(long ticks) {
        return new TimeSpan(ticks / 10000); // 1 tick = 0.0001 毫秒，所以将 ticks 除以 10000 得到毫秒数
    }

    public static TimeSpan fromMilliseconds(long ms) {
        return new TimeSpan(ms);
    }

    // 获取总毫秒数
    public double getTotalMilliseconds() {
        return milliseconds;
    }

    // 获取总秒数
    public double getTotalSeconds() {
        return milliseconds / 1000.0;
    }

    // 获取总分钟数
    public double getTotalMinutes() {
        return milliseconds / (60.0 * 1000);
    }

    // 获取总小时数
    public double getTotalHours() {
        return milliseconds / (60.0 * 60 * 1000);
    }

    // 获取总天数
    public double getTotalDays() {
        return milliseconds / (24.0 * 60 * 60 * 1000);
    }

    // 加上另一个 TimeSpan
    public TimeSpan add(TimeSpan other) {
        return new TimeSpan(this.milliseconds + other.milliseconds);
    }

    // 减去另一个 TimeSpan
    public TimeSpan subtract(TimeSpan other) {
        return new TimeSpan(this.milliseconds - other.milliseconds);
    }

    // 比较两个 TimeSpan 的大小
    public int compareTo(TimeSpan other) {
        return Double.compare(this.milliseconds, other.milliseconds);
    }

    // 判断两个 TimeSpan 是否相等
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TimeSpan timeSpan = (TimeSpan) o;
        return milliseconds == timeSpan.milliseconds;
    }

    // 重写 hashCode 方法
    @Override
    public int hashCode() {
        return Double.hashCode(milliseconds);
    }

    // 重写 toString 方法
    @Override
    public String toString() {
        long days = (long) getTotalDays();
        long hours = (long) (getTotalHours() % 24);
        long minutes = (long) (getTotalMinutes() % 60);
        long seconds = (long) (getTotalSeconds() % 60);
        //long milliseconds = milliseconds % 1000;
        long ticks = (long) (getTicks() % 10000000);
        String res = String.format("%02d:%02d:%02d", hours, minutes, seconds);
        String dayStr = days > 0 ? String.format("%d.", days) : "";
        String milliStr = ticks > 0 ? String.format(".%07d", ticks) : "";
        return dayStr + res + milliStr;
    }

    public static void main(String[] args) {
        TimeSpan timeSpan1 = new TimeSpan(172885000);
        TimeSpan timeSpan2 = new TimeSpan(172895865);
        TimeSpan timeSpan3 = new TimeSpan(120000);
        TimeSpan timeSpan4 = new TimeSpan(120156);

        System.out.println(timeSpan1.toString());
        System.out.println(timeSpan2.toString());
        System.out.println(timeSpan3.toString());
        System.out.println(timeSpan4.toString());
    }
}
