package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryTelemetry {

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant LastConnectedTimeUtc;

    private HardwareModelEnum BatteryType;
    private Map<String, BatteryCommsUnitTelemetry> CommsUnits;
    private List<PylonBatteryUnitTelemetry> Units;
    @Deprecated
    private PylonStatusDto PylonStatus;
    private List<PylonBatteryAggregateTelemetry> Aggregates;
    private Set<String> ActiveInterlocks;
    private Set<String> EnabledInterlocks;
    private Boolean OverallInterlockActive;
    private Boolean OverallInterlockEnabled;
    private String MismatchPeriod;

    public BatteryTelemetry() {}

    @Deprecated
    public BatteryTelemetry(
            Instant lastConnectedTimeUtc,
            PylonStatusDto pylonStatus,
            List<PylonBatteryUnitTelemetry> units,
            List<PylonBatteryAggregateTelemetry> aggregates) {
        this.LastConnectedTimeUtc = lastConnectedTimeUtc;
        this.PylonStatus = pylonStatus;
        this.Units = units;
        this.Aggregates = aggregates;
    }

    public BatteryTelemetry(
            Instant lastConnectedTimeUtc,
            HardwareModelEnum batteryType,
            Map<String, BatteryCommsUnitTelemetry> commsUnits) {
        this.LastConnectedTimeUtc = lastConnectedTimeUtc;
        this.BatteryType = batteryType;
        this.CommsUnits = commsUnits;
    }

    public BatteryTelemetry(
            Instant lastConnectedTimeUtc,
            HardwareModelEnum batteryType,
            Map<String, BatteryCommsUnitTelemetry> commsUnits,
            Set<String> activeInterlocks,
            Set<String> enabledInterlocks,
            Boolean overallInterlockActive,
            Boolean overallInterlockEnabled,
            String mismatchPeriod) {
        this.LastConnectedTimeUtc = lastConnectedTimeUtc;
        this.BatteryType = batteryType;
        this.CommsUnits = commsUnits;
        this.ActiveInterlocks = activeInterlocks;
        this.EnabledInterlocks = enabledInterlocks;
        this.OverallInterlockActive = overallInterlockActive;
        this.OverallInterlockEnabled = overallInterlockEnabled;
        this.MismatchPeriod = mismatchPeriod;
    }

    // Getters and Setters
    public Instant getLastConnectedTimeUtc() { return LastConnectedTimeUtc; }
    public void setLastConnectedTimeUtc(Instant lastConnectedTimeUtc) { this.LastConnectedTimeUtc = lastConnectedTimeUtc; }

    public HardwareModelEnum getBatteryType() { return BatteryType; }
    public void setBatteryType(HardwareModelEnum batteryType) { this.BatteryType = batteryType; }

    public Map<String, BatteryCommsUnitTelemetry> getCommsUnits() { return CommsUnits; }
    public void setCommsUnits(Map<String, BatteryCommsUnitTelemetry> commsUnits) { this.CommsUnits = commsUnits; }

    public List<PylonBatteryUnitTelemetry> getUnits() { return Units; }
    public void setUnits(List<PylonBatteryUnitTelemetry> units) { this.Units = units; }

    @Deprecated
    public PylonStatusDto getPylonStatus() { return PylonStatus; }
    @Deprecated
    public void setPylonStatus(PylonStatusDto pylonStatus) { this.PylonStatus = pylonStatus; }

    public List<PylonBatteryAggregateTelemetry> getAggregates() { return Aggregates; }
    public void setAggregates(List<PylonBatteryAggregateTelemetry> aggregates) { this.Aggregates = aggregates; }

    public Set<String> getActiveInterlocks() { return ActiveInterlocks; }
    public void setActiveInterlocks(Set<String> activeInterlocks) { this.ActiveInterlocks = activeInterlocks; }

    public Set<String> getEnabledInterlocks() { return EnabledInterlocks; }
    public void setEnabledInterlocks(Set<String> enabledInterlocks) { this.EnabledInterlocks = enabledInterlocks; }

    public Boolean isOverallInterlockActive() { return OverallInterlockActive; }
    public void setOverallInterlockActive(Boolean overallInterlockActive) { this.OverallInterlockActive = overallInterlockActive; }

    public Boolean isOverallInterlockEnabled() { return OverallInterlockEnabled; }
    public void setOverallInterlockEnabled(Boolean overallInterlockEnabled) { this.OverallInterlockEnabled = overallInterlockEnabled; }

    public String getMismatchPeriod() { return MismatchPeriod; }
    public void setMismatchPeriod(String mismatchPeriod) { this.MismatchPeriod = mismatchPeriod; }
}