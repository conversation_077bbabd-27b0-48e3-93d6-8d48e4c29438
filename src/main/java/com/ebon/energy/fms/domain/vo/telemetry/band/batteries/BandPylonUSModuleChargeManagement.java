package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



 






public class BandPylonUSModuleChargeManagement extends DataBackedBand
{
	public BandPylonUSModuleChargeManagement()
	{
		super(BandForge.<BandPylonUSModuleChargeManagement>getMetadataFor(BandPylonUSModuleChargeManagement.class));
	}



	public BandPylonUSModuleChargeManagement(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylonUSModuleChargeManagement>getMetadataFor(BandPylonUSModuleChargeManagement.class));
	}

	public BandPylonUSModuleChargeManagement(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylonUSModuleChargeManagement>getMetadataFor(BandPylonUSModuleChargeManagement.class));
	}


	


	public final byte getCommandValue92()
	{
		return GetU8(0);
	}

	
	public final Volt getModuleChargeVoltageLimit()
	{
		return GetU16(1, Volt.Milli);
	}

	
	public final Volt getModuleDischargeVoltageLimit()
	{
		return GetU16(3, Volt.Milli);
	}

	
	public final Ampere getModuleChargeCurrentLimit()
	{
		return GetU16(5, Ampere.Deci);
	}

	
	public final Ampere getModuleDischargeCurrentLimit()
	{
		return GetS16(7, Ampere.Deci);
	}

	
	public final String getModuleChargeDischargeStatus()
	{
		return PylonChargeStatus.parse(GetU8(9));
	}
}
