package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESM extends DataBackedBand
{
	public BandESM()
	{
		super(BandForge.<BandESM>getMetadataFor(BandESM.class));
	}



	public BandESM(byte[] bytes)
	{
		super(bytes, BandForge.<BandESM>getMetadataFor(BandESM.class));
	}

	public BandESM(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESM>getMetadataFor(BandESM.class));
	}



	public final boolean getDredOffGridCheck()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}
}
