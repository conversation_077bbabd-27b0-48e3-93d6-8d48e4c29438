package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandBHS extends DataBackedBand
{
	public BandBHS()
	{
		super(BandForge.<BandBHS>getMetadataFor(BandBHS.class));
	}



	public BandBHS(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHS>getMetadataFor(BandBHS.class));
	}

	public BandBHS(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHS>getMetadataFor(BandBHS.class));
	}


	


	public final int getOldMeterProtocol() { return GetU16(0); }
}
