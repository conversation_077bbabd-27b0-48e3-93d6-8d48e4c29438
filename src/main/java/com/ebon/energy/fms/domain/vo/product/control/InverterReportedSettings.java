package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.GoodWeSiteExportLimitType;
import com.ebon.energy.fms.common.enums.GoodWeSiteGenerationLimitType;
import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.report.BatteryReportedSettings;
import com.ebon.energy.fms.domain.vo.product.control.report.InverterFirmwareReportedSettings;
import com.ebon.energy.fms.domain.vo.product.control.report.InverterRelayCheckReportedSettings;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode
public class InverterReportedSettings {

    @JsonProperty("battery")
    private BatteryReportedSettings batterySettings;

    @JsonProperty("firmware")
    private InverterFirmwareReportedSettings firmwareSettings;

    @JsonProperty("ModelName")
    private String modelName;

    @JsonProperty("SafetyCountry")
    private Integer safetyCountry;

    @JsonProperty("IsShadowScanEnabled")
    private Boolean isShadowScanEnabled;

    @JsonProperty("workMode")
    private SystemStatusEnum.InverterModeValue workMode;

    @JsonProperty("workModePowerW")
    private Watt workModePowerW;

    @JsonProperty("SiteExportLimitW")
    private Watt siteExportLimitW;

    @JsonProperty("DoesSiteRequireExportLimit")
    private Boolean doesSiteRequireExportLimit;

    @JsonProperty("SiteExportLimitType")
    private GoodWeSiteExportLimitType siteExportLimitType;

    /**
     * Gets or sets the PowerFactor as a decimal number between -1.0 to 1.0
     * Negative is Lagging, Unity can be -1 or 1
     */
    @JsonProperty("PowerFactor")
    private BigDecimal powerFactor;

    @JsonProperty("BackupStartDelay")
    private Integer backupStartDelay;

    @JsonProperty("RecoverTimeEE")
    private Integer recoverTimeEE;

    @JsonProperty("ArbitrarySettingRegister")
    private String arbitrarySettingRegister;

    @JsonProperty("ArbitrarySettingValue")
    private String arbitrarySettingValue;

    @JsonProperty("ArbitrarySettingToken")
    private String arbitrarySettingToken;

    @JsonProperty("ArbitrarySettingProtocol")
    private String arbitrarySettingProtocol;

    @JsonProperty("DisablePowerModeManager")
    private Boolean disablePowerModeManager;

    @JsonProperty("DisableBMSProcessor")
    private Boolean disableBMSProcessor;

    @JsonProperty("GridProfileId")
    private UUID gridProfileId;

    @JsonProperty("GridProfileCorrelationId")
    private String gridProfileCorrelationId;

    @JsonProperty("relayCheck")
    private InverterRelayCheckReportedSettings relayCheckSettings;

    @JsonProperty("GenLimitControlType")
    private GoodWeSiteGenerationLimitType genLimitControlType;

    @JsonProperty("GenLimitControlSoftLimVA")
    private VoltAmps genLimitControlSoftLimVA;

    @JsonProperty("GenLimitControlHardLimVA")
    private VoltAmps genLimitControlHardLimVA;

    // 你可以根据需要添加构造函数、方法等
}
