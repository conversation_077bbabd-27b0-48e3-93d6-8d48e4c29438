package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandH2InverterEnergyData1 extends DataBackedBand
{
	public BandH2InverterEnergyData1()
	{
		super(BandForge.<BandH2InverterEnergyData1>getMetadataFor(BandH2InverterEnergyData1.class));
	}



	public BandH2InverterEnergyData1(byte[] bytes)
	{
		super(bytes, BandForge.<BandH2InverterEnergyData1>getMetadataFor(BandH2InverterEnergyData1.class));
	}

	public BandH2InverterEnergyData1(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandH2InverterEnergyData1>getMetadataFor(BandH2InverterEnergyData1.class));
	}


	
	public final TimeSpan getGenerationHoursToday()
	{
		return GetU16(0, TimeSpan.fromHours(1));
	}

	
	public final TimeSpan getGenerationHoursTotal()
	{
		return GetU32(2, TimeSpan.fromHours(1));
	}

	
	public final WattHour getPVEnergyToday()
	{
		return GetU32(6, WattHour.Centi);
	}

	
	public final WattHour getPVEnergyMonth()
	{
		return GetU32(10, WattHour.Centi);
	}

	
	public final WattHour getPVEnergyYear()
	{
		return GetU32(14, WattHour.Centi);
	}

	
	public final WattHour getPVEnergyTotal()
	{
		return GetU32(18, WattHour.Centi);
	}

	
	public final WattHour getBatChargeEnergyToday()
	{
		return GetU32(22, WattHour.Centi);
	}

	
	public final WattHour getBatChargeEnergyMonth()
	{
		return GetU32(26, WattHour.Centi);
	}

	
	public final WattHour getBatChargeEnergyYear()
	{
		return GetU32(30, WattHour.Centi);
	}

	
	public final WattHour getBatChargeEnergyTotal()
	{
		return GetU32(34, WattHour.Centi);
	}

	
	public final WattHour getBatDischargeEnergyToday()
	{
		return GetU32(38, WattHour.Centi);
	}

	
	public final WattHour getBatDischargeEnergyMonth()
	{
		return GetU32(42, WattHour.Centi);
	}

	
	public final WattHour getBatDischargeEnergyYear()
	{
		return GetU32(46, WattHour.Centi);
	}

	
	public final WattHour getBatDischargeEnergyTotal()
	{
		return GetU32(50, WattHour.Centi);
	}

	
	public final WattHour getInvGenEnergyToday()
	{
		return GetU32(54, WattHour.Centi);
	}

	
	public final WattHour getInvGenEnergyMonth()
	{
		return GetU32(58, WattHour.Centi);
	}

	
	public final WattHour getInvGenEnergyYear()
	{
		return GetU32(62, WattHour.Centi);
	}

	
	public final WattHour getInvGenEnergyTotal()
	{
		return GetU32(66, WattHour.Centi);
	}

	
	public final WattHour getTotalLoadEnergyToday()
	{
		return GetU32(70, WattHour.Centi);
	}

	
	public final WattHour getTotalLoadEnergyMonth()
	{
		return GetU32(74, WattHour.Centi);
	}

	
	public final WattHour getTotalLoadEnergyYear()
	{
		return GetU32(78, WattHour.Centi);
	}

	
	public final WattHour getTotalLoadEnergyTotal()
	{
		return GetU32(82, WattHour.Centi);
	}

	
	public final WattHour getBackupLoadEnergyToday()
	{
		return GetU32(86, WattHour.Centi);
	}

	
	public final WattHour getBackupLoadEnergyMonth()
	{
		return GetU32(90, WattHour.Centi);
	}

	
	public final WattHour getBackupLoadEnergyYear()
	{
		return GetU32(94, WattHour.Centi);
	}

	
	public final WattHour getBackupLoadEnergyTotal()
	{
		return GetU32(98, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyTodayL1()
	{
		return GetU32(102, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyMonthL1()
	{
		return GetU32(106, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyYearL1()
	{
		return GetU32(110, WattHour.Centi);
	}

	
	public final WattHour getSellEnergyTotalL1()
	{
		return GetU32(114, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyTodayL1()
	{
		return GetU32(118, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyMonthL1()
	{
		return GetU32(122, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyYearL1()
	{
		return GetU32(126, WattHour.Centi);
	}

	
	public final WattHour getBuyEnergyTotalL1()
	{
		return GetU32(130, WattHour.Centi);
	}
}
