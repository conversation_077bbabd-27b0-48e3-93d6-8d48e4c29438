package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.BigDecimal;

/**
 * MeterHarmonicData.
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterHarmonicData {
    /**
     * Harmonic ID (0=Total, 3=3rd, 5=5th, 7=7th, etc).
     */
    private int HarmonicId;

    public final int getHarmonicId() {
        return HarmonicId;
    }

    public final void setHarmonicId(int value) {
        HarmonicId = value;
    }

    /**
     * Average over time period.
     */
    private BigDecimal Average = null;

    public final BigDecimal getAverage() {
        return Average;
    }

    public final void setAverage(BigDecimal value) {
        Average = value;
    }

    /**
     * Min over time period.
     */
    private BigDecimal Min = null;

    public final BigDecimal getMin() {
        return Min;
    }

    public final void setMin(BigDecimal value) {
        Min = value;
    }

    /**
     * Max over time period.
     */
    private BigDecimal Max = null;

    public final BigDecimal getMax() {
        return Max;
    }

    public final void setMax(BigDecimal value) {
        Max = value;
    }

    /**
     * Instantaneous.
     */
    private BigDecimal Instantaneous = null;

    public final BigDecimal getInstantaneous() {
        return Instantaneous;
    }

    public final void setInstantaneous(BigDecimal value) {
        Instantaneous = value;
    }
}
