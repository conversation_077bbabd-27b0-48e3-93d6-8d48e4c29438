package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;




 





public class BandBHF extends DataBackedBand
{
	public BandBHF()
	{
		super(BandForge.<BandBHF>getMetadataFor(BandBHF.class));
	}



	public BandBHF(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHF>getMetadataFor(BandBHF.class));
	}

	public BandBHF(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHF>getMetadataFor(BandBHF.class));
	}


	


	public final int getLeadBatCapacity() { return GetU16(0); }

	


	public final int getBattStrings() { return GetU16(2); }

	
	public final Volt getBattChargeVoltMax()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Ampere getBattChargeCurrMax()
	{
		return GetU16(6, Ampere.Deci);
	}

	
	public final Volt getBattVoltUnderMin()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Ampere getBattDisChgCurrMax()
	{
		return GetU16(10, Ampere.Deci);
	}

	
	public final BigDecimal getBattSOCUnderMin()
	{
		return new BigDecimal(GetU16(12)).multiply(Percentage._1);
	}

	
	public final Volt getBattOfflineVoltUnderMin()
	{
		return GetU16(14, Volt.Deci);
	}

	
	public final BigDecimal getBattOfflineSOCUnderMin()
	{
		return new BigDecimal(GetU16(16)).multiply(Percentage._1);
	}

	


	public final int getReserved0xB12F() { return GetU16(18); }
}
