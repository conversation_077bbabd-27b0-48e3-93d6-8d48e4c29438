package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.TelemetryErrorCodeEnum;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandMetadata;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class TelemetryExtensions {

    // Duration.FromMilliseconds(rossDeviceTelemetry.OuijaBoard.RossUpTimeMilliseconds)
    public static Duration getRossUptimeDuration(RossDeviceTelemetry rossDeviceTelemetry) {
        return Duration.ofMillis(rossDeviceTelemetry.getOuijaBoard().getRossUpTimeMilliseconds());
    }

    // Instant.FromDateTimeUtc(rossDeviceTelemetry.TimestampUtc) - duration
    public static Instant getRossStartTimeInstant(RossDeviceTelemetry rossDeviceTelemetry) {
        return rossDeviceTelemetry.getTimestampUtc()
                .minus(getRossUptimeDuration(rossDeviceTelemetry));
    }

    // frames.Select(f => f.AsTelemetry()).OrderBy(f => f.Name);
    public static <T extends DataBackedBand> List<TelemetryFrame> asTelemetry(List<IDataFrame<T>> frames) {
        return frames.stream()
                .map(TelemetryExtensions::asTelemetry)
                .sorted(Comparator.comparing(f -> f.getName()))
                .collect(Collectors.toList());
    }

    // new TelemetryFrame(...)
    public static <T extends DataBackedBand> TelemetryFrame asTelemetry(IDataFrame<T> frame) {
        ObjectMapper mapper = new ObjectMapper();
        byte[] bytes;
        try {
            bytes = mapper.writeValueAsBytes(frame.getData());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        String base64 = Base64.getEncoder().encodeToString(bytes);
        return new TelemetryFrame(
                frame.getData().getClass().getSimpleName(),
                frame.getTimestampUtc(),
                base64);
    }

    // RehydrateFrame: 使用 BandForge 动态重建 Frame
    public static IDataFrame<DataBackedBand> rehydrateFrame(TelemetryFrame telemetryFrame, Instant rossStartTime) {
        Instant frameCreation = telemetryFrame.getTimestampUtc();
        BandMetadata bandMetadata = BandForge.GetMetadataForBand(telemetryFrame.getName());

        return bandMetadata.RehydrateFrameFor(telemetryFrame, rossStartTime);
    }

    public static void rebuildDataBackedBands(RossDeviceTelemetry telemetry) {
        List<TelemetryFrame> badBands = new ArrayList<>();
        Instant rossStartTime = getRossStartTimeInstant(telemetry);

        if (telemetry.getInverter() != null && telemetry.getInverter().getRawDataBands() != null) {
            List<IDataFrame<DataBackedBand>> inverterBands = new ArrayList<>();

            for (TelemetryFrame frame : telemetry.getInverter().getRawDataBands()) {
                try {
                    if (frame == null || frame.getData() == null || frame.getData().trim().isEmpty()) {
                        badBands.add(frame);
                    } else {
                        inverterBands.add(rehydrateFrame(frame, rossStartTime));
                    }
                } catch (Exception ex) {
                    badBands.add(frame);
                }
            }

            telemetry.getInverter().setDataBands(inverterBands);
        }

        if (telemetry.getBatteries() != null && telemetry.getBatteries().getCommsUnits() != null) {
            for (Map.Entry<String, BatteryCommsUnitTelemetry> entry : telemetry.getBatteries().getCommsUnits().entrySet()) {
                BatteryCommsUnitTelemetry commsUnit = entry.getValue();
                if (commsUnit == null || commsUnit.getRawDataBands() == null) {
                    continue;
                }

                List<IDataFrame<DataBackedBand>> batteryBands = new ArrayList<>();

                for (TelemetryFrame frame : commsUnit.getRawDataBands()) {
                    try {
                        if (frame == null || frame.getData() == null) {
                            badBands.add(frame);
                        } else {
                            batteryBands.add(rehydrateFrame(frame, rossStartTime));
                        }
                    } catch (Exception ex) {
                        badBands.add(frame);
                    }
                }

                commsUnit.setDataBands(batteryBands);
            }
        }

        if (!badBands.isEmpty() &&
                !telemetry.getErrors().stream().anyMatch(e -> e.getErrorCode() == TelemetryErrorCodeEnum.UnknownTelemetryBandType)) {
            Set<String> distinctBadNames = badBands.stream()
                    .map(TelemetryFrame::getName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            for (String name : distinctBadNames) {
                telemetry.getErrors().add(new ErrorTelemetry(
                        TelemetryErrorCodeEnum.UnknownTelemetryBandType,
                        "Invalid band found in telemetry: " + name,
                        ZonedDateTime.now()
                ));
            }
        }
    }

}