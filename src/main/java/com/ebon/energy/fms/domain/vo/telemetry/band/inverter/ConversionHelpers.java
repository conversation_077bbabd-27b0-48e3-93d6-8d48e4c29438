package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class ConversionHelpers {

	/**
	 * Converts the inverter representative value to a decimal representing leading or lagging power factor.
	 * @param powerFactorIndicator The inverter representative value for power factor
	 * @return The converted decimal value for power factor (leading is negative, 1 is unity, 0 is error/unknown)
	 */
	public static BigDecimal convertToPowerFactor(int powerFactorIndicator) {
		if (powerFactorIndicator <= 20) {
			return new BigDecimal((100.0 - powerFactorIndicator) / 100.0);
		}

		if (powerFactorIndicator >= 80 && powerFactorIndicator < 100) {
			return new BigDecimal((powerFactorIndicator / 100.0) * -1.0);
		}

		return (powerFactorIndicator == 100) ? new BigDecimal("1.0") : new BigDecimal("0.0");
	}

	/**
	 * Converts the decimal value for power factor to an inverter representative value.
	 * @param powerFactor The decimal value for power factor (leading is negative, 1 is unity, 0 is error/unknown)
	 * @return The converted inverter representative value for power factor
	 */
	public static Short convertFromPowerFactor(Double powerFactor) {
		if (powerFactor == null) {
			return null;
		}

		if (powerFactor <= -0.8 && powerFactor >= -0.99) {
			return (short)(powerFactor * -100.0);
		} else if (powerFactor >= 0.8 && powerFactor < 1.0) {
			return (short)(100 - (powerFactor * 100.0));
		}

		return (short)((powerFactor == 1.0) ? 100 : 0);
	}

	public static LocalDateTime convertToEpoch(long epoch) {
		try {
			return Instant.ofEpochMilli(epoch)
					.atZone(ZoneId.systemDefault())
					.toLocalDateTime();
		} catch (Exception e) {
			return LocalDateTime.MIN;
		}
	}

	public static long convertFromEpoch(LocalDateTime now) {
		try {
			return now.atZone(ZoneId.systemDefault())
					.toInstant()
					.toEpochMilli();
		} catch (Exception e) {
			return 0;
		}
	}
}
