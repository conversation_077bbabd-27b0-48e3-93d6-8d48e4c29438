package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandHVP1CellTemperature extends DataBackedBand
{
	public BandHVP1CellTemperature()
	{
		super(BandForge.<BandHVP1CellTemperature>getMetadataFor(BandHVP1CellTemperature.class));
	}



	public BandHVP1CellTemperature(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVP1CellTemperature>getMetadataFor(BandHVP1CellTemperature.class));
	}

	public BandHVP1CellTemperature(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVP1CellTemperature>getMetadataFor(BandHVP1CellTemperature.class));
	}


	
	public final Celsius getTemperatureOfCellNumber000()
	{
		return GetS16(0, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber001()
	{
		return GetS16(2, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber002()
	{
		return GetS16(4, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber003()
	{
		return GetS16(6, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber004()
	{
		return GetS16(8, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber005()
	{
		return GetS16(10, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber006()
	{
		return GetS16(12, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber007()
	{
		return GetS16(14, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber008()
	{
		return GetS16(16, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber009()
	{
		return GetS16(18, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber010()
	{
		return GetS16(20, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber011()
	{
		return GetS16(22, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber012()
	{
		return GetS16(24, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber013()
	{
		return GetS16(26, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber014()
	{
		return GetS16(28, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber015()
	{
		return GetS16(30, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber016()
	{
		return GetS16(32, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber017()
	{
		return GetS16(34, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber018()
	{
		return GetS16(36, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber019()
	{
		return GetS16(38, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber020()
	{
		return GetS16(40, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber021()
	{
		return GetS16(42, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber022()
	{
		return GetS16(44, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber023()
	{
		return GetS16(46, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber024()
	{
		return GetS16(48, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber025()
	{
		return GetS16(50, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber026()
	{
		return GetS16(52, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber027()
	{
		return GetS16(54, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber028()
	{
		return GetS16(56, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber029()
	{
		return GetS16(58, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber030()
	{
		return GetS16(60, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber031()
	{
		return GetS16(62, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber032()
	{
		return GetS16(64, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber033()
	{
		return GetS16(66, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber034()
	{
		return GetS16(68, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber035()
	{
		return GetS16(70, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber036()
	{
		return GetS16(72, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber037()
	{
		return GetS16(74, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber038()
	{
		return GetS16(76, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber039()
	{
		return GetS16(78, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber040()
	{
		return GetS16(80, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber041()
	{
		return GetS16(82, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber042()
	{
		return GetS16(84, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber043()
	{
		return GetS16(86, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber044()
	{
		return GetS16(88, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber045()
	{
		return GetS16(90, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber046()
	{
		return GetS16(92, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber047()
	{
		return GetS16(94, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber048()
	{
		return GetS16(96, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber049()
	{
		return GetS16(98, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber050()
	{
		return GetS16(100, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber051()
	{
		return GetS16(102, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber052()
	{
		return GetS16(104, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber053()
	{
		return GetS16(106, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber054()
	{
		return GetS16(108, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber055()
	{
		return GetS16(110, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber056()
	{
		return GetS16(112, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber057()
	{
		return GetS16(114, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber058()
	{
		return GetS16(116, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber059()
	{
		return GetS16(118, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber060()
	{
		return GetS16(120, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber061()
	{
		return GetS16(122, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber062()
	{
		return GetS16(124, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber063()
	{
		return GetS16(126, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber064()
	{
		return GetS16(128, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber065()
	{
		return GetS16(130, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber066()
	{
		return GetS16(132, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber067()
	{
		return GetS16(134, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber068()
	{
		return GetS16(136, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber069()
	{
		return GetS16(138, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber070()
	{
		return GetS16(140, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber071()
	{
		return GetS16(142, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber072()
	{
		return GetS16(144, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber073()
	{
		return GetS16(146, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber074()
	{
		return GetS16(148, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber075()
	{
		return GetS16(150, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber076()
	{
		return GetS16(152, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber077()
	{
		return GetS16(154, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber078()
	{
		return GetS16(156, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber079()
	{
		return GetS16(158, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber080()
	{
		return GetS16(160, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber081()
	{
		return GetS16(162, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber082()
	{
		return GetS16(164, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber083()
	{
		return GetS16(166, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber084()
	{
		return GetS16(168, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber085()
	{
		return GetS16(170, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber086()
	{
		return GetS16(172, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber087()
	{
		return GetS16(174, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber088()
	{
		return GetS16(176, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber089()
	{
		return GetS16(178, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber090()
	{
		return GetS16(180, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber091()
	{
		return GetS16(182, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber092()
	{
		return GetS16(184, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber093()
	{
		return GetS16(186, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber094()
	{
		return GetS16(188, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber095()
	{
		return GetS16(190, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber096()
	{
		return GetS16(192, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber097()
	{
		return GetS16(194, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber098()
	{
		return GetS16(196, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber099()
	{
		return GetS16(198, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber100()
	{
		return GetS16(200, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber101()
	{
		return GetS16(202, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber102()
	{
		return GetS16(204, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber103()
	{
		return GetS16(206, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber104()
	{
		return GetS16(208, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber105()
	{
		return GetS16(210, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber106()
	{
		return GetS16(212, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber107()
	{
		return GetS16(214, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber108()
	{
		return GetS16(216, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber109()
	{
		return GetS16(218, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber110()
	{
		return GetS16(220, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber111()
	{
		return GetS16(222, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber112()
	{
		return GetS16(224, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber113()
	{
		return GetS16(226, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber114()
	{
		return GetS16(228, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber115()
	{
		return GetS16(230, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber116()
	{
		return GetS16(232, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber117()
	{
		return GetS16(234, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber118()
	{
		return GetS16(236, Celsius.Deci);
	}

	
	public final Celsius getTemperatureOfCellNumber119()
	{
		return GetS16(238, Celsius.Deci);
	}
}
