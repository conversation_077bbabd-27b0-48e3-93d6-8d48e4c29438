package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import java.math.*;

public class BandPylonUSStackInfo extends DataBackedBand
{
	public BandPylonUSStackInfo()
	{
		super(BandForge.<BandPylonUSStackInfo>getMetadataFor(BandPylonUSStackInfo.class));
	}



	public BandPylonUSStackInfo(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylonUSStackInfo>getMetadataFor(BandPylonUSStackInfo.class));
	}

	public BandPylonUSStackInfo(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylonUSStackInfo>getMetadataFor(BandPylonUSStackInfo.class));
	}


	
	public final Volt getAvgVoltage()
	{
		return GetU16(0, Volt.Milli);
	}

	
	public final Ampere getTotalCurrent()
	{
		return GetS16(2, Ampere.Centi);
	}

	
	public final BigDecimal getSOC()
	{
		return new BigDecimal(GetU8(4)).multiply(Percentage._1);
	}

	


	public final int getNumCyclesAvg() { return GetU16(5); }

	


	public final int getNumCyclesMax() { return GetU16(7); }

	public final BigDecimal getSOHAvg()
	{
		return new BigDecimal(GetU8(9)).multiply(Percentage._1);
	}

	
	public final BigDecimal getSOHMin()
	{
		return new BigDecimal(GetU8(10)).multiply(Percentage._1);
	}

	
	public final Volt getCellVoltageMax()
	{
		return GetS16(11, Volt.Milli);
	}

	


	public final int getCellVoltageMaxModuleNum() { return GetU16(13); }

	
	public final Volt getCellVoltageMin()
	{
		return GetS16(15, Volt.Milli);
	}

	


	public final int getCellVoltageMinModuleNum() { return GetU16(17); }

	
	public final Celsius getCellTempAvg()
	{
		return GetS16(19,Kelvin.Deci).ToCelsius();
	}

	
	public final Celsius getCellTempMax()
	{
		return GetS16(21,Kelvin.Deci).ToCelsius();
	}

	


	public final int getCellTempMaxModuleNum() { return GetU16(23); }

	
	public final Celsius getCellTempMin()
	{
		return GetS16(25,Kelvin.Deci).ToCelsius();
	}

	


	public final int getCellTempMinModuleNum() { return GetU16(27); }

	
	public final Celsius getMOSFETTempAvg()
	{
		return GetS16(29,Kelvin.Deci).ToCelsius();
	}

	
	public final Celsius getMOSFETTempMax()
	{
		return GetS16(31,Kelvin.Deci).ToCelsius();
	}

	


	public final int getMOSFETTempMaxModuleNum() { return GetU16(33); }

	
	public final Celsius getMOSFETTempMin()
	{
		return GetS16(35,Kelvin.Deci).ToCelsius();
	}

	


	public final int getMOSFETTempMinModuleNum() { return GetU16(37); }

	
	public final Celsius getBMSTempAvg()
	{
		return GetS16(39,Kelvin.Deci).ToCelsius();
	}

	
	public final Celsius getBMSTempMax()
	{
		return GetS16(41,Kelvin.Deci).ToCelsius();
	}

	


	public final int getBMSTempMaxModuleNum() { return GetU16(43); }

	
	public final Celsius getBMSTempMin()
	{
		return GetS16(45,Kelvin.Deci).ToCelsius();
	}

	


	public final int getBMSTempMinModuleNum() { return GetU16(47); }
}
