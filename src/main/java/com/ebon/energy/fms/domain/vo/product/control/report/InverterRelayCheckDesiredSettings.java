package com.ebon.energy.fms.domain.vo.product.control.report;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InverterRelayCheckDesiredSettings implements Serializable {
    @JsonProperty("Enabled")
    private Boolean enabled;

    @JsonProperty("MaximumAttempts")
    private Integer maximumAttempts;

    @JsonProperty("LowPVAttempts")
    private Integer lowPVAttempts;

    @JsonProperty("PVVoltageMinimum")
    private BigDecimal pvVoltageMinimum;

    @JsonProperty("FailureStateDuration")
    private Duration failureStateDuration;

    @JsonProperty("SuccessStateDuration")
    private Duration successStateDuration;

    @JsonProperty("RebootGracePeriod")
    private Duration rebootGracePeriod;

    public static InverterRelayCheckRossInternalSettings withRossDefaults(InverterRelayCheckDesiredSettings desired) {
        return InverterRelayCheckRossInternalSettings.builder()
                .enabled(desired != null && desired.getEnabled() != null ? desired.getEnabled() : false)
                .maximumAttempts(desired != null && desired.getMaximumAttempts() != null ? desired.getMaximumAttempts() : 3)
                .lowPVAttempts(desired != null && desired.getLowPVAttempts() != null ? desired.getLowPVAttempts() : 1)
                .pvVoltageMinimum(desired != null && desired.getPvVoltageMinimum() != null ? desired.getPvVoltageMinimum() : new BigDecimal("250"))
                .failureStateDuration(desired != null && desired.getFailureStateDuration() != null ? desired.getFailureStateDuration() : Duration.ofMinutes(1))
                .successStateDuration(desired != null && desired.getSuccessStateDuration() != null ? desired.getSuccessStateDuration() : Duration.ofMinutes(1))
                .rebootGracePeriod(desired != null && desired.getRebootGracePeriod() != null ? desired.getRebootGracePeriod() : Duration.ofHours(1))
                .build();
    }
}