package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandSGInverterIsolation extends DataBackedBand
{
	public BandSGInverterIsolation()
	{
		super(BandForge.<BandSGInverterIsolation>getMetadataFor(BandSGInverterIsolation.class));
	}



	public BandSGInverterIsolation(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterIsolation>getMetadataFor(BandSGInverterIsolation.class));
	}

	public BandSGInverterIsolation(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterIsolation>getMetadataFor(BandSGInverterIsolation.class));
	}



	public final Ohm getIsolationTestResult()
	{
		return GetU16(0, Ohm.Kilo);
	}
}
