package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.BMSStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandEH1PAdditionalBMSSettings extends DataBackedBand
{
	public BandEH1PAdditionalBMSSettings()
	{
		super(BandForge.<BandEH1PAdditionalBMSSettings>getMetadataFor(BandEH1PAdditionalBMSSettings.class));
	}



	public BandEH1PAdditionalBMSSettings(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PAdditionalBMSSettings>getMetadataFor(BandEH1PAdditionalBMSSettings.class));
	}

	public BandEH1PAdditionalBMSSettings(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PAdditionalBMSSettings>getMetadataFor(BandEH1PAdditionalBMSSettings.class));
	}


	
	public final Volt getBMSDischargeVoltageLimit()
	{
		return GetU16(0, Volt.Deci);
	}

	
	public final Object getBMSStatus()
	{
		return BMSStatus.parse(GetU16(2));
	}
}
