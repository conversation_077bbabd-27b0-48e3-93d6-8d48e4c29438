package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.SGMeterStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandSGMeterStatus extends DataBackedBand
{
	public BandSGMeterStatus()
	{
		super(BandForge.<BandSGMeterStatus>getMetadataFor(BandSGMeterStatus.class));
	}



	public BandSGMeterStatus(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterStatus>getMetadataFor(BandSGMeterStatus.class));
	}

	public BandSGMeterStatus(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterStatus>getMetadataFor(BandSGMeterStatus.class));
	}


	
	public final Object getMeterStatus()
	{
		return SGMeterStatus.parse(GetU16(0));
	}

	
	public final boolean getRelay1Command()
	{
		return GetBool((int) GetU16(2), 1, 0, false);
	}
}
