package com.ebon.energy.fms.domain.vo.product.control;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class BatteryFirmwareDesiredSettings {
    private Boolean force;
    private String token;
    private String pylonHVCmu;
    private String pylonHVBmu;
    private String pylonLVFw;
    private Byte packIndex;

    // Extension methods as static utility methods in Java
    public String getCompleteTokenString() {
        if (this == null) return null;
        return String.format("%s.%s.%s", pylonHVCmu, pylonHVBmu, token);
    }

    public BatteryFirmwareReportedSettings translateToReported() {
        if (this == null) return null;
        return new BatteryFirmwareReportedSettings(pylonHVCmu, pylonHVBmu);
    }
}
