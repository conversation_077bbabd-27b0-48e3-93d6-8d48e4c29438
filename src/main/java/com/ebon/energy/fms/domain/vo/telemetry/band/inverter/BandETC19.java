package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.MeterConnectStatus;
import com.ebon.energy.fms.common.enums.MeterType;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandETC19 extends DataBackedBand implements IBandETC
{
	public BandETC19()
	{
		super(BandForge.<BandETC19>getMetadataFor(BandETC19.class));
	}



	public BandETC19(byte[] bytes)
	{
		super(bytes, BandForge.<BandETC19>getMetadataFor(BandETC19.class));
	}

	public BandETC19(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandETC19>getMetadataFor(BandETC19.class));
	}


	


	public final int getCommunicationsMode() { return GetU16(0); }

	


	public final int getRSSI() { return GetU16(2); }

	


	public final int getManufacturerCode() { return GetU16(4); }

	
	public final Object getMeterConnectStatusL1()
	{


		return MeterConnectStatus.parse(GetMaskedU16(6,  (short) 3, (short) 0));
	}

	public final Object getMeterConnectStatusL2()
	{


		return MeterConnectStatus.parse(GetMaskedU16(6,  (short)48, (short) 4));
	}

	public final Object getMeterConnectStatusL3()
	{


		return MeterConnectStatus.parse(GetMaskedU16(6,  (short)768, (short) 8));
	}

	
	public final boolean getMeterStatus()
	{
		return GetBool((int) GetU16(8), 1, 0, false);
	}

	
	public final Watt getMTActivePowerL1Unused()
	{
		return GetS16(10, Watt.Unit);
	}

	
	public final Watt getMTActivePowerL2Unused()
	{
		return GetS16(12, Watt.Unit);
	}

	
	public final Watt getMTActivePowerL3Unused()
	{
		return GetS16(14, Watt.Unit);
	}

	
	public final Watt getMTTotalActivePowerUnused()
	{
		return GetS16(16, Watt.Unit);
	}

	
	public final VoltAmpsReactive getMTTotalReactivePowerUnused()
	{
		return GetU16(18, VoltAmpsReactive.Unit);
	}

	
	public final BigDecimal getPhaseL1PowerFactor()
	{
		return new BigDecimal(GetS16(20)).multiply(Percentage._01);
	}

	
	public final BigDecimal getPhaseL2PowerFactor()
	{
		return new BigDecimal(GetS16(22)).multiply(Percentage._01);
	}

	
	public final BigDecimal getPhaseL3PowerFactor()
	{
		return new BigDecimal(GetS16(24)).multiply(Percentage._01);
	}

	
	public final BigDecimal getTotalPowerfactor()
	{
		return new BigDecimal(GetS16(26)).multiply(Percentage._01);
	}

	
	public final Frequency getFrequency()
	{
		return GetU16(28, Frequency.Centi);
	}

	
	public final WattHour getETotalSell()
	{
		return GetFloat(30, WattHour.Unit);
	}

	
	public final WattHour getETotalBuy()
	{
		return GetFloat(34, WattHour.Unit);
	}

	
	public final Watt getMTActivePowerL1()
	{
		return GetS32(38, Watt.Unit);
	}

	
	public final Watt getMTActivePowerL2()
	{
		return GetS32(42, Watt.Unit);
	}

	
	public final Watt getMTActivePowerL3()
	{
		return GetS32(46, Watt.Unit);
	}

	
	public final Watt getMTTotalActivePower()
	{
		return GetS32(50, Watt.Unit);
	}

	
	public final VoltAmpsReactive getMTReactivePowerL1()
	{
		return GetS32(54, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmpsReactive getMTReactivePowerL2()
	{
		return GetS32(58, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmpsReactive getMTReactivePowerL3()
	{
		return GetS32(62, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmpsReactive getMTTotalReactivePower()
	{
		return GetS32(66, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmps getMTApparentPowerL1()
	{
		return GetS32(70, VoltAmps.Unit);
	}

	
	public final VoltAmps getMTApparentPowerL2()
	{
		return GetS32(74, VoltAmps.Unit);
	}

	
	public final VoltAmps getMTApparentPowerL3()
	{
		return GetS32(78, VoltAmps.Unit);
	}

	
	public final VoltAmps getMTTotalApparentPower()
	{
		return GetS32(82, VoltAmps.Unit);
	}

	
	public final Object getMeterType()
	{
		return MeterType.parse(GetU16(86));
	}

	


	public final int getMeterSoftwareVersion() { return GetU16(88); }

	
	public final Watt getMeterCT2Activepower()
	{
		return GetS32(90, Watt.Unit);
	}

	


	public final int getCT2ETotalSell()
	{
		return GetU32(94);
	}

	


	public final int getCT2ETotalBuy()
	{
		return GetU32(98);
	}

	
	public final boolean getMeterCT2Status()
	{
		return GetBool((int) GetU16(102), 1, 0, false);
	}

	
	public final Volt getMeterVoltageL1()
	{
		return GetU16(104, Volt.Deci);
	}

	
	public final Volt getMeterVoltageL2()
	{
		return GetU16(106, Volt.Deci);
	}

	
	public final Volt getMeterVoltageL3()
	{
		return GetU16(108, Volt.Deci);
	}

	
	public final Ampere getMeterCurrentL1()
	{
		return GetU16(110, Ampere.Deci);
	}

	
	public final Ampere getMeterCurrentL2()
	{
		return GetU16(112, Ampere.Deci);
	}

	
	public final Ampere getMeterCurrentL3()
	{
		return GetU16(114, Ampere.Deci);
	}
}
