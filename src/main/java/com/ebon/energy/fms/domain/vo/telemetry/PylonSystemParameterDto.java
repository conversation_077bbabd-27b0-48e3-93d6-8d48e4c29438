package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.PylonInfoFlagState;
import com.ebon.energy.fms.domain.vo.Ampere;
import com.ebon.energy.fms.domain.vo.Kelvin;
import com.ebon.energy.fms.domain.vo.Volt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonSystemParameterDto extends PylonDto {
    private PylonInfoFlagState InfoFlag;
    private Volt UnitCellVoltage;
    private Volt UnitCellLowVolt;
    private Volt UnitCellUnderVolt;
    private Kelvin ChargeUpperLimitTemp;
    private Kelvin ChargeLowerLimitTemp;
    private Ampere ChargeUpperLimitCurrent;
    private Volt UpperLimitVoltTotal;
    private Volt LowerLimitVoltTotal;
    private Volt UnderVoltOfTotal;
    private Kelvin DischargeUpperLimitTemp;
    private Kelvin DischargeLowerLimitTemp;
    private Ampere DischargeUpperLimitCurrent;

    public PylonSystemParameterDto(
            String rawMessage,
            PylonInfoFlagState infoFlag,
            Volt unitCellVoltage,
            Volt unitCellLowVolt,
            Volt unitCellUnderVolt,
            Kelvin chargeUpperLimitTemp,
            Kelvin chargeLowerLimitTemp,
            Ampere chargeUpperLimitCurrent,
            Volt upperLimitVoltTotal,
            Volt lowerLimitVoltTotal,
            Volt underVoltOfTotal,
            Kelvin dischargeUpperLimitTemp,
            Kelvin dischargeLowerLimitTemp,
            Ampere dischargeUpperLimitCurrent) {

        super(rawMessage);
        this.InfoFlag = infoFlag;
        this.UnitCellVoltage = unitCellVoltage;
        this.UnitCellLowVolt = unitCellLowVolt;
        this.UnitCellUnderVolt = unitCellUnderVolt;
        this.ChargeUpperLimitTemp = chargeUpperLimitTemp;
        this.ChargeLowerLimitTemp = chargeLowerLimitTemp;
        this.ChargeUpperLimitCurrent = chargeUpperLimitCurrent;
        this.UpperLimitVoltTotal = upperLimitVoltTotal;
        this.LowerLimitVoltTotal = lowerLimitVoltTotal;
        this.UnderVoltOfTotal = underVoltOfTotal;
        this.DischargeUpperLimitTemp = dischargeUpperLimitTemp;
        this.DischargeLowerLimitTemp = dischargeLowerLimitTemp;
        this.DischargeUpperLimitCurrent = dischargeUpperLimitCurrent;
    }

    // Getters and Setters
    public PylonInfoFlagState getInfoFlag() { return InfoFlag; }
    public void setInfoFlag(PylonInfoFlagState infoFlag) { this.InfoFlag = infoFlag; }

    public Volt getUnitCellVoltage() { return UnitCellVoltage; }
    public void setUnitCellVoltage(Volt unitCellVoltage) { this.UnitCellVoltage = unitCellVoltage; }

    public Volt getUnitCellLowVolt() { return UnitCellLowVolt; }
    public void setUnitCellLowVolt(Volt unitCellLowVolt) { this.UnitCellLowVolt = unitCellLowVolt; }

    public Volt getUnitCellUnderVolt() { return UnitCellUnderVolt; }
    public void setUnitCellUnderVolt(Volt unitCellUnderVolt) { this.UnitCellUnderVolt = unitCellUnderVolt; }

    public Kelvin getChargeUpperLimitTemp() { return ChargeUpperLimitTemp; }
    public void setChargeUpperLimitTemp(Kelvin chargeUpperLimitTemp) { this.ChargeUpperLimitTemp = chargeUpperLimitTemp; }

    public Kelvin getChargeLowerLimitTemp() { return ChargeLowerLimitTemp; }
    public void setChargeLowerLimitTemp(Kelvin chargeLowerLimitTemp) { this.ChargeLowerLimitTemp = chargeLowerLimitTemp; }

    public Ampere getChargeUpperLimitCurrent() { return ChargeUpperLimitCurrent; }
    public void setChargeUpperLimitCurrent(Ampere chargeUpperLimitCurrent) { this.ChargeUpperLimitCurrent = chargeUpperLimitCurrent; }

    public Volt getUpperLimitVoltTotal() { return UpperLimitVoltTotal; }
    public void setUpperLimitVoltTotal(Volt upperLimitVoltTotal) { this.UpperLimitVoltTotal = upperLimitVoltTotal; }

    public Volt getLowerLimitVoltTotal() { return LowerLimitVoltTotal; }
    public void setLowerLimitVoltTotal(Volt lowerLimitVoltTotal) { this.LowerLimitVoltTotal = lowerLimitVoltTotal; }

    public Volt getUnderVoltOfTotal() { return UnderVoltOfTotal; }
    public void setUnderVoltOfTotal(Volt underVoltOfTotal) { this.UnderVoltOfTotal = underVoltOfTotal; }

    public Kelvin getDischargeUpperLimitTemp() { return DischargeUpperLimitTemp; }
    public void setDischargeUpperLimitTemp(Kelvin dischargeUpperLimitTemp) { this.DischargeUpperLimitTemp = dischargeUpperLimitTemp; }

    public Kelvin getDischargeLowerLimitTemp() { return DischargeLowerLimitTemp; }
    public void setDischargeLowerLimitTemp(Kelvin dischargeLowerLimitTemp) { this.DischargeLowerLimitTemp = dischargeLowerLimitTemp; }

    public Ampere getDischargeUpperLimitCurrent() { return DischargeUpperLimitCurrent; }
    public void setDischargeUpperLimitCurrent(Ampere dischargeUpperLimitCurrent) { this.DischargeUpperLimitCurrent = dischargeUpperLimitCurrent; }
}