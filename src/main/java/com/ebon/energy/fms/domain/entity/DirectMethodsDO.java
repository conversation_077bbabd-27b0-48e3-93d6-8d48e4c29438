package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;

/**
 * 直接方法配置实体类
 */
@Data
@Accessors(chain = true)
@TableName("DirectMethods")
public class DirectMethodsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID (自增长)
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 方法标题
     */
    @TableField("Title")
    private String title;

    /**
     * 方法名称
     */
    @TableField("MethodName")
    private String methodName;

    /**
     * 方法负载数据(JSON格式)
     */
    @TableField("Payload")
    private String payload;

    /**
     * 设备类型
     */
    @TableField("DeviceType")
    private String deviceType;

    /**
     * 最后修改时间(UTC)
     */
    @TableField("LastModifiedUtc")
    private Date lastModifiedUtc;

    /**
     * 创建人
     */
    @TableField("CreatedBy")
    private String createdBy;
}