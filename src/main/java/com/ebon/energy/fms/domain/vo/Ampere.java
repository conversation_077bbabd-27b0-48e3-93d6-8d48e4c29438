package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Ampere implements IUnit {
    public static final Ampere Unit = new Ampere(new BigDecimal("1.0"));
    public static final Ampere Zero = new Ampere(new BigDecimal("0.0"));
    public static final Ampere Tera = new Ampere(new BigDecimal("1000000000000"));
    public static final Ampere Giga = new Ampere(new BigDecimal("1000000000"));
    public static final Ampere Mega = new Ampere(new BigDecimal("1000000"));
    public static final Ampere Kilo = new Ampere(new BigDecimal("1000"));
    public static final Ampere Hecto = new Ampere(new BigDecimal("100"));
    public static final Ampere Deca = new Ampere(new BigDecimal("10"));
    public static final Ampere Deci = new Ampere(new BigDecimal("0.1"));
    public static final Ampere Centi = new Ampere(new BigDecimal("0.01"));
    public static final Ampere Milli = new Ampere(new BigDecimal("0.001"));

    public static final String SYMBOL = "A";

    private BigDecimal value;

    public Ampere() {
    }

    public Ampere(BigDecimal value) {
        this.value = value;
    }

    public Ampere(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Ampere(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getAmpereValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    @Override
    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    @Override
    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public BigDecimal asDecimal(Ampere unit) {
        return value.divide(unit.value);
    }

    public int asInt() {
        return value.divide(Unit.value).intValue();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }

    public Ampere add(Ampere other) {
        return new Ampere(value.add(other.value));
    }

    public Ampere subtract(Ampere other) {
        return new Ampere(value.subtract(other.value));
    }

    public Ampere multiply(BigDecimal multiplier) {
        return new Ampere(value.multiply(multiplier));
    }

    public Ampere divide(BigDecimal divisor) {
        return new Ampere(value.divide(divisor));
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Ampere)) {
            return false;
        }
        Ampere other = (Ampere) obj;
        return value.compareTo(other.value) == 0;
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    public int compareTo(Ampere other) {
        return value.compareTo(other.value);
    }
}