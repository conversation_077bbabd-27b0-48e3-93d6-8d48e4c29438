package com.ebon.energy.fms.domain.vo.product.control.invert;

import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleDays;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.FutureOrPresent;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Past;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GenericScheduleItemViewModel implements ICalendarEvent {

    @JsonProperty("Id")
    private String id;

    @NotNull
    @JsonProperty("StartTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX") // ISO-8601 标准格式
    private OffsetDateTime startTime;

    @NotNull
    @JsonProperty("EndTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX") // ISO-8601 标准格式
    private OffsetDateTime endTime;

    @JsonProperty("IsRecurringDaily")
    private Boolean isRecurringDaily;

    @JsonProperty("ScheduleDays")
    private ScheduleDays scheduleDays;

    @NotNull
    @JsonProperty("Timezone")
    private String timezone ="Australia/Brisbane";

    @JsonProperty("HideDelete")
    private Boolean hideDelete = false;


    public ScheduleDays getScheduleDaysInternal() {
        return scheduleDays == null ? (isRecurringDaily ? ScheduleDays.EVERYDAY : null) : scheduleDays;
    }

    public void validate() {
        if (isRecurringDaily) {
            return;
        }

        if (endTime.atZoneSimilarLocal(TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(Optional.ofNullable(timezone).orElse("Australia/Brisbane"))).isBefore(ZonedDateTime.now())) {
            throw new BizException("The scheduled end time cannot be in the past.");
        }

        if (startTime.isEqual(endTime)) {
            throw new BizException("Date range duration is zero, end date is equal to start date.");
        }

        if (startTime.isAfter(endTime)) {
            throw new BizException("Date range duration is negative, end date proceeds start date.");
        }
    }


    public static Boolean doesOverlapWith(GenericScheduleItemViewModel a, GenericScheduleItemViewModel b){
        if (Boolean.TRUE.equals(a.getIsRecurringDaily()) || Boolean.TRUE.equals(b.getIsRecurringDaily())) {
            // Compare only the time-of-day portion
            java.time.LocalTime aStart = a.getStartTime().toLocalTime();
            java.time.LocalTime aEnd = a.getEndTime().toLocalTime();
            java.time.LocalTime bStart = b.getStartTime().toLocalTime();
            java.time.LocalTime bEnd = b.getEndTime().toLocalTime();
            java.time.LocalTime latestStart = aStart.isAfter(bStart) ? aStart : bStart;
            java.time.LocalTime earliestEnd = aEnd.isBefore(bEnd) ? aEnd : bEnd;
            return latestStart.isBefore(earliestEnd);
        } else {
            java.time.OffsetDateTime aStart = a.getStartTime();
            java.time.OffsetDateTime aEnd = a.getEndTime();
            java.time.OffsetDateTime bStart = b.getStartTime();
            java.time.OffsetDateTime bEnd = b.getEndTime();
            java.time.OffsetDateTime latestStart = aStart.isAfter(bStart) ? aStart : bStart;
            java.time.OffsetDateTime earliestEnd = aEnd.isBefore(bEnd) ? aEnd : bEnd;
            return latestStart.isBefore(earliestEnd);
        }
    }
}
