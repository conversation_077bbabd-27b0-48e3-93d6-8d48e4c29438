package com.ebon.energy.fms.domain.vo.setting.provider.types;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.RossSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.SGSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.path.SGSettingPaths;
import com.ebon.energy.fms.domain.vo.setting.provider.ICommonSettingsBuilder;
import com.ebon.energy.fms.domain.vo.setting.provider.RossSettingPaths;
import com.ebon.energy.fms.domain.vo.setting.provider.SchedulePriority;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.fasterxml.jackson.databind.JsonNode;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class SGSettingsBuilder extends  SettingsBuilder<SGSettingsReader, SGSettingPaths> {

    public SGSettingsBuilder(DeviceInfoAndSettings deviceInfoAndSettings, Instant nowInUtc) {
        super(deviceInfoAndSettings, nowInUtc, new SGSettingsReader(deviceInfoAndSettings), new SGSettingPaths());
    }

    @Override
    public ICommonSettingsBuilder patchManagedInverterSetting(String settingName, JsonNode value, String settingIndex, ManagedInverterSettingDesired.InverterSettingValueType settingType, ManagedInverterSettingDesired.InverterSettingExecutionType executionType, ManagedInverterSettingDesired.InverterSettingSource source, String uniqueId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder buildInverterPatch(RossDesiredSettings desired, List<ManagedInverterSettingDesired> expectedSettings, Map<String, Object> toPatch) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addShadowScan(boolean enableShadowScan) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addACCoupled(boolean enableACCoupledMode) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(BigDecimal powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(Double powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteExportLimit(Watt limit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftAndHardSiteExportLimit(Watt soft, Watt hard) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteGenerationLimit(VoltAmps generationLimit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSiteExportLimit(boolean enabled, Watt power) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder overrideDesiredPowerRampRateLimit(Duration rampTime, String settingId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addBatterySettings(ManufacturerEnum manufacturer, Integer batteryCount, Integer maxChargeCurrent, Integer maxDischargeCurrent, Integer minSoc, Integer minOffgridSoc) {
        throw new BizException("Smart Inverter models do not support battery settings.");
    }

    @Override
    public ICommonSettingsBuilder addCtFlipSettings(Boolean flipCt1, Boolean flipCt2) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDefaultTelemetryPeriod() {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addTelemetryPeriod(Duration period, Instant endDateUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDredSettings(Boolean dredSubscribed) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder removeSchedule(String scheduleId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, Instant endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, Instant startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, LocalDateTime endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, LocalDateTime startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public SchedulePriority getSchedulePriority() {
        return null;
    }

    @Override
    public void deleteRelaySchedule(int relayNumber1Based, String id, Map<String, Object> patch) {

    }

    @Override
    public ICommonSettingsBuilder disableAgeingMode() {
        return null;
    }
}
