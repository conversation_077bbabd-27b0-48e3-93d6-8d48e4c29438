package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.ConversionIssueType;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.DataVersion;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.Generation;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.ZonedDateTime;
import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class SystemStatus {
    private static final int WattPrecision = 0;
    private static final int KiloWattPrecision = 2;
    private static final int AmperePrecision = 2;
    private static final int VoltPrecision = 2;
    private static final int FrequencyPrecision = 2;
    private static final int VoltAmpsPrecision = 2;

    @JsonIgnore
    private String DeviceId;
    private String HardwareConfig;
    private DataVersion DataVersion;
    private String SchemaVersion;
    private Generation Generation;
    private String ReferenceMessageId = "00000000-0000-0000-0000-000000000000";

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime Date;
    private long Epoch;
    private DREDStatus DRED;
    private ACLoadStatus ACLoad;
    private BackupLoadStatus BackupLoad;
    private LoadStatus Load;
    private GridStatus Grid;
    private InverterStatus Inverter;
    private ThirdPartyInverterStatus ThirdPartyInverter;
    private PVStatus PV;
    private BatteryCabinetStatus BatteryCabinet;
    private BatteryStatus Battery;
    private GridInteractivePort GridInteractivePort;
    private ConnectionStatus ConnectionStatus;
    private List<RelayStatus> Relays;
    private OuijaBoard OuijaBoard;
    private List<IoTDevice> IoTDevices;
    private List<Error> SystemStatusErrors;
    private List<Event> Events;
    private List<ConversionIssueType> ConversionInfos;
    private SiteLimits SiteLimits;

    public SystemStatus() {
        this.SchemaVersion = "2.0";
    }

    public static int getWattPrecision() {
        return WattPrecision;
    }

    public static int getKiloWattPrecision() {
        return KiloWattPrecision;
    }

    public static int getAmperePrecision() {
        return AmperePrecision;
    }

    public static int getVoltPrecision() {
        return VoltPrecision;
    }

    public static int getFrequencyPrecision() {
        return FrequencyPrecision;
    }

    public static int getVoltAmpsPrecision() {
        return VoltAmpsPrecision;
    }

    public String getDeviceId() {
        return DeviceId;
    }

    public void setDeviceId(String deviceId) {
        DeviceId = deviceId;
    }

    public String getHardwareConfig() {
        return HardwareConfig;
    }

    public void setHardwareConfig(String hardwareConfig) {
        HardwareConfig = hardwareConfig;
    }

    public DataVersion getDataVersion() {
        return DataVersion;
    }

    public void setDataVersion(DataVersion dataVersion) {
        DataVersion = dataVersion;
    }

    public String getSchemaVersion() {
        return SchemaVersion;
    }

    public void setSchemaVersion(String schemaVersion) {
        SchemaVersion = schemaVersion;
    }

    public Generation getGeneration() {
        return Generation;
    }

    public void setGeneration(Generation generation) {
        Generation = generation;
    }

    public String getReferenceMessageId() {
        return ReferenceMessageId;
    }

    public void setReferenceMessageId(String referenceMessageId) {
        ReferenceMessageId = referenceMessageId;
    }

    public ZonedDateTime getDate() {
        return Date;
    }

    public void setDate(ZonedDateTime date) {
        Date = date;
    }

    public long getEpoch() {
        return Epoch;
    }

    public void setEpoch(long epoch) {
        Epoch = epoch;
    }

    public DREDStatus getDRED() {
        return DRED;
    }

    public void setDRED(DREDStatus dred) {
        DRED = dred;
    }

    public ACLoadStatus getACLoad() {
        return ACLoad;
    }

    public void setACLoad(ACLoadStatus acLoad) {
        ACLoad = acLoad;
    }

    public BackupLoadStatus getBackupLoad() {
        return BackupLoad;
    }

    public void setBackupLoad(BackupLoadStatus backupLoad) {
        BackupLoad = backupLoad;
    }

    public LoadStatus getLoad() {
        return Load;
    }

    public void setLoad(LoadStatus load) {
        Load = load;
    }

    public GridStatus getGrid() {
        return Grid;
    }

    public void setGrid(GridStatus grid) {
        Grid = grid;
    }

    public InverterStatus getInverter() {
        return Inverter;
    }

    public void setInverter(InverterStatus inverter) {
        Inverter = inverter;
    }

    public ThirdPartyInverterStatus getThirdPartyInverter() {
        return ThirdPartyInverter;
    }

    public void setThirdPartyInverter(ThirdPartyInverterStatus thirdPartyInverter) {
        ThirdPartyInverter = thirdPartyInverter;
    }

    public PVStatus getPV() {
        return PV;
    }

    public void setPV(PVStatus pv) {
        PV = pv;
    }

    public BatteryCabinetStatus getBatteryCabinet() {
        return BatteryCabinet;
    }

    public void setBatteryCabinet(BatteryCabinetStatus batteryCabinet) {
        BatteryCabinet = batteryCabinet;
    }

    public BatteryStatus getBattery() {
        return Battery;
    }

    public void setBattery(BatteryStatus battery) {
        Battery = battery;
    }

    public GridInteractivePort getGridInteractivePort() {
        return GridInteractivePort;
    }

    public void setGridInteractivePort(GridInteractivePort gridInteractivePort) {
        GridInteractivePort = gridInteractivePort;
    }

    public ConnectionStatus getConnectionStatus() {
        return ConnectionStatus;
    }

    public void setConnectionStatus(ConnectionStatus connectionStatus) {
        ConnectionStatus = connectionStatus;
    }

    public List<RelayStatus> getRelays() {
        return Relays;
    }

    public void setRelays(List<RelayStatus> relays) {
        Relays = relays;
    }

    public OuijaBoard getOuijaBoard() {
        return OuijaBoard;
    }

    public void setOuijaBoard(OuijaBoard ouijaBoard) {
        OuijaBoard = ouijaBoard;
    }

    public List<IoTDevice> getIoTDevices() {
        return IoTDevices;
    }

    public void setIoTDevices(List<IoTDevice> ioTDevices) {
        IoTDevices = ioTDevices;
    }

    public List<Error> getSystemStatusErrors() {
        return SystemStatusErrors;
    }

    public void setSystemStatusErrors(List<Error> systemStatusErrors) {
        SystemStatusErrors = systemStatusErrors;
    }

    public List<Event> getEvents() {
        return Events;
    }

    public void setEvents(List<Event> events) {
        Events = events;
    }

    public List<ConversionIssueType> getConversionInfos() {
        return ConversionInfos;
    }

    public void setConversionInfos(List<ConversionIssueType> conversionInfos) {
        ConversionInfos = conversionInfos;
    }

    public SiteLimits getSiteLimits() {
        return SiteLimits;
    }

    public void setSiteLimits(SiteLimits siteLimits) {
        SiteLimits = siteLimits;
    }
}