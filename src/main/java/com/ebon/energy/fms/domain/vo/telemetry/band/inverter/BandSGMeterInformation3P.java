package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandSGMeterInformation3P extends DataBackedBand
{
	public BandSGMeterInformation3P()
	{
		super(BandForge.<BandSGMeterInformation3P>getMetadataFor(BandSGMeterInformation3P.class));
	}



	public BandSGMeterInformation3P(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterInformation3P>getMetadataFor(BandSGMeterInformation3P.class));
	}

	public BandSGMeterInformation3P(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterInformation3P>getMetadataFor(BandSGMeterInformation3P.class));
	}



	public final Volt getVoltageL2L3()
	{
		return GetU16(0, Volt.Deci);
	}


	public final Volt getVoltageL1L3()
	{
		return GetU16(2, Volt.Deci);
	}


	public final Volt getVoltageL3()
	{
		return GetU16(4, Volt.Deci);
	}


	public final Ampere getCurrentL3()
	{
		return GetS16(6, Ampere.Centi);
	}


	public final BigDecimal getPowerFactorL3()
	{
		return new BigDecimal(GetS16(8)).multiply(Percentage.Tenth);
	}


	public final Watt getActivePowerL3()
	{
		return GetS16(10, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL3()
	{
		return GetU16(12, VoltAmps.Deci);
	}


	public final Watt getTotalActivePower()
	{
		return GetS16(14, Watt.Deci);
	}


	public final VoltAmps getTotalApparentPower()
	{
		return GetU16(16, VoltAmps.Deci);
	}


	public final Ohm getImpedanceL3()
	{
		return GetU16(18, Ohm.Centi);
	}


	public final BigDecimal getTotalHarmonicDistortionVoltageL3()
	{
		return new BigDecimal(GetU16(20)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getThirdHarmonicVoltageL3()
	{
		return new BigDecimal(GetU16(22)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getFifthHarmonicVoltageL3()
	{
		return new BigDecimal(GetU16(24)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getSeventhHarmonicVoltageL3()
	{
		return new BigDecimal(GetU16(26)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getTotalHarmonicDistortionCurrentL3()
	{
		return new BigDecimal(GetU16(28)).multiply(Percentage.Tenth);
	}


	public final Volt getVoltageL3Max()
	{
		return GetU16(30, Volt.Deci);
	}


	public final Volt getVoltageL3Min()
	{
		return GetU16(32, Volt.Deci);
	}


	public final Volt getVoltageL3Avg()
	{
		return GetU16(34, Volt.Deci);
	}


	public final Ampere getCurrentL3Max()
	{
		return GetU16(36, Ampere.Centi);
	}


	public final Ampere getCurrentL3Min()
	{
		return GetU16(38, Ampere.Centi);
	}


	public final Ampere getCurrentL3Avg()
	{
		return GetU16(40, Ampere.Centi);
	}


	public final Watt getActivePowerL3Max()
	{
		return GetU16(42, Watt.Deci);
	}


	public final Watt getActivePowerL3Min()
	{
		return GetU16(44, Watt.Deci);
	}


	public final Watt getActivePowerL3Avg()
	{
		return GetU16(46, Watt.Deci);
	}


	public final VoltAmps getApparentPowerL3Max()
	{
		return GetU16(48, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL3Min()
	{
		return GetU16(50, VoltAmps.Deci);
	}


	public final VoltAmps getApparentPowerL3Avg()
	{
		return GetU16(52, VoltAmps.Deci);
	}


	public final BigDecimal getPowerFactorL3Max()
	{
		return new BigDecimal(GetS16(54)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL3Min()
	{
		return new BigDecimal(GetS16(56)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorL3Avg()
	{
		return new BigDecimal(GetS16(58)).multiply(Percentage.Tenth);
	}


	public final WattHour getActiveEnergySellL3()
	{
		return GetU32(60, WattHour.Unit);
	}


	public final WattHour getActiveEnergyBuyL3()
	{
		return GetU32(64, WattHour.Unit);
	}


	public final WattHour getImportActiveEnergyTotal()
	{
		return GetU32(68, WattHour.Unit);
	}


	public final WattHour getExportActiveEnergyTotal()
	{
		return GetU32(72, WattHour.Unit);
	}
}
