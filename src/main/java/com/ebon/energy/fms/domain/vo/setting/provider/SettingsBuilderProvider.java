package com.ebon.energy.fms.domain.vo.setting.provider;// Copyright (c) Redback Technologies. All Rights Reserved.

import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.SettingsProtocol;
import com.ebon.energy.fms.common.enums.SettingsType;
import com.ebon.energy.fms.common.utils.ModelInfo;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.vo.product.control.InverterIdentityCard;
import com.ebon.energy.fms.domain.vo.product.control.RossReportedSettings;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.provider.types.*;

import java.time.Instant;
import java.util.Optional;

public class SettingsBuilderProvider {
    
    public static ICommonSettingsBuilder get(DeviceInfoAndSettings deviceSettingsDto) {
        if (deviceSettingsDto == null || deviceSettingsDto.getReported() == null) {
            throw new RuntimeException("Unable to establish type of device");
        }

        // Use settings protocol from reported to decide how to parse desired/reported
        SettingsProtocol protocol = Optional.ofNullable(deviceSettingsDto.getReported())
                                          .map(RossReportedSettings::getProtocol)
                                          .orElse(null);
        
        switch (protocol) {
            case V2:
                Integer majorVersion = Optional.ofNullable(deviceSettingsDto.getIdentityCard())
                                             .map(InverterIdentityCard::getSofterVersion)
                                             .map(RossVersion::getRossVersionNumber)
                                             .map(Version::getMajor)
                                             .orElse(null);
                
                switch (majorVersion) {
                    case 2:
                        return new RossSettingsBuilder(deviceSettingsDto, Instant.now());

                    case 3:
                        return new SGSettingsBuilder(deviceSettingsDto,Instant.now());

                    case 4:
                        ModelInfo model = Optional.ofNullable(deviceSettingsDto.getIdentityCard())
                                                .map(InverterIdentityCard::getModelInfo)
                                                .orElse(null);
                        if (model == null) {
                            throw new RuntimeException("Unable to determine which settings builder to use. Please make sure your device has a valid software version number.");
                        }

                        SettingsType settingsType = Optional.ofNullable(model.getHardwareModel())
                                                          .map(HardwareModelEnum::getSettingsTypeEnum)
                                                          .orElseThrow(() -> new RuntimeException("SettingsType missing for " + model.getHardwareModel()));

                        ICommonSettingsBuilder builder;
                        switch (settingsType) {
                            case None:
                                throw new RuntimeException("No builder for model '" + model + "'");
                            case ESETSG:
                                builder = new RossSettingsBuilder(deviceSettingsDto,Instant.now());
                                break;
                            case SG:
                                builder = new SGSettingsBuilder(deviceSettingsDto,Instant.now());
                                break;
                            case ESG:
                                builder = new ESGSettingsBuilder(deviceSettingsDto,Instant.now());
                                break;
                            case EH1P:
                                builder = new EH1PSettingsBuilder(deviceSettingsDto,Instant.now());
                                break;
                            case HVH3P:
                                builder = new HVH3PSettingsBuilder(deviceSettingsDto,Instant.now());
                                break;
                            default:
                                throw new RuntimeException("Unknown settings type: " + settingsType);
                        }

                        return builder;

                    default:
                        throw new RuntimeException("Unable to determine which settings builder to use. Please make sure your device has a valid ROSS version number.");
                }

            default:
                return new RossSettingsBuilder(deviceSettingsDto,Instant.now());
        }
    }
}

