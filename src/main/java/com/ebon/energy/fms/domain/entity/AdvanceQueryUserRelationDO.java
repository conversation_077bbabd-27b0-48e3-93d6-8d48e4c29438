package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 高级查询与用户关联表 数据对象
 * (多对多关联表)
 */
@Data
@Accessors(chain = true)
@TableName("AdvanceQueryFleetMonitoringUsers")
public class AdvanceQueryUserRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 高级查询ID 
     * (关联AdvanceQueries表的QueryId)
     */
    @TableField("AdvanceQuery_QueryId")
    private Integer advanceQueryId;

    /**
     * 用户邮箱 
     * (关联FleetMonitoringUsers表的Email)
     */
    @TableField("FleetMonitoringUser_Email")
    private String userEmail;

}