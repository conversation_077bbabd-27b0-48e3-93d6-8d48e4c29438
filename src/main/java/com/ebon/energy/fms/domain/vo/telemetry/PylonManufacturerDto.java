package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class PylonManufacturerDto extends PylonDto {
    private String DeviceName;
    private String Manufacturer;
    private byte VersionMajor;
    private byte VersionMinor;

    public PylonManufacturerDto(
            String rawMessage,
            String deviceName,
            String manufacturer,
            byte versionMajor,
            byte versionMinor) {

        super(rawMessage);
        this.DeviceName = deviceName;
        this.Manufacturer = manufacturer;
        this.VersionMajor = versionMajor;
        this.VersionMinor = versionMinor;
    }

    public String getDeviceName() { return DeviceName; }
    public void setDeviceName(String deviceName) { this.DeviceName = deviceName; }

    public String getManufacturer() { return Manufacturer; }
    public void setManufacturer(String manufacturer) { this.Manufacturer = manufacturer; }

    public byte getVersionMajor() { return VersionMajor; }
    public void setVersionMajor(byte versionMajor) { this.VersionMajor = versionMajor; }

    public byte getVersionMinor() { return VersionMinor; }
    public void setVersionMinor(byte versionMinor) { this.VersionMinor = versionMinor; }
}