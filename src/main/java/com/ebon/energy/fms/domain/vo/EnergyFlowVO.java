package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.EnergyFlowIssue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.GridStatusValue;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EnergyFlowVO {
    public Watt ACLoadW;
    public Watt BackupLoadW;
    public Watt PVW;
    public Watt ThirdPartyW;
    public Boolean HasBatteries;
    public Watt GridNegativeIsImportW;
    public Watt BatteryNegativeIsChargingW;
    public BigDecimal BatterySoC0to1;
    public EnergyFlowIssue[] Issues;

    public EnergyFlowVO(Watt ACLoadW, Watt BackupLoadW, Watt PVW, Watt ThirdPartyW,
                        boolean HasBatteries, Watt GridNegativeIsImportW,
                        Watt BatteryNegativeIsChargingW, BigDecimal BatterySoC0to1,
                        EnergyFlowIssue[] Issues) {
        this.ACLoadW = ACLoadW;
        this.BackupLoadW = BackupLoadW;
        this.PVW = PVW;
        this.ThirdPartyW = ThirdPartyW;
        this.HasBatteries = HasBatteries;
        this.GridNegativeIsImportW = GridNegativeIsImportW;
        this.BatteryNegativeIsChargingW = BatteryNegativeIsChargingW;
        this.BatterySoC0to1 = BatterySoC0to1;
        this.Issues = Issues;
    }

    @Deprecated
    public BigDecimal getACLoadkW() {
        return toKiloWattsTruncated(ACLoadW);
    }

    @Deprecated
    public BigDecimal getBackupLoadkW() {
        return toKiloWattsTruncated(BackupLoadW);
    }

    @Deprecated
    public BigDecimal getPVkW() {
        return toKiloWattsTruncated(PVW);
    }

    @Deprecated
    public BigDecimal getGridkW() {
        return toKiloWattsTruncated(GridNegativeIsImportW);
    }

    @Deprecated
    public BigDecimal getBatterykW() {
        return toKiloWattsTruncated(BatteryNegativeIsChargingW);
    }

    @Deprecated
    public boolean getIsBalanced() {
        return true;
    }

    @Deprecated
    public static BigDecimal truncate100(Watt v) {
        if (v == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal absValue = v.asDecimal().abs();
        return absValue.divide(BigDecimal.valueOf(100), 0, BigDecimal.ROUND_FLOOR).multiply(BigDecimal.valueOf(100));
    }

    @Deprecated
    public static BigDecimal toKiloWattsTruncated(Watt value) {
        return truncate100(value).divide(BigDecimal.valueOf(1000), 0, BigDecimal.ROUND_FLOOR);
    }

    public static GridStatusValue deduceGridStatus(Watt negativeIsImportW) {
        if (negativeIsImportW == null) {
            return GridStatusValue.Disconnected;
        }
        BigDecimal value = negativeIsImportW.asDecimal();
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return GridStatusValue.Idle;
        } else if (value.compareTo(BigDecimal.ZERO) < 0) {
            return GridStatusValue.Import;
        } else {
            return GridStatusValue.Export;
        }
    }

}