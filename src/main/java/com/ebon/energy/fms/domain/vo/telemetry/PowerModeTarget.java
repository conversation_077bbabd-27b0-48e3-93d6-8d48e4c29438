package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.PowerModeSource;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;

import static com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility.*;

@JsonAutoDetect(
        fieldVisibility = ANY,
        getterVisibility = NONE,
        setterVisibility = NONE
)
public class PowerModeTarget {

    public InverterModeValue Mode;
    public Watt Power;
    public PowerModeSource Source;
    public int Priority = 0;
    public boolean AllowPromotion = false;
    public boolean BeenPromoted = false;
    public String Comment = null;
    public InverterModeValue ActualMode = null;
    public Integer ActualPower = null;
    public BigDecimal ActualGenerationLimitPct = null;

    public PowerModeTarget() {
    }

    public PowerModeTarget(InverterModeValue Mode, Watt Power, PowerModeSource Source) {
        this.Mode = Mode;
        this.Power = Power;
        this.Source = Source;
    }


}