package com.ebon.energy.fms.domain.vo.product.control;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
public class ConfigurationDifference {
    private String settingName;
    private Object desiredValue;
    private Object reportedValue;

    @Override
    public String toString() {
        return String.format("%s %s vs %s",
                settingName,
                desiredValue != null ? desiredValue : "null",
                reportedValue != null ? reportedValue : "null");
    }

    public static void add(List<ConfigurationDifference> differences, String name, Object desired, Object reported) {
        // If the desired is null, then we don’t intend to change it
        if (desired == null) {
            return;
        }
        if (reported == null || !Objects.equals(reported, desired)) {
            differences.add(new ConfigurationDifference(name, desired, reported));
        }
    }
}
