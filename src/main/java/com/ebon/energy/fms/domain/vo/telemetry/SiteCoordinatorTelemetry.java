package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.util.List;
import java.util.Map;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class SiteCoordinatorTelemetry {
    private String Calculator;
    private Map<String, Object> FromCalculator;
    private Map<String, SiteCoordinatorTarget> Targets;
    private List<String> PeersInFault;
    private Boolean WeAreInFault;

}