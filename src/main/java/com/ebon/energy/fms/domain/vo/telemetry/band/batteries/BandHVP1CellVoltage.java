package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



 



public class BandHVP1CellVoltage extends DataBackedBand
{
	public BandHVP1CellVoltage()
	{
		super(BandForge.<BandHVP1CellVoltage>getMetadataFor(BandHVP1CellVoltage.class));
	}

	public BandHVP1CellVoltage(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVP1CellVoltage>getMetadataFor(BandHVP1CellVoltage.class));
	}

	public BandHVP1CellVoltage(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVP1CellVoltage>getMetadataFor(BandHVP1CellVoltage.class));
	}



	public final Volt getVoltageOfCellNumber000()
	{
		return GetU16(0, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber001()
	{
		return GetU16(2, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber002()
	{
		return GetU16(4, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber003()
	{
		return GetU16(6, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber004()
	{
		return GetU16(8, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber005()
	{
		return GetU16(10, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber006()
	{
		return GetU16(12, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber007()
	{
		return GetU16(14, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber008()
	{
		return GetU16(16, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber009()
	{
		return GetU16(18, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber010()
	{
		return GetU16(20, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber011()
	{
		return GetU16(22, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber012()
	{
		return GetU16(24, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber013()
	{
		return GetU16(26, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber014()
	{
		return GetU16(28, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber015()
	{
		return GetU16(30, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber016()
	{
		return GetU16(32, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber017()
	{
		return GetU16(34, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber018()
	{
		return GetU16(36, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber019()
	{
		return GetU16(38, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber020()
	{
		return GetU16(40, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber021()
	{
		return GetU16(42, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber022()
	{
		return GetU16(44, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber023()
	{
		return GetU16(46, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber024()
	{
		return GetU16(48, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber025()
	{
		return GetU16(50, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber026()
	{
		return GetU16(52, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber027()
	{
		return GetU16(54, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber028()
	{
		return GetU16(56, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber029()
	{
		return GetU16(58, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber030()
	{
		return GetU16(60, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber031()
	{
		return GetU16(62, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber032()
	{
		return GetU16(64, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber033()
	{
		return GetU16(66, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber034()
	{
		return GetU16(68, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber035()
	{
		return GetU16(70, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber036()
	{
		return GetU16(72, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber037()
	{
		return GetU16(74, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber038()
	{
		return GetU16(76, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber039()
	{
		return GetU16(78, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber040()
	{
		return GetU16(80, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber041()
	{
		return GetU16(82, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber042()
	{
		return GetU16(84, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber043()
	{
		return GetU16(86, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber044()
	{
		return GetU16(88, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber045()
	{
		return GetU16(90, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber046()
	{
		return GetU16(92, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber047()
	{
		return GetU16(94, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber048()
	{
		return GetU16(96, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber049()
	{
		return GetU16(98, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber050()
	{
		return GetU16(100, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber051()
	{
		return GetU16(102, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber052()
	{
		return GetU16(104, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber053()
	{
		return GetU16(106, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber054()
	{
		return GetU16(108, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber055()
	{
		return GetU16(110, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber056()
	{
		return GetU16(112, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber057()
	{
		return GetU16(114, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber058()
	{
		return GetU16(116, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber059()
	{
		return GetU16(118, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber060()
	{
		return GetU16(120, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber061()
	{
		return GetU16(122, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber062()
	{
		return GetU16(124, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber063()
	{
		return GetU16(126, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber064()
	{
		return GetU16(128, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber065()
	{
		return GetU16(130, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber066()
	{
		return GetU16(132, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber067()
	{
		return GetU16(134, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber068()
	{
		return GetU16(136, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber069()
	{
		return GetU16(138, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber070()
	{
		return GetU16(140, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber071()
	{
		return GetU16(142, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber072()
	{
		return GetU16(144, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber073()
	{
		return GetU16(146, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber074()
	{
		return GetU16(148, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber075()
	{
		return GetU16(150, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber076()
	{
		return GetU16(152, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber077()
	{
		return GetU16(154, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber078()
	{
		return GetU16(156, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber079()
	{
		return GetU16(158, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber080()
	{
		return GetU16(160, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber081()
	{
		return GetU16(162, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber082()
	{
		return GetU16(164, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber083()
	{
		return GetU16(166, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber084()
	{
		return GetU16(168, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber085()
	{
		return GetU16(170, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber086()
	{
		return GetU16(172, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber087()
	{
		return GetU16(174, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber088()
	{
		return GetU16(176, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber089()
	{
		return GetU16(178, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber090()
	{
		return GetU16(180, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber091()
	{
		return GetU16(182, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber092()
	{
		return GetU16(184, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber093()
	{
		return GetU16(186, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber094()
	{
		return GetU16(188, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber095()
	{
		return GetU16(190, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber096()
	{
		return GetU16(192, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber097()
	{
		return GetU16(194, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber098()
	{
		return GetU16(196, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber099()
	{
		return GetU16(198, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber100()
	{
		return GetU16(200, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber101()
	{
		return GetU16(202, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber102()
	{
		return GetU16(204, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber103()
	{
		return GetU16(206, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber104()
	{
		return GetU16(208, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber105()
	{
		return GetU16(210, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber106()
	{
		return GetU16(212, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber107()
	{
		return GetU16(214, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber108()
	{
		return GetU16(216, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber109()
	{
		return GetU16(218, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber110()
	{
		return GetU16(220, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber111()
	{
		return GetU16(222, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber112()
	{
		return GetU16(224, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber113()
	{
		return GetU16(226, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber114()
	{
		return GetU16(228, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber115()
	{
		return GetU16(230, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber116()
	{
		return GetU16(232, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber117()
	{
		return GetU16(234, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber118()
	{
		return GetU16(236, Volt.Milli);
	}


	public final Volt getVoltageOfCellNumber119()
	{
		return GetU16(238, Volt.Milli);
	}
}
