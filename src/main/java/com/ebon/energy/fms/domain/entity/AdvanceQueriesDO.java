package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 高级查询配置数据对象
 */
@Data
@Accessors(chain = true)
@TableName("AdvanceQueries")
public class AdvanceQueriesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询ID (主键，自增长)
     */
    @TableId(value = "QueryId", type = IdType.AUTO)
    private Integer queryId;

    /**
     * 查询名称
     */
    @TableField(value = "QueryName")
    private String queryName;

    /**
     * 查询语句内容
     */
    @TableField(value = "Query")
    private String query;

    /**
     * 创建人姓名
     */
    @TableField(value = "CreatedBy")
    private String createdBy;

    /**
     * 创建人邮箱 (关联用户表)
     */
    @TableField(value = "CreatedByEmail")
    private String createdByEmail;

}