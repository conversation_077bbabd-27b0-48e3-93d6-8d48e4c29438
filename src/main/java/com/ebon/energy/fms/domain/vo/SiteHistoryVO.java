package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.util.Objects;

@Data
public class SiteHistoryVO {

    private SevenDayHistoryVO powerUsage;
    private SevenDayHistoryVO solarProduction;
    private BoughtSoldVO boughtSold;
    private boolean isAcCoupledMode;

    // 构造函数
    public SiteHistoryVO(
            SevenDayHistoryVO powerUsage,
            SevenDayHistoryVO solarProduction,
            BoughtSoldVO boughtSold,
            boolean isAcCoupledMode) {
        this.powerUsage = powerUsage;
        this.solarProduction = solarProduction;
        this.boughtSold = Objects.requireNonNull(boughtSold, "boughtSold must not be null");
        this.isAcCoupledMode = isAcCoupledMode;
    }
}