package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandSGMeterData extends DataBackedBand {
    public BandSGMeterData() {
        super(BandForge.<BandSGMeterData>getMetadataFor(BandSGMeterData.class));
    }


    public BandSGMeterData(byte[] bytes) {
        super(bytes, BandForge.<BandSGMeterData>getMetadataFor(BandSGMeterData.class));
    }

    public BandSGMeterData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandSGMeterData>getMetadataFor(BandSGMeterData.class));
    }


    public final Volt getVoltageL1L2() {
        return GetU16(0, Volt.Deci);
    }


    public final Volt getVoltageL1() {
        return GetU16(2, Volt.Deci);
    }


    public final Volt getVoltageL2() {
        return GetU16(4, Volt.Deci);
    }


    public final Ampere getCurrentL1() {
        return GetS32(6, Ampere.Centi);
    }


    public final Ampere getCurrentL2() {
        return GetS32(10, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL1() {
        return new BigDecimal(GetS16(14)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2() {
        return new BigDecimal(GetS16(16)).multiply(Percentage.Tenth);
    }


    public final Frequency getFrequencyL1() {
        return GetU16(18, Frequency.Centi);
    }


    public final Watt getActivePowerL1() {
        return GetS32(20, Watt.Deci);
    }


    public final Watt getActivePowerL2() {
        return GetS32(24, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL1() {
        return GetU32(28, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2() {
        return GetU32(32, VoltAmps.Deci);
    }


    public final Ohm getImpedanceL1() {
        return GetU16(36, Ohm.Centi);
    }


    public final Ohm getImpedanceL2() {
        return GetU16(38, Ohm.Centi);
    }


    public final BigDecimal getTotalHarmonicDistortionVoltageL1() {
        return new BigDecimal(GetU16(40)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getThirdHarmonicVoltageL1() {
        return new BigDecimal(GetU16(42)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getFifthHarmonicVoltageL1() {
        return new BigDecimal(GetU16(44)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getSeventhHarmonicVoltageL1() {
        return new BigDecimal(GetU16(46)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getTotalHarmonicDistortionVoltageL2() {
        return new BigDecimal(GetU16(48)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getThirdHarmonicVoltageL2() {
        return new BigDecimal(GetU16(50)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getFifthHarmonicVoltageL2() {
        return new BigDecimal(GetU16(52)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getSeventhHarmonicVoltageL2() {
        return new BigDecimal(GetU16(54)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getTotalHarmonicDistortionCurrentL1() {
        return new BigDecimal(GetU16(56)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getTotalHarmonicDistortionCurrentL2() {
        return new BigDecimal(GetU16(58)).multiply(Percentage.Tenth);
    }


    public final Volt getVoltageL1ToL2Max() {
        return GetU16(60, Volt.Deci);
    }


    public final Volt getVoltageL1ToL2Min() {
        return GetU16(62, Volt.Deci);
    }


    public final Volt getVoltageL1Max() {
        return GetU16(64, Volt.Deci);
    }


    public final Volt getVoltageL1Min() {
        return GetU16(66, Volt.Deci);
    }


    public final Volt getVoltageL2Max() {
        return GetU16(68, Volt.Deci);
    }


    public final Volt getVoltageL2Min() {
        return GetU16(70, Volt.Deci);
    }


    public final Ampere getCurrentL1Max() {
        return GetS32(72, Ampere.Centi);
    }


    public final Ampere getCurrentL1Min() {
        return GetS32(76, Ampere.Centi);
    }


    public final Ampere getCurrentL2Max() {
        return GetS32(80, Ampere.Centi);
    }


    public final Ampere getCurrentL2Min() {
        return GetS32(84, Ampere.Centi);
    }


    public final Watt getActivePowerL1Max() {
        return GetS32(88, Watt.Deci);
    }


    public final Watt getActivePowerL1Min() {
        return GetS32(92, Watt.Deci);
    }


    public final Watt getActivePowerL2Max() {
        return GetS32(96, Watt.Deci);
    }


    public final Watt getActivePowerL2Min() {
        return GetS32(100, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL1Max() {
        return GetU32(104, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL1Min() {
        return GetU32(108, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2Max() {
        return GetU32(112, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2Min() {
        return GetU32(116, VoltAmps.Deci);
    }


    public final BigDecimal getPowerFactorL1Max() {
        return new BigDecimal(GetS16(120)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL1Min() {
        return new BigDecimal(GetS16(122)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2Max() {
        return new BigDecimal(GetS16(124)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2Min() {
        return new BigDecimal(GetS16(126)).multiply(Percentage.Tenth);
    }


    public final Frequency getFrequencyL1Max() {
        return GetU16(128, Frequency.Centi);
    }


    public final Frequency getFrequencyL1Min() {
        return GetU16(130, Frequency.Centi);
    }


    public final WattHour getActiveEnergySellL1() {
        return GetU32(132, WattHour.Hecto);
    }


    public final WattHour getActiveEnergySellL2() {
        return GetU32(136, WattHour.Hecto);
    }


    public final WattHour getActiveEnergyBuyL1() {
        return GetU32(140, WattHour.Hecto);
    }


    public final WattHour getActiveEnergyBuyL2() {
        return GetU32(144, WattHour.Hecto);
    }


    public final Volt getVoltageL1ToL2Avg() {
        return GetU16(148, Volt.Deci);
    }


    public final Volt getVoltageL1Avg() {
        return GetU16(150, Volt.Deci);
    }


    public final Volt getVoltageL2Avg() {
        return GetU16(152, Volt.Deci);
    }


    public final Ampere getCurrentL1Avg() {
        return GetS32(154, Ampere.Centi);
    }


    public final Ampere getCurrentL2Avg() {
        return GetS32(158, Ampere.Centi);
    }


    public final Watt getActivePowerL1Avg() {
        return GetS32(162, Watt.Deci);
    }


    public final Watt getActivePowerL2Avg() {
        return GetS32(166, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL1Avg() {
        return GetU32(170, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2Avg() {
        return GetU32(174, VoltAmps.Deci);
    }


    public final BigDecimal getPowerFactorL1Avg() {
        return new BigDecimal(GetS16(178)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2Avg() {
        return new BigDecimal(GetS16(180)).multiply(Percentage.Tenth);
    }


    public final Frequency getFrequencyL1Avg() {
        return GetU16(182, Frequency.Centi);
    }


    public final Volt getVoltageL1ToL2Snapshot() {
        return GetU16(184, Volt.Deci);
    }


    public final Volt getVoltageL1Snapshot() {
        return GetU16(186, Volt.Deci);
    }


    public final Volt getVoltageL2Snapshot() {
        return GetU16(188, Volt.Deci);
    }


    public final Ampere getCurrentL1Snapshot() {
        return GetS32(190, Ampere.Centi);
    }


    public final Ampere getCurrentL2Snapshot() {
        return GetS32(194, Ampere.Centi);
    }


    public final Watt getActivePowerL1Snapshot() {
        return GetS32(198, Watt.Deci);
    }


    public final Watt getActivePowerL2Snapshot() {
        return GetS32(202, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL1Snapshot() {
        return GetU32(206, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2Snapshot() {
        return GetU32(210, VoltAmps.Deci);
    }


    public final BigDecimal getPowerFactorL1Snapshot() {
        return new BigDecimal(GetS16(214)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2Snapshot() {
        return new BigDecimal(GetS16(216)).multiply(Percentage.Tenth);
    }


    public final Frequency getFrequencyL1Snapshot() {
        return GetU16(218, Frequency.Centi);
    }
}
