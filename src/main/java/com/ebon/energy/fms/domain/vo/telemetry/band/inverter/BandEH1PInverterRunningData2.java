package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandEH1PInverterRunningData2 extends DataBackedBand {
    public BandEH1PInverterRunningData2() {
        super(BandForge.<BandEH1PInverterRunningData2>getMetadataFor(BandEH1PInverterRunningData2.class));
    }


    public BandEH1PInverterRunningData2(byte[] bytes) {
        super(bytes, BandForge.<BandEH1PInverterRunningData2>getMetadataFor(BandEH1PInverterRunningData2.class));
    }

    public BandEH1PInverterRunningData2(String encodedBytes) {
        super(encodedBytes, BandForge.<BandEH1PInverterRunningData2>getMetadataFor(BandEH1PInverterRunningData2.class));
    }


    public final Volt getBattChargeVoltMax() {
        return GetU16(0, Volt.Deci);
    }


    public final Object getBMSState() {
        return BMSState.parse(GetU16(2));
    }


    public final Object getInverterInitState() {
        return InvertersInitState.parse(GetU16(4));
    }


    public final Object getSupportBatchUpgrade() {
        return SupportBatchUpgrade.parse(GetU16(6));
    }


    public final boolean getFCASRunState() {
        return GetBool((int) GetU16(8), 1, 0, false);
    }


    public final Object getNationalStandard() {
        return SetFlag.parse(GetU16(10));
    }


    public final Object getFaultCode01() {
        return FaultCode01.parse(GetU16(12));
    }


    public final Object getFaultCode02() {
        return FaultCode02.parse(GetU16(14));
    }


    public final Object getFaultCode03() {
        return FaultCode03.parse(GetU16(16));
    }


    public final Object getFaultCode04() {
        return FaultCode04.parse(GetU16(18));
    }


    public final Object getFaultCode05() {
        return FaultCode05.parse(GetU16(20));
    }


    public final Object getOperatingState() {
        return WorkState.parse(GetU16(22));
    }


    public final Object getPowerMode() {
        return EMSPowerMode.parse(GetU16(24));
    }


    public final Object getGridResponseCurve() {
        return StandardWorkModeActState.parse(GetU16(26));
    }


    public final Object getFaultCode06() {
        return FaultCode06.parse(GetU16(28));
    }


    public final Object getFaultCode07() {
        return FaultCode07.parse(GetU16(30));
    }


    public final Object getBatteryControlMode() {
        return EnergyControlSwitch.parse(GetU16(44));
    }


    public final Volt getBatteryVoltage() {
        return GetU16(46, Volt.Deci);
    }


    public final Ampere getBatteryCurrent() {
        return GetS16(48, Ampere.Deci);
    }


    public final boolean getBatteryCurrentDirection() {
        return GetBool((int) GetU16(50), 1, 0, false);
    }


    public final Volt getLLCBusVoltage() {
        return GetU16(52, Volt.Deci);
    }


    public final Volt getBackupVoltageL1() {
        return GetU16(54, Volt.Deci);
    }


    public final Ampere getBackupCurrentL1() {
        return GetU16(56, Ampere.Deci);
    }


    public final BigDecimal getSOC() {
        return new BigDecimal(GetU16(58)).multiply(Percentage._1);
    }


    public final BigDecimal getSOH() {
        return new BigDecimal(GetU16(60)).multiply(Percentage._1);
    }


    public final Volt getVbattery() {
        return GetU16(62, Volt.Centi);
    }


    public final Ampere getIbattery() {
        return GetS16(64, Ampere.Deci);
    }


    public final Ampere getBatteryChargeLimit() {
        return GetU16(66, Ampere.Deci);
    }


    public final Ampere getBatteryDischargeLimit() {
        return GetU16(68, Ampere.Deci);
    }


    public final Object getBatteryFaultCode01() {
        return BatteryFaultCode01.parse(GetU16(70));
    }


    public final Object getBatteryFaultCode02() {
        return BatteryFaultCode02.parse(GetU16(72));
    }


    public final Watt getACLoadPower() {
        return GetU16(74, Watt.Unit);
    }


    public final Watt getBackupLoadPower() {
        return GetS16(76, Watt.Unit);
    }


    public final Watt getPbattery() {
        return GetS32(78, Watt.Unit);
    }


    public final Watt getGridActivePower() {
        return GetS32(82, Watt.Unit);
    }


    public final Volt getBackupVoltageL2() {
        return GetU16(86, Volt.Deci);
    }


    public final Ampere getBackupCurrentL2() {
        return GetU16(88, Ampere.Deci);
    }


    public final Volt getBackupVoltageL3() {
        return GetU16(90, Volt.Deci);
    }


    public final Ampere getBackupCurrentL3() {
        return GetU16(92, Ampere.Deci);
    }


    public final Watt getInverterPower() {
        return GetS16(94, Watt.Deca);
    }


    public final BatteryManufacturer getBatteryModelDSP() {
        return BatteryManufacturer.forValue(GetU16(96));
    }


    public final boolean getBatteryDiscernFlag() {
        return GetBool((int) GetU16(98), 1, 0, false);
    }


    public final BatteryManufacturer getBatteryModel() {
        return BatteryManufacturer.forValue(GetU16(100));
    }


    public final WattHour getEBatteryChargeTotal() {
        return GetU32(102, WattHour.Kilo);
    }


    public final WattHour getEBatteryChargeDay() {
        return GetU16(106, WattHour.Hecto);
    }


    public final WattHour getEBatteryChargeYesterday() {
        return GetU16(108, WattHour.Hecto);
    }


    public final WattHour getEBatteryDischargeTotal() {
        return GetU32(110, WattHour.Kilo);
    }


    public final WattHour getEBatteryDischargeDay() {
        return GetU16(114, WattHour.Hecto);
    }


    public final WattHour getEBatteryDischargeYesterday() {
        return GetU16(116, WattHour.Hecto);
    }


    public final WattHour getEBuyTotal() {
        return GetU32(118, WattHour.Kilo);
    }


    public final WattHour getEBuyDay() {
        return GetU16(122, WattHour.Hecto);
    }


    public final WattHour getEBuyYesterday() {
        return GetU16(124, WattHour.Hecto);
    }


    public final WattHour getESellTotal() {
        return GetU32(126, WattHour.Kilo);
    }


    public final WattHour getESellDay() {
        return GetU16(130, WattHour.Hecto);
    }


    public final WattHour getESellYesterday() {
        return GetU16(132, WattHour.Hecto);
    }


    public final WattHour getELoadTotal() {
        return GetU32(134, WattHour.Kilo);
    }


    public final WattHour getELoadDay() {
        return GetU16(138, WattHour.Hecto);
    }


    public final WattHour getELoadYesterday() {
        return GetU16(140, WattHour.Hecto);
    }


    public final BigDecimal getCleanEnergySchedule() {
        return new BigDecimal(GetU16(142)).multiply(Percentage._1);
    }


    public final WattHour getInverterTotalOutputPower() {
        return GetU32(144, WattHour.Kilo);
    }


    public final WattHour getEGridBatteryCharge() {
        return GetU32(148, WattHour.Kilo);
    }


    public final WattHour getEInverterGenerationTotal() {
        return GetU32(152, WattHour.Kilo);
    }


    public final WattHour getEInverterAbsorbTotal() {
        return GetU32(156, WattHour.Kilo);
    }
}
