package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.*;

@JsonAutoDetect(
		fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
		getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
		setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandETelemetry implements ITelemetryBand
{
	public InverterBandETelemetry(ACRMeterType aCRMeterType, boolean meterStatus2, Volt phaseAVoltage, Volt phaseBVoltage, Volt phaseCVoltage, Ampere phaseACurrent, Ampere phaseBCurrent, Ampere phaseCCurrent, Watt phaseAActivePower, Watt phaseBActivePower, Watt phaseCActivePower, Watt totalActivePower, VoltAmpsReactive phaseAReactivePower, VoltAmpsReactive phaseBReactivePower, VoltAmpsReactive phaseCReactivePower, VoltAmpsReactive totalReactivePower, VoltAmps phaseAApparentPower, VoltAmps phaseBApparentPower, VoltAmps phaseCApparentPower, VoltAmps totalApparentPower, BigDecimal phaseAPowerFactor, BigDecimal phaseBPowerFactor, BigDecimal phaseCPowerFactor, BigDecimal totalPowerFactor, Frequency frequency, WattHour eTotalSell, WattHour eTotalBuy)
	{
		setACRMeterType(aCRMeterType);
		setMeterStatus2(meterStatus2);
		setPhaseAVoltage(phaseAVoltage);
		setPhaseBVoltage(phaseBVoltage);
		setPhaseCVoltage(phaseCVoltage);
		setPhaseACurrent(phaseACurrent);
		setPhaseBCurrent(phaseBCurrent);
		setPhaseCCurrent(phaseCCurrent);
		setPhaseAActivePower(phaseAActivePower);
		setPhaseBActivePower(phaseBActivePower);
		setPhaseCActivePower(phaseCActivePower);
		setTotalActivePower(totalActivePower);
		setPhaseAReactivePower(phaseAReactivePower);
		setPhaseBReactivePower(phaseBReactivePower);
		setPhaseCReactivePower(phaseCReactivePower);
		setTotalReactivePower(totalReactivePower);
		setPhaseAApparentPower(phaseAApparentPower);
		setPhaseBApparentPower(phaseBApparentPower);
		setPhaseCApparentPower(phaseCApparentPower);
		setTotalApparentPower(totalApparentPower);
		setPhaseAPowerFactor(phaseAPowerFactor);
		setPhaseBPowerFactor(phaseBPowerFactor);
		setPhaseCPowerFactor(phaseCPowerFactor);
		setTotalPowerFactor(totalPowerFactor);
		setFrequency(frequency);
		setETotalSell(eTotalSell);
		setETotalBuy(eTotalBuy);
	}


	private ACRMeterType ACRMeterType;
	public final ACRMeterType getACRMeterType()
	{
		return ACRMeterType;
	}
	private void setACRMeterType(ACRMeterType value)
	{
		ACRMeterType = value;
	}

	private boolean MeterStatus2;
	public final boolean getMeterStatus2()
	{
		return MeterStatus2;
	}
	private void setMeterStatus2(boolean value)
	{
		MeterStatus2 = value;
	}

	private Volt PhaseAVoltage;
	public final Volt getPhaseAVoltage()
	{
		return PhaseAVoltage;
	}
	private void setPhaseAVoltage(Volt value)
	{
		PhaseAVoltage = value;
	}

	private Volt PhaseBVoltage;
	public final Volt getPhaseBVoltage()
	{
		return PhaseBVoltage;
	}
	private void setPhaseBVoltage(Volt value)
	{
		PhaseBVoltage = value;
	}

	private Volt PhaseCVoltage;
	public final Volt getPhaseCVoltage()
	{
		return PhaseCVoltage;
	}
	private void setPhaseCVoltage(Volt value)
	{
		PhaseCVoltage = value;
	}

	private Ampere PhaseACurrent;
	public final Ampere getPhaseACurrent()
	{
		return PhaseACurrent;
	}
	private void setPhaseACurrent(Ampere value)
	{
		PhaseACurrent = value;
	}

	private Ampere PhaseBCurrent;
	public final Ampere getPhaseBCurrent()
	{
		return PhaseBCurrent;
	}
	private void setPhaseBCurrent(Ampere value)
	{
		PhaseBCurrent = value;
	}

	private Ampere PhaseCCurrent;
	public final Ampere getPhaseCCurrent()
	{
		return PhaseCCurrent;
	}
	private void setPhaseCCurrent(Ampere value)
	{
		PhaseCCurrent = value;
	}

	private Watt PhaseAActivePower;
	public final Watt getPhaseAActivePower()
	{
		return PhaseAActivePower;
	}
	private void setPhaseAActivePower(Watt value)
	{
		PhaseAActivePower = value;
	}

	private Watt PhaseBActivePower;
	public final Watt getPhaseBActivePower()
	{
		return PhaseBActivePower;
	}
	private void setPhaseBActivePower(Watt value)
	{
		PhaseBActivePower = value;
	}

	private Watt PhaseCActivePower;
	public final Watt getPhaseCActivePower()
	{
		return PhaseCActivePower;
	}
	private void setPhaseCActivePower(Watt value)
	{
		PhaseCActivePower = value;
	}

	private Watt TotalActivePower;
	public final Watt getTotalActivePower()
	{
		return TotalActivePower;
	}
	private void setTotalActivePower(Watt value)
	{
		TotalActivePower = value;
	}

	private VoltAmpsReactive PhaseAReactivePower;
	public final VoltAmpsReactive getPhaseAReactivePower()
	{
		return PhaseAReactivePower;
	}
	private void setPhaseAReactivePower(VoltAmpsReactive value)
	{
		PhaseAReactivePower = value;
	}

	private VoltAmpsReactive PhaseBReactivePower;
	public final VoltAmpsReactive getPhaseBReactivePower()
	{
		return PhaseBReactivePower;
	}
	private void setPhaseBReactivePower(VoltAmpsReactive value)
	{
		PhaseBReactivePower = value;
	}

	private VoltAmpsReactive PhaseCReactivePower;
	public final VoltAmpsReactive getPhaseCReactivePower()
	{
		return PhaseCReactivePower;
	}
	private void setPhaseCReactivePower(VoltAmpsReactive value)
	{
		PhaseCReactivePower = value;
	}

	private VoltAmpsReactive TotalReactivePower;
	public final VoltAmpsReactive getTotalReactivePower()
	{
		return TotalReactivePower;
	}
	private void setTotalReactivePower(VoltAmpsReactive value)
	{
		TotalReactivePower = value;
	}

	private VoltAmps PhaseAApparentPower;
	public final VoltAmps getPhaseAApparentPower()
	{
		return PhaseAApparentPower;
	}
	private void setPhaseAApparentPower(VoltAmps value)
	{
		PhaseAApparentPower = value;
	}

	private VoltAmps PhaseBApparentPower;
	public final VoltAmps getPhaseBApparentPower()
	{
		return PhaseBApparentPower;
	}
	private void setPhaseBApparentPower(VoltAmps value)
	{
		PhaseBApparentPower = value;
	}

	private VoltAmps PhaseCApparentPower;
	public final VoltAmps getPhaseCApparentPower()
	{
		return PhaseCApparentPower;
	}
	private void setPhaseCApparentPower(VoltAmps value)
	{
		PhaseCApparentPower = value;
	}

	private VoltAmps TotalApparentPower;
	public final VoltAmps getTotalApparentPower()
	{
		return TotalApparentPower;
	}
	private void setTotalApparentPower(VoltAmps value)
	{
		TotalApparentPower = value;
	}

	/** 
	 Gets Phase A power factor, INT16S, R, -99~100, A-phase power factor in 3-phase meter/1-phase meter power factor
	 
	 It should be 'short'. Only decimal to support ROSS2 deployments which send it using old format
	*/
	private BigDecimal PhaseAPowerFactor = new BigDecimal(0);
	public final BigDecimal getPhaseAPowerFactor()
	{
		return PhaseAPowerFactor;
	}
	private void setPhaseAPowerFactor(BigDecimal value)
	{
		PhaseAPowerFactor = value;
	}

	/** 
	 Gets Phase B power factor, INT16S, R, -99~100, B-phase power factor in 3-phase meter
	 
	 It should be 'short'. Only decimal to support ROSS2 deployments which send it using old format
	*/
	private BigDecimal PhaseBPowerFactor = new BigDecimal(0);
	public final BigDecimal getPhaseBPowerFactor()
	{
		return PhaseBPowerFactor;
	}
	private void setPhaseBPowerFactor(BigDecimal value)
	{
		PhaseBPowerFactor = value;
	}

	/** 
	 Gets Phase C power factor, INT16S, R, -99~100, C-phase power factor in 3-phase meter
	 
	 It should be 'short'. Only decimal to support ROSS2 deployments which send it using old format
	*/
	private BigDecimal PhaseCPowerFactor = new BigDecimal(0);
	public final BigDecimal getPhaseCPowerFactor()
	{
		return PhaseCPowerFactor;
	}
	private void setPhaseCPowerFactor(BigDecimal value)
	{
		PhaseCPowerFactor = value;
	}

	/**
	 Gets total power factor, INT16S, R, -99~100, Total power factor

	 but Bug 30275 shows that we cand get value of -100

	 It should be 'short'. Only decimal to support ROSS2 deployments which send it using old format
	*/
	private BigDecimal TotalPowerFactor = new BigDecimal(0);
	public final BigDecimal getTotalPowerFactor()
	{
		return TotalPowerFactor;
	}
	private void setTotalPowerFactor(BigDecimal value)
	{
		TotalPowerFactor = value;
	}

	private Frequency Frequency;
	public final Frequency getFrequency()
	{
		return Frequency;
	}
	private void setFrequency(Frequency value)
	{
		Frequency = value;
	}

	private WattHour ETotalSell;
	public final WattHour getETotalSell()
	{
		return ETotalSell;
	}
	private void setETotalSell(WattHour value)
	{
		ETotalSell = value;
	}

	private WattHour ETotalBuy;
	public final WattHour getETotalBuy()
	{
		return ETotalBuy;
	}
	private void setETotalBuy(WattHour value)
	{
		ETotalBuy = value;
	}
}
