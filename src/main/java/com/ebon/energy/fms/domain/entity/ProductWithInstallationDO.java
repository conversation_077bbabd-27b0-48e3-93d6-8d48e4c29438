package com.ebon.energy.fms.domain.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ProductWithInstallationDO {
    private String serialNumber;
    private Timestamp activeInstallationDate;
    private Boolean isInWarranty;
    private Timestamp warrantyEndDate;
    private String rossVersion;
    private String hardwareConfig;
    private String modelName;
    private String lastConnected;

    private String latestSystemStatus;
    private Timestamp lastSystemStatusReceived;
}