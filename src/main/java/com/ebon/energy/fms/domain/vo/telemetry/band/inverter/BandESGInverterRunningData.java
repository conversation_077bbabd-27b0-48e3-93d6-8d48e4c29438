package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.Country;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.*;
import java.math.*;

public class BandESGInverterRunningData extends DataBackedBand {
    public BandESGInverterRunningData() {
        super(BandForge.<BandESGInverterRunningData>getMetadataFor(BandESGInverterRunningData.class));
    }


    public BandESGInverterRunningData(byte[] bytes) {
        super(bytes, BandForge.<BandESGInverterRunningData>getMetadataFor(BandESGInverterRunningData.class));
    }

    public BandESGInverterRunningData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandESGInverterRunningData>getMetadataFor(BandESGInverterRunningData.class));
    }

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public final LocalDateTime getRTCTime() {
        return GetDateTime(0);
    }


    public final Volt getVpv1() {
        return GetU16(6, Volt.Deci);
    }


    public final Ampere getIpv1() {
        return GetU16(8, Ampere.Deci);
    }


    public final Watt getPpv1() {
        return GetU32(10, Watt.Unit);
    }


    public final Volt getVpv2() {
        return GetU16(14, Volt.Deci);
    }


    public final Ampere getIpv2() {
        return GetU16(16, Ampere.Deci);
    }


    public final Watt getPpv2() {
        return GetU32(18, Watt.Unit);
    }


    public final Volt getVpv3() {
        return GetU16(22, Volt.Deci);
    }


    public final Ampere getIpv3() {
        return GetU16(24, Ampere.Deci);
    }


    public final Watt getPpv3() {
        return GetU32(26, Watt.Unit);
    }


    public final Volt getVpv4() {
        return GetU16(30, Volt.Deci);
    }


    public final Ampere getIpv4() {
        return GetU16(32, Ampere.Deci);
    }


    public final Watt getPpv4() {
        return GetU32(34, Watt.Unit);
    }


    public final Object getPV1Mode() {

        return PvMode.parse((byte) GetMaskedU32(38, 3, (short) 0));
    }


    public final Volt getVInverterGridL1() {
        return GetU16(42, Volt.Deci);
    }


    public final Ampere getIInverterGridL1() {
        return GetU16(44, Ampere.Deci);
    }


    public final Frequency getFInverterGridL1() {
        return GetU16(46, Frequency.Centi);
    }


    public final Watt getPInverterGridL1() {
        return GetS32(48, Watt.Unit);
    }


    public final Volt getVInverterGridL2() {
        return GetU16(52, Volt.Deci);
    }


    public final Ampere getIInverterGridL2() {
        return GetU16(54, Ampere.Deci);
    }


    public final Frequency getFInverterGridL2() {
        return GetU16(56, Frequency.Centi);
    }


    public final Watt getPInverterGridL2() {
        return GetS32(58, Watt.Unit);
    }


    public final Volt getVInverterGridL3() {
        return GetU16(62, Volt.Deci);
    }


    public final Ampere getIInverterGridL3() {
        return GetU16(64, Ampere.Deci);
    }


    public final Frequency getFInverterGridL3() {
        return GetU16(66, Frequency.Centi);
    }


    public final Watt getPInverterGridL3() {
        return GetS32(68, Watt.Unit);
    }


    public final Object getGridMode() {
        return GridMode.parse((byte) GetU16(72));
    }


    public final Watt getInverterActivePower() {
        return GetS32(74, Watt.Unit);
    }


    public final Watt getGridActivePower() {
        return GetS32(78, Watt.Unit);
    }


    public final VoltAmpsReactive getInverterReactivePower() {
        return GetS32(82, VoltAmpsReactive.Unit);
    }


    public final VoltAmps getInverterApparentPower() {
        return GetS32(86, VoltAmps.Unit);
    }


    public final Volt getBackUpVloadL1() {
        return GetU16(90, Volt.Deci);
    }


    public final Ampere getBackUpIloadL1() {
        return GetU16(92, Ampere.Deci);
    }


    public final Frequency getBackUpFloadL1() {
        return GetU16(94, Frequency.Centi);
    }


    public final Object getBackUpLoadModeL1() {
        return LoadMode.parse(GetU16(96));
    }


    public final Watt getBackUpPloadL1() {
        return GetS32(98, Watt.Unit);
    }


    public final Volt getBackUpVloadL2() {
        return GetU16(102, Volt.Deci);
    }


    public final Ampere getBackUpIloadL2() {
        return GetU16(104, Ampere.Deci);
    }


    public final Frequency getBackUpFloadL2() {
        return GetU16(106, Frequency.Centi);
    }


    public final Object getBackUpLoadModeL2() {
        return LoadMode.parse(GetU16(108));
    }


    public final Watt getBackUpPloadL2() {
        return GetS32(110, Watt.Unit);
    }


    public final Volt getBackUpVloadL3() {
        return GetU16(114, Volt.Deci);
    }


    public final Ampere getBackUpIloadL3() {
        return GetU16(116, Ampere.Deci);
    }


    public final Frequency getBackUpFloadL3() {
        return GetU16(118, Frequency.Centi);
    }


    public final Object getBackUpLoadModeL3() {
        return LoadMode.parse(GetU16(120));
    }


    public final Watt getBackUpPloadL3() {
        return GetS32(122, Watt.Unit);
    }


    public final Watt getPloadL1() {
        return GetS32(126, Watt.Unit);
    }


    public final Watt getPloadL2() {
        return GetS32(130, Watt.Unit);
    }


    public final Watt getPloadL3() {
        return GetS32(134, Watt.Unit);
    }


    public final Watt getBackUpLoadPower() {
        return GetS32(138, Watt.Unit);
    }


    public final Watt getTotalLoadPower() {
        return GetS16(142, Watt.Unit);
    }


    public final BigDecimal getUPSLoadPercent() {
        return new BigDecimal(GetU16(146)).multiply(Percentage._1);
    }


    public final Celsius getInternalTemperature() {
        return GetS16(148, Celsius.Deci);
    }


    public final Celsius getModuleTemperature() {
        return GetS16(150, Celsius.Deci);
    }


    public final Celsius getHeatSinkTemperature() {
        return GetS16(152, Celsius.Deci);
    }


    public final Volt getBUSVoltage() {
        return GetU16(156, Volt.Deci);
    }


    public final Volt getNBUSVoltage() {
        return GetU16(158, Volt.Deci);
    }


    public final Volt getVbattery() {
        return GetU16(160, Volt.Deci);
    }


    public final Ampere getIbattery() {
        return GetS16(162, Ampere.Deci);
    }


    public final Watt getPbattery() {
        return GetS32(164, Watt.Unit);
    }


    public final Object getBatteryMode() {
        return BattMode.parse((byte) GetU16(168));
    }


    public final int getInverterWarningCode() { return GetU16(170); }


    public final Country getSafetyCountry() {
        return Country.fromValue((int) GetU16(172));
    }


    public final Object getWorkMode() {
        return InverterETWorkMode.parse((byte) GetU16(174));
    }


    public final Object getReserved0x8974() {
        return InverterWorkMode.parse(GetU16(176));
    }


    public final Object getErrorMessage() {
        return InverterErrorMode.parse(GetU32(178));
    }


    public final WattHour getEPVTotal() {
        return GetU32(182, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getEPVDay() {
        return GetU32(186, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getReserved0x897B() {
        return GetU32(190, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final TimeSpan getHTotal() {
        return GetU32(194, TimeSpan.fromHours(1));
    }


    public final WattHour getEDaySell() {
        return GetU16(198, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getReserved0x8980() {
        return GetU32(200, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getEDayBuy() {
        return GetU16(204, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getETotalLoad() {
        return GetU32(206, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getELoadDay() {
        return GetU16(210, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getEBatteryCharge() {
        return GetU32(212, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getEChargeDay() {
        return GetU16(216, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getEBatteryDischarge() {
        return GetU32(218, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getEDischargeDay() {
        return GetU16(222, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final int getBattStrings() { return GetU16(224); }


    public final Object getCPLDWarningCode() {
        return CPLDWarningCode.parse(GetU16(226));
    }


    public final boolean getReserved0x898E() {
        return GetBool((int) GetU16(228), 1, 0, false);
    }


    public final Object getReserved0x898F() {
        return DerateFlag.parse(GetU16(230));
    }


    public final int getReserved0x8990() { return GetU16(232); }


    public final Watt getReserved0x8991() {
        return GetS16(234, Watt.Unit);
    }


    public final Object getDiagStatusH() {
        return DiagStatus.parse(GetU32(236));
    }


    public final Object getDiagStatusL() {
        return DiagStatus.parse(GetU32(240));
    }
}
