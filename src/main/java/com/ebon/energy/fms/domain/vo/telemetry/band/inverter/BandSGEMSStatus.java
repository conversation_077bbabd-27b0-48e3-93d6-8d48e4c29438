package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.SGConnectionState;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandSGEMSStatus extends DataBackedBand {
    public BandSGEMSStatus() {
        super(BandForge.<BandSGEMSStatus>getMetadataFor(BandSGEMSStatus.class));
    }


    public BandSGEMSStatus(byte[] bytes) {
        super(bytes, BandForge.<BandSGEMSStatus>getMetadataFor(BandSGEMSStatus.class));
    }

    public BandSGEMSStatus(String encodedBytes) {
        super(encodedBytes, BandForge.<BandSGEMSStatus>getMetadataFor(BandSGEMSStatus.class));
    }


    public final boolean getEMSReady() {
        return GetBool((int) GetU16(0), 1, 0, false);
    }


    public final int getEMSRxKilobytes() {
        return GetU32(0);
    }


    public final int getEMSTxKilobytes() {
        return GetU32(0);
    }


    public final boolean getBLEConnectionStatus() {
        return GetBool((int) GetU16(0), 1, 0, false);
    }


    public final int getNTPTimeLastSync() {
        return GetU32(0);
    }


    public final Object getConnectionStatus() {
        return SGConnectionState.parse(GetU16(0));
    }


    public final Object getLANConnectionStatus() {
        return SGConnectionState.parse(GetU16(0));
    }


    public final Object getWifiConnectionStatus() {
        return SGConnectionState.parse(GetU16(0));
    }


    public final BigDecimal getWifiRSSI() {
        return new BigDecimal(GetS16(0)).multiply(new BigDecimal("0.1"));
    }
}
