package com.ebon.energy.fms.domain.vo.setting.provider;// Copyright (c) Redback Technologies. All rights reserved.

import java.util.*;
import java.time.*;
import java.math.BigDecimal;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.ICommonSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 设置补丁构建器从一个或多个更改请求创建单个合并补丁
 */
public interface ICommonSettingsBuilder {
    
    ICommonSettingsReader getSettingsReader();

    ICommonSettingsBuilder patchManagedInverterSetting(
            String settingName,
            JsonNode value,
            String settingIndex,
            ManagedInverterSettingDesired.InverterSettingValueType settingType,
            ManagedInverterSettingDesired.InverterSettingExecutionType executionType,
            ManagedInverterSettingDesired.InverterSettingSource source,
            String uniqueId);

    ICommonSettingsBuilder buildInverterPatch(
            RossDesiredSettings desired,
            List<ManagedInverterSettingDesired> expectedSettings, 
            Map<String, Object> toPatch);

    ICommonSettingsBuilder addShadowScan(boolean enableShadowScan);

    ICommonSettingsBuilder addSiteExportLimit(boolean enabled, Watt power);

    ICommonSettingsBuilder addACCoupled(boolean enableACCoupledMode);

    ICommonSettingsBuilder addPowerFactor(BigDecimal powerFactorMinus1To1);
    

    ICommonSettingsBuilder addBatterySettings(
            ManufacturerEnum manufacturer,
            Integer batteryCount,
            Integer maxChargeCurrent,
            Integer maxDischargeCurrent,
            Integer minSoc0to100,
            Integer minOffgridSoc0to100);

    /**
     * 更改遥测周期时，请提供修改后遥测周期的预期持续时间，
     * 以便检查/警报未恢复的遥测周期
     * 
     * @param period 新的遥测周期
     * @param endDateUtc 更改的预期结束日期，如果打算永久则为null
     * @return 构建器
     */
    ICommonSettingsBuilder addTelemetryPeriod(Duration period, Instant endDateUtc);

    ICommonSettingsBuilder addDefaultTelemetryPeriod();

    ICommonSettingsBuilder addCtFlipSettings(Boolean flipCt1, Boolean flipCt2);


    /**
     * 通过路径向累积补丁添加额外设置
     * 
     * @param settingPath 相对于"desired.settings"根的路径
     * @param value 要设置的值
     * @return 返回自身引用以支持链式调用
     */
    ICommonSettingsBuilder patchArbitrarySetting(String settingPath, JsonNode value);

    ICommonSettingsBuilder addPatch(JsonNode patch);

    SettingsPatchResult toPatch();

    SettingsPatchStringResult toPatchString();


    ICommonSettingsBuilder addSoftSiteExportLimit(Watt limit);

    ICommonSettingsBuilder addSoftSiteGenerationLimit(VoltAmps limit);

    ICommonSettingsBuilder addDredSettings(Boolean dredSubscribed);


    /**
     * 空值等于无限制。为所有构建器类型实现。
     * 我们将让构建器实现自己决定是使用ISetAS4777_2_2020_ExportLimitSettingsDto
     * 还是SetAS4777_2_2020_GoodWeSiteExportLimitSettingsDto。
     */
    ICommonSettingsBuilder addSiteLimits(
            Watt softExportLimit, 
            Watt hardExportLimit, 
            VoltAmps generationLimit);

    /**
     * 空值等于无限制。为所有构建器类型实现。
     * 我们将让构建器实现自己决定是使用ISetAS4777_2_2020_ExportLimitSettingsDto
     * 还是SetAS4777_2_2020_GoodWeSiteExportLimitSettingsDto。
     */
    ICommonSettingsBuilder addSoftSiteLimits(Watt exportLimit, VoltAmps generationLimit);

    /**
     * 空值等于无限制。为所有构建器类型实现。
     * 我们将让构建器实现自己决定是使用ISetAS4777_2_2020_ExportLimitSettingsDto
     * 还是SetAS4777_2_2020_GoodWeSiteExportLimitSettingsDto。
     */
    ICommonSettingsBuilder addExportSiteLimits(Watt softExportLimit, Watt hardExportLimit);

    /**
     * 警告：除非您真的知道自己在做什么，否则不要更改。
     * 将功率斜坡率限制更改为新值。默认设置为AS4777.2:2020澳大利亚B。
     * 
     * @param rampTime 表示逆变器从0%到100%应该采用的时间斜坡时间/速率的Duration
     * @param settingId 设置的唯一ID，以便我们有参考它被更新的原因
     * @throws NoSuchElementException 当在现有desired中找不到PowerRateLimitGenerate时
     */
    ICommonSettingsBuilder overrideDesiredPowerRampRateLimit(Duration rampTime, String settingId);

    ICommonSettingsBuilder removeSchedule(String scheduleId);

    ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, Instant endTimeUtc);


    /**
     * 创建覆盖仪表检查完整持续时间的计划。
     * 逆变器将设置适当的模式以在提供的持续时间内支持此检查。
     */
    ICommonSettingsBuilder addMeterCheckModeSchedule(
            String id,
            Instant startTimeUtc,
            Duration duration,
            Watt maxTestPower);

    ICommonSettingsBuilder disableAgeingMode();

    SchedulePriority getSchedulePriority();

    void deleteRelaySchedule(int relayNumber1Based, String id, Map<String, Object> patch);



}
