package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 模型版本关系数据对象
 */
@Data
@Accessors(chain = true)
@TableName("FmsModelVersionRelation")
public class FmsModelVersionRelationDO {

    /**
     * 模型ID
     */
    @TableField(value = "ModelId")
    private Integer modelId;

    /**
     * 型号类型，0：EMSFirmware 1：Firmware
     * @see com.ebon.energy.fms.common.enums.ModelTypeEnum
     */
    @TableField(value = "Type")
    private Integer type;

    /**
     * 版本号
     */
    @TableField(value = "Version")
    private String version;

}