package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.<PERSON>;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import static com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility.*;

@JsonAutoDetect(
        fieldVisibility = ANY,
        getterVisibility = NONE,
        setterVisibility = NONE
)
@Data
public class SiteTelemetry {

    public Boolean MeasuringThirdPartyInverter;
    public Integer ThirdPartyExportCt;
    public Watt SiteImportLimitW;
    public Watt SiteExportLimitW;

    public SiteTelemetry() {
    }

    public SiteTelemetry(Boolean MeasuringThirdPartyInverter,
                         Integer ThirdPartyExportCt,
                         Watt SiteImportLimitW,
                         Watt SiteExportLimitW) {
        this.MeasuringThirdPartyInverter = MeasuringThirdPartyInverter;
        this.ThirdPartyExportCt = ThirdPartyExportCt;
        this.SiteImportLimitW = SiteImportLimitW;
        this.SiteExportLimitW = SiteExportLimitW;
    }
}
