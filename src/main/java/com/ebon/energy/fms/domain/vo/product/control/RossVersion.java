package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.utils.Version;
import lombok.Getter;
import lombok.ToString;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.Nullable;

@Getter
@ToString
@EqualsAndHashCode
public class RossVersion {
    private static final Version EARLIEST_VERSION_THAT_SUPPORTS_WA_COUNTRY = new Version(1, 5, 17, 29);

    @Nullable
    private final Version rossVersionNumber;

    public RossVersion() {
        this.rossVersionNumber = null;
    }

    public RossVersion(String versionString) {
        this.rossVersionNumber = convertVersionStringToVersion(versionString);
    }

    public RossVersion(Version rossVersionNumber) {
        this.rossVersionNumber = rossVersionNumber;
    }

    public boolean isRoss() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(3, 0)) < 0;
    }

    public boolean isEmsAny() {
        return isEmsGen2() || isEmsGen3();
    }

    public boolean isEmsGen2() {
        return rossVersionNumber != null
                && rossVersionNumber.compareTo(new Version(3, 0)) >= 0
                && rossVersionNumber.compareTo(new Version(4, 0)) < 0;
    }

    public boolean isEmsGen3() {
        return rossVersionNumber != null
                && rossVersionNumber.compareTo(new Version(4, 0)) >= 0
                && rossVersionNumber.compareTo(new Version(5, 0)) < 0;
    }

    public boolean isRoss1() {
        return rossVersionNumber != null && rossVersionNumber.getMajor() == 1;
    }

    public boolean isRoss2() {
        return rossVersionNumber != null && rossVersionNumber.getMajor() == 2;
    }

    public boolean isRoss2OrAbove() {
        return rossVersionNumber != null && rossVersionNumber.getMajor() >= 2;
    }

    public boolean supportsWACountry() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(EARLIEST_VERSION_THAT_SUPPORTS_WA_COUNTRY) >= 0;
    }

    public boolean supportsRoss2Schedules() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(2, 1)) >= 0;
    }

    public boolean supportsRoss2UserSchedules() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(2, 4)) >= 0;
    }

    public boolean supportsRossConfigurableRelays() {
        return isRossOnlyVersionOfAtLeast(2, 7);
    }

    public boolean supportsEmsRelaySettings() {
        return isEmsGen2OnlyVersionOfAtLeast(3, 2) || isEmsGen3OnlyVersionOfAtLeast(4, 1);
    }

    public boolean supportsSmartRelaySettings() {
        return isRossOnlyVersionOfAtLeast(2, 18)
                || isEmsGen2OnlyVersionOfAtLeast(3, 2)
                || isEmsGen3OnlyVersionOfAtLeast(4, 2);
    }

    public boolean supportsAcCoupledMode() {
        return isRossOnlyVersionOfAtLeast(2, 6)
                || isEmsGen3OnlyVersionOfAtLeast(4, 1);
    }

    public boolean supportsDred() {
        return isEmsGen2()
                || isEmsGen3OnlyVersionOfAtLeast(4, 1);
    }

    public boolean supportsImprovedSocCalc() {
        return isRossOnlyVersionOfAtLeast(2, 17)
                || isEmsGen2OnlyVersionOfAtLeast(3, 0);
    }

    public boolean supportsSiteCoordinator() {
        return isRossOnlyVersionOfAtLeast(2, 17)
                || isEmsGen2OnlyVersionOfAtLeast(3, 0)
                || isEmsGen3OnlyVersionOfAtLeast(4, 2);
    }

    public boolean supportsNetworkLimits() {
        return isRossOnlyVersionOfAtLeast(2, 18) || isEmsAny();
    }

    public boolean supportsGenerationLimits() {
        return isRossOnlyVersionOfAtLeast(2, 18) || isEmsAny();
    }

    public boolean hasControlCapability() {
        return isRoss1()
                || isRossOnlyVersionOfAtLeast(2, 4)
                || isEmsGen2OnlyVersionOfAtLeast(3, 2)
                || isEmsGen3OnlyVersionOfAtLeast(4, 0);
    }

    public boolean supportsSmartLoadControl() {
        return isRossOnlyVersionOfAtLeast(2, 18)
                || isEmsGen2OnlyVersionOfAtLeast(3, 2)
                || isEmsGen3OnlyVersionOfAtLeast(4, 2);
    }

    public boolean softwareSupportsStandardSiteLimits() {
        return isEmsGen2();
    }

    public boolean softwareSupportsGoodweSiteLimits() {
        return isRoss() || isEmsGen3();
    }

    public boolean isSiInverter() {
        return isEmsGen2();
    }

    public boolean rossSupportsBleCommsStatus() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(2, 5)) >= 0;
    }

    public boolean rossSupportsBandReaderService() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(2, 5)) >= 0;
    }

    public boolean rossSupportsRelayState() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(2, 8)) >= 0;
    }

    public boolean rossSupportsAcCoupledMode() {
        return rossVersionNumber != null && rossVersionNumber.compareTo(new Version(2, 8)) >= 0;
    }

    public boolean rossSupportsV1MeterTest() {
        return isRossOnlyVersionOfAtLeast(2, 3);
    }

    public boolean rossSupportsV2MeterTest() {
        return isRossOnlyVersionOfAtLeast(2, 9);
    }

    public boolean supportsCloudMeterCheck() {
        return isEmsGen3OnlyVersionOfAtLeast(4, 1);
    }

    public boolean supportsMeterCheck() {
        return rossSupportsV1MeterTest() || isEmsGen2() || supportsCloudMeterCheck();
    }

    public boolean rossSupportsAS477722020GridSafteyProfile() {
        return isRossOnlyVersionOfAtLeast(2, 13);
    }

    public boolean supportsCsipFlexibleExports() {
        return (isRoss() && supportsRoss2UserSchedules())
                || isEmsGen2()
                || isEmsGen3OnlyVersionOfAtLeast(4, 2);
    }

    public boolean supportsOnlineBatterySetup() {
        return isEmsGen3OnlyVersionOfAtLeast(4, 1);
    }

    public boolean supportsEmsSchedules() {
        return isEmsGen2OnlyVersionOfAtLeast(3, 2)
                || isEmsGen3OnlyVersionOfAtLeast(4, 2);
    }

    // Private helpers
    private boolean isRossOnlyVersionOfAtLeast(int major, int minor) {
        return rossVersionNumber != null
                && isRoss()
                && rossVersionNumber.compareTo(new Version(major, minor)) >= 0;
    }

    private boolean isEmsGen2OnlyVersionOfAtLeast(int major, int minor) {
        return rossVersionNumber != null
                && isEmsGen2()
                && rossVersionNumber.compareTo(new Version(major, minor)) >= 0;
    }

    private boolean isEmsGen3OnlyVersionOfAtLeast(int major, int minor) {
        return rossVersionNumber != null
                && isEmsGen3()
                && rossVersionNumber.compareTo(new Version(major, minor)) >= 0;
    }

    private static Version convertVersionStringToVersion(String versionString) {
        if (versionString != null) {
            String cleaned = versionString.replace("ROSS v", "").replace(", ", ".");
            try {
                return Version.parse(cleaned);
            } catch (Exception e) {
                // ignore
            }
        }
        return null;
    }
}
