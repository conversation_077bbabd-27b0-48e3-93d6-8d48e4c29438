package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.AFCIfaultNum;
import com.ebon.energy.fms.domain.vo.Ampere;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandEH1PInverterProt extends DataBackedBand
{
	public BandEH1PInverterProt()
	{
		super(BandForge.<BandEH1PInverterProt>getMetadataFor(BandEH1PInverterProt.class));
	}



	public BandEH1PInverterProt(byte[] bytes)
	{
		super(bytes, BandForge.<BandEH1PInverterProt>getMetadataFor(BandEH1PInverterProt.class));
	}

	public BandEH1PInverterProt(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandEH1PInverterProt>getMetadataFor(BandEH1PInverterProt.class));
	}


	
	public final Object getAFCIFaultNum()
	{
		return AFCIfaultNum.parse(GetU16(0));
	}

	
	public final Ampere getEffectiveLeakageCurrent()
	{
		return GetU16(2, Ampere.Unit);
	}
}
