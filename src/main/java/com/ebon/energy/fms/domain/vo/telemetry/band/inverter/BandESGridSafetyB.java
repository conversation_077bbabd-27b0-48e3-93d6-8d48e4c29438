package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandESGridSafetyB extends DataBackedBand
{
	public BandESGridSafetyB()
	{
		super(BandForge.<BandESGridSafetyB>getMetadataFor(BandESGridSafetyB.class));
	}



	public BandESGridSafetyB(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGridSafetyB>getMetadataFor(BandESGridSafetyB.class));
	}

	public BandESGridSafetyB(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGridSafetyB>getMetadataFor(BandESGridSafetyB.class));
	}



	public final Frequency getPowerFreqFStopChg()
	{
		return GetU16(0, Frequency.Centi);
	}


	public final Frequency getPowerFreqFStopTransition()
	{
		return GetU16(2, Frequency.Centi);
	}


	public final boolean getPowerAndFrequencyCurveEnabled2()
	{
		return GetBool((int) GetU16(4), 1, 0, false);
	}
}
