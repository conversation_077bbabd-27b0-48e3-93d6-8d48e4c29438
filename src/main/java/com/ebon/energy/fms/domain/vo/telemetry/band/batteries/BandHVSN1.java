package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;





public class BandHVSN1 extends DataBackedBand
{
	public BandHVSN1()
	{
		super(BandForge.<BandHVSN1>getMetadataFor(BandHVSN1.class));
	}



	public BandHVSN1(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVSN1>getMetadataFor(BandHVSN1.class));
	}

	public BandHVSN1(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVSN1>getMetadataFor(BandHVSN1.class));
	}


	
	public final String getBatteryModule0SerialNumber()
	{
		return GetBufS(0, 32, StringProcessors.PylonHVDecode);
	}

	
	public final String getBatteryModule1SerialNumber()
	{
		return GetBufS(32, 32, StringProcessors.PylonHVDecode);
	}

	
	public final String getBatteryModule2SerialNumber()
	{
		return GetBufS(64, 32, StringProcessors.PylonHVDecode);
	}

	
	public final String getBatteryModule3SerialNumber()
	{
		return GetBufS(96, 32, StringProcessors.PylonHVDecode);
	}
}
