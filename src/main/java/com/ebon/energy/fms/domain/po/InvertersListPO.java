package com.ebon.energy.fms.domain.po;

import lombok.Data;

import java.util.List;

@Data
public class InvertersListPO {

    /**
     * 页码
     */
    private Integer current = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 20;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 是否小时在线
     */
    private Boolean isHourlyOnline;

    /**
     * 是否日在线
     */
    private Boolean isDailyOnline;

    /**
     * 是否有电池
     */
    private Boolean hasBattery;

    /**
     * 是否有错误
     */
    private Boolean hasError;

    /**
     * 是否需要关注
     */
    private Boolean needAttention;

    /**
     * ross版本号
     */
    private String rossVersion;

    /**
     * 固件版本号
     */
    private String firmwareVersion;

    /**
     * 逆变器型号
     */
    private String inverterModelName;

    /**
     * 逆变器模式
     */
    private String inverterMode;

    /**
     * 安装公司
     */
    private String installerCompany;

    /**
     * 标签ID列表
     */
    private List<Integer> tagIds;

}
