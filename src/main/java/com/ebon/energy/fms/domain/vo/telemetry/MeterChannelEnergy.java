package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

/**
 * Telemetry from meter energy measurement.
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterChannelEnergy {
    /**
     * Channel identifier (0=Total, 1=L1, 2=L2, 3=L3, 4=N).
     */
    private int ChannelId;

    public final int getChannelId() {
        return ChannelId;
    }

    public final void setChannelId(int value) {
        ChannelId = value;
    }

    /**
     * Total Import Energy (Wh/VARh/VAh).
     */
    private Integer ImportEnergy = null;

    public final Integer getImportEnergy() {
        return ImportEnergy;
    }

    public final void setImportEnergy(Integer value) {
        ImportEnergy = value;
    }

    /**
     * Total Export Energy (Wh/VARh/VAh).
     */
    private Integer ExportEnergy = null;

    public final Integer getExportEnergy() {
        return ExportEnergy;
    }

    public final void setExportEnergy(Integer value) {
        ExportEnergy = value;
    }

    /**
     * Net energy (Wh/VARh/VAh).
     */
    private Integer NetEnergy = null;

    public final Integer getNetEnergy() {
        return NetEnergy;
    }

    public final void setNetEnergy(Integer value) {
        NetEnergy = value;
    }

    /**
     * Index in the array
     */
    private int Index;

    public final int getIndex() {
        return Index;
    }

    public final void setIndex(int value) {
        Index = value;
    }
}
