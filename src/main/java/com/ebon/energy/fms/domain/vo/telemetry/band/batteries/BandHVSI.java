package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import java.math.*;

public class BandHVSI extends DataBackedBand
{
	public BandHVSI()
	{
		super(BandForge.<BandHVSI>getMetadataFor(BandHVSI.class));
	}



	public BandHVSI(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVSI>getMetadataFor(BandHVSI.class));
	}

	public BandHVSI(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVSI>getMetadataFor(BandHVSI.class));
	}


	
	public final Object getBasicStatus()
	{
		return PylonHVBasicStatus.parse(GetU16(0));
	}

	
	public final Object getProtectionStatus()
	{
		return PylonHVProtectionStatus.parse(GetU16(2));
	}

	
	public final Object getAlarmStatus()
	{
		return PylonHVAlarmStatus.parse(GetU16(4));
	}

	
	public final Volt getTotalVoltage()
	{
		return GetU16(6, Volt.Deci);
	}

	
	public final Ampere getCurrent()
	{
		return GetS32(8, Ampere.Centi);
	}

	
	public final Celsius getTemperature()
	{
		return GetS16(12, Celsius.Deci);
	}

	
	public final BigDecimal getSOC()
	{
		return new BigDecimal(GetU16(14)).multiply(Percentage._1);
	}

	


	public final int getCycleTimes() { return GetU16(16); }

	
	public final Volt getPileMaxChargeVoltage()
	{
		return GetU16(18, Volt.Deci);
	}

	
	public final Ampere getPileMaxChargeCurrent()
	{
		return GetU32(20, Ampere.Centi);
	}

	
	public final Volt getPileMinDischargeVoltage()
	{
		return GetU16(24, Volt.Deci);
	}

	
	public final Ampere getPileMaxDischargeCurrent()
	{
		return GetS32(26, Ampere.Centi);
	}

	
	public final Object getSwitchValueIndicate()
	{
		return PylonHVSwitchValueIndicate.parse(GetU16(30));
	}

	
	public final Volt getBatteryCellMaxVoltage()
	{
		return GetU16(32, Volt.Milli);
	}

	
	public final Volt getBatteryCellMinVoltage()
	{
		return GetU16(34, Volt.Milli);
	}

	


	public final int getBatteryCellMaxVoltageChannel() { return GetU16(36); }

	


	public final int getBatteryCellMinVoltageChannel() { return GetU16(38); }

	
	public final Celsius getBatteryCellMaxTemperature()
	{
		return GetS16(40, Celsius.Deci);
	}

	
	public final Celsius getBatteryCellMinTemperature()
	{
		return GetS16(42, Celsius.Deci);
	}

	


	public final int getBatteryCellMaxTemperatureChannel() { return GetU16(44); }

	


	public final int getBatteryCellMinTemperatureChannel() { return GetU16(46); }

	
	public final Volt getModuleMaxVoltage()
	{
		return GetU16(48, Volt.Centi);
	}

	
	public final Volt getModuleMinVoltage()
	{
		return GetU16(50, Volt.Centi);
	}

	


	public final int getModuleMaxVoltageChannel() { return GetU16(52); }

	


	public final int getModuleMinVoltageChannel() { return GetU16(54); }

	
	public final Celsius getModuleMaxTemperature()
	{
		return GetS16(56, Celsius.Deci);
	}

	
	public final Celsius getModuleMinTemperature()
	{
		return GetS16(58, Celsius.Deci);
	}

	


	public final int getModuleMaxTemperatureChannel() { return GetU16(60); }

	


	public final int getModuleMinTemperatureChannel() { return GetU16(62); }

	
	public final BigDecimal getSOH()
	{
		return new BigDecimal(GetU16(64)).multiply(Percentage._1);
	}

	
	public final WattHour getRemainCapacity()
	{
		return GetU32(66, WattHour.Unit);
	}

	
	public final WattHour getChargeCapacity()
	{
		return GetU32(70, WattHour.Unit);
	}

	
	public final WattHour getDischargeCapacity()
	{
		return GetU32(74, WattHour.Unit);
	}

	
	public final WattHour getDailyAccumulateChargeCapacity()
	{
		return GetU32(78, WattHour.Unit);
	}

	
	public final WattHour getDailyAccumulateDischargeCapacity()
	{
		return GetU32(82, WattHour.Unit);
	}

	
	public final WattHour getHistoryAccumulateChargeCapacity()
	{
		return GetU32(86, WattHour.Kilo);
	}

	
	public final WattHour getHistoryAccumulateDischargeCapacity()
	{
		return GetU32(90, WattHour.Kilo);
	}

	
	public final boolean getRequestForceChargeMark()
	{
		return GetBool((int) GetU16(94), 1, 0, false);
	}

	
	public final boolean getRequestBalanceChargeMark()
	{
		return GetBool((int) (int) GetU16(96), 1, 0, false);
	}

	


	public final int getPileNumberInParallel() { return GetU16(98); }

	
	public final Object getErrorCode1()
	{
		return PylonHVErrorCode1.parse(GetU32(100));
	}

	public final String getErrorCode2()
	{
		return String.valueOf(GetU32(104));
	}


	public final int getNumberOfBatteryModulesInSeriesConnectionInOnePile() { return GetU16(108); }

	


	public final int getNumberOfCellInSeriesInOnePile() { return GetU16(110); }

	
	public final boolean getChargeForbiddenMark()
	{
		return GetBool((int) GetU16(112), 1, 0, false);
	}

	
	public final boolean getDischargeForbiddenMark()
	{
		return GetBool((int) GetU16(114), 1, 0, false);
	}

	


	public final byte[] getReserved0x113A()
	{
		return GetRaw(116, 28);
	}

	public final Ohm getInsulationResistance()
	{
		return GetU16(144, Ohm.Kilo);
	}

	


	public final int getInsulationResistanceErrorLevel() { return GetU16(146); }
}
