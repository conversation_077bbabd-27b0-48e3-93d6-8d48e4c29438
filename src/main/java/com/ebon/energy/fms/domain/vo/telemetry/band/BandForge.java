package com.ebon.energy.fms.domain.vo.telemetry.band;

import com.ebon.energy.fms.common.enums.BandReadMethod;
import com.ebon.energy.fms.common.enums.Endianness;
import com.ebon.energy.fms.domain.vo.telemetry.band.batteries.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.*;

import java.time.Duration;

public final class BandForge {

    public static <T extends DataBackedBand> BandMetadata getMetadataFor(Class<T> bandClass) {
        return GetMetadataForBand(bandClass.getSimpleName());
    }
    public static <TBand extends DataBackedBand> BandMetadata GetMetadataFor() {
        return GetMetadataForBand(DataBackedBand.class.getName());
    }

    public static BandMetadata GetMetadataForBandOrNull(String bandName) {
        switch (bandName) {
            case "BandSGInverterData":
                return new BandMetadata(BandSGInverterData.class,
                        0x00,
                        BandReadMethod.ModbusRegister,
                        "0",
                        0,
                        0x1001,
                        122,
                        2,
                        Endianness.Big,
                        true,
                        false,
                        Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterIsolation":
                return new BandMetadata(BandSGInverterIsolation.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x1F01, 2, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterData":
                return new BandMetadata(BandSGMeterData.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x2208, 220, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGEMSStatus":
                return new BandMetadata(BandSGEMSStatus.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x0000, 4, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterPowerSystem":
                return new BandMetadata(BandSGInverterPowerSystem.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x1300, 100, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterAnalytics":
                return new BandMetadata(BandSGInverterAnalytics.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x7100, 84, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterBooleans":
                return new BandMetadata(BandSGInverterBooleans.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x7300, 4, 2, Endianness.Big, true, true, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterVRT":
                return new BandMetadata(BandSGInverterVRT.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x7600, 6, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterWarningInfo":
                return new BandMetadata(BandSGInverterWarningInfo.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0xB400, 66, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGFirmware":
                return new BandMetadata(BandSGFirmware.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x0000, 50, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterStatus":
                return new BandMetadata(BandSGMeterStatus.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x2300, 4, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterCommsStats":
                return new BandMetadata(BandSGMeterCommsStats.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x2400, 20, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterData3P":
                return new BandMetadata(BandSGMeterData3P.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x3800, 76, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterWindowed3P":
                return new BandMetadata(BandSGMeterWindowed3P.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x3500, 192, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterEnergy3P":
                return new BandMetadata(BandSGMeterEnergy3P.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x3600, 32, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterInformation3P":
                return new BandMetadata(BandSGMeterInformation3P.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x2276, 76, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterAnalytics3P":
                return new BandMetadata(BandSGInverterAnalytics3P.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x7100, 120, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGMeterControl":
                return new BandMetadata(BandSGMeterControl.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x2302, 8, 2, Endianness.Big, true, true, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGCurrentInverterAnalytics":
                return new BandMetadata(BandSGCurrentInverterAnalytics.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x712A, 12, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandSGInverterCountInfo":
                return new BandMetadata(BandSGInverterCountInfo.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x7603, 4, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandTGTInverterAnalytics":
                return new BandMetadata(BandTGTInverterAnalytics.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x7100, 120, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandTGTMeterData":
                return new BandMetadata(BandTGTMeterData.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x3800, 76, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandTGTMeterWindowed":
                return new BandMetadata(BandTGTMeterWindowed.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x3500, 192, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandTGTMeterEnergy":
                return new BandMetadata(BandTGTMeterEnergy.class,  0x00, BandReadMethod.ModbusRegister, "0", 0, 0x3600, 32, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandESA":
                return new BandMetadata(BandESA.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0200, 88, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandESB":
                return new BandMetadata(BandESB.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0500, 156, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESC":
                return new BandMetadata(BandESC.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0550, 168, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESD":
                return new BandMetadata(BandESD.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0701, 32, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESE":
                return new BandMetadata(BandESE.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x6000, 58, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESF":
                return new BandMetadata(BandESF.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x7100, 50, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESG":
                return new BandMetadata(BandESG.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0010, 6, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESH":
                return new BandMetadata(BandESH.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0000, 12, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESI":
                return new BandMetadata(BandESI.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0100, 4, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESJ":
                return new BandMetadata(BandESJ.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0780, 32, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESEco":
                return new BandMetadata(BandESEco.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0700, 2, 2, Endianness.Big, false, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESFallback":
                return new BandMetadata(BandESFallback.class,  0xF7, BandReadMethod.Other, "0002", 0, 0x0000, 77, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(30));
            case "BandESFallbackMinimal":
                return new BandMetadata(BandESFallbackMinimal.class,  0xF7, BandReadMethod.Other, "0002", 0, 0x0000, 64, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(30));
            case "BandESGridSafety":
                return new BandMetadata(BandESGridSafety.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB158, 220, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESLed":
                return new BandMetadata(BandESLed.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0600, 2, 2, Endianness.Big, false, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESC10":
                return new BandMetadata(BandESC10.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x05A4, 10, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESGridStatus":
                return new BandMetadata(BandESGridStatus.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x89B2, 24, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESC12":
                return new BandMetadata(BandESC12.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x0550, 178, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESF12":
                return new BandMetadata(BandESF12.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x7100, 56, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESGridSafety12":
                return new BandMetadata(BandESGridSafety12.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB158, 226, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESK":
                return new BandMetadata(BandESK.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x07A0, 6, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESL":
                return new BandMetadata(BandESL.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB090, 134, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(90), Duration.ofSeconds(60));
            case "BandESM":
                return new BandMetadata(BandESM.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB7A2, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(90), Duration.ofSeconds(60));
            case "BandESN":
                return new BandMetadata(BandESN.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB7A7, 4, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(90), Duration.ofSeconds(60));
            case "BandESR":
                return new BandMetadata(BandESR.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x7506, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESGridSafetyB":
                return new BandMetadata(BandESGridSafetyB.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB1D0, 6, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESInverterAnalytics":
                return new BandMetadata(BandESInverterAnalytics.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x89C4, 48, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESO":
                return new BandMetadata(BandESO.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x8CCC, 2, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESP":
                return new BandMetadata(BandESP.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB928, 48, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandESGInverterRunningData":
                return new BandMetadata(BandESGInverterRunningData.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x891C, 244, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandESGInverterAnalytics":
                return new BandMetadata(BandESGInverterAnalytics.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x89B2, 84, 2, Endianness.Big, true, false, Duration.ofSeconds(12000), Duration.ofSeconds(12000), Duration.ofSeconds(6000));
            case "BandESGMeterData":
                return new BandMetadata(BandESGMeterData.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x8CA0, 116, 2, Endianness.Big, true, false, Duration.ofSeconds(12000), Duration.ofSeconds(12000), Duration.ofSeconds(6000));
            case "BandESGMeterEnergy":
                return new BandMetadata(BandESGMeterEnergy.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x8CFC, 64, 2, Endianness.Big, true, false, Duration.ofSeconds(120000), Duration.ofSeconds(120000), Duration.ofSeconds(60000));
            case "BandESGInverterBMS":
                return new BandMetadata(BandESGInverterBMS.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x9088, 112, 2, Endianness.Big, true, false, Duration.ofSeconds(12000), Duration.ofSeconds(12000), Duration.ofSeconds(6000));
            case "BandESGPeripherals":
                return new BandMetadata(BandESGPeripherals.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0xB7A5, 54, 2, Endianness.Big, true, true, Duration.ofSeconds(120000), Duration.ofSeconds(120000), Duration.ofSeconds(60000));
            case "BandESGBattControl":
                return new BandMetadata(BandESGBattControl.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0xBB1C, 36, 2, Endianness.Big, true, true, Duration.ofSeconds(20000), Duration.ofSeconds(20000), Duration.ofSeconds(10000));
            case "BandEH1PAdditionalBMSSettings":
                return new BandMetadata(BandEH1PAdditionalBMSSettings.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x862A, 4, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PAFCISettings":
                return new BandMetadata(BandEH1PAFCISettings.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x85C0, 6, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterAC":
                return new BandMetadata(BandEH1PInverterAC.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x812E, 80, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterACP":
                return new BandMetadata(BandEH1PInverterACP.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x82E8, 36, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterDC":
                return new BandMetadata(BandEH1PInverterDC.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x8119, 20, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterEquipmentFault":
                return new BandMetadata(BandEH1PInverterEquipmentFault.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x82E6, 2, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterLoadEnergy":
                return new BandMetadata(BandEH1PInverterLoadEnergy.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x832C, 36, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterProt":
                return new BandMetadata(BandEH1PInverterProt.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x81BF, 4, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterRunningData1":
                return new BandMetadata(BandEH1PInverterRunningData1.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x80FE, 50, 2, Endianness.Big, true, true, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PMeterData":
                return new BandMetadata(BandEH1PMeterData.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x81E3, 88, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandEH1PInverterRunningData2":
                return new BandMetadata(BandEH1PInverterRunningData2.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x8156, 160, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterRunningData1":
                return new BandMetadata(BandH2InverterRunningData1.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x4000, 68, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterSettingData":
                return new BandMetadata(BandH2InverterSettingData.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x4022, 30, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterRunningData2":
                return new BandMetadata(BandH2InverterRunningData2.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x4031, 72, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterRunningData3":
                return new BandMetadata(BandH2InverterRunningData3.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x4055, 54, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterPVData":
                return new BandMetadata(BandH2InverterPVData.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x4071, 18, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterMeterData":
                return new BandMetadata(BandH2InverterMeterData.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x408D, 16, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterRunningData4":
                return new BandMetadata(BandH2InverterRunningData4.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x40A0, 46, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterEnergyData1":
                return new BandMetadata(BandH2InverterEnergyData1.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x40BC, 134, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterEnergyData2":
                return new BandMetadata(BandH2InverterEnergyData2.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x4137, 192, 2, Endianness.Big, true, false, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandH2InverterSettings4W":
                return new BandMetadata(BandH2InverterSettings4W.class,  0x00, BandReadMethod.ModbusRegister, "0001", 0, 0x8015, 16, 2, Endianness.Big, false, true, Duration.ofSeconds(2000), Duration.ofSeconds(2000), Duration.ofSeconds(1000));
            case "BandETA":
                return new BandMetadata(BandETA.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x88B8, 66, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandETB":
                return new BandMetadata(BandETB.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x891C, 244, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETC":
                return new BandMetadata(BandETC.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x8CA0, 90, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETD":
                return new BandMetadata(BandETD.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x9088, 28, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETE":
                return new BandMetadata(BandETE.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB090, 134, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETF":
                return new BandMetadata(BandETF.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB126, 20, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETG":
                return new BandMetadata(BandETG.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB158, 220, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETH":
                return new BandMetadata(BandETH.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB798, 30, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETI":
                return new BandMetadata(BandETI.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB98C, 78, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETJ":
                return new BandMetadata(BandETJ.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xBB1C, 32, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETK":
                return new BandMetadata(BandETK.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB047, 14, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETG14":
                return new BandMetadata(BandETG14.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB158, 226, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETL":
                return new BandMetadata(BandETL.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB928, 48, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETR":
                return new BandMetadata(BandETR.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB9EF, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETGridSafetyB":
                return new BandMetadata(BandETGridSafetyB.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB1D0, 6, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETH19":
                return new BandMetadata(BandETH19.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB798, 80, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETInverterAnalytics":
                return new BandMetadata(BandETInverterAnalytics.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x89C4, 48, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETGridStatus":
                return new BandMetadata(BandETGridStatus.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x89B2, 24, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETC19":
                return new BandMetadata(BandETC19.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x8CA0, 116, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandETS":
                return new BandMetadata(BandETS.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB9F1, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHA":
                return new BandMetadata(BandBHA.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x88B8, 66, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(300), Duration.ofSeconds(300));
            case "BandBHB":
                return new BandMetadata(BandBHB.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x891C, 244, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHC":
                return new BandMetadata(BandBHC.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x8CA0, 90, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHD":
                return new BandMetadata(BandBHD.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x9088, 28, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHE":
                return new BandMetadata(BandBHE.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB090, 134, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHF":
                return new BandMetadata(BandBHF.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB126, 20, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHG":
                return new BandMetadata(BandBHG.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB158, 220, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHH":
                return new BandMetadata(BandBHH.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB798, 30, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHExtraLed":
                return new BandMetadata(BandBHExtraLed.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB7AB, 10, 2, Endianness.Big, false, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHI":
                return new BandMetadata(BandBHI.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB98C, 78, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHJ":
                return new BandMetadata(BandBHJ.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xBB1C, 32, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHK":
                return new BandMetadata(BandBHK.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB047, 14, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHG14":
                return new BandMetadata(BandBHG14.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB158, 226, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHL":
                return new BandMetadata(BandBHL.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB928, 48, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHR":
                return new BandMetadata(BandBHR.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB9EF, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHGridSafetyB":
                return new BandMetadata(BandBHGridSafetyB.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB1D0, 6, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHH19":
                return new BandMetadata(BandBHH19.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB798, 80, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHInverterAnalytics":
                return new BandMetadata(BandBHInverterAnalytics.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x89C4, 48, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHGridStatus":
                return new BandMetadata(BandBHGridStatus.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x89B2, 24, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHC19":
                return new BandMetadata(BandBHC19.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0x8CA0, 116, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandBHS":
                return new BandMetadata(BandBHS.class,  0xF7, BandReadMethod.ModbusRegister, "0002", 0, 0xB9F1, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(120), Duration.ofSeconds(20), Duration.ofSeconds(10));
            case "BandHVEI":
                return new BandMetadata(BandHVEI.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1000, 26, 2, Endianness.Big, true, false, Duration.ofSeconds(1200), Duration.ofSeconds(1200), Duration.ofSeconds(600));
            case "BandHVEI10":
                return new BandMetadata(BandHVEI10.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1000, 28, 2, Endianness.Big, true, false, Duration.ofSeconds(1200), Duration.ofSeconds(1200), Duration.ofSeconds(600));
            case "BandHVRC":
                return new BandMetadata(BandHVRC.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1090, 8, 2, Endianness.Big, false, true, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandHVSI":
                return new BandMetadata(BandHVSI.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1100, 148, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandHVP1":
                return new BandMetadata(BandHVP1.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1400, 192, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandHVP1ModuleVoltage":
                return new BandMetadata(BandHVP1ModuleVoltage.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1460, 16, 2, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(10));
            case "BandHVP1ModuleTemperature":
                return new BandMetadata(BandHVP1ModuleTemperature.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x14B0, 16, 2, Endianness.Big, true, false, Duration.ofSeconds(1200), Duration.ofSeconds(1200), Duration.ofSeconds(600));
            case "BandHVP1CellVoltage":
                return new BandMetadata(BandHVP1CellVoltage.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1500, 240, 2, Endianness.Big, true, false, Duration.ofSeconds(1200), Duration.ofSeconds(1200), Duration.ofSeconds(600));
            case "BandHVP1CellTemperature":
                return new BandMetadata(BandHVP1CellTemperature.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x1800, 240, 2, Endianness.Big, true, false, Duration.ofSeconds(1200), Duration.ofSeconds(1200), Duration.ofSeconds(600));
            case "BandHVSN1":
                return new BandMetadata(BandHVSN1.class,  0x01, BandReadMethod.ModbusFile, null, 1, 0x0000, 128, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandHVSN2":
                return new BandMetadata(BandHVSN2.class,  0x01, BandReadMethod.ModbusFile, null, 1, 0x0040, 128, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandHVFWSize":
                return new BandMetadata(BandHVFWSize.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0F80, 4, 2, Endianness.Big, true, true, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandHVFWCRC":
                return new BandMetadata(BandHVFWCRC.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0F82, 2, 2, Endianness.Big, true, true, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandHVFWStart":
                return new BandMetadata(BandHVFWStart.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0F83, 2, 2, Endianness.Big, false, true, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandHVFWState":
                return new BandMetadata(BandHVFWState.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0F84, 2, 2, Endianness.Big, true, false, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandHVFWBlock":
                return new BandMetadata(BandHVFWBlock.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0F85, 132, 2, Endianness.Big, true, true, Duration.ofSeconds(600), Duration.ofSeconds(600), Duration.ofSeconds(300));
            case "BandPylonUSModuleAnalogData":
                return new BandMetadata(BandPylonUSModuleAnalogData.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 63, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(10000));
            case "BandPylonUSStackInfo":
                return new BandMetadata(BandPylonUSStackInfo.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 49, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(5000));
            case "BandPylonUSModuleChargeManagement":
                return new BandMetadata(BandPylonUSModuleChargeManagement.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 10, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(10000));
            case "BandPylonUSModuleAlarmInfo":
                return new BandMetadata(BandPylonUSModuleAlarmInfo.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 34, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(10000));
            case "BandPylonUSStackAlarms":
                return new BandMetadata(BandPylonUSStackAlarms.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 4, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(5000));
            case "BandPylonUSStackLimits":
                return new BandMetadata(BandPylonUSStackLimits.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 9, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(5000));
            case "BandPylon42_2000":
                return new BandMetadata(BandPylon42_2000.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 55, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon42_3000":
                return new BandMetadata(BandPylon42_3000.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 61, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon42_5000":
                return new BandMetadata(BandPylon42_5000.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 63, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon44":
                return new BandMetadata(BandPylon44.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 32, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon44C":
                return new BandMetadata(BandPylon44C.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 33, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon44_5000":
                return new BandMetadata(BandPylon44_5000.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 34, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon47":
                return new BandMetadata(BandPylon47.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 25, 1, Endianness.Big, true, false, Duration.ofSeconds(31536000), Duration.ofSeconds(31535000), Duration.ofSeconds(31535000));
            case "BandPylon4F":
                return new BandMetadata(BandPylon4F.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 1, 1, Endianness.Big, true, false, Duration.ofSeconds(31536000), Duration.ofSeconds(31535000), Duration.ofSeconds(31535000));
            case "BandPylon51":
                return new BandMetadata(BandPylon51.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 32, 1, Endianness.Big, true, false, Duration.ofSeconds(31536000), Duration.ofSeconds(31535000), Duration.ofSeconds(31535000));
            case "BandPylon92":
                return new BandMetadata(BandPylon92.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 10, 1, Endianness.Big, true, false, Duration.ofSeconds(120), Duration.ofSeconds(30), Duration.ofSeconds(2));
            case "BandPylon93":
                return new BandMetadata(BandPylon93.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 17, 1, Endianness.Big, true, false, Duration.ofSeconds(31536000), Duration.ofSeconds(31535000), Duration.ofSeconds(31535000));
            case "BandPylon96":
                return new BandMetadata(BandPylon96.class,  0x00, BandReadMethod.Other, null, 0, 0x0000, 6, 1, Endianness.Big, true, false, Duration.ofSeconds(31536000), Duration.ofSeconds(31535000), Duration.ofSeconds(31535000));
            case "BandModuleAnalogData":
                return new BandMetadata(BandModuleAnalogData.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 65, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(10000));
            case "BandModuleAlarmInfo":
                return new BandMetadata(BandModuleAlarmInfo.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 35, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(5000));
            case "BandStackInfo":
                return new BandMetadata(BandStackInfo.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 49, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(10000));
            case "BandStackAlarms":
                return new BandMetadata(BandStackAlarms.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 4, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(10000));
            case "BandStackLimits":
                return new BandMetadata(BandStackLimits.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 9, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(5000));
            case "BandModuleChargeManagement":
                return new BandMetadata(BandModuleChargeManagement.class,  0x01, BandReadMethod.ModbusRegister, null, 0, 0x0001, 10, 1, Endianness.Big, true, false, Duration.ofSeconds(15000), Duration.ofSeconds(15000), Duration.ofSeconds(5000));
            default:
                return null;
        }
    }

    public static BandMetadata GetMetadataForBand(String bandName) {
        var bandMetadata = GetMetadataForBandOrNull(bandName);
        if (bandMetadata == null) {
            throw new UnsupportedOperationException(String.format("Unknown Band requested: %1$s", bandName));
        }

        return bandMetadata;
    }

}