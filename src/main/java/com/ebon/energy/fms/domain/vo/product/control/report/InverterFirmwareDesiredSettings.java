package com.ebon.energy.fms.domain.vo.product.control.report;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@Data
@NoArgsConstructor
@AllArgsConstructor()
@EqualsAndHashCode
public final class InverterFirmwareDesiredSettings {

    @JsonProperty("ARM")
    private String arm;

    @JsonProperty("DSP")
    private String dsp;

    @JsonProperty("Token")
    private String token;

    @JsonProperty("Force")
    private Boolean force;

    @JsonProperty("GracePeriodArmInSeconds")
    private Long gracePeriodArmInSeconds;

    @JsonProperty("GracePeriodDspInSeconds")
    private Long gracePeriodDspInSeconds;

    // Copy constructor
    public InverterFirmwareDesiredSettings(InverterFirmwareDesiredSettings f) {
        this.arm = f.arm;
        this.dsp = f.dsp;
        this.token = f.token;
        this.force = f.force;
        this.gracePeriodArmInSeconds = f.gracePeriodArmInSeconds;
        this.gracePeriodDspInSeconds = f.gracePeriodDspInSeconds;
    }
}
