package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.ZonedDateTime;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class Event {
    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED)
    // @Display(name = "Message")
    private String Message;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER, visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "When (UTC)")
    // @JsonConverter(UtcTimestampConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime WhenUtc;

    public Event(String message, ZonedDateTime whenUtc) {
        this.Message = message;
        this.WhenUtc = whenUtc;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String message) {
        this.Message = message;
    }

    public ZonedDateTime getWhenUtc() {
        return WhenUtc;
    }

    public void setWhenUtc(ZonedDateTime whenUtc) {
        this.WhenUtc = whenUtc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Event event = (Event) o;
        return Objects.equals(Message, event.Message) &&
                Objects.equals(WhenUtc, event.WhenUtc);
    }

    @Override
    public int hashCode() {
        int hash = 17;
        hash = hash * 23 + (Message != null ? Message.hashCode() : 0);
        hash = hash * 23 + (WhenUtc != null ? WhenUtc.hashCode() : 0);
        return hash;
    }

    @Override
    public String toString() {
        return "Event{" +
                "message='" + Message + '\'' +
                ", whenUtc=" + WhenUtc +
                '}';
    }
}