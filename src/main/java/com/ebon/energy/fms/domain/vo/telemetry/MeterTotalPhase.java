package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

/**
 * Based of the EMS Pro Gen 3 protobuf telemetry.
 */
@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class MeterTotalPhase {
    /**
     * Channel identifier  (0=Total, 1=L1, 2=L2, 3=L3, 4=N).
     */
    private int ChannelId;

    public final int getChannelId() {
        return ChannelId;
    }

    public final void setChannelId(int value) {
        ChannelId = value;
    }

    /**
     * Current (A).
     */
    private AvgMinMax Current;

    public final AvgMinMax getCurrent() {
        return Current;
    }

    public final void setCurrent(AvgMinMax value) {
        Current = value;
    }

    /**
     * Apparent Power (VA).
     */
    private AvgMinMax ApparentPower;

    public final AvgMinMax getApparentPower() {
        return ApparentPower;
    }

    public final void setApparentPower(AvgMinMax value) {
        ApparentPower = value;
    }

    /**
     * Active Power (W).
     */
    private AvgMinMax ActivePower;

    public final AvgMinMax getActivePower() {
        return ActivePower;
    }

    public final void setActivePower(AvgMinMax value) {
        ActivePower = value;
    }

    /**
     * Reactive Power (VAR).
     */
    private AvgMinMax ReactivePower;

    public final AvgMinMax getReactivePower() {
        return ReactivePower;
    }

    public final void setReactivePower(AvgMinMax value) {
        ReactivePower = value;
    }

    /**
     * Total Power Factor.
     */
    private AvgMinMax PowerFactor;

    public final AvgMinMax getPowerFactor() {
        return PowerFactor;
    }

    public final void setPowerFactor(AvgMinMax value) {
        PowerFactor = value;
    }
}
