package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.EnergyFlowIssue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.BatteryStatusValue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.GridStatusValue;
import com.ebon.energy.fms.config.CustomZonedDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class EnergyFlowExtVO {
    private Watt ACLoadW;
    private Watt BackupLoadW;
    private Watt PVW;
    private Watt ThirdPartyW;
    private GridStatusValue GridStatus;
    private Watt GridNegativeIsImportW;
    private Boolean HasBatteries;
    private Watt BatteryNegativeIsChargingW;
    private BatteryStatusValue BatteryStatus;
    private BigDecimal BatterySoC0to1;
    private EnergyFlowIssue[] Issues;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime AtUtc;
    @JsonSerialize(using = CustomZonedDateTimeSerializer.class)
    private ZonedDateTime AtLocal;
    private Boolean DRM0Enable;

    public EnergyFlowExtVO(
            Watt ACLoadW,
            Watt BackupLoadW,
            Watt PVW,
            Watt ThirdPartyW,
            Watt GridNegativeIsImportW,
            boolean HasBatteries,
            Watt BatteryNegativeIsChargingW,
            BigDecimal BatterySoC0to1,
            EnergyFlowIssue[] Issues,
            ZonedDateTime AtUtc,
            ZonedDateTime AtLocal,
            boolean batteriesDisabled,
            boolean DRM0Enable) {
        this.ACLoadW = ACLoadW;
        this.BackupLoadW = BackupLoadW;
        this.PVW = PVW;
        this.ThirdPartyW = ThirdPartyW;
        this.GridStatus = EnergyFlowExtVO.deduceGridStatus(GridNegativeIsImportW);
        this.GridNegativeIsImportW = GridNegativeIsImportW != null ? GridNegativeIsImportW : new Watt(0d);
        this.BatteryNegativeIsChargingW = BatteryNegativeIsChargingW != null ? BatteryNegativeIsChargingW : new Watt(0d);
        this.HasBatteries = HasBatteries;
        this.BatteryStatus = deduceBatteryStatus(HasBatteries, BatteryNegativeIsChargingW, batteriesDisabled);
        this.BatterySoC0to1 = BatterySoC0to1;
        this.Issues = Issues;
        this.AtUtc = AtUtc;
        this.AtLocal = AtLocal;
        this.DRM0Enable = DRM0Enable;
    }

    /*public EnergyFlowExtVO(
            Watt ACLoadW,
            Watt BackupLoadW,
            Watt PVW,
            Watt ThirdPartyW,
            GridStatusValue GridStatus,
            Watt GridNegativeIsImportW,
            boolean HasBatteries,
            Watt BatteryNegativeIsChargingW,
            BatteryStatusValue BatteryStatus,
            Double BatterySoC0to1,
            EnergyFlowIssueEnum[] Issues,
            ZonedDateTime AtUtc,
            ZonedDateTime AtLocal) {
        this.ACLoadW = ACLoadW;
        this.BackupLoadW = BackupLoadW;
        this.PVW = PVW;
        this.ThirdPartyW = ThirdPartyW;
        this.GridStatus = GridStatus;
        this.GridNegativeIsImportW = GridNegativeIsImportW;
        this.HasBatteries = HasBatteries;
        this.BatteryNegativeIsChargingW = BatteryNegativeIsChargingW;
        this.BatteryStatus = BatteryStatus;
        this.BatterySoC0to1 = BatterySoC0to1;
        this.Issues = Issues;
        this.AtUtc = AtUtc;
        this.AtLocal = AtLocal;
        this.DRM0Enable = false;
    }*/

    public Watt getACLoadW() {
        return ACLoadW;
    }

    public Watt getBackupLoadW() {
        return BackupLoadW;
    }

    public Watt getPVW() {
        return PVW;
    }

    public Watt getThirdPartyW() {
        return ThirdPartyW;
    }

    public GridStatusValue getGridStatus() {
        return GridStatus;
    }

    public Watt getGridNegativeIsImportW() {
        return GridNegativeIsImportW;
    }

    public Watt getBatteryNegativeIsChargingW() {
        return BatteryNegativeIsChargingW;
    }

    public BatteryStatusValue getBatteryStatus() {
        return BatteryStatus;
    }

    public BigDecimal getBatterySoC0to1() {
        return BatterySoC0to1;
    }

    public EnergyFlowIssue[] getIssues() {
        return Issues;
    }

    public ZonedDateTime getAtUtc() {
        return AtUtc;
    }

    public ZonedDateTime getAtLocal() {
        return AtLocal;
    }

    public static String getBatteryStatusString(EnergyFlowExtVO flow) {
        return flow.HasBatteries ? flow.BatteryStatus.toString() : "No Battery";
    }

    public static String getGridStatusString(GridStatusValue status) {
        if (status == GridStatusValue.Disconnected) {
            return "Disconnected";
        } else if (status == GridStatusValue.Idle) {
            return "Idle";
        } else if (status == GridStatusValue.Import) {
            return "Buying";
        } else if (status == GridStatusValue.Export) {
            return "Selling";
        }
        return status.toString();
    }

    private static BatteryStatusValue deduceBatteryStatus(boolean hasBatteries, Watt negativeIsImportW, boolean batteriesDisabled) {
        if (batteriesDisabled) {
            return BatteryStatusValue.Disabled;
        }
        if (!hasBatteries) {
            return null;
        }
        if (negativeIsImportW == null) {
            return BatteryStatusValue.Disconnected;
        }
        if (negativeIsImportW.getValue().compareTo(BigDecimal.ZERO) == 0) {
            return BatteryStatusValue.Idle;
        }
        if (negativeIsImportW.getValue().compareTo(BigDecimal.ZERO) < 0) {
            return BatteryStatusValue.Charging;
        }
        return BatteryStatusValue.Discharging;
    }

    public static GridStatusValue deduceGridStatus(Watt negativeIsImportW) {
        if (negativeIsImportW == null) {
            return GridStatusValue.Disconnected;
        }
        BigDecimal value = negativeIsImportW.getValue();
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return GridStatusValue.Idle;
        }
        if (value.compareTo(BigDecimal.ZERO) < 0) {
            return GridStatusValue.Import;
        }
        return GridStatusValue.Export;
    }

}