package com.ebon.energy.fms.domain.vo.telemetry;

import java.time.Instant;
import java.time.Duration;

public interface IFrame {
    /**
     * Gets the date and time that the frame was created.
     */
    Instant getTimestampUtc();

    /**
     * Gets the uptime of the system when the frame was created.
     * This value should be used when determining how old the frame is as
     * it is not affected by the system time changing.
     */
    Duration getCreationUptime();

    /**
     * Gets the frames dispose threshold, inherited from the Data band.
     */
    Duration getTimeToLive();

    /**
     * Gets the maximum duration until the frame should be considered historical data, inherited from the Data band.
     */
    Duration getFreshDuration();

    /**
     * Gets the maximum duration until the frame should be refreshed, inherited from the Data band.
     */
    Duration getReadPeriod();

    /**
     * Gets the instant in time when the frame should be refreshed, calculated from the ReadPeriod and CreationUptime.
     */
    Duration getRefreshAt();

    /**
     * Gets the instant in time when the frame should be not be considered for use, calculated from the FreshDuration and CreationUptime.
     */
    Duration getStaleAt();

    /**
     * Gets the instant in time when the frame should be discarded, calculated from the TimeToLive and CreationUptime.
     */
    Duration getExpiresAt();
}
