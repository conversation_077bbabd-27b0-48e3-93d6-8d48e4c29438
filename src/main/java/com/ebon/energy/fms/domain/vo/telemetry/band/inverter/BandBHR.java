package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandBHR extends DataBackedBand
{
	public BandBHR()
	{
		super(BandForge.<BandBHR>getMetadataFor(BandBHR.class));
	}



	public BandBHR(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHR>getMetadataFor(BandBHR.class));
	}

	public BandBHR(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHR>getMetadataFor(BandBHR.class));
	}


	
	public final boolean getHardwareFeedPowerDisable()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}
}
