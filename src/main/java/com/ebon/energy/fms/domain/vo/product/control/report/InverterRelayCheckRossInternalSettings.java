package com.ebon.energy.fms.domain.vo.product.control.report;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InverterRelayCheckRossInternalSettings implements Serializable {
    @JsonProperty("Enabled")
    private boolean enabled;

    @JsonProperty("MaximumAttempts")
    private int maximumAttempts;

    @JsonProperty("LowPVAttempts")
    private int lowPVAttempts;

    @JsonProperty("PVVoltageMinimum")
    private BigDecimal pvVoltageMinimum;

    @JsonProperty("FailureStateDuration")
    private Duration failureStateDuration;

    @JsonProperty("SuccessStateDuration")
    private Duration successStateDuration;

    @JsonProperty("RebootGracePeriod")
    private Duration rebootGracePeriod;

    public InverterRelayCheckReportedSettings asReported() {
        return InverterRelayCheckReportedSettings.builder()
                .enabled(enabled)
                .maximumAttempts(maximumAttempts)
                .lowPVAttempts(lowPVAttempts)
                .pvVoltageMinimum(pvVoltageMinimum)
                .failureStateDuration(failureStateDuration)
                .successStateDuration(successStateDuration)
                .rebootGracePeriod(rebootGracePeriod)
                .build();
    }
}