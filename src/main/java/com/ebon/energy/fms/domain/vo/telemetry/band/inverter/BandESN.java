package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESN extends DataBackedBand
{
	public BandESN()
	{
		super(BandForge.<BandESN>getMetadataFor(BandESN.class));
	}



	public BandESN(byte[] bytes)
	{
		super(bytes, BandForge.<BandESN>getMetadataFor(BandESN.class));
	}

	public BandESN(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESN>getMetadataFor(BandESN.class));
	}


	
	public final boolean getMeterCT1ReverseEnable()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}

	
	public final boolean getErrLogCurrentPage()
	{
		return GetBool((int) GetU16(2), 63, 0, false);
	}
}
