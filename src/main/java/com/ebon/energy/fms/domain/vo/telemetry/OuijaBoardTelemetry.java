package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class OuijaBoardTelemetry {
    private String RossVersion;
    private long RossUpTimeMilliseconds;
    private String OSVersion;
    private String HardwareVersion;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ssXXX")
    private ZonedDateTime WindowsTime;
    private String TimeZone;
    private String Offset;
    private String ComputerName;
    private String IP;
    private String[] IPs;
    private Boolean HasTimeSynced;
    private int SoftwareTimeEpoch;

    public OuijaBoardTelemetry() {
    }

    public String getRossVersion() {
        return RossVersion;
    }

    public void setRossVersion(String rossVersion) {
        RossVersion = rossVersion;
    }

    public Long getRossUpTimeMilliseconds() {
        return RossUpTimeMilliseconds;
    }

    public void setRossUpTimeMilliseconds(long rossUpTimeMilliseconds) {
        RossUpTimeMilliseconds = rossUpTimeMilliseconds;
    }

    public String getOSVersion() {
        return OSVersion;
    }

    public void setOSVersion(String OSVersion) {
        this.OSVersion = OSVersion;
    }

    public String getHardwareVersion() {
        return HardwareVersion;
    }

    public void setHardwareVersion(String hardwareVersion) {
        HardwareVersion = hardwareVersion;
    }

    public ZonedDateTime getWindowsTime() {
        return WindowsTime;
    }

    public void setWindowsTime(ZonedDateTime windowsTime) {
        WindowsTime = windowsTime;
    }

    public String getTimeZone() {
        return TimeZone;
    }

    public void setTimeZone(String timeZone) {
        TimeZone = timeZone;
    }

    public String getOffset() {
        return Offset;
    }

    public void setOffset(String offset) {
        Offset = offset;
    }

    public String getComputerName() {
        return ComputerName;
    }

    public void setComputerName(String computerName) {
        ComputerName = computerName;
    }

    public String getIP() {
        return IP;
    }

    public void setIP(String IP) {
        this.IP = IP;
    }

    public String[] getIPs() {
        return IPs;
    }

    public void setIPs(String[] IPs) {
        this.IPs = IPs;
    }

    public Boolean getHasTimeSynced() {
        return HasTimeSynced;
    }

    public void setHasTimeSynced(Boolean hasTimeSynced) {
        HasTimeSynced = hasTimeSynced;
    }

    public int getSoftwareTimeEpoch() {
        return SoftwareTimeEpoch;
    }

    public void setSoftwareTimeEpoch(int softwareTimeEpoch) {
        SoftwareTimeEpoch = softwareTimeEpoch;
    }
}