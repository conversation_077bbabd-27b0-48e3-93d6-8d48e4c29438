package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandSGMeterControl extends DataBackedBand
{
	public BandSGMeterControl()
	{
		super(BandForge.<BandSGMeterControl>getMetadataFor(BandSGMeterControl.class));
	}



	public BandSGMeterControl(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGMeterControl>getMetadataFor(BandSGMeterControl.class));
	}

	public BandSGMeterControl(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGMeterControl>getMetadataFor(BandSGMeterControl.class));
	}


	
	public final Ampere getCT1ReferenceCurrent()
	{
		return GetU16(0, Ampere.Centi);
	}

	


	public final int getResetMaxMinValues() { return GetU16(6); }
}
