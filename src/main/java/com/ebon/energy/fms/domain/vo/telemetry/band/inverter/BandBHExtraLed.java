package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandBHExtraLed extends DataBackedBand {
    public BandBHExtraLed() {
        super(BandForge.<BandBHExtraLed>getMetadataFor(BandBHExtraLed.class));
    }


    public BandBHExtraLed(byte[] bytes) {
        super(bytes, BandForge.<BandBHExtraLed>getMetadataFor(BandBHExtraLed.class));
    }

    public BandBHExtraLed(String encodedBytes) {
        super(encodedBytes, BandForge.<BandBHExtraLed>getMetadataFor(BandBHExtraLed.class));
    }
}
