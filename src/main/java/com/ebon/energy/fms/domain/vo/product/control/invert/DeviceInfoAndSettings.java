package com.ebon.energy.fms.domain.vo.product.control.invert;

import com.ebon.energy.fms.domain.vo.DesiredAndReportedVO;
import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.product.control.ExplicitSettings;
import com.ebon.energy.fms.domain.vo.product.control.InverterIdentityCard;
import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.RossReportedSettings;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Data
public class DeviceInfoAndSettings extends DesiredAndReported {

    @JsonProperty("Intent")
    private DeviceSettingsIntentVO intent;

    @JsonProperty("IdentityCard")
    private final InverterIdentityCard identityCard;

    @JsonProperty("ExplicitSettings")
    private final ExplicitSettings explicitSettings;

    public DeviceInfoAndSettings(
            @JsonProperty("Reported") RossReportedSettings reported,
            @JsonProperty("Desired") RossDesiredSettings desired,
            @JsonProperty("Intent") DeviceSettingsIntentVO intent,
            @JsonProperty("IdentityCard") InverterIdentityCard identityCard,
            @JsonProperty("ExplicitSettings") ExplicitSettings explicitSettings) {
        super(desired, reported);
        this.intent = intent;
        this.identityCard = identityCard;
        this.explicitSettings = explicitSettings;
    }
}
