package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.time.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandGTelemetry implements ITelemetryBand {
    public InverterBandGTelemetry(ZonedDateTime inverterTimeLocal) {
        setInverterTimeLocal(inverterTimeLocal);
    }

    private ZonedDateTime InverterTimeLocal;

    public final ZonedDateTime getInverterTimeLocal() {
        return InverterTimeLocal;
    }

    private void setInverterTimeLocal(ZonedDateTime value) {
        InverterTimeLocal = value;
    }
}
