package com.ebon.energy.fms.domain.vo;

import com.microsoft.azure.storage.OperationContext;
import com.microsoft.azure.storage.StorageException;
import com.microsoft.azure.storage.table.EntityProperty;
import com.microsoft.azure.storage.table.TableEntity;
import java.util.Date;
import java.util.HashMap;

public class RedbackTableStorageEntity implements TableEntity {
    public static final int MAX_STRING_PROPERTY_CHARACTER_LENGTH = 32000;
    public static final int MAX_TELEMETRY_CHAR_LENGTH = MAX_STRING_PROPERTY_CHARACTER_LENGTH * 2;

    private String partitionKey;
    private String rowKey;
    private Date timestamp;
    private String etag;

    private String document;
    private int generation;
    private String rossTelemetry;
    private String rossTelemetry2;
    private String deviceTelemetry;
    private String deviceTelemetry2;

    /**
     * 空构造方法，Azure SDK 需要
     */
    public RedbackTableStorageEntity() {
    }

    /**
     * 构造方法 - 直接指定 rowKey
     */
    public RedbackTableStorageEntity(String partitionKey, String rowKey,
                                     String document, int generation,
                                     String rossTelemetry) {
        this.partitionKey = partitionKey;
        this.rowKey = rowKey;
        this.document = document;
        this.generation = generation;
        setRossTelemetry(rossTelemetry);
    }

    /**
     * 构造方法 - 自动生成 rowKey
     */
    public RedbackTableStorageEntity(String partitionKey, long epoch,
                                     String document, int generation,
                                     String rossTelemetry,
                                     boolean enableSavingToNewTelemetryStorageAccount) {
        this.partitionKey = partitionKey;
        this.document = document;
        this.generation = generation;
        setRossTelemetry(rossTelemetry);

        if (enableSavingToNewTelemetryStorageAccount) {
            this.rowKey = String.format("%010d", Long.MAX_VALUE - epoch);
        } else {
            this.rowKey = String.format("%010d", epoch);
        }
    }

    // 实现 TableEntity 接口方法
    @Override
    public String getPartitionKey() {
        return partitionKey;
    }

    @Override
    public void setPartitionKey(String partitionKey) {
        this.partitionKey = partitionKey;
    }

    @Override
    public String getRowKey() {
        return rowKey;
    }

    @Override
    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    @Override
    public Date getTimestamp() {
        return timestamp;
    }

    @Override
    public void readEntity(HashMap<String, EntityProperty> properties, OperationContext operationContext) throws StorageException {
        if (properties.containsKey("Document")) {
            this.document = properties.get("Document").getValueAsString();
        }
        if (properties.containsKey("Generation")) {
            this.generation = properties.get("Generation").getValueAsInteger();
        }
        if (properties.containsKey("RossTelemetry")) {
            this.rossTelemetry = properties.get("RossTelemetry").getValueAsString();
        }
        if (properties.containsKey("RossTelemetry2")) {
            this.rossTelemetry2 = properties.get("RossTelemetry2").getValueAsString();
        }
        if (properties.containsKey("DeviceTelemetry")) {
            this.deviceTelemetry = properties.get("DeviceTelemetry").getValueAsString();
        }
        if (properties.containsKey("DeviceTelemetry2")) {
            this.deviceTelemetry2 = properties.get("DeviceTelemetry2").getValueAsString();
        }
    }

    @Override
    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String getEtag() {
        return etag;
    }

    @Override
    public void setEtag(String etag) {
        this.etag = etag;
    }

    @Override
    public HashMap<String, EntityProperty> writeEntity(OperationContext opContext) {
        HashMap<String, EntityProperty> properties = new HashMap<>();
        if (document != null) {
            properties.put("Document", new EntityProperty(document));
        }
        properties.put("Generation", new EntityProperty(generation));
        if (rossTelemetry != null) {
            properties.put("RossTelemetry", new EntityProperty(rossTelemetry));
        }
        if (rossTelemetry2 != null) {
            properties.put("RossTelemetry2", new EntityProperty(rossTelemetry2));
        }
        if (deviceTelemetry != null) {
            properties.put("DeviceTelemetry", new EntityProperty(deviceTelemetry));
        }
        if (deviceTelemetry2 != null) {
            properties.put("DeviceTelemetry2", new EntityProperty(deviceTelemetry2));
        }
        return properties;
    }

    // 其他 getter 和 setter 方法保持不变...
    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }

    public int getGeneration() {
        return generation;
    }

    public void setGeneration(int generation) {
        this.generation = generation;
    }

    public String getRossTelemetry() {
        return rossTelemetry;
    }

    public void setRossTelemetry(String rossTelemetry) {
        this.rossTelemetry = getChunk(rossTelemetry, 0);
        this.rossTelemetry2 = getChunk(rossTelemetry, 1);
    }

    public String getRossTelemetry2() {
        return rossTelemetry2;
    }

    public void setRossTelemetry2(String rossTelemetry2) {
        this.rossTelemetry2 = rossTelemetry2;
    }

    public String getDeviceTelemetry() {
        return deviceTelemetry;
    }

    public void setDeviceTelemetry(String deviceTelemetry) {
        this.deviceTelemetry = getChunk(deviceTelemetry, 0);
        this.deviceTelemetry2 = getChunk(deviceTelemetry, 1);
    }

    public String getDeviceTelemetry2() {
        return deviceTelemetry2;
    }

    public void setDeviceTelemetry2(String deviceTelemetry2) {
        this.deviceTelemetry2 = deviceTelemetry2;
    }

    // 其他辅助方法保持不变...
    public static String getChunk(String s, int zeroBasedIndex, int chunkLength) {
        if (s == null) {
            return null;
        }
        int start = zeroBasedIndex * chunkLength;
        if (start >= s.length()) {
            return null;
        }
        int end = Math.min(start + chunkLength, s.length());
        return s.substring(start, end);
    }

    private static String getChunk(String s, int zeroBasedIndex) {
        return getChunk(s, zeroBasedIndex, MAX_STRING_PROPERTY_CHARACTER_LENGTH);
    }

    public String getFullRossTelemetry() {
        return combineChunks(rossTelemetry, rossTelemetry2);
    }

    public String getFullDeviceTelemetry() {
        return combineChunks(deviceTelemetry, deviceTelemetry2);
    }

    private String combineChunks(String part1, String part2) {
        if (part1 == null) return part2;
        if (part2 == null) return part1;
        return part1 + part2;
    }
}