package com.ebon.energy.fms.domain.vo.product.control.report;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InverterRelayCheckReportedSettings implements Serializable {
    @JsonProperty("Enabled")
    private Boolean enabled;

    @JsonProperty("MaximumAttempts")
    private Integer maximumAttempts;

    @JsonProperty("LowPVAttempts")
    private Integer lowPVAttempts;

    @JsonProperty("PVVoltageMinimum")
    private BigDecimal pvVoltageMinimum;

    @JsonProperty("FailureStateDuration")
    private Duration failureStateDuration;

    @JsonProperty("SuccessStateDuration")
    private Duration successStateDuration;

    @JsonProperty("RebootGracePeriod")
    private Duration rebootGracePeriod;
}