package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.ACRMeterType;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandESE extends DataBackedBand
{
	public BandESE()
	{
		super(BandForge.<BandESE>getMetadataFor(BandESE.class));
	}



	public BandESE(byte[] bytes)
	{
		super(bytes, BandForge.<BandESE>getMetadataFor(BandESE.class));
	}

	public BandESE(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESE>getMetadataFor(BandESE.class));
	}


	
	public final Object getACRMeterType()
	{
		return ACRMeterType.parse(GetU16(0));
	}

	
	public final boolean getMeterStatus2()
	{
		return GetBool((int) GetU16(2), 1, 0, true);
	}

	
	public final Volt getPhaseAVoltage()
	{
		return GetU16(4, Volt.Deci);
	}

	
	public final Volt getPhaseBVoltage()
	{
		return GetU16(6, Volt.Deci);
	}

	
	public final Volt getPhaseCVoltage()
	{
		return GetU16(8, Volt.Deci);
	}

	
	public final Ampere getPhaseACurrent()
	{
		return GetU16(10, Ampere.Centi);
	}

	
	public final Ampere getPhaseBCurrent()
	{
		return GetU16(12, Ampere.Centi);
	}

	
	public final Ampere getPhaseCCurrent()
	{
		return GetU16(14, Ampere.Centi);
	}

	
	public final Watt getPhaseAActivePower()
	{
		return GetS16(16, Watt.Unit);
	}

	
	public final Watt getPhaseBActivePower()
	{
		return GetS16(18, Watt.Unit);
	}

	
	public final Watt getPhaseCActivePower()
	{
		return GetS16(20, Watt.Unit);
	}

	
	public final Watt getTotalActivePower()
	{
		return GetS16(22, Watt.Unit);
	}

	
	public final VoltAmpsReactive getPhaseAReactivePower()
	{
		return GetS16(24, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmpsReactive getPhaseBReactivePower()
	{
		return GetS16(26, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmpsReactive getPhaseCReactivePower()
	{
		return GetS16(28, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmpsReactive getTotalReactivePower()
	{
		return GetS16(30, VoltAmpsReactive.Unit);
	}

	
	public final VoltAmps getPhaseAApparentPower()
	{
		return GetS16(32, VoltAmps.Unit);
	}

	
	public final VoltAmps getPhaseBApparentPower()
	{
		return GetS16(34, VoltAmps.Unit);
	}

	
	public final VoltAmps getPhaseCApparentPower()
	{
		return GetS16(36, VoltAmps.Unit);
	}

	
	public final VoltAmps getTotalApparentPower()
	{
		return GetS16(38, VoltAmps.Unit);
	}

	
	public final short getPhaseAPowerFactor()
	{
		return GetS16(40);
	}

	
	public final short getPhaseBPowerFactor()
	{
		return GetS16(42);
	}

	
	public final short getPhaseCPowerFactor()
	{
		return GetS16(44);
	}

	
	public final short getTotalPowerFactor()
	{
		return GetS16(46);
	}

	
	public final Frequency getFrequency()
	{
		return GetU16(48, Frequency.Centi);
	}

	
	public final WattHour getETotalSell()
	{
		return GetFloat(50, WattHour.Unit);
	}

	
	public final WattHour getETotalBuy()
	{
		return GetFloat(54, WattHour.Unit);
	}
}
