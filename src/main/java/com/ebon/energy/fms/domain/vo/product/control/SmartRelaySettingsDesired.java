package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.SmartRelayLoadType;
import com.ebon.energy.fms.common.enums.SmartRelayMode;
import com.ebon.energy.fms.common.json.HmsDurationDeserializer;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Value;
import lombok.With;
import lombok.EqualsAndHashCode;

import java.time.Duration;

@Value
@With
@EqualsAndHashCode
public class SmartRelaySettingsDesired {

    // 常量字段
    public static final String LOAD_CONTROL_NAME = "loadControlName";
    public static final String LOAD_CONTROL_INSTALLED_NAME = "loadControlInstalled";
    public static final String AUTO_LOAD_CONTROL_TYPE_NAME = "autoLoadControlType";
    public static final String AUTO_LOAD_CONTROL_ENABLED_NAME = "autoLoadControlEnabled";
    public static final String AUTO_LOAD_CONTROL_SOURCE_NAME = "autoLoadControlSource";
    public static final String AUTO_LOAD_CONTROL_ON_TRIGGER_NAME = "autoLoadControlOnTrigger";
    public static final String AUTO_LOAD_CONTROL_ON_DELAY_NAME = "autoLoadControlOnDelay";
    public static final String AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME = "autoLoadControlOffTrigger";
    public static final String AUTO_LOAD_CONTROL_OFF_DELAY_NAME = "autoLoadControlOffDelay";
    public static final String AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME = "autoLoadControlMinRunTime";
    public static final String AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME = "autoLoadControlMinOffTime";
    public static final String AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME = "autoLoadControlDailyRunTimeTarget";
    public static final String AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME = "autoLoadControlDailyRunTimeCompletionTime";
    public static final String AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME = "autoLoadControlDailyRunTimeUseExcessPower";
    public static final String LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME = "loadRelayActiveHigh";
    public static final String LOAD_CONTROL_MODE_NAME = "loadControlMode";
    public static final String LOAD_CONTROL_TIME_ZONE_NAME = "loadControlTimeZone";
    public static final String LOAD_CONTROL_NOMINATED_RELAY = "loadControlNominatedRelay";
    public static final String SMART_LOAD_CONTROL_DEFAULT_NAME = "Relay";

    // 属性字段
    @JsonProperty(LOAD_CONTROL_NAME)
    String name;

    @JsonProperty(LOAD_CONTROL_INSTALLED_NAME)
    Boolean installed;

    @JsonProperty(AUTO_LOAD_CONTROL_TYPE_NAME)
    SmartRelayLoadType loadType;

    @JsonProperty(AUTO_LOAD_CONTROL_ENABLED_NAME)
    Boolean enabled;

    @JsonProperty(AUTO_LOAD_CONTROL_SOURCE_NAME)
    Integer source; // Java 没有 ushort，通常用 Integer

    @JsonProperty(AUTO_LOAD_CONTROL_ON_TRIGGER_NAME)
    Integer onTrigger;

    @JsonProperty(AUTO_LOAD_CONTROL_ON_DELAY_NAME)
    @JsonDeserialize(using = HmsDurationDeserializer.class)
    Duration onDelay;

    @JsonProperty(AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME)
    Integer offTrigger;

    @JsonProperty(AUTO_LOAD_CONTROL_OFF_DELAY_NAME)
    @JsonDeserialize(using = HmsDurationDeserializer.class)
    Duration offDelay;

    @JsonDeserialize(using = HmsDurationDeserializer.class)
    @JsonProperty(AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME)
    Duration minRunTime;

    @JsonDeserialize(using = HmsDurationDeserializer.class)
    @JsonProperty(AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME)
    Duration minOffTime;

    @JsonDeserialize(using = HmsDurationDeserializer.class)
    @JsonProperty(AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME)
    Duration dailyRunTimeTarget;

    @JsonDeserialize(using = HmsDurationDeserializer.class)
    @JsonProperty(AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME)
    Duration dailyRunTimeCompletionTime;

    @JsonProperty(AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME)
    Boolean dailyRunTimeUseExcessPower;

    @JsonProperty(LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME)
    Boolean relayActiveHigh;

    @JsonProperty(LOAD_CONTROL_MODE_NAME)
    SmartRelayMode mode;

    @JsonProperty(LOAD_CONTROL_TIME_ZONE_NAME)
    String timeZoneAlias;

    @JsonProperty(LOAD_CONTROL_NOMINATED_RELAY)
    Integer nominatedRelay;
}
