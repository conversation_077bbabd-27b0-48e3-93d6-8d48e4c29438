package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("RedbackProducts")
public class RedbackProductsDO {

    @TableField(value = "RedbackProductSn")
    private String redbackProductSn;

    @TableField(value = "LatestSystemStatus")
    private String latestSystemStatus;

    @TableField(value = "LastSystemStatusReceived")
    private Timestamp lastSystemStatusReceived;

    @TableField(value = "LatestRossTelemetry")
    private String latestRossTelemetry;

    @TableField(value = "LatestRossDeviceTelemetry")
    private String latestRossDeviceTelemetry;

}