package com.ebon.energy.fms.domain.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ProductDailyHistoryDO {

    private String serialNumber;

    private String date;

    private Timestamp dateDate;

    private Integer dailyUsageWh;

    private Integer dailySoldWh;

    private Integer dailyBoughtWh;

    private Integer dailyGenerationWh;

    public Integer dailyBatteryChargedWh;

    public Integer dailyBatteryDischargedWh;

    public Boolean dailyUsageAdjusted;

    public Boolean dailySoldAdjusted;

    public Boolean dailyBoughtAdjusted;

    public Boolean dailyGenerationAdjusted;

    public Boolean dailyBatteryChargedAdjusted;

    public Boolean dailyBatteryDischargedAdjusted;
    
}
