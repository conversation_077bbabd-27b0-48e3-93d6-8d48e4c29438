package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.time.LocalDateTime;
import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryFirmwareUpdateTelemetry {

    private final List<IndividualFirmwareUpdateTelemetry> Progress;
    private final String Status;
    private final String ConfigurationId;
    private final LocalDateTime StartedAt;
    private final LocalDateTime CompletedAt;

    public BatteryFirmwareUpdateTelemetry() {
        this(null, null, null, null, null);
    }

    public BatteryFirmwareUpdateTelemetry(
            List<IndividualFirmwareUpdateTelemetry> Progress,
            String Status,
            String ConfigurationId,
            LocalDateTime StartedAt,
            LocalDateTime CompletedAt) {
        this.Progress = Progress;
        this.Status = Status;
        this.ConfigurationId = ConfigurationId;
        this.StartedAt = StartedAt;
        this.CompletedAt = CompletedAt;
    }

    public List<IndividualFirmwareUpdateTelemetry> getProgress() {
        return Progress;
    }

    public String getStatus() {
        return Status;
    }

    public String getConfigurationId() {
        return ConfigurationId;
    }

    public LocalDateTime getStartedAt() {
        return StartedAt;
    }

    public LocalDateTime getCompletedAt() {
        return CompletedAt;
    }
}
