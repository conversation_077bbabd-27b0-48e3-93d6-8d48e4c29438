package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class AllAboutSiteOverview {

    private String publicSiteId;
    private DataForDashboardVO dataForDashboard;
    private List<AllAboutDevice> deviceDetails;
    private LocalDate todaysDateInLocalTime;
    private Boolean hasSupportForConnectedPV;
    private WattHour maximumPossiblePVOutput;
    private Boolean hasSolar;
    private Boolean hasBatteries;
    private Boolean batteryMismatchProtectionEnabled;
    private LocalDate supportsLoadContributorsSince;
    private Boolean measuringThirdPartyInverter;
    private Boolean isAcCoupledMode;

    public AllAboutSiteOverview(
            String publicSiteId,
            DataForDashboardVO dataForDashboard,
            List<AllAboutDevice> deviceDetails,
            LocalDate todaysDateInLocalTime,
            Boolean hasSupportForConnectedPV,
            WattHour maximumPossiblePVOutput,
            Boolean hasSolar,
            Boolean hasBatteries,
            Boolean batteryMismatchProtectionEnabled,
            LocalDate supportsLoadContributorsSince,
            Boolean measuringThirdPartyInverter,
            Boolean isAcCoupledMode) {
        this.publicSiteId = publicSiteId;
        this.dataForDashboard = dataForDashboard;
        this.deviceDetails = deviceDetails;
        this.todaysDateInLocalTime = todaysDateInLocalTime;
        this.maximumPossiblePVOutput = maximumPossiblePVOutput;
        this.hasSupportForConnectedPV = hasSupportForConnectedPV;
        this.hasSolar = hasSolar;
        this.hasBatteries = hasBatteries;
        this.batteryMismatchProtectionEnabled = batteryMismatchProtectionEnabled;
        this.supportsLoadContributorsSince = supportsLoadContributorsSince;
        this.measuringThirdPartyInverter = measuringThirdPartyInverter;
        this.isAcCoupledMode = isAcCoupledMode;
    }

}