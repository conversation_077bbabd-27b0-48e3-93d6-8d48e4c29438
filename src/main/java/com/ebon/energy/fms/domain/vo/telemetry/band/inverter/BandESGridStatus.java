package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.ExtGridDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedErr;
import com.ebon.energy.fms.common.enums.ExtInvDetailedStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESGridStatus extends DataBackedBand
{
	public BandESGridStatus()
	{
		super(BandForge.<BandESGridStatus>getMetadataFor(BandESGridStatus.class));
	}



	public BandESGridStatus(byte[] bytes)
	{
		super(bytes, BandForge.<BandESGridStatus>getMetadataFor(BandESGridStatus.class));
	}

	public BandESGridStatus(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESGridStatus>getMetadataFor(BandESGridStatus.class));
	}



	public final Object getExtGridDetailedErr()
	{
		return ExtGridDetailedErr.parse(GetU64(0));
	}


	public final Object getExtInvDetailedErr()
	{
		return ExtInvDetailedErr.parse(GetU64(8));
	}


	public final Object getExtInvDetailedStatus()
	{
		return ExtInvDetailedStatus.parse(GetU64(16));
	}
}
