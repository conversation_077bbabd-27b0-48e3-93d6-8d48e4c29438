package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.TelemetryErrorCodeEnum;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Optional;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class ErrorTelemetry {
    private TelemetryErrorCodeEnum ErrorCode;

    public Integer ErrorNumber;
    public String Message;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public ZonedDateTime FirstUtc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    public ZonedDateTime LatestUtc;
    public Long Count;

    public ErrorTelemetry(TelemetryErrorCodeEnum errorCode, ZonedDateTime whenUtc) {
        this(errorCode, null, null, null, whenUtc, null);
    }

    public ErrorTelemetry(TelemetryErrorCodeEnum errorCode, String message, ZonedDateTime whenUtc) {
        this(errorCode, null, message, null, whenUtc, null);
    }

    public ErrorTelemetry(TelemetryErrorCodeEnum errorCode, String message, Exception ex, ZonedDateTime whenUtc) {
        this(errorCode, null, WithExceptionInfo(message, ex), null, whenUtc, null);
    }

    public ErrorTelemetry(TelemetryErrorCodeEnum errorCode, Integer errorNumber, String message,
                          ZonedDateTime firstUtc, ZonedDateTime latestUtc, Long count) {
        this.ErrorCode = errorCode;
        this.ErrorNumber = errorNumber;
        this.Message = message;
        this.FirstUtc = firstUtc;
        this.LatestUtc = latestUtc;
        this.Count = count;
    }

    public static ErrorTelemetry ForTestsOnly(TelemetryErrorCodeEnum errorCode, String message,
                                              ZonedDateTime firstUtc, ZonedDateTime latestUtc, Long count) {
        return new ErrorTelemetry(errorCode, null, message, firstUtc, latestUtc, count);
    }

    public ErrorTelemetry Again(ZonedDateTime whenUtc) {
        return new ErrorTelemetry(
                ErrorCode,
                ErrorNumber,
                Message,
                Optional.ofNullable(FirstUtc).orElse(whenUtc),
                whenUtc,
                (Count != null ? Count : 1L) + 1L);
    }

    public String GetCodeAndMessage() {
        return Amalgamate(ErrorCode, Message);
    }

    public ZonedDateTime GetComputedFirstUtc() {
        return FirstUtc != null ? FirstUtc : LatestUtc;
    }

    public static String Amalgamate(TelemetryErrorCodeEnum errorCode, String message) {
        return errorCode == TelemetryErrorCodeEnum.GeneralError ? message :
                errorCode.toString() + ": " + message;
    }

    public static String GetThrowingSpotInfo(Exception ex) {
        try {
            if (ex != null && ex.getStackTrace().length > 0) {
                return ex.getStackTrace()[0].getMethodName();
            }
            return "(Unknown)";
        } catch (Exception e) {
            return "(Unknown)";
        }
    }

    public static String WithExceptionInfo(String msg, Exception ex) {
        if (ex == null) {
            return msg;
        }

        if (ex.getCause() instanceof NullPointerException) {
            return msg + ": NullPointerException @ " + GetThrowingSpotInfo(ex);
        }

        String exMsg = ex.getMessage();
        String exBaseMsg = ex.getCause() != null ? ex.getCause().getMessage() : exMsg;

        String exFullMsg = !exMsg.equals(exBaseMsg) ?
                exMsg + " / " + exBaseMsg : exMsg;

        return msg == null ? exMsg : msg + ": " + exFullMsg;
    }
}