package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 逆变器关注表
 */
@Data
@Accessors
@TableName("InverterAttention")
public class InverterAttentionDO {

    /**
     * 逆变器序列号
     */
    @TableId(value = "RedbackProductSn")
    private String redbackProductSn;

    @TableField(value = "NeedAttention")
    private Boolean needAttention;

    @TableField(value = "HasError")
    private Boolean hasError;
    
    /**
     * 0-待处理 1-已处理
     */
    @TableField(value = "Status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "CreatedOnUtc")
    private Timestamp createdOnUtc;

    @TableField(value = "LastSystemStatusReceived")
    private Timestamp lastSystemStatusReceived;

    @TableField("IncidentsForRb")
    private Integer incidentsForRb;

    @TableField("IncidentsForInstaller")
    private Integer incidentsForInstaller;

    @TableField("IncidentsForHomeUser")
    private Integer incidentsForHomeUser;

    @TableField("LastErrors")
    private String lastErrors;

}