package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class InverterBandsTelemetry {
    public TelemetryBandFrame<InverterBandATelemetry> BandA;
    public TelemetryBandFrame<InverterBandBTelemetry> BandB;
    public TelemetryBandFrame<InverterBandCTelemetry> BandC;
    public TelemetryBandFrame<InverterBandDTelemetry> BandD;
    public TelemetryBandFrame<InverterBandETelemetry> BandE;
    public TelemetryBandFrame<InverterBandFTelemetry> BandF;
    public TelemetryBandFrame<InverterBandGTelemetry> BandG;
    public TelemetryBandFrame<InverterBandHTelemetry> BandH;
    public TelemetryBandFrame<InverterBandITelemetry> BandI;
    public TelemetryBandFrame<InverterBandJTelemetry> BandJ;

    public InverterBandsTelemetry() {}

    public InverterBandsTelemetry(
            TelemetryBandFrame<InverterBandATelemetry> bandA,
            TelemetryBandFrame<InverterBandBTelemetry> bandB,
            TelemetryBandFrame<InverterBandCTelemetry> bandC,
            TelemetryBandFrame<InverterBandDTelemetry> bandD,
            TelemetryBandFrame<InverterBandETelemetry> bandE,
            TelemetryBandFrame<InverterBandFTelemetry> bandF,
            TelemetryBandFrame<InverterBandGTelemetry> bandG,
            TelemetryBandFrame<InverterBandHTelemetry> bandH,
            TelemetryBandFrame<InverterBandITelemetry> bandI,
            TelemetryBandFrame<InverterBandJTelemetry> bandJ) {

        this.BandA = bandA;
        this.BandB = bandB;
        this.BandC = bandC;
        this.BandD = bandD;
        this.BandE = bandE;
        this.BandF = bandF;
        this.BandG = bandG;
        this.BandH = bandH;
        this.BandI = bandI;
        this.BandJ = bandJ;
    }
}