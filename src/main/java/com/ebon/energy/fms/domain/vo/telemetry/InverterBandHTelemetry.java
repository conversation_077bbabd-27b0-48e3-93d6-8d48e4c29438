package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.*;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandHTelemetry implements ITelemetryBand {
    public InverterBandHTelemetry(Volt lowestFeedingPVVoltage, TimeSpan reconnectTime, Volt lowLimitGridVoltage, Volt highLimitGridVoltage, Frequency lowLimitGridFrequency, Frequency highLimitGridFreqency) {
        LowestFeedingPVVoltage = lowestFeedingPVVoltage;
        ReconnectTime = reconnectTime;
        LowLimitGridVoltage = lowLimitGridVoltage;
        HighLimitGridVoltage = highLimitGridVoltage;
        LowLimitGridFrequency = lowLimitGridFrequency;
        HighLimitGridFreqency = highLimitGridFreqency;
    }

    private final Volt LowestFeedingPVVoltage;

    public final Volt getLowestFeedingPVVoltage() {
        return LowestFeedingPVVoltage;
    }

    private final TimeSpan ReconnectTime;

    public final TimeSpan getReconnectTime() {
        return ReconnectTime;
    }

    private final Volt LowLimitGridVoltage;

    public final Volt getLowLimitGridVoltage() {
        return LowLimitGridVoltage;
    }

    private final Volt HighLimitGridVoltage;

    public final Volt getHighLimitGridVoltage() {
        return HighLimitGridVoltage;
    }

    private final Frequency LowLimitGridFrequency;

    public final Frequency getLowLimitGridFrequency() {
        return LowLimitGridFrequency;
    }

    private final Frequency HighLimitGridFreqency;

    public final Frequency getHighLimitGridFreqency() {
        return HighLimitGridFreqency;
    }
}
