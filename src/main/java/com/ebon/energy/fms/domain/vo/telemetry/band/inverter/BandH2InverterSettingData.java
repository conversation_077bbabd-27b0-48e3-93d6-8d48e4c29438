package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.BatteryProtocolH2;
import com.ebon.energy.fms.common.enums.H2BatStatus;
import com.ebon.energy.fms.common.enums.InverterWorkMode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandH2InverterSettingData extends DataBackedBand {
    public BandH2InverterSettingData() {
        super(BandForge.<BandH2InverterSettingData>getMetadataFor(BandH2InverterSettingData.class));
    }


    public BandH2InverterSettingData(byte[] bytes) {
        super(bytes, BandForge.<BandH2InverterSettingData>getMetadataFor(BandH2InverterSettingData.class));
    }

    public BandH2InverterSettingData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandH2InverterSettingData>getMetadataFor(BandH2InverterSettingData.class));
    }


    public final Object getInverterMode() {
        return InverterWorkMode.parse(GetS16(0));
    }


    public final Watt getInvDisPowerSet() {
        return GetS16(2, Watt.Unit);
    }


    public final Watt getInvChgPowerSet() {
        return GetS16(4, Watt.Unit);
    }


    public final Ampere getBatDisCurrSet() {
        return GetS16(6, Ampere.Deci);
    }


    public final Ampere getBatChgCurrSet() {
        return GetS16(8, Ampere.Deci);
    }


    public final Object getBatStatus() {
        return H2BatStatus.parse(GetU16(10));
    }


    public final Object getBatProtocolSet() {
        return BatteryProtocolH2.parse(GetS16(12));
    }


    public final BigDecimal getBatMaxSOC() {
        return new BigDecimal(GetS16(14)).multiply(Percentage._1);
    }


    public final BigDecimal getBatMinSOC() {
        return new BigDecimal(GetS16(16)).multiply(Percentage._1);
    }


    public final BigDecimal getBatDODSet() {
        return new BigDecimal(GetS16(18)).multiply(Percentage._1);
    }


    public final BigDecimal getBatReserveSoc() {
        return new BigDecimal(GetS16(20)).multiply(Percentage._1);
    }


    public final Volt getBmsChargeVoltageSet() {
        return GetS16(22, Volt.Deci);
    }


    public final Ampere getBmsDischargePowerSet() {
        return GetS16(24, Ampere.Unit);
    }


    public final Ampere getBmsChargePowerSet() {
        return GetS16(26, Ampere.Unit);
    }


    public final short getMeterModeSet() {
        return (short) GetS16(28);
    }
}
