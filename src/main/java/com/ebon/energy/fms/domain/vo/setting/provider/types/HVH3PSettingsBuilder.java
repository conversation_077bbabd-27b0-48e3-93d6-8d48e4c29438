package com.ebon.energy.fms.domain.vo.setting.provider.types;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.utils.path.ESGSettingPaths;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.ebon.energy.fms.domain.vo.product.control.ESGSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.HVH3PSettingPaths;
import com.ebon.energy.fms.domain.vo.product.control.HVH3PSettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.RossDesiredSettings;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.provider.ICommonSettingsBuilder;
import com.ebon.energy.fms.domain.vo.setting.provider.SchedulePriority;
import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.ManagedInverterSettingDesired;
import com.ebon.energy.fms.domain.vo.product.control.SettingsReader;
import com.ebon.energy.fms.domain.vo.product.control.V2Setting;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class HVH3PSettingsBuilder extends SettingsBuilder<HVH3PSettingsReader, HVH3PSettingPaths>{

    public HVH3PSettingsBuilder(DeviceInfoAndSettings deviceInfoAndSettings, Instant nowInUtc) {
        super(deviceInfoAndSettings, nowInUtc, new HVH3PSettingsReader(deviceInfoAndSettings), new HVH3PSettingPaths());
    }

    @Override
    public ICommonSettingsBuilder patchManagedInverterSetting(String settingName, JsonNode value, String settingIndex, ManagedInverterSettingDesired.InverterSettingValueType settingType, ManagedInverterSettingDesired.InverterSettingExecutionType executionType, ManagedInverterSettingDesired.InverterSettingSource source, String uniqueId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder buildInverterPatch(RossDesiredSettings desired, List<ManagedInverterSettingDesired> expectedSettings, Map<String, Object> toPatch) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addShadowScan(boolean enableShadowScan) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addACCoupled(boolean enableACCoupledMode) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(BigDecimal powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addPowerFactor(Double powerFactorMinus1To1) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteExportLimit(Watt limit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftAndHardSiteExportLimit(Watt soft, Watt hard) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSoftSiteGenerationLimit(VoltAmps generationLimit) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addSiteExportLimit(boolean enabled, Watt power) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder overrideDesiredPowerRampRateLimit(Duration rampTime, String settingId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addBatterySettings(ManufacturerEnum manufacturer, Integer batteryCount, Integer maxChargeCurrent, Integer maxDischargeCurrent, Integer minSoc, Integer minOffgridSoc) {
        ObjectMapper objectMapper = new ObjectMapper();

        // 设置 Intent 字段
        getDeviceSettings().getIntent().setBatteryManufacturer(manufacturer != null ? manufacturer.toString() : null);
        getDeviceSettings().getIntent().setBatteryCount(batteryCount);
        getDeviceSettings().getIntent().setBatteryMaxChargeCurrent(maxChargeCurrent);
        getDeviceSettings().getIntent().setBatteryMaxDischargeCurrent(maxDischargeCurrent);
        getDeviceSettings().getIntent().setBatteryMinSoc(minSoc);
        getDeviceSettings().getIntent().setBatteryMinOffgridSoc(minOffgridSoc);
        getDeviceSettings().getIntent().setBatterySettingsModifiedUtc(getNowInUtc());

        // 获取制造商配置
        HVH3PSettingPaths.BatteryConfig config = HVH3PSettingsReader.fromManufacturer(manufacturer);
        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryProtocol(), objectMapper.valueToTree(config.getProtocol()));
        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryArchitecture(), objectMapper.valueToTree(config.getBatteryArchitecture()));
        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryCount(), objectMapper.valueToTree(batteryCount));
        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryMaxChargeCurrent(), objectMapper.valueToTree(maxChargeCurrent));
        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryMaxDischargeCurrent(), objectMapper.valueToTree(maxDischargeCurrent));

        // 转换 SOC 从百分比到小数 (0-100 -> 0.0-1.0)
        BigDecimal minSocDecimal = minSoc != null ? new BigDecimal(minSoc).divide(new BigDecimal("100")) : null;
        BigDecimal minOffgridSocDecimal = minOffgridSoc != null ? new BigDecimal(minOffgridSoc).divide(new BigDecimal("100")) : null;

        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryMinSoc0to1(), objectMapper.valueToTree(minSocDecimal));
        addValue(getDesiredPatch(), HVH3PSettingPaths.getBatteryMinOffgridSoc0to1(), objectMapper.valueToTree(minOffgridSocDecimal));

        // 添加额外的 Gen3 电池设置
        addAdditionalRequiredGen3BatterySettings();

        return this;
    }

    @Override
    public ICommonSettingsBuilder addCtFlipSettings(Boolean flipCt1, Boolean flipCt2) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDefaultTelemetryPeriod() {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addTelemetryPeriod(Duration period, Instant endDateUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addDredSettings(Boolean dredSubscribed) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder removeSchedule(String scheduleId) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, Instant endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, Instant startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder updateScheduleEndTime(String scheduleId, LocalDateTime endTimeUtc) {
        return null;
    }

    @Override
    public ICommonSettingsBuilder addMeterCheckModeSchedule(String id, LocalDateTime startTimeUtc, Duration duration, Watt maxTestPower) {
        return null;
    }

    @Override
    public SchedulePriority getSchedulePriority() {
        return null;
    }

    @Override
    public void deleteRelaySchedule(int relayNumber1Based, String id, Map<String, Object> patch) {

    }

    @Override
    public ICommonSettingsBuilder disableAgeingMode() {
        return null;
    }

    /**
     * 添加Gen3电池所需的额外设置
     * 注意：如果将来不同的Gen 3型号需要不同的值，部分逻辑需要推回到SettingChangeService中。
     */
    private void addAdditionalRequiredGen3BatterySettings() {
        ObjectMapper objectMapper = new ObjectMapper();

        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMaxChargeCurrent(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMaxChargeCurrentDefault().asDecimal()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMaxDischargeCurrent(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMaxDischargeCurrentDefault().asDecimal()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMaxChargeVoltage(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMaxChargeVoltageDefault().asDecimal()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMinDischargeVoltage(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMinDischargeVoltageDefault().asDecimal()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMinDischargeVoltageOffgrid(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMinDischargeVoltageOffgridDefault().asDecimal()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMinSOC(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMinSOCDefault()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryModuleRatedMinSOCOffgrid(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryModuleRatedMinSOCOffgridDefault()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryInverterRatedMaxChargeCurrent(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryInverterRatedMaxChargeCurrentDefault().asDecimal()));
        addValueIfNotSet(getDeviceSettings(), true, getDesiredPatch(), HVH3PSettingPaths.getBatteryInverterRatedMaxDischargeCurrent(),
                objectMapper.valueToTree(HVH3PSettingPaths.getBatteryInverterRatedMaxDischargeCurrentDefault().asDecimal()));
    }

}
