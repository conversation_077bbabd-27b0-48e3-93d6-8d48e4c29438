package com.ebon.energy.fms.domain.vo;

public class DeviceSettingsVO {

    private RossDesiredSettingsVO desired;
    private RossReportedSettingsVO reported;
    private DeviceSettingsIntentVO intent;

    public DeviceSettingsVO() {
    }

    public DeviceSettingsVO(
            RossReportedSettingsVO reported,
            RossDesiredSettingsVO desired) {
        this.reported = reported;
        this.desired = desired;
    }

    public static DeviceSettingsVO getDefault() {
        return new DeviceSettingsVO(
                new RossReportedSettingsVO(),
                new RossDesiredSettingsVO(null, BatteryManagerDesiredSettingsVO.getDefault())
        );
    }

    public RossDesiredSettingsVO getDesired() {
        return desired;
    }

    public void setDesired(RossDesiredSettingsVO desired) {
        this.desired = desired;
    }

    public RossReportedSettingsVO getReported() {
        return reported;
    }

    public void setReported(RossReportedSettingsVO reported) {
        this.reported = reported;
    }

    public DeviceSettingsIntentVO getIntent() {
        return intent;
    }

    public void setIntent(DeviceSettingsIntentVO intent) {
        this.intent = intent;
    }
}
