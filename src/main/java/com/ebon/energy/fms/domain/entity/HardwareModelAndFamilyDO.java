package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
public class HardwareModelAndFamilyDO {

    /**
     * 主键ID (与HardwareFamilyId组成复合主键)
     */
    private Integer id;

    /**
     * 硬件系列ID (外键关联HardwareFamily表)
     */
    private Integer hardwareFamilyId;

    /**
     * 型号名称
     */
    private String name;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 是否并网型号
     */
    private Boolean isGridTie;

    private String hardwareFamilyName;

    private Boolean displayInLargeInstaller;

    private String marketingDisplayName;
    
    private String marketingShortCode;
}