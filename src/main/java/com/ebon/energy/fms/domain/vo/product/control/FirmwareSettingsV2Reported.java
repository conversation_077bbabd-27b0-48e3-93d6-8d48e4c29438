package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public final class FirmwareSettingsV2Reported {

    public static final String VERSION_NUMBER_SETTING_NAME = "versionNumber";

    @JsonProperty(VERSION_NUMBER_SETTING_NAME)
    private String versionNumber;

    @JsonCreator
    public FirmwareSettingsV2Reported(
            @JsonProperty(VERSION_NUMBER_SETTING_NAME) String versionNumber) {
        this.versionNumber = versionNumber;
    }

    // 复制构造函数
    public FirmwareSettingsV2Reported(FirmwareSettingsV2Reported other) {
        this.versionNumber = other.getVersionNumber();
    }
}
