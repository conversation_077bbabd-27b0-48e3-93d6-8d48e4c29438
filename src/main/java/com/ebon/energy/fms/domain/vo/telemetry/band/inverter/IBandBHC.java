package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.MeterConnectStatus;
import com.ebon.energy.fms.common.enums.MeterType;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.IBand;

import java.math.*;




 


public interface IBandBHC extends IBand
{


	int getCommunicationsMode();
	WattHour getETotalBuy();
	WattHour getETotalSell();
	Frequency getFrequency();


	int getManufacturerCode();
	Object getMeterConnectStatusL1();
	Object getMeterConnectStatusL2();
	Object getMeterConnectStatusL3();


	int getMeterSoftwareVersion();
	boolean getMeterStatus();
	Object getMeterType();
	Watt getMTActivePowerL1();
	Watt getMTActivePowerL1Unused();
	Watt getMTActivePowerL2();
	Watt getMTActivePowerL2Unused();
	Watt getMTActivePowerL3();
	Watt getMTActivePowerL3Unused();
	VoltAmps getMTApparentPowerL1();
	VoltAmps getMTApparentPowerL2();
	VoltAmps getMTApparentPowerL3();
	VoltAmpsReactive getMTReactivePowerL1();
	VoltAmpsReactive getMTReactivePowerL2();
	VoltAmpsReactive getMTReactivePowerL3();
	Watt getMTTotalActivePower();
	Watt getMTTotalActivePowerUnused();
	VoltAmps getMTTotalApparentPower();
	VoltAmpsReactive getMTTotalReactivePower();
	VoltAmpsReactive getMTTotalReactivePowerUnused();
	BigDecimal getPhaseL1PowerFactor();
	BigDecimal getPhaseL2PowerFactor();
	BigDecimal getPhaseL3PowerFactor();


	int getRSSI();
	BigDecimal getTotalPowerfactor();
}
