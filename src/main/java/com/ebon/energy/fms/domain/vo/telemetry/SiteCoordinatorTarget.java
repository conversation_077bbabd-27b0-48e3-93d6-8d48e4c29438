package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;

import java.math.BigDecimal;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@Data
public class SiteCoordinatorTarget {
    private Watt BatteryPowerTarget;
    private BigDecimal LimitPercent;
}