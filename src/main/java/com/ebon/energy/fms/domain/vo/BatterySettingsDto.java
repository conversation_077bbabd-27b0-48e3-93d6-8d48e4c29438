// BatterySettingsDto.java
package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatterySettingsDto {

    @JsonProperty("Manufacturer")
    private String manufacturer;

    @JsonProperty("BatteryCount")
    private MinMaxValueDto batteryCount;

    @JsonProperty("MaxChargeCurrent")
    private MinMaxValueDto maxChargeCurrent;

    @JsonProperty("MaxChargeVoltageV")
    private BigDecimal maxChargeVoltageV;

    @JsonProperty("MaxDischargeCurrent")
    private MinMaxValueDto maxDischargeCurrent;

    @JsonProperty("MinSoc")
    private MinMaxValueDto minSoc;

    @JsonProperty("MinOffgridSoc")
    private MinMaxValueDto minOffgridSoc;

    @JsonProperty("TotalCapacityAh")
    private Integer totalCapacityAh;

    @JsonProperty("BatteryType")
    private String batteryType;

    @JsonProperty("RedbackBatterySwitch")
    private Boolean redbackBatterySwitch = false;
}
