package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.HardwareTypeEnum;

import java.util.*;

public class HardwareSettingsVO {

    private String ConfigurationName;
    private HardwareTypeEnum HardwareType;
    private HardwareFamilyEnum HardwareFamily;
    private List<HardwareModelEnum> SupportedModels;
    private boolean SupportedByRoss;
    private int MinFirmwareVersion;
    private int MaxFirmwareVersion;
    private int DeviceBaseAddress;
    private String SourceIdentifier;
    private Map<HardwareModelEnum, HardwareCapabilityVO> ModelCapabilities;
    private HardwareCapabilityVO DefaultCapabilities;

    public HardwareSettingsVO(
            String configName,
            HardwareTypeEnum HardwareType,
            HardwareFamilyEnum HardwareFamily,
            HardwareModelEnum[] hardwareModels,
            boolean supportedByRoss,
            int minFirmwareVersion,
            int maxFirmwareVersion,
            byte baseAddress,
            String sourceIdentifier) {
        this(
                configName,
                HardwareType,
                HardwareFamily,
                hardwareModels,
                supportedByRoss,
                minFirmwareVersion,
                maxFirmwareVersion,
                baseAddress,
                sourceIdentifier,
                null,
                null);
    }

    public HardwareSettingsVO(
            String configName,
            HardwareTypeEnum HardwareType,
            HardwareFamilyEnum HardwareFamily,
            HardwareModelEnum[] HardwareModels,
            boolean supportedByRoss,
            int minFirmwareVersion,
            int maxFirmwareVersion,
            int baseAddress,
            String sourceIdentifier,
            HardwareCapabilityVO capabilities,
            Map<HardwareModelEnum, HardwareCapabilityVO> modelCapabilities) {
        this.ConfigurationName = configName;
        this.HardwareType = HardwareType;
        this.HardwareFamily = HardwareFamily;
        this.SupportedModels = HardwareModels != null ? Arrays.asList(HardwareModels) : new ArrayList<>();
        this.SupportedByRoss = supportedByRoss;
        this.MinFirmwareVersion = minFirmwareVersion;
        this.MaxFirmwareVersion = maxFirmwareVersion;
        this.DeviceBaseAddress = baseAddress;
        this.SourceIdentifier = sourceIdentifier;
        this.DefaultCapabilities = capabilities;
        this.ModelCapabilities = modelCapabilities != null ? modelCapabilities : new HashMap<>();
    }

    public String getConfigurationName() {
        return ConfigurationName;
    }

    public HardwareTypeEnum getHardwareType() {
        return HardwareType;
    }

    public HardwareFamilyEnum getHardwareFamily() {
        return HardwareFamily;
    }

    public List<HardwareModelEnum> getSupportedModels() {
        return SupportedModels;
    }

    public boolean isSupportedByRoss() {
        return SupportedByRoss;
    }

    public int getMinFirmwareVersion() {
        return MinFirmwareVersion;
    }

    public int getMaxFirmwareVersion() {
        return MaxFirmwareVersion;
    }

    public int getDeviceBaseAddress() {
        return DeviceBaseAddress;
    }

    public String getSourceIdentifier() {
        return SourceIdentifier;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HardwareSettingsVO that = (HardwareSettingsVO) o;
        return SupportedByRoss == that.SupportedByRoss &&
                MinFirmwareVersion == that.MinFirmwareVersion &&
                MaxFirmwareVersion == that.MaxFirmwareVersion &&
                DeviceBaseAddress == that.DeviceBaseAddress &&
                Objects.equals(ConfigurationName, that.ConfigurationName) &&
                HardwareType == that.HardwareType &&
                HardwareFamily == that.HardwareFamily &&
                Objects.equals(SupportedModels, that.SupportedModels) &&
                Objects.equals(SourceIdentifier, that.SourceIdentifier);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                ConfigurationName,
                HardwareType,
                HardwareFamily,
                SupportedModels,
                SupportedByRoss,
                MinFirmwareVersion,
                MaxFirmwareVersion,
                DeviceBaseAddress,
                SourceIdentifier);
    }

    public boolean supportsHardwareModel(HardwareModelEnum HardwareModel) {
        return SupportedModels.contains(HardwareModel);
    }

    public HardwareCapabilityVO getCapabilities(HardwareModelEnum HardwareModel) {
        if (ModelCapabilities.containsKey(HardwareModel)) {
            return ModelCapabilities.get(HardwareModel);
        } else {
            return DefaultCapabilities;
        }
    }
}
