package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.GoodWeSiteExportLimitType;
import com.ebon.energy.fms.common.enums.GoodWeSiteGenerationLimitType;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class DeviceSettingsIntentVO {

    public DeviceSettingsIntentVO() {
    }

    public static DeviceSettingsIntentVO getDefault() {
        return new DeviceSettingsIntentVO();
    }

    private String GridSafetyProfileId;
    private String GridSafetyProfileCorrelationId;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant GridSafetyProfileModifiedDateUtc;

    private Integer SiteExportLimitInWatts;
    private Boolean EnableSiteExportLimit;
    private GoodWeSiteExportLimitType SiteExportLimitType;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant SiteExportModifiedDateUtc;
    private GoodWeSiteGenerationLimitType SiteGenerationLimitType;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant SiteGenerationModifiedDateUtc;
    private Boolean EnableShadowScan;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant EnableShadowScanModifiedUtc;
    private Boolean EnableACCoupledMode;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant EnableACCoupledModeModifiedUtc;
    private String BatteryManufacturer;
    private String BatteryType;
    private Integer BatteryCount;
    private Integer BatteryMaxChargeCurrent;
    private Integer BatteryMaxDischargeCurrent;
    private Integer BatteryMinSoc;
    private Integer BatteryMinOffgridSoc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant BatterySettingsModifiedUtc;

    private List<RelaySettingsVO> RelaySettings = new ArrayList<>();
    private SmartRelaySettingsVO SmartRelaySettings;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant RelaySettingsModifiedUtc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant SmartRelaySettingsModifiedUtc;
    private TimeZoneAliasSettingsVO TimeZoneAliasSettings;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant TimeZoneAliasSettingsModifiedUtc;

    private Boolean IsCt1Flipped;
    private Boolean IsCt2Flipped;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant CtFlippedModifiedUtc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant TelemetryPeriodModifiedUtc;
    private Long GridProfileDesiredVersion;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant ExportLimitsModifiedDateUtc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant GenerationLimitsModifiedDateUtc;
    private Boolean GenerationHardLimitEnable;
    private VoltAmps GenerationHardLimitVA;
    private Boolean GenerationSoftLimitEnable;
    private VoltAmps GenerationSoftLimitVA;
    private Boolean ExportHardLimitEnable;
    private Watt ExportHardLimitW;
    private BigDecimal PowerFactorMinus1To1;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant PowerFactorModifiedUtc;
    private Boolean DredSubscribed;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'", timezone = "UTC")
    private Instant DredSubscribedModifiedUtc;
    private SiteSettingsChangeRequestVO Site;

    public String getGridSafetyProfileId() {
        return GridSafetyProfileId;
    }

    public void setGridSafetyProfileId(String gridSafetyProfileId) {
        GridSafetyProfileId = gridSafetyProfileId;
    }

    public String getGridSafetyProfileCorrelationId() {
        return GridSafetyProfileCorrelationId;
    }

    public void setGridSafetyProfileCorrelationId(String gridSafetyProfileCorrelationId) {
        GridSafetyProfileCorrelationId = gridSafetyProfileCorrelationId;
    }

    public Instant getGridSafetyProfileModifiedDateUtc() {
        return GridSafetyProfileModifiedDateUtc;
    }

    public void setGridSafetyProfileModifiedDateUtc(Instant gridSafetyProfileModifiedDateUtc) {
        GridSafetyProfileModifiedDateUtc = gridSafetyProfileModifiedDateUtc;
    }

    public Integer getSiteExportLimitInWatts() {
        return SiteExportLimitInWatts;
    }

    public void setSiteExportLimitInWatts(Integer siteExportLimitInWatts) {
        SiteExportLimitInWatts = siteExportLimitInWatts;
    }

    public Boolean getEnableSiteExportLimit() {
        return EnableSiteExportLimit;
    }

    public void setEnableSiteExportLimit(Boolean enableSiteExportLimit) {
        EnableSiteExportLimit = enableSiteExportLimit;
    }

    public GoodWeSiteExportLimitType getSiteExportLimitType() {
        return SiteExportLimitType;
    }

    public void setSiteExportLimitType(GoodWeSiteExportLimitType siteExportLimitType) {
        SiteExportLimitType = siteExportLimitType;
    }

    public Instant getSiteExportModifiedDateUtc() {
        return SiteExportModifiedDateUtc;
    }

    public void setSiteExportModifiedDateUtc(Instant siteExportModifiedDateUtc) {
        SiteExportModifiedDateUtc = siteExportModifiedDateUtc;
    }

    public GoodWeSiteGenerationLimitType getSiteGenerationLimitType() {
        return SiteGenerationLimitType;
    }

    public void setSiteGenerationLimitType(GoodWeSiteGenerationLimitType siteGenerationLimitType) {
        SiteGenerationLimitType = siteGenerationLimitType;
    }

    public Instant getSiteGenerationModifiedDateUtc() {
        return SiteGenerationModifiedDateUtc;
    }

    public void setSiteGenerationModifiedDateUtc(Instant siteGenerationModifiedDateUtc) {
        SiteGenerationModifiedDateUtc = siteGenerationModifiedDateUtc;
    }

    public Boolean getEnableShadowScan() {
        return EnableShadowScan;
    }

    public void setEnableShadowScan(Boolean enableShadowScan) {
        EnableShadowScan = enableShadowScan;
    }

    public Instant getEnableShadowScanModifiedUtc() {
        return EnableShadowScanModifiedUtc;
    }

    public void setEnableShadowScanModifiedUtc(Instant enableShadowScanModifiedUtc) {
        EnableShadowScanModifiedUtc = enableShadowScanModifiedUtc;
    }

    public Boolean getEnableACCoupledMode() {
        return EnableACCoupledMode;
    }

    public void setEnableACCoupledMode(Boolean enableACCoupledMode) {
        EnableACCoupledMode = enableACCoupledMode;
    }

    public Instant getEnableACCoupledModeModifiedUtc() {
        return EnableACCoupledModeModifiedUtc;
    }

    public void setEnableACCoupledModeModifiedUtc(Instant enableACCoupledModeModifiedUtc) {
        EnableACCoupledModeModifiedUtc = enableACCoupledModeModifiedUtc;
    }

    public String getBatteryManufacturer() {
        return BatteryManufacturer;
    }

    public void setBatteryManufacturer(String batteryManufacturer) {
        BatteryManufacturer = batteryManufacturer;
    }

    public String getBatteryType() {
        return BatteryType;
    }

    public void setBatteryType(String batteryType) {
        BatteryType = batteryType;
    }

    public Integer getBatteryCount() {
        return BatteryCount;
    }

    public void setBatteryCount(Integer batteryCount) {
        BatteryCount = batteryCount;
    }

    public Integer getBatteryMaxChargeCurrent() {
        return BatteryMaxChargeCurrent;
    }

    public void setBatteryMaxChargeCurrent(Integer batteryMaxChargeCurrent) {
        BatteryMaxChargeCurrent = batteryMaxChargeCurrent;
    }

    public Integer getBatteryMaxDischargeCurrent() {
        return BatteryMaxDischargeCurrent;
    }

    public void setBatteryMaxDischargeCurrent(Integer batteryMaxDischargeCurrent) {
        BatteryMaxDischargeCurrent = batteryMaxDischargeCurrent;
    }

    public Integer getBatteryMinSoc() {
        return BatteryMinSoc;
    }

    public void setBatteryMinSoc(Integer batteryMinSoc) {
        BatteryMinSoc = batteryMinSoc;
    }

    public Integer getBatteryMinOffgridSoc() {
        return BatteryMinOffgridSoc;
    }

    public void setBatteryMinOffgridSoc(Integer batteryMinOffgridSoc) {
        BatteryMinOffgridSoc = batteryMinOffgridSoc;
    }

    public Instant getBatterySettingsModifiedUtc() {
        return BatterySettingsModifiedUtc;
    }

    public void setBatterySettingsModifiedUtc(Instant batterySettingsModifiedUtc) {
        BatterySettingsModifiedUtc = batterySettingsModifiedUtc;
    }

    public List<RelaySettingsVO> getRelaySettings() {
        return RelaySettings;
    }

    public void setRelaySettings(List<RelaySettingsVO> relaySettings) {
        RelaySettings = relaySettings;
    }

    public SmartRelaySettingsVO getSmartRelaySettings() {
        return SmartRelaySettings;
    }

    public void setSmartRelaySettings(SmartRelaySettingsVO smartRelaySettings) {
        SmartRelaySettings = smartRelaySettings;
    }

    public Instant getRelaySettingsModifiedUtc() {
        return RelaySettingsModifiedUtc;
    }

    public void setRelaySettingsModifiedUtc(Instant relaySettingsModifiedUtc) {
        RelaySettingsModifiedUtc = relaySettingsModifiedUtc;
    }

    public Instant getSmartRelaySettingsModifiedUtc() {
        return SmartRelaySettingsModifiedUtc;
    }

    public void setSmartRelaySettingsModifiedUtc(Instant smartRelaySettingsModifiedUtc) {
        SmartRelaySettingsModifiedUtc = smartRelaySettingsModifiedUtc;
    }

    public TimeZoneAliasSettingsVO getTimeZoneAliasSettings() {
        return TimeZoneAliasSettings;
    }

    public void setTimeZoneAliasSettings(TimeZoneAliasSettingsVO timeZoneAliasSettings) {
        TimeZoneAliasSettings = timeZoneAliasSettings;
    }

    public Instant getTimeZoneAliasSettingsModifiedUtc() {
        return TimeZoneAliasSettingsModifiedUtc;
    }

    public void setTimeZoneAliasSettingsModifiedUtc(Instant timeZoneAliasSettingsModifiedUtc) {
        TimeZoneAliasSettingsModifiedUtc = timeZoneAliasSettingsModifiedUtc;
    }

    public Boolean getIsCt1Flipped() {
        return IsCt1Flipped;
    }

    public void setIsCt1Flipped(Boolean isCt1Flipped) {
        IsCt1Flipped = isCt1Flipped;
    }

    public Boolean getIsCt2Flipped() {
        return IsCt2Flipped;
    }

    public void setIsCt2Flipped(Boolean isCt2Flipped) {
        IsCt2Flipped = isCt2Flipped;
    }

    public Instant getCtFlippedModifiedUtc() {
        return CtFlippedModifiedUtc;
    }

    public void setCtFlippedModifiedUtc(Instant ctFlippedModifiedUtc) {
        CtFlippedModifiedUtc = ctFlippedModifiedUtc;
    }

    public Instant getTelemetryPeriodModifiedUtc() {
        return TelemetryPeriodModifiedUtc;
    }

    public void setTelemetryPeriodModifiedUtc(Instant telemetryPeriodModifiedUtc) {
        TelemetryPeriodModifiedUtc = telemetryPeriodModifiedUtc;
    }

    public Long getGridProfileDesiredVersion() {
        return GridProfileDesiredVersion;
    }

    public void setGridProfileDesiredVersion(Long gridProfileDesiredVersion) {
        GridProfileDesiredVersion = gridProfileDesiredVersion;
    }

    public Instant getExportLimitsModifiedDateUtc() {
        return ExportLimitsModifiedDateUtc;
    }

    public void setExportLimitsModifiedDateUtc(Instant exportLimitsModifiedDateUtc) {
        ExportLimitsModifiedDateUtc = exportLimitsModifiedDateUtc;
    }

    public Instant getGenerationLimitsModifiedDateUtc() {
        return GenerationLimitsModifiedDateUtc;
    }

    public void setGenerationLimitsModifiedDateUtc(Instant generationLimitsModifiedDateUtc) {
        GenerationLimitsModifiedDateUtc = generationLimitsModifiedDateUtc;
    }

    public Boolean getGenerationHardLimitEnable() {
        return GenerationHardLimitEnable;
    }

    public void setGenerationHardLimitEnable(Boolean generationHardLimitEnable) {
        GenerationHardLimitEnable = generationHardLimitEnable;
    }

    public VoltAmps getGenerationHardLimitVA() {
        return GenerationHardLimitVA;
    }

    public void setGenerationHardLimitVA(VoltAmps generationHardLimitVA) {
        GenerationHardLimitVA = generationHardLimitVA;
    }

    public Boolean getGenerationSoftLimitEnable() {
        return GenerationSoftLimitEnable;
    }

    public void setGenerationSoftLimitEnable(Boolean generationSoftLimitEnable) {
        GenerationSoftLimitEnable = generationSoftLimitEnable;
    }

    public VoltAmps getGenerationSoftLimitVA() {
        return GenerationSoftLimitVA;
    }

    public void setGenerationSoftLimitVA(VoltAmps generationSoftLimitVA) {
        GenerationSoftLimitVA = generationSoftLimitVA;
    }

    public Boolean getExportHardLimitEnable() {
        return ExportHardLimitEnable;
    }

    public void setExportHardLimitEnable(Boolean exportHardLimitEnable) {
        ExportHardLimitEnable = exportHardLimitEnable;
    }

    public Watt getExportHardLimitW() {
        return ExportHardLimitW;
    }

    public void setExportHardLimitW(Watt exportHardLimitW) {
        ExportHardLimitW = exportHardLimitW;
    }

    public BigDecimal getPowerFactorMinus1To1() {
        return PowerFactorMinus1To1;
    }

    public void setPowerFactorMinus1To1(BigDecimal powerFactorMinus1To1) {
        PowerFactorMinus1To1 = powerFactorMinus1To1;
    }

    public Instant getPowerFactorModifiedUtc() {
        return PowerFactorModifiedUtc;
    }

    public void setPowerFactorModifiedUtc(Instant powerFactorModifiedUtc) {
        PowerFactorModifiedUtc = powerFactorModifiedUtc;
    }

    public Boolean getDredSubscribed() {
        return DredSubscribed;
    }

    public void setDredSubscribed(Boolean dredSubscribed) {
        DredSubscribed = dredSubscribed;
    }

    public Instant getDredSubscribedModifiedUtc() {
        return DredSubscribedModifiedUtc;
    }

    public void setDredSubscribedModifiedUtc(Instant dredSubscribedModifiedUtc) {
        DredSubscribedModifiedUtc = dredSubscribedModifiedUtc;
    }

    public SiteSettingsChangeRequestVO getSite() {
        return Site;
    }

    public void setSite(SiteSettingsChangeRequestVO site) {
        Site = site;
    }
}