package com.ebon.energy.fms.domain.vo;

import java.util.Objects;

public class RelaySettingsVO {
    private String Name;
    private boolean Installed;
    private boolean Active;

    public RelaySettingsVO(String Name, Boolean Installed, Boolean Active) {
        this.Name = Name;
        this.Installed = Installed == null ? false : Installed;
        this.Active = Active == null ? false : Active;
    }

    public String getName() {
        return Name;
    }

    public boolean isInstalled() {
        return Installed;
    }

    public boolean isActive() {
        return Active;
    }

    public static boolean equals(RelaySettingsVO left, RelaySettingsVO right) {
        return Objects.equals(left, right);
    }

    public static boolean notEquals(RelaySettingsVO left, RelaySettingsVO right) {
        return !equals(left, right);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RelaySettingsVO that = (RelaySettingsVO) o;
        return Installed == that.Installed &&
                Active == that.Active &&
                Objects.equals(Name, that.Name);
    }

}