package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.domain.vo.VoltAmps;
import com.ebon.energy.fms.domain.vo.Watt;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Builder;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class InstallationSpecification extends HardwareFirmwareSpecification {

    @JsonProperty("IsInAcCoupledMode")
    private boolean isInAcCoupledMode;

    @JsonProperty("HasIncorrectLoadandPvDailyCacheValues")
    private boolean hasIncorrectLoadandPvDailyCacheValues;

    @JsonProperty("MustHaveGrid")
    private boolean mustHaveGrid;

    // 构造函数1
    @Builder
    public InstallationSpecification(
            @JsonProperty("HardwareModel") HardwareModelEnum hardwareModel,
            @JsonProperty("ArmVersion") int armVersion,
            @JsonProperty("MaximumExportPowerInWatts") int maximumExportPowerInWatts,
            @JsonProperty("MinimumExportPowerInWatts") int minimumExportPowerInWatts,
            @JsonProperty("MaxInverterExportPowerPlateRatingW") int maxInverterExportPowerPlateRatingW,
            @JsonProperty("MaxInverterImportPowerPlateRatingW") int maxInverterImportPowerPlateRatingW,
            @JsonProperty("MaximumBatteryChargePowerInWatts") int maximumBatteryChargePowerInWatts,
            @JsonProperty("MaximumBatteryDischargePowerInWatts") int maximumBatteryDischargePowerInWatts,
            @JsonProperty("MinimumBatteryCount") int minimumBatteryCount,
            @JsonProperty("MaximumBatteryCount") int maximumBatteryCount,
            @JsonProperty("MinimumMaxChargeCurrentAmpere") int minimumMaxChargeCurrentAmpere,
            @JsonProperty("MaximumMaxChargeCurrentAmpere") int maximumMaxChargeCurrentAmpere,
            @JsonProperty("MinimumMaxDischargeCurrentAmpere") int minimumMaxDischargeCurrentAmpere,
            @JsonProperty("MaximumMaxDischargeCurrentAmpere") int maximumMaxDischargeCurrentAmpere,
            @JsonProperty("MinimumWorkPowerModeWatts") int minimumWorkPowerModeWatts,
            @JsonProperty("MaximumWorkPowerModeWatts") int maximumWorkPowerModeWatts,
            @JsonProperty("MinSiteExportPowerSoftW") Watt minSiteExportPowerSoftW,
            @JsonProperty("MinimumSiteExportWatts") int minimumSiteExportWatts,
            @JsonProperty("MaximumSiteExportWatts") int maximumSiteExportWatts,
            @JsonProperty("MinSiteExportPowerHardW") Watt minSiteExportPowerHardW,
            @JsonProperty("MaxSiteExportPowerHardW") Watt maxSiteExportPowerHardW,
            @JsonProperty("MinSiteGenerationPowerSoftVA") VoltAmps minSiteGenerationPowerSoftVA,
            @JsonProperty("MaxSiteGenerationPowerSoftVA") VoltAmps maxSiteGenerationPowerSoftVA,
            @JsonProperty("MinSiteGenerationPowerHardVA") VoltAmps minSiteGenerationPowerHardVA,
            @JsonProperty("MaxSiteGenerationPowerHardVA") VoltAmps maxSiteGenerationPowerHardVA,
            @JsonProperty("MaxLaggingPowerFactor") BigDecimal maxLaggingPowerFactor,
            @JsonProperty("MaxLeadingPowerFactor") BigDecimal maxLeadingPowerFactor,
            @JsonProperty("MinOnGridSoCLimit") int minOnGridSoCLimit,
            @JsonProperty("MaxOnGridSoCLimit") int maxOnGridSoCLimit,
            @JsonProperty("MinOffGridSoCLimit") int minOffGridSoCLimit,
            @JsonProperty("MaxOffGridSoCLimit") int maxOffGridSoCLimit,
            @JsonProperty("MinSmartLoadControlTriggerPowerW") Watt minSmartLoadControlTriggerPowerW,
            @JsonProperty("MaxSmartLoadControlTriggerPowerW") Watt maxSmartLoadControlTriggerPowerW,
            @JsonProperty("FirstRelayConfigurable") boolean firstRelayConfigurable,
            @JsonProperty("HasShadowScanFeature") boolean hasShadowScanFeature,
            @JsonProperty("CanToggleMeasuringThirdPartyInverter") boolean canToggleMeasuringThirdPartyInverter,
            @JsonProperty("HardwareFamily") HardwareFamilyEnum hardwareFamily,
            @JsonProperty("SupportsConnectedPV") boolean supportsConnectedPV,
            @JsonProperty("SupportsDredSubscribed") boolean supportsDredSubscribed,
            @JsonProperty("HasIncorrectLoadandPvDailyCacheValues") boolean hasIncorrectLoadandPvDailyCacheValues,
            @JsonProperty("IsInAcCoupledMode") boolean isInAcCoupledMode,
            @JsonProperty("MustHaveGrid") boolean mustHaveGrid
    ) {
        super(
                hardwareModel, armVersion, maximumExportPowerInWatts, minimumExportPowerInWatts,
                maxInverterExportPowerPlateRatingW, maxInverterImportPowerPlateRatingW,
                maximumBatteryChargePowerInWatts, maximumBatteryDischargePowerInWatts,
                minimumBatteryCount, maximumBatteryCount,
                minimumMaxChargeCurrentAmpere, maximumMaxChargeCurrentAmpere,
                minimumMaxDischargeCurrentAmpere, maximumMaxDischargeCurrentAmpere,
                minimumWorkPowerModeWatts, maximumWorkPowerModeWatts,
                minSiteExportPowerSoftW, minimumSiteExportWatts, maximumSiteExportWatts,
                minSiteExportPowerHardW, maxSiteExportPowerHardW,
                minSiteGenerationPowerSoftVA, maxSiteGenerationPowerSoftVA,
                minSiteGenerationPowerHardVA, maxSiteGenerationPowerHardVA,
                maxLaggingPowerFactor, maxLeadingPowerFactor,
                minOnGridSoCLimit, maxOnGridSoCLimit, minOffGridSoCLimit, maxOffGridSoCLimit,
                minSmartLoadControlTriggerPowerW, maxSmartLoadControlTriggerPowerW,
                firstRelayConfigurable, hasShadowScanFeature, canToggleMeasuringThirdPartyInverter,
                hardwareFamily, supportsConnectedPV, supportsDredSubscribed
        );
        this.hasIncorrectLoadandPvDailyCacheValues = hasIncorrectLoadandPvDailyCacheValues;
        this.isInAcCoupledMode = isInAcCoupledMode;
        this.mustHaveGrid = mustHaveGrid;
    }

    // 构造函数2
    public InstallationSpecification(
            HardwareFirmwareSpecification hardandfirm,
            boolean hasIncorrectLoadandPvDailyCacheValues,
            boolean isInAcCoupledMode,
            boolean mustHaveGrid
    ) {
        this(
                hardandfirm.getHardwareModel(),
                hardandfirm.getArmVersion(),
                hardandfirm.getMaximumExportPowerInWatts(),
                hardandfirm.getMinimumExportPowerInWatts(),
                hardandfirm.getMaxInverterExportPowerPlateRatingW(),
                hardandfirm.getMaxInverterImportPowerPlateRatingW(),
                hardandfirm.getMaximumBatteryChargePowerInWatts(),
                hardandfirm.getMaximumBatteryDischargePowerInWatts(),
                hardandfirm.getMinimumBatteryCount(),
                hardandfirm.getMaximumBatteryCount(),
                hardandfirm.getMinimumMaxChargeCurrentAmpere(),
                hardandfirm.getMaximumMaxChargeCurrentAmpere(),
                hardandfirm.getMinimumMaxDischargeCurrentAmpere(),
                hardandfirm.getMaximumMaxDischargeCurrentAmpere(),
                hardandfirm.getMinimumWorkPowerModeWatts(),
                hardandfirm.getMaximumWorkPowerModeWatts(),
                hardandfirm.getMinSiteExportPowerSoftW(),
                hardandfirm.getMinimumSiteExportWatts(),
                hardandfirm.getMaximumSiteExportWatts(),
                hardandfirm.getMinSiteExportPowerHardW(),
                hardandfirm.getMaxSiteExportPowerHardW(),
                hardandfirm.getMinSiteGenerationPowerSoftVA(),
                hardandfirm.getMaxSiteGenerationPowerSoftVA(),
                hardandfirm.getMinSiteGenerationPowerHardVA(),
                hardandfirm.getMaxSiteGenerationPowerHardVA(),
                hardandfirm.getMaxLaggingPowerFactor(),
                hardandfirm.getMaxLeadingPowerFactor(),
                hardandfirm.getMinOnGridSoCLimit(),
                hardandfirm.getMaxOnGridSoCLimit(),
                hardandfirm.getMinOffGridSoCLimit(),
                hardandfirm.getMaxOffGridSoCLimit(),
                hardandfirm.getMinSmartLoadControlTriggerPowerW(),
                hardandfirm.getMaxSmartLoadControlTriggerPowerW(),
                hardandfirm.isFirstRelayConfigurable(),
                hardandfirm.isHasShadowScanFeature(),
                hardandfirm.isCanToggleMeasuringThirdPartyInverter(),
                hardandfirm.getHardwareFamily(),
                hardandfirm.isSupportsConnectedPV(),
                hardandfirm.isSupportsDredSubscribed(),
                hasIncorrectLoadandPvDailyCacheValues,
                isInAcCoupledMode,
                mustHaveGrid
        );
    }

    @JsonProperty("IsMeasuringThirdPartyInverter")
    public boolean isMeasuringThirdPartyInverter() {
        return isInAcCoupledMode || isSmartBatteryInverter();
    }

    @JsonProperty("HasSolar")
    public boolean isHasSolar() {
        return true;
    }

}
