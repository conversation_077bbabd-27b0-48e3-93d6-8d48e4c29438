package com.ebon.energy.fms.domain.vo.setting.provider;

import com.ebon.energy.fms.domain.vo.InverterDesiredSettingsVO;
import com.ebon.energy.fms.domain.vo.RossDesiredSettingsVO;
import com.ebon.energy.fms.domain.vo.product.control.SmartRelaySettingsDesired;
import lombok.Data;
import lombok.Getter;


import java.util.Arrays;
import java.util.List;

@Data
public class RossSettingPaths extends SettingPaths {
    
    public static final String POWER_FACTOR_MINUS_1_TO_1_KEY = "PowerFactor";
    public static final String POWER_FACTOR_SETTING = "inverter." + POWER_FACTOR_MINUS_1_TO_1_KEY;
    public static final String DRED_SUBSCRIBED_SETTING = "dredSubscribed";
    public static final String RELAY_KEY = "relaySettings";
    public static final String ENABLE_SHADOW_SCAN_SETTING = "inverter.IsShadowScanEnabled";
    public static final String ENABLED_AC_COUPLED_MODE_SETTING = "measuringThirdPartyInverter";

    @Getter
    private final String siteExportLimitTypeSetting;
    
    @Getter
    private final String exportHardLimitEnableManagedSettingsId;
    
    @Getter
    private final String exportLimitControlHardLimManagedSettingsId;
    
    @Getter
    private final String generationLimitTypePath;
    
    @Getter
    private final String genLimitControlSoftLimPath;
    
    @Getter
    private final String genLimitControlHardLimName;
    
    @Getter
    private final String relayOverrideSettingTemplate;

    public RossSettingPaths() {
        super(
            "inverter.DoesSiteRequireExportLimit", // enableSiteExportLimitSetting
            "inverter.SiteExportLimitW", // siteExportLimitSetting
            "inverter.GridProfileId", // gridProfileIdSetting
            "inverter.GridProfileCorrelationId", // gridProfileCorrelationIdSetting
            "batteryManager.Manufacturer", // batteryManufacturerSetting
            "batteryManager.BatteryCount", // batteryCountSetting
            "inverter.battery.maxChargeCurrent", // batteryMaxChargeCurrentSetting
            "inverter.battery.maxDischargeCurrent", // batteryMaxDischargeCurrentSetting
            "inverter.battery.minStateOfChargePercent", // batteryMinSocSetting
            "inverter.battery.MinOffgridSoCPercent", // batteryMinOffgridSocSetting
            RossDesiredSettingsVO.ThirdPartyExportCtName, // thirdPartyExportCt
            "relays.{0}.name", // relayNameSettingTemplate
            "relays.{0}.installed", // relayInstalledSettingTemplate
            "relays.{0}.base", // relayActiveSettingTemplate
            "site" // siteSetting
        );
        
        this.siteExportLimitTypeSetting = "inverter.SiteExportLimitType";
        this.exportHardLimitEnableManagedSettingsId = "ExportHardLimitEnable";
        this.exportLimitControlHardLimManagedSettingsId = "ExportHardLimitW";
        this.relayOverrideSettingTemplate = "relays.{0}.override";
        this.generationLimitTypePath = "inverter." + InverterDesiredSettingsVO.GEN_LIMIT_CONTROL_TYPE_NAME;
        this.genLimitControlSoftLimPath = "inverter." + InverterDesiredSettingsVO.GEN_LIMIT_CONTROL_SOFT_LIM_VA_NAME;
        this.genLimitControlHardLimName = "inverter." + InverterDesiredSettingsVO.GEN_LIMIT_CONTROL_HARD_LIM_VA_NAME;
    }

    // Smart relay properties
    public String getSmartRelayName() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.LOAD_CONTROL_NAME;
    }

    public String getSmartRelayInstalled() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.LOAD_CONTROL_INSTALLED_NAME;
    }

    public String getSmartRelayLoadType() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_TYPE_NAME;
    }

    public String getSmartRelayEnabled() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_ENABLED_NAME;
    }

    public String getSmartRelaySource() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_SOURCE_NAME;
    }

    public String getSmartRelayOnTrigger() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_ON_TRIGGER_NAME;
    }

    public String getSmartRelayOnDelay() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_ON_DELAY_NAME;
    }

    public String getSmartRelayOffTrigger() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_OFF_TRIGGER_NAME;
    }

    public String getSmartRelayOffDelay() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_OFF_DELAY_NAME;
    }

    public String getSmartRelayMinRunTime() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_MIN_RUN_TIME_NAME;
    }

    public String getSmartRelayMinOffTime() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_MIN_OFF_TIME_NAME;
    }

    public String getSmartRelayRunTimeTarget() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_RUN_TIME_TARGET_NAME;
    }

    public String getSmartRelayRunTimeCompletionTime() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_RUN_TIME_COMPLETION_TIME_NAME;
    }

    public String getSmartRelayRunTimeUseExcessPower() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.AUTO_LOAD_CONTROL_RUN_TIME_USE_EXCESS_POWER_NAME;
    }

    public String getSmartRelayActiveHigh() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.LOAD_CONTROL_RELAY_ACTIVE_HIGH_NAME;
    }

    public String getSmartRelayControlMode() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.LOAD_CONTROL_MODE_NAME;
    }

    public String getSmartRelayTimeZone() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.LOAD_CONTROL_TIME_ZONE_NAME;
    }

    public String getSmartRelayNominatedRelay() {
        return RELAY_KEY + "." + SmartRelaySettingsDesired.LOAD_CONTROL_NOMINATED_RELAY;
    }

    public String getTimeZoneAlias() {
        return "TzAlias";
    }

    public static List<String> getBandUpdateRates() {
        return Arrays.asList("connectivity.TelemetryPeriodInSeconds");
    }

}
