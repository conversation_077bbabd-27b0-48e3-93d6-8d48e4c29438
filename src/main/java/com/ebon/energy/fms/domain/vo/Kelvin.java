package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Kelvin implements IUnit {
    public static final Kelvin Unit = new Kelvin(new BigDecimal("1.0"));
    public static final Kelvin Zero = new Kelvin(new BigDecimal("0.0"));
    public static final Kelvin Tera = new Kelvin(new BigDecimal("1000000000000"));
    public static final Kelvin Giga = new Kelvin(new BigDecimal("1000000000"));
    public static final Kelvin Mega = new Kelvin(new BigDecimal("1000000"));
    public static final Kelvin Kilo = new Kelvin(new BigDecimal("1000"));
    public static final Kelvin Hecto = new Kelvin(new BigDecimal("100"));
    public static final Kelvin Deca = new Kelvin(new BigDecimal("10"));
    public static final Kelvin Deci = new Kelvin(new BigDecimal("0.1"));
    public static final Kelvin Centi = new Kelvin(new BigDecimal("0.01"));
    public static final Kelvin Milli = new Kelvin(new BigDecimal("0.001"));

    public static final String SYMBOL = "K";

    private BigDecimal value;

    public Kelvin() {
    }

    public Kelvin(BigDecimal value) {
        this.value = value;
    }

    public Kelvin(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Kelvin(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public BigDecimal getValue() {
        return value;
    }

    @Override
    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public BigDecimal asDecimal(Kelvin unit) {
        return value.divide(unit.value);
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }

    public Kelvin add(Kelvin other) {
        return new Kelvin(value.add(other.value));
    }

    public Kelvin subtract(Kelvin other) {
        return new Kelvin(value.subtract(other.value));
    }

    public Kelvin multiply(BigDecimal multiplier) {
        return new Kelvin(value.multiply(multiplier));
    }

    public Kelvin divide(BigDecimal divisor) {
        return new Kelvin(value.divide(divisor));
    }

    public Celsius ToCelsius(){
        return new Celsius(this.value);
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Kelvin)) {
            return false;
        }
        Kelvin other = (Kelvin) obj;
        return value.compareTo(other.value) == 0;
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    public int compareTo(Kelvin other) {
        return value.compareTo(other.value);
    }
}
