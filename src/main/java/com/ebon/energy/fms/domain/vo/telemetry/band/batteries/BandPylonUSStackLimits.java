package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;










public class BandPylonUSStackLimits extends DataBackedBand
{
	public BandPylonUSStackLimits()
	{
		super(BandForge.<BandPylonUSStackLimits>getMetadataFor(BandPylonUSStackLimits.class));
	}



	public BandPylonUSStackLimits(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylonUSStackLimits>getMetadataFor(BandPylonUSStackLimits.class));
	}

	public BandPylonUSStackLimits(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylonUSStackLimits>getMetadataFor(BandPylonUSStackLimits.class));
	}


	
	public final Volt getStackChargeVoltageLimit()
	{
		return GetU16(0, Volt.Milli);
	}

	
	public final Volt getStackDischargeVoltageLimit()
	{
		return GetU16(2, Volt.Milli);
	}

	
	public final Ampere getStackChargeCurrentLimit()
	{
		return GetU16(4, Ampere.Deci);
	}

	
	public final Ampere getStackDischargeCurrentLimit()
	{
		return GetS16(6, Ampere.Deci);
	}

	
	public final String getStackChargeDischargeStatus()
	{
		return PylonChargeStatus.parse(GetU8(8));
	}
}
