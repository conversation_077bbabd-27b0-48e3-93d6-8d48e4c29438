package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.MeterConnectStatus;
import com.ebon.energy.fms.common.enums.MeterType;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandESGMeterData extends DataBackedBand {
    public BandESGMeterData() {
        super(BandForge.<BandESGMeterData>getMetadataFor(BandESGMeterData.class));
    }


    public BandESGMeterData(byte[] bytes) {
        super(bytes, BandForge.<BandESGMeterData>getMetadataFor(BandESGMeterData.class));
    }

    public BandESGMeterData(String encodedBytes) {
        super(encodedBytes, BandForge.<BandESGMeterData>getMetadataFor(BandESGMeterData.class));
    }





    public final int getCommunicationsMode() { return GetU16(0); }




    public final int getRSSI() { return GetU16(2); }




    public final int getMeterManufacturerCode() { return GetU16(4); }


    public final Object getMeterConnectStatusL1() {


        return MeterConnectStatus.parse(GetMaskedU16(6, (short) 3, (short) 0));
    }


    public final Object getMeterConnectStatusL2() {


        return MeterConnectStatus.parse(GetMaskedU16(6, (short) 48, (short) 4));
    }


    public final Object getMeterConnectStatusL3() {


        return MeterConnectStatus.parse(GetMaskedU16(6, (short) 768, (short) 8));
    }


    public final boolean getMeterStatus() {
        return GetBool((int) GetU16(8), 1, 0, false);
    }


    public final Watt getMTActivePowerL1Unused() {
        return GetS16(10, Watt.Unit);
    }


    public final Watt getMTActivePowerL2Unused() {
        return GetS16(12, Watt.Unit);
    }


    public final Watt getMTActivePowerL3Unused() {
        return GetS16(14, Watt.Unit);
    }


    public final Watt getMTTotalActivePowerUnused() {
        return GetS16(16, Watt.Unit);
    }


    public final VoltAmpsReactive getMTTotalReactivePowerUnused() {
        return GetS16(18, VoltAmpsReactive.Unit);
    }


    public final BigDecimal getMeterPowerFactorL1() {
        return new BigDecimal(GetS16(20)).multiply(Percentage._01);
    }


    public final BigDecimal getMeterPowerFactorL2() {
        return new BigDecimal(GetS16(22)).multiply(Percentage._01);
    }


    public final BigDecimal getMeterPowerFactorL3() {
        return new BigDecimal(GetS16(24)).multiply(Percentage._01);
    }


    public final BigDecimal getMeterTotalPowerfactor() {
        return new BigDecimal(GetS16(26)).multiply(Percentage._01);
    }


    public final Frequency getMeterFrequency() {
        return GetU16(28, Frequency.Centi);
    }


    public final WattHour getMeterETotalSell() {
        return GetFloat(30, WattHour.Unit);
    }


    public final WattHour getMeterETotalBuy() {
        return GetFloat(34, WattHour.Unit);
    }


    public final Watt getMeterActivePowerL1() {
        return GetS32(38, Watt.Unit);
    }


    public final Watt getMeterActivePowerL2() {
        return GetS32(42, Watt.Unit);
    }


    public final Watt getMeterActivePowerL3() {
        return GetS32(46, Watt.Unit);
    }


    public final Watt getMeterTotalActivePower() {
        return GetS32(50, Watt.Unit);
    }


    public final VoltAmpsReactive getMeterReactivePowerL1() {
        return GetS32(54, VoltAmpsReactive.Unit);
    }


    public final VoltAmpsReactive getMeterReactivePowerL2() {
        return GetS32(58, VoltAmpsReactive.Unit);
    }


    public final VoltAmpsReactive getMeterReactivePowerL3() {
        return GetS32(62, VoltAmpsReactive.Unit);
    }


    public final VoltAmpsReactive getMeterTotalReactivePower() {
        return GetS32(66, VoltAmpsReactive.Unit);
    }


    public final VoltAmps getMTApparentPowerL1() {
        return GetS32(70, VoltAmps.Unit);
    }


    public final VoltAmps getMTApparentPowerL2() {
        return GetS32(74, VoltAmps.Unit);
    }


    public final VoltAmps getMTApparentPowerL3() {
        return GetS32(78, VoltAmps.Unit);
    }


    public final VoltAmps getMTTotalApparentPower() {
        return GetS32(82, VoltAmps.Unit);
    }


    public final Object getMeterType() {
        return MeterType.parse(GetU16(86));
    }




    public final int getMeterSoftwareVersion() { return GetU16(88); }


    public final Watt getMeterCT2Activepower() {
        return GetS32(90, Watt.Unit);
    }


    public final WattHour getCT2ETotalSell() {
        return GetU32(94, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }


    public final WattHour getCT2ETotalBuy() {
        return GetU32(98, WattHour.operatorMultiply(new BigDecimal("0.01"), WattHour.Kilo));
    }




    public final int getMeterCT2Status() { return GetU16(102); }


    public final Volt getMeterVoltageL1() {
        return GetU16(104, Volt.Deci);
    }


    public final Volt getMeterVoltageL2() {
        return GetU16(106, Volt.Deci);
    }


    public final Volt getMeterVoltageL3() {
        return GetU16(108, Volt.Deci);
    }


    public final Ampere getMeterCurrentL1() {
        return GetU16(110, Ampere.Deci);
    }


    public final Ampere getMeterCurrentL2() {
        return GetU16(112, Ampere.Deci);
    }


    public final Ampere getMeterCurrentL3() {
        return GetU16(114, Ampere.Deci);
    }
}
