package com.ebon.energy.fms.domain.vo.product.control.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConfigurationDifference {
    private String field;
    private Object desired;
    private Object reported;

    public static void add(List<ConfigurationDifference> list, String field, Object desired, Object reported) {
        if (!Objects.equals(desired, reported)) {
            list.add(new ConfigurationDifference(field, desired, reported));
        }
    }
}
