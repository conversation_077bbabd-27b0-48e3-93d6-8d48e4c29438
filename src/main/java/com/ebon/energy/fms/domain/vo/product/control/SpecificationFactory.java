package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.HardwareTypeEnum;
import com.ebon.energy.fms.common.utils.HardwareModelHelpers;
import com.ebon.energy.fms.common.utils.ModelInfo;
import com.ebon.energy.fms.config.SupportedHardwareConfigurations;
import com.ebon.energy.fms.domain.po.HardwareSpecification;
import com.ebon.energy.fms.domain.vo.HardwareCapabilityVO;
import com.ebon.energy.fms.domain.vo.HardwareSettingsVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public class SpecificationFactory {

    public static HardwareFirmwareSpecification get(InverterIdentityCard identityCard) {
        if (identityCard == null) {
            return get(new ModelInfo(HardwareModelEnum.Unknown, null));
        }
        ModelInfo modelInfo = identityCard.getModelInfo();
        if (modelInfo == null) {
            return get(new ModelInfo(HardwareModelEnum.Unknown, null));
        }
        return get(modelInfo);
    }

    public static HardwareFirmwareSpecification get(ModelInfo details) {
        return doGet(details.getHardwareModel(), details.getArmVersion());
    }

//    public static HardwareSettingsVO getHardware(ModelInfo details) {
//        return new HardwareConfigurationProvider()
//                .tryIdentifyDevice(HardwareTypeEnum.Inverter, details.getHardwareModel(), details.getArmVersion())
//                .stream().findFirst().orElse(null);
//    }

    public static InstallationSpecification forTestsOnly(ModelInfo details) {
        return new InstallationSpecification(
                get(details),
                false,
                false,
                false
        );
    }

    public static HardwareSpecification get(String inverterModel) {
        HardwareModelEnum hardwareModel = HardwareModelHelpers.parseModelName(inverterModel);
        if (hardwareModel == HardwareModelEnum.Unknown) {
            throw new IllegalArgumentException("Unable to provide specification for '" + inverterModel + "'");
        }
        HardwareFamilyEnum family = new HardwareConfigurationProvider().identifyFamily(HardwareTypeEnum.Inverter, hardwareModel);
        return new HardwareSpecification(family, hardwareModel);
    }

    public static boolean tryGet(String inverterModel, HardwareSpecification[] specification) {
        try {
            if (HardwareModelHelpers.parseModelName(inverterModel) == HardwareModelEnum.Unknown) {
                specification[0] = null;
                return false;
            }
            specification[0] = get(inverterModel);
            return true;
        } catch (Exception e) {
            specification[0] = null;
            return false;
        }
    }

//    public static UnifiedElectricalSettings getUnifiedElectricalSettings(
//            String softwareVersion,
//            String electricalConfiguration,
//            String desiredDeviceSettings,
//            String deviceSettingsIntent,
//            String reportedDeviceSettings,
//            String hardwareConfigurationName,
//            String inverterSerialNumber,
//            String inverterModelName,
//            String inverterFirmwareVersion,
//            LocalDateTime reportedLastUpdated,
//            Long desiredVersion,
//            ExplicitSettings explicitSettings
//    ) {
//        RossVersion rossVersion = new RossVersion(softwareVersion);
//
//        if (rossVersion.isRoss1()) {
//            ElectricalConfiguration conf = JsonUtils.fromJson(electricalConfiguration, ElectricalConfiguration.class);
//            conf.patchLegacyBatteryInfo();
//            return UnifiedElectricalSettingsHelper.fromElectricalConfiguration(conf);
//        } else {
//            RossDesiredSettings desired = RossDesiredSettings.fromJson(Optional.ofNullable(desiredDeviceSettings).orElse("{}"));
//            RossReportedSettings reported = RossReportedSettings.fromJson(Optional.ofNullable(reportedDeviceSettings).orElse("{}"));
//            DeviceSettingsIntent intent = JsonUtils.fromJson(
//                    Optional.ofNullable(deviceSettingsIntent).orElse("{}"),
//                    DeviceSettingsIntent.class
//            );
//            InverterIdentityCard idCard = new InverterIdentityCard(
//                    inverterSerialNumber,
//                    inverterModelName,
//                    inverterFirmwareVersion,
//                    rossVersion.getRossVersionNumber(),
//                    hardwareConfigurationName
//            );
//            SettingsReader reader = SettingsReaderProvider.get(
//                    new DeviceSettingsDto(reported, desired, intent, idCard, explicitSettings, reportedLastUpdated, desiredVersion)
//            );
//            return UnifiedElectricalSettingsHelper.fromSettings(reader);
//        }
//    }

    private static HardwareFirmwareSpecification doGet(HardwareModelEnum hardwareModel, int armVersion) {
        if (hardwareModel == HardwareModelEnum.Unknown) {
            return toProductModelDefaults(
                    hardwareModel,
                    SupportedHardwareConfigurations.getUnsupportedInverterConfiguration(),
                    armVersion
            );
        }
        IHardwareConfigurationProvider hardwareConfig = new HardwareConfigurationProvider();
        HardwareSettingsVO config = hardwareConfig.tryIdentifyDevice(HardwareTypeEnum.Inverter, hardwareModel, armVersion)
                .stream().findFirst().orElse(null);

        if (config == null || config.getHardwareFamily() == HardwareFamilyEnum.Unknown) {
            config = hardwareConfig.tryIdentifyDevice(HardwareTypeEnum.Inverter, hardwareModel, armVersion)
                    .stream().findFirst().orElse(null);
            if (config == null) {
                config = SupportedHardwareConfigurations.getUnsupportedInverterConfiguration();
            }
        }
        return toProductModelDefaults(hardwareModel, config, armVersion);
    }

    private static HardwareFirmwareSpecification toProductModelDefaults(
            HardwareModelEnum hardwareModel,
            HardwareSettingsVO hardwareConfig,
            int armVersion
    ) {
        HardwareCapabilityVO c = hardwareConfig.getCapabilities(hardwareModel);

        return new HardwareFirmwareSpecification(
                hardwareModel,
                armVersion,
                c.getMaxExportPower().asInt(),
                c.getMinExportPower().asInt(),
                c.getMaxInverterExportPowerPlateRating().asInt(),
                c.getMaxInverterImportPowerPlateRating().asInt(),
                c.getMaxBatteryChargePower().asInt(),
                c.getMaxBatteryDischargePower().asInt(),
                c.getMinBatteryCount(),
                c.getMaxBatteryCount(),
                c.getMinChargeCurrent().asInt(),
                c.getMaxChargeCurrent().asInt(),
                c.getMinDischargeCurrent().asInt(),
                c.getMaxDischargeCurrent().asInt(),
                c.getMinWorkModePower().asInt(),
                c.getMaxWorkModePower().asInt(),
                c.getMinSiteExportPower(),
                c.getMinSiteExportPower().asInt(),
                c.getMaxSiteExportPower().asInt(),
                c.getMinSiteExportPowerHardW(),
                c.getMaxSiteExportPowerHardW(),
                c.getMinSiteGenerationPowerSoftVA(),
                c.getMaxSiteGenerationPowerSoftVA(),
                c.getMinSiteGenerationPowerHardVA(),
                c.getMaxSiteGenerationPowerHardVA(),
                new BigDecimal(c.getMaxLaggingPowerFactor()),
                new BigDecimal(c.getMaxLeadingPowerFactor()),
                c.getMinOnGridSoCLimit(),
                c.getMaxOnGridSoCLimit(),
                c.getMinOffGridSoCLimit(),
                c.getMaxOffGridSoCLimit(),
                c.getMinSmartLoadControlTriggerPowerW(),
                c.getMaxSmartLoadControlTriggerPowerW(),
                c.isFirstRelayConfigurable(),
                c.isSupportsConnectedPV(),
                c.isSupportsMeasuringThirdPartyInverter() &&
                        hardwareConfig.getHardwareFamily() != HardwareFamilyEnum.Inverter_Goodwe_BH,
                hardwareConfig.getHardwareFamily(),
                c.isSupportsConnectedPV(),
                c.isSupportsDredSubscribed()
        );
    }


}
