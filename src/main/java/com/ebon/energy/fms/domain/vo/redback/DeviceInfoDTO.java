package com.ebon.energy.fms.domain.vo.redback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceInfoDTO {
    private String serialNumber;
    private String edgeId;
    private String rossDeviceId;
    private String rossDeviceLastActivity;
    private String watchdogDeviceId;
    private String watchdogDeviceLastActivity;
    @JsonProperty("isWatchdogConnected")
    private boolean isWatchdogConnected;
    private String osVersion;
    private String firmwareVersion;
    private String batteryManufacturer;
    private String batteryModel;
    private int batteryCount;
    private String batteryStatus;
    private int relayCount;
    private int scheduleCount;
    @JsonProperty("isOnlineSystemStatus")
    private boolean isOnlineSystemStatus;
    private String errorMessage;
    private DeviceVersionInfoDTO reported;
    private DeviceVersionInfoDTO desired;
    private boolean hasPendingChanges;
    private boolean hasWatchdog;
    private boolean hasLegacyRoss;
    private String cloudPlatformName;
}

