package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Ohm implements IUnit {

    public static final String SYMBOL = "Ω";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final Ohm Unit = new Ohm(new BigDecimal("1.0"));
    public static final Ohm Zero = new Ohm(new BigDecimal("0.0"));
    public static final Ohm Tera = new Ohm(new BigDecimal("1000000000000"));
    public static final Ohm Giga = new Ohm(new BigDecimal("1000000000"));
    public static final Ohm Mega = new Ohm(new BigDecimal("1000000"));
    public static final Ohm Kilo = new Ohm(new BigDecimal("1000"));
    public static final Ohm Hecto = new Ohm(new BigDecimal("100"));
    public static final Ohm Deca = new Ohm(new BigDecimal("10"));
    public static final Ohm Deci = new Ohm(new BigDecimal("0.1"));
    public static final Ohm Centi = new Ohm(new BigDecimal("0.01"));
    public static final Ohm Milli = new Ohm(new BigDecimal("0.001"));

    public Ohm() {
    }

    public Ohm(BigDecimal value) {
        this.value = value;
    }

    public Ohm(String value) {
        this.value = new BigDecimal(value);
    }


    public Ohm(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Ohm(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Ohm abs() {
        return new Ohm(value.abs());
    }

    public Ohm subtract(Ohm other) {
        return new Ohm(this.value.subtract(other.value));
    }

    public Ohm add(Ohm other) {
        return new Ohm(this.value.add(other.value));
    }

    public static Ohm add(Ohm a, Ohm b) {
        return new Ohm(a.value.add(b.value));
    }

    public static Ohm subtract(Ohm a, Ohm b) {
        return new Ohm(a.value.subtract(b.value));
    }

    public boolean greaterThan(Ohm other) {
        return this.value.compareTo(other.value) > 0;
    }

    public boolean lessThan(Ohm other) {
        return this.value.compareTo(other.value) < 0;
    }

    public boolean greaterThanOrEqual(Ohm other) {
        return this.value.compareTo(other.value) >= 0;
    }

    public boolean lessThanOrEqual(Ohm other) {
        return this.value.compareTo(other.value) <= 0;
    }

    public static Ohm getValueOrZero(Ohm watt) {
        return watt == null || watt.getValue() == null ? Ohm.Zero : watt;
    }

    // 重载一元 + 运算符
    public static Ohm operatorPlus(Ohm a) {
        return a;
    }

    // 重载一元 - 运算符
    public static Ohm operatorMinus(Ohm a) {
        return new Ohm(a.value.negate());
    }

    // 重载二元 + 运算符
    public static Ohm operatorAdd(Ohm a, Ohm b) {
        return new Ohm(a.value.add(b.value));
    }

    // 重载二元 - 运算符
    public static Ohm operatorSubtract(Ohm a, Ohm b) {
        return new Ohm(a.value.subtract(b.value));
    }

    // 重载二元 / 运算符，返回 BigDecimal
    public static BigDecimal operatorDivide(Ohm a, Ohm b) {
        return a.value.divide(b.value);
    }

    // 重载 Watt 除以 BigDecimal 的 / 运算符
    public static Ohm operatorDivide(Ohm a, BigDecimal b) {
        return new Ohm(a.value.divide(b));
    }

    // 重载 Watt 乘以 BigDecimal 的 * 运算符
    public static Ohm operatorMultiply(Ohm a, BigDecimal value) {
        return new Ohm(a.value.multiply(value));
    }

    // 重载 BigDecimal 乘以 Watt 的 * 运算符
    public static Ohm operatorMultiply(BigDecimal value, Ohm a) {
        return new Ohm(value.multiply(a.value));
    }

    // 重载 == 运算符
    public static boolean operatorEqual(Ohm a, Ohm b) {
        return a.value.equals(b.value);
    }

    // 重载 != 运算符
    public static boolean operatorNotEqual(Ohm a, Ohm b) {
        return !a.value.equals(b.value);
    }

    // 重载 < 运算符
    public static boolean operatorLessThan(Ohm a, Ohm b) {
        return a.value.compareTo(b.value) < 0;
    }

    // 重载 <= 运算符
    public static boolean operatorLessThanOrEqual(Ohm a, Ohm b) {
        return a.value.compareTo(b.value) <= 0;
    }

    // 重载 > 运算符
    public static boolean operatorGreaterThan(Ohm a, Ohm b) {
        return a.value.compareTo(b.value) > 0;
    }

    // 重载 >= 运算符
    public static boolean operatorGreaterThanOrEqual(Ohm a, Ohm b) {
        return a.value.compareTo(b.value) >= 0;
    }

    // 从 BigDecimal 隐式转换为 Watt
    public static Ohm fromBigDecimal(BigDecimal d) {
        return new Ohm(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public BigDecimal asDecimal(Ohm unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(Ohm unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(Ohm unit) {
        return value.divide(unit.value);
    }

    public double asDouble(Ohm unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(Ohm unit) {
        return value.divide(unit.value).longValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Ohm other = (Ohm) obj;
        return value.compareTo(other.value) == 0;
    }

    public int compareTo(Ohm other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}
