package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.time.ZonedDateTime;

@Data
public class SiteInverterInfoVO {

    /**
     * 站点ID
     */
    private String siteId;
    
    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 逆变器名称
     */
    private String inverterName;
    
    /**
     * 型号
     */
    private String model;
    
    /**
     * 当前角色
     */
    private String currentRole;

    /**
     * 状态
     */
    private String status;

    /**
     * Ross版本
     */
    private String rossVersion;

    /**
     * 固件版本
     */
    private String firmwareVersion;

    /**
     * 电表状态
     */
    private String meter;

    /**
     * 选择角色
     */
    private String selectedRole;

    /**
     * 最后遥测时间
     */
    private ZonedDateTime latestTelemetryUtc;

    /**
     * 计算机名称
     */
    private String computerName;

    /**
     * 是否小时在线
     */
    private Boolean isHourlyOnline;

    /**
     * 是否日在线
     */
    private Boolean isDailyOnline;

}
