package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.json.CustomZonedDateTimeDeserializer;
import com.ebon.energy.fms.common.json.CustomZonedDateTimeSerializer;
import com.ebon.energy.fms.common.json.DurationFromTimeDeserializer;
import com.ebon.energy.fms.common.json.DurationToTimeSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import java.time.LocalDateTime;
import java.time.Duration;
import java.time.ZonedDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ScheduleV2Dto {

    public static final String PRIORITY_SETTINGS_NAME = "priority";
    public static final String TZ_ALIAS_SETTINGS_NAME = "TzAlias";
    public static final String START_AT_UTC_SETTINGS_NAME = "startAtUtc";
    public static final String END_AT_UTC_SETTINGS_NAME = "endAtUtc";
    public static final String DURATION_SETTINGS_NAME = "duration";
    public static final String DAYS_OF_WEEK_SETTINGS_NAME = "daysOfWeekActive";
    public static final String START_TIME_OF_DAY_SETTINGS_NAME = "startTimeOfDay";
    public static final String END_TIME_OF_DAY_SETTINGS_NAME = "endTimeOfDay";
    public static final String DESIRED_SCHEDULED_SETTINGS_NAME = "settings";

    public static final int USER_SCHEDULE_PRIORITY = 10;
    public static final int REDBACK_SCHEDULE_PRIORITY = 20;
    public static final int NETWORK_LEVEL_PRIORITY = 21;

    @JsonProperty(PRIORITY_SETTINGS_NAME)
    private Integer priority;

    @JsonProperty(TZ_ALIAS_SETTINGS_NAME)
    private String tzAlias;

    @JsonProperty(START_AT_UTC_SETTINGS_NAME)
    @JsonSerialize(using = CustomZonedDateTimeSerializer.class)
    @JsonDeserialize(using = CustomZonedDateTimeDeserializer.class)
    private ZonedDateTime startAtUtc;

    @JsonProperty(END_AT_UTC_SETTINGS_NAME)
    @JsonSerialize(using = CustomZonedDateTimeSerializer.class)
    @JsonDeserialize(using = CustomZonedDateTimeDeserializer.class)
    private ZonedDateTime endAtUtc;

    @JsonProperty(DAYS_OF_WEEK_SETTINGS_NAME)
    private Byte daysOfWeekActive;

    @JsonProperty(START_TIME_OF_DAY_SETTINGS_NAME)
    @JsonSerialize(using = DurationToTimeSerializer.class)
    @JsonDeserialize(using = DurationFromTimeDeserializer.class)
    private Duration startTimeOfDay;

    @JsonProperty(END_TIME_OF_DAY_SETTINGS_NAME)
    @JsonSerialize(using = DurationToTimeSerializer.class)
    @JsonDeserialize(using = DurationFromTimeDeserializer.class)
    private Duration endTimeOfDay;

    @JsonProperty(DURATION_SETTINGS_NAME)
    @JsonSerialize(using = DurationToTimeSerializer.class)
    @JsonDeserialize(using = DurationFromTimeDeserializer.class)
    private Duration duration;

    @JsonProperty(DESIRED_SCHEDULED_SETTINGS_NAME)
    private SettingsV2DesiredBase desiredScheduledSettings;

    // 复制构造器
    public ScheduleV2Dto(ScheduleV2Dto other) {
        this.priority = other.priority;
        this.tzAlias = other.tzAlias;
        this.startAtUtc = other.startAtUtc;
        this.endAtUtc = other.endAtUtc;
        this.daysOfWeekActive = other.daysOfWeekActive;
        this.startTimeOfDay = other.startTimeOfDay;
        this.endTimeOfDay = other.endTimeOfDay;
        this.duration = other.duration;
        this.desiredScheduledSettings = other.desiredScheduledSettings;
    }

    // 默认实例
    public static ScheduleV2Dto getDefault() {
        return new ScheduleV2Dto();
    }
}
