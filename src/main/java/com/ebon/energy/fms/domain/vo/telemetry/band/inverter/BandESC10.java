package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESC10 extends DataBackedBand
{
	public BandESC10()
	{
		super(BandForge.<BandESC10>getMetadataFor(BandESC10.class));
	}



	public BandESC10(byte[] bytes)
	{
		super(bytes, BandForge.<BandESC10>getMetadataFor(BandESC10.class));
	}

	public BandESC10(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESC10>getMetadataFor(BandESC10.class));
	}


	


	public final int getHVBatString() { return GetU16(0); }

	


	public final int getOfflineMPPTScanEnable() { return GetU16(2); }

	


	public final int getPVExtendenable() { return GetU16(4); }

	


	public final int getPERelayCheckEn() { return GetU16(6); }

	


	public final int getBatteryModePVCharge() { return GetU16(8); }

}
