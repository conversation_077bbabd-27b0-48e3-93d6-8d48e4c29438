package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class ConnectionStatus implements Cloneable {
    private boolean IsWIFIAccessable;
    private boolean IsIOTHubAccessable;
    private boolean IsLANAccessable;
    private ExternalConnectionType LANConnectionType;
    private String LANConnectionName;
    private SignalStrength LANConnectionSignalStrength;

    public ConnectionStatus() {
    }

    public ConnectionStatus(boolean isWIFIAccessable, boolean isIOTHubAccessable, boolean isLANAccessable,
                            ExternalConnectionType lANConnectionType, String lANConnectionName,
                            SignalStrength lANConnectionSignalStrength) {
        IsWIFIAccessable = isWIFIAccessable;
        IsIOTHubAccessable = isIOTHubAccessable;
        IsLANAccessable = isLANAccessable;
        LANConnectionType = lANConnectionType;
        LANConnectionName = lANConnectionName;
        LANConnectionSignalStrength = lANConnectionSignalStrength;
    }

    public boolean getIsWIFIAccessable() {
        return IsWIFIAccessable;
    }

    public void setIsWIFIAccessable(boolean isWIFIAccessable) {
        IsWIFIAccessable = isWIFIAccessable;
    }

    public boolean getIsIOTHubAccessable() {
        return IsIOTHubAccessable;
    }

    public void setIsIOTHubAccessable(boolean isIOTHubAccessable) {
        IsIOTHubAccessable = isIOTHubAccessable;
    }

    public boolean getIsLANAccessable() {
        return IsLANAccessable;
    }

    public void setIsLANAccessable(boolean isLANAccessable) {
        IsLANAccessable = isLANAccessable;
    }

    public ExternalConnectionType getLANConnectionType() {
        return LANConnectionType;
    }

    public void setLANConnectionType(ExternalConnectionType lANConnectionType) {
        LANConnectionType = lANConnectionType;
    }

    public String getLANConnectionName() {
        return LANConnectionName;
    }

    public void setLANConnectionName(String lANConnectionName) {
        LANConnectionName = lANConnectionName;
    }

    public SignalStrength getLANConnectionSignalStrength() {
        return LANConnectionSignalStrength;
    }

    public void setLANConnectionSignalStrength(SignalStrength lANConnectionSignalStrength) {
        LANConnectionSignalStrength = lANConnectionSignalStrength;
    }

    @JsonProperty("WiFiSignalStrength")
    public SignalStrength getWiFiSignalStrength() {
        return LANConnectionType == ExternalConnectionType.WIFI? LANConnectionSignalStrength : null;
    }

    @Override
    public ConnectionStatus clone() {
        try {
            return (ConnectionStatus) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}