package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

@Data
public class HardwareCapabilityVO {

    private Watt MinExportPower;
    private Watt MaxExportPower;
    private Watt MaxInverterExportPowerPlateRating;
    private Watt MaxInverterImportPowerPlateRating;
    private Watt MinSiteExportPower;
    private Watt MaxSiteExportPower;
    private Watt MinSiteExportPowerHardW;
    private Watt MaxSiteExportPowerHardW;
    private VoltAmps MinSiteGenerationPowerSoftVA;
    private VoltAmps MaxSiteGenerationPowerSoftVA;
    private VoltAmps MinSiteGenerationPowerHardVA;
    private VoltAmps MaxSiteGenerationPowerHardVA;
    private double MaxLaggingPowerFactor;
    private double MaxLeadingPowerFactor;
    private String SiteExportLimitHardPath;
    private String SiteExportLimitHardEnablePath;
    private String SiteGenerationLimitSoftPath;
    private String SiteGenerationLimitSoftEnablePath;
    private String SiteGenerationLimitHardPath;
    private String SiteGenerationLimitHardEnablePath;
    private int MinBatteryCount;
    private int MaxBatteryCount;
    private Watt MinWorkModePower;
    private Watt MaxWorkModePower;
    private Watt MinBatteryChargePower;
    private Watt MaxBatteryChargePower;
    private Watt MinBatteryDischargePower;
    private Watt MaxBatteryDischargePower;
    private Watt MinSmartLoadControlTriggerPowerW;
    private Watt MaxSmartLoadControlTriggerPowerW;
    private Ampere MinChargeCurrent;
    private Ampere MaxChargeCurrent;
    private Ampere MinDischargeCurrent;
    private Ampere MaxDischargeCurrent;
    private int MinOnGridSoCLimit;
    private int MaxOnGridSoCLimit;
    private int MinOffGridSoCLimit;
    private int MaxOffGridSoCLimit;
    private boolean SupportsThreePhasePower;
    private boolean SupportsConnectedPV;
    private boolean SupportsMeasuringThirdPartyInverter;
    private boolean SupportsAS477722020GridProfiles;
    private boolean SupportsAS477722015GridProfiles;
    private boolean SupportsAS477722020SiteLimits;
    private boolean IsFirstRelayConfigurable;
    private Optional<Integer> PlateRatedBatteryCapacityWattHours;
    private boolean SupportsDredSubscribed;

    public HardwareCapabilityVO(
            Watt MinExportPower,
            Watt MaxExportPower,
            Watt MaxInverterExportPowerPlateRating,
            Watt MaxInverterImportPowerPlateRating,
            Watt MinSiteExportPower,
            Watt MaxSiteExportPower,
            Watt MinSiteExportPowerHardW,
            Watt MaxSiteExportPowerHardW,
            VoltAmps MinSiteGenerationPowerSoftVA,
            VoltAmps MaxSiteGenerationPowerSoftVA,
            VoltAmps MinSiteGenerationPowerHardVA,
            VoltAmps MaxSiteGenerationPowerHardVA,
            double MaxLaggingPowerFactor,
            double MaxLeadingPowerFactor,
            String SiteExportLimitHardPath,
            String SiteExportLimitHardEnablePath,
            String SiteGenerationLimitSoftPath,
            String SiteGenerationLimitSoftEnablePath,
            String SiteGenerationLimitHardPath,
            String SiteGenerationLimitHardEnablePath,
            int MinBatteryCount,
            int MaxBatteryCount,
            Watt MinWorkModePower,
            Watt MaxWorkModePower,
            Watt MinBatteryChargePower,
            Watt MaxBatteryChargePower,
            Watt MinBatteryDischargePower,
            Watt MaxBatteryDischargePower,
            Watt MinSmartLoadControlTriggerPowerW,
            Watt MaxSmartLoadControlTriggerPowerW,
            Ampere MinChargeCurrent,
            Ampere MaxChargeCurrent,
            Ampere MinDischargeCurrent,
            Ampere MaxDischargeCurrent,
            int MinOnGridSoCLimit,
            int MaxOnGridSoCLimit,
            int MinOffGridSoCLimit,
            int MaxOffGridSoCLimit,
            boolean SupportsThreePhasePower,
            boolean SupportsConnectedPV,
            boolean SupportsThirdPartyInverter,
            boolean SupportsAS477722020GridProfiles,
            boolean SupportsAS477722015GridProfiles,
            boolean SupportsAS477722020SiteLimits,
            boolean FirstRelayConfigurable,
            Integer PlateRatedBatteryCapacityWattHours,
            boolean SupportsDredSubscribed) {
        this.MinExportPower = MinExportPower;
        this.MaxExportPower = MaxExportPower;
        this.MaxInverterExportPowerPlateRating = MaxInverterExportPowerPlateRating;
        this.MaxInverterImportPowerPlateRating = MaxInverterImportPowerPlateRating;
        this.MinSiteExportPower = MinSiteExportPower;
        this.MaxSiteExportPower = MaxSiteExportPower;
        this.MinSiteExportPowerHardW = MinSiteExportPowerHardW;
        this.MaxSiteExportPowerHardW = MaxSiteExportPowerHardW;
        this.MinSiteGenerationPowerSoftVA = MinSiteGenerationPowerSoftVA;
        this.MaxSiteGenerationPowerSoftVA = MaxSiteGenerationPowerSoftVA;
        this.MinSiteGenerationPowerHardVA = MinSiteGenerationPowerHardVA;
        this.MaxSiteGenerationPowerHardVA = MaxSiteGenerationPowerHardVA;
        this.MaxLaggingPowerFactor = MaxLaggingPowerFactor;
        this.MaxLeadingPowerFactor = MaxLeadingPowerFactor;
        this.SiteExportLimitHardPath = SiteExportLimitHardPath;
        this.SiteExportLimitHardEnablePath = SiteExportLimitHardEnablePath;
        this.SiteGenerationLimitSoftPath = SiteGenerationLimitSoftPath;
        this.SiteGenerationLimitSoftEnablePath = SiteGenerationLimitSoftEnablePath;
        this.SiteGenerationLimitHardPath = SiteGenerationLimitHardPath;
        this.SiteGenerationLimitHardEnablePath = SiteGenerationLimitHardEnablePath;
        this.MinBatteryCount = MinBatteryCount;
        this.MaxBatteryCount = MaxBatteryCount;
        this.MinWorkModePower = MinWorkModePower;
        this.MaxWorkModePower = MaxWorkModePower;
        this.MinBatteryChargePower = MinBatteryChargePower;
        this.MaxBatteryChargePower = MaxBatteryChargePower;
        this.MinBatteryDischargePower = MinBatteryDischargePower;
        this.MaxBatteryDischargePower = MaxBatteryDischargePower;
        this.MinSmartLoadControlTriggerPowerW = MinSmartLoadControlTriggerPowerW;
        this.MaxSmartLoadControlTriggerPowerW = MaxSmartLoadControlTriggerPowerW;
        this.MinChargeCurrent = MinChargeCurrent;
        this.MaxChargeCurrent = MaxChargeCurrent;
        this.MinDischargeCurrent = MinDischargeCurrent;
        this.MaxDischargeCurrent = MaxDischargeCurrent;
        this.MinOnGridSoCLimit = MinOnGridSoCLimit;
        this.MaxOnGridSoCLimit = MaxOnGridSoCLimit;
        this.MinOffGridSoCLimit = MinOffGridSoCLimit;
        this.MaxOffGridSoCLimit = MaxOffGridSoCLimit;
        this.SupportsThreePhasePower = SupportsThreePhasePower;
        this.SupportsConnectedPV = SupportsConnectedPV;
        this.SupportsMeasuringThirdPartyInverter = SupportsThirdPartyInverter;
        this.SupportsAS477722020GridProfiles = SupportsAS477722020GridProfiles;
        this.SupportsAS477722015GridProfiles = SupportsAS477722015GridProfiles;
        this.SupportsAS477722020SiteLimits = SupportsAS477722020SiteLimits;
        this.IsFirstRelayConfigurable = FirstRelayConfigurable;
        this.PlateRatedBatteryCapacityWattHours = Optional.ofNullable(PlateRatedBatteryCapacityWattHours);
        this.SupportsDredSubscribed = SupportsDredSubscribed;
    }

    public HardwareCapabilityVO(int minExportPower, int maxExportPower,
                                int maxInverterExportPowerPlateRating, int maxInverterImportPowerPlateRating,
                                int minSiteExportPower, int maxSiteExportPower,
                                int minSiteExportPowerHardW, int maxSiteExportPowerHardW,
                                int minSiteGenerationPowerSoftVA, int maxSiteGenerationPowerSoftVA,
                                int minSiteGenerationPowerHardVA, int maxSiteGenerationPowerHardVA,
                                BigDecimal maxLaggingPowerFactor, BigDecimal maxLeadingPowerFactor,
                                String siteExportLimitHardPath, String siteExportLimitHardEnablePath,
                                String siteGenerationLimitSoftPath, String siteGenerationLimitSoftEnablePath,
                                String siteGenerationLimitHardPath, String siteGenerationLimitHardEnablePath,
                                int minBatteryCount, int maxBatteryCount,
                                int minWorkModePower, int maxWorkModePower,
                                int minBatteryChargePower, int maxBatteryChargePower,
                                int minBatteryDischargePower, int maxBatteryDischargePower,
                                int minSmartLoadControlTriggerPowerW, int maxSmartLoadControlTriggerPowerW,
                                int minChargeCurrent, int maxChargeCurrent,
                                int minDischargeCurrent, int maxDischargeCurrent,
                                int minOnGridSoCLimit, int maxOnGridSoCLimit,
                                int minOffGridSoCLimit, int maxOffGridSoCLimit,
                                boolean supportsThreePhasePower, boolean supportsConnectedPV,
                                boolean supportsThirdPartyInverter, boolean supportsAS477722015GridProfiles,
                                boolean supportsAS477722020GridProfiles, boolean supportsAS477722020SiteLimits,
                                Integer plateRatedBatteryCapacityWattHours, boolean firstRelayConfigurable,
                                boolean supportsDredSubscribed) {
        this.MinExportPower = new Watt(new BigDecimal(minExportPower));
        this.MaxExportPower = new Watt(new BigDecimal(maxExportPower));
        this.MaxInverterExportPowerPlateRating = new Watt(new BigDecimal(maxInverterExportPowerPlateRating));
        this.MaxInverterImportPowerPlateRating = new Watt(new BigDecimal(maxInverterImportPowerPlateRating));
        this.MinSiteExportPower = new Watt(new BigDecimal(minSiteExportPower));
        this.MaxSiteExportPower = new Watt(new BigDecimal(maxSiteExportPower));
        this.MinSiteExportPowerHardW = new Watt(new BigDecimal(minSiteExportPowerHardW));
        this.MaxSiteExportPowerHardW = new Watt(new BigDecimal(maxSiteExportPowerHardW));
        this.MinWorkModePower = new Watt(new BigDecimal(minWorkModePower));
        this.MaxWorkModePower = new Watt(new BigDecimal(maxWorkModePower));
        this.MinBatteryChargePower = new Watt(new BigDecimal(minBatteryChargePower));
        this.MaxBatteryChargePower = new Watt(new BigDecimal(maxBatteryChargePower));
        this.MinBatteryDischargePower = new Watt(new BigDecimal(minBatteryDischargePower));
        this.MaxBatteryDischargePower = new Watt(new BigDecimal(maxBatteryDischargePower));
        this.MinSmartLoadControlTriggerPowerW = new Watt(new BigDecimal(minSmartLoadControlTriggerPowerW));
        this.MaxSmartLoadControlTriggerPowerW = new Watt(new BigDecimal(maxSmartLoadControlTriggerPowerW));

        this.MinSiteGenerationPowerSoftVA = new VoltAmps(new BigDecimal(minSiteGenerationPowerSoftVA));
        this.MaxSiteGenerationPowerSoftVA = new VoltAmps(new BigDecimal(maxSiteGenerationPowerSoftVA));
        this.MinSiteGenerationPowerHardVA = new VoltAmps(new BigDecimal(minSiteGenerationPowerHardVA));
        this.MaxSiteGenerationPowerHardVA = new VoltAmps(new BigDecimal(maxSiteGenerationPowerHardVA));

        this.MinChargeCurrent = new Ampere(new BigDecimal(minChargeCurrent));
        this.MaxChargeCurrent = new Ampere(new BigDecimal(maxChargeCurrent));
        this.MinDischargeCurrent = new Ampere(new BigDecimal(minDischargeCurrent));
        this.MaxDischargeCurrent = new Ampere(new BigDecimal(maxDischargeCurrent));

        this.MaxLaggingPowerFactor = maxLaggingPowerFactor.doubleValue();
        this.MaxLeadingPowerFactor = maxLeadingPowerFactor.doubleValue();
        this.SiteExportLimitHardPath = siteExportLimitHardPath;
        this.SiteExportLimitHardEnablePath = siteExportLimitHardEnablePath;
        this.SiteGenerationLimitSoftPath = siteGenerationLimitSoftPath;
        this.SiteGenerationLimitSoftEnablePath = siteGenerationLimitSoftEnablePath;
        this.SiteGenerationLimitHardPath = siteGenerationLimitHardPath;
        this.SiteGenerationLimitHardEnablePath = siteGenerationLimitHardEnablePath;
        this.MinBatteryCount = minBatteryCount;
        this.MaxBatteryCount = maxBatteryCount;
        this.MinOnGridSoCLimit = minOnGridSoCLimit;
        this.MaxOnGridSoCLimit = maxOnGridSoCLimit;
        this.MinOffGridSoCLimit = minOffGridSoCLimit;
        this.MaxOffGridSoCLimit = maxOffGridSoCLimit;
        this.SupportsThreePhasePower = supportsThreePhasePower;
        this.SupportsConnectedPV = supportsConnectedPV;
        this.SupportsMeasuringThirdPartyInverter = supportsThirdPartyInverter;
        this.SupportsAS477722020GridProfiles = supportsAS477722020GridProfiles;
        this.SupportsAS477722015GridProfiles = supportsAS477722015GridProfiles;
        this.SupportsAS477722020SiteLimits = supportsAS477722020SiteLimits;
        this.IsFirstRelayConfigurable = firstRelayConfigurable;
        this.PlateRatedBatteryCapacityWattHours = Optional.ofNullable(plateRatedBatteryCapacityWattHours);
        this.SupportsDredSubscribed = supportsDredSubscribed;
    }

    public Watt getMinExportPower() {
        return MinExportPower;
    }


    public void setMinExportPower(Watt MinExportPower) {
        this.MinExportPower = MinExportPower;
    }

    public Watt getMaxExportPower() {
        return MaxExportPower;
    }

    public void setMaxExportPower(Watt MaxExportPower) {
        this.MaxExportPower = MaxExportPower;
    }

    public Watt getMaxInverterExportPowerPlateRating() {
        return MaxInverterExportPowerPlateRating;
    }

    public void setMaxInverterExportPowerPlateRating(Watt MaxInverterExportPowerPlateRating) {
        this.MaxInverterExportPowerPlateRating = MaxInverterExportPowerPlateRating;
    }

    public Watt getMaxInverterImportPowerPlateRating() {
        return MaxInverterImportPowerPlateRating;
    }

    public void setMaxInverterImportPowerPlateRating(Watt MaxInverterImportPowerPlateRating) {
        this.MaxInverterImportPowerPlateRating = MaxInverterImportPowerPlateRating;
    }

    public Watt getMinSiteExportPower() {
        return MinSiteExportPower;
    }

    public Watt getMaxSiteExportPower() {
        return MaxSiteExportPower;
    }

    public Watt getMinSiteExportPowerHardW() {
        return MinSiteExportPowerHardW;
    }

    public Watt getMaxSiteExportPowerHardW() {
        return MaxSiteExportPowerHardW;
    }

    public VoltAmps getMinSiteGenerationPowerSoftVA() {
        return MinSiteGenerationPowerSoftVA;
    }

    public VoltAmps getMaxSiteGenerationPowerSoftVA() {
        return MaxSiteGenerationPowerSoftVA;
    }

    public VoltAmps getMinSiteGenerationPowerHardVA() {
        return MinSiteGenerationPowerHardVA;
    }

    public VoltAmps getMaxSiteGenerationPowerHardVA() {
        return MaxSiteGenerationPowerHardVA;
    }

    public double getMaxLaggingPowerFactor() {
        return MaxLaggingPowerFactor;
    }

    public double getMaxLeadingPowerFactor() {
        return MaxLeadingPowerFactor;
    }

    public String getSiteExportLimitHardPath() {
        return SiteExportLimitHardPath;
    }

    public void setSiteExportLimitHardPath(String SiteExportLimitHardPath) {
        this.SiteExportLimitHardPath = SiteExportLimitHardPath;
    }

    public String getSiteExportLimitHardEnablePath() {
        return SiteExportLimitHardEnablePath;
    }

    public void setSiteExportLimitHardEnablePath(String SiteExportLimitHardEnablePath) {
        this.SiteExportLimitHardEnablePath = SiteExportLimitHardEnablePath;
    }

    public String getSiteGenerationLimitSoftPath() {
        return SiteGenerationLimitSoftPath;
    }

    public void setSiteGenerationLimitSoftPath(String SiteGenerationLimitSoftPath) {
        this.SiteGenerationLimitSoftPath = SiteGenerationLimitSoftPath;
    }

    public String getSiteGenerationLimitSoftEnablePath() {
        return SiteGenerationLimitSoftEnablePath;
    }

    public void setSiteGenerationLimitSoftEnablePath(String SiteGenerationLimitSoftEnablePath) {
        this.SiteGenerationLimitSoftEnablePath = SiteGenerationLimitSoftEnablePath;
    }

    public String getSiteGenerationLimitHardPath() {
        return SiteGenerationLimitHardPath;
    }

    public void setSiteGenerationLimitHardPath(String SiteGenerationLimitHardPath) {
        this.SiteGenerationLimitHardPath = SiteGenerationLimitHardPath;
    }

    public String getSiteGenerationLimitHardEnablePath() {
        return SiteGenerationLimitHardEnablePath;
    }

    public void setSiteGenerationLimitHardEnablePath(String SiteGenerationLimitHardEnablePath) {
        this.SiteGenerationLimitHardEnablePath = SiteGenerationLimitHardEnablePath;
    }

    public int getMinBatteryCount() {
        return MinBatteryCount;
    }

    public int getMaxBatteryCount() {
        return MaxBatteryCount;
    }

    public Watt getMinWorkModePower() {
        return MinWorkModePower;
    }

    public Watt getMaxWorkModePower() {
        return MaxWorkModePower;
    }

    public Watt getMinBatteryChargePower() {
        return MinBatteryChargePower;
    }

    public Watt getMaxBatteryChargePower() {
        return MaxBatteryChargePower;
    }

    public Watt getMinBatteryDischargePower() {
        return MinBatteryDischargePower;
    }

    public Watt getMaxBatteryDischargePower() {
        return MaxBatteryDischargePower;
    }

    public Watt getMinSmartLoadControlTriggerPowerW() {
        return MinSmartLoadControlTriggerPowerW;
    }

    public Watt getMaxSmartLoadControlTriggerPowerW() {
        return MaxSmartLoadControlTriggerPowerW;
    }

    public Ampere getMinChargeCurrent() {
        return MinChargeCurrent;
    }

    public Ampere getMaxChargeCurrent() {
        return MaxChargeCurrent;
    }

    public Ampere getMinDischargeCurrent() {
        return MinDischargeCurrent;
    }

    public Ampere getMaxDischargeCurrent() {
        return MaxDischargeCurrent;
    }

    public int getMinOnGridSoCLimit() {
        return MinOnGridSoCLimit;
    }

    public int getMaxOnGridSoCLimit() {
        return MaxOnGridSoCLimit;
    }

    public int getMinOffGridSoCLimit() {
        return MinOffGridSoCLimit;
    }

    public int getMaxOffGridSoCLimit() {
        return MaxOffGridSoCLimit;
    }

    public boolean isSupportsThreePhasePower() {
        return SupportsThreePhasePower;
    }

    public boolean isSupportsConnectedPV() {
        return SupportsConnectedPV;
    }

    public boolean isSupportsMeasuringThirdPartyInverter() {
        return SupportsMeasuringThirdPartyInverter;
    }

    public boolean isSupportsAS477722020GridProfiles() {
        return SupportsAS477722020GridProfiles;
    }

    public boolean isSupportsAS477722015GridProfiles() {
        return SupportsAS477722015GridProfiles;
    }

    public boolean isSupportsAS477722020SiteLimits() {
        return SupportsAS477722020SiteLimits;
    }

    public boolean isFirstRelayConfigurable() {
        return IsFirstRelayConfigurable;
    }

    public Optional<Integer> getPlateRatedBatteryCapacityWattHours() {
        return PlateRatedBatteryCapacityWattHours;
    }

    public boolean isSupportsDredSubscribed() {
        return SupportsDredSubscribed;
    }

}
