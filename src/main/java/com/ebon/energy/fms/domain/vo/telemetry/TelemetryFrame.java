package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class TelemetryFrame {
    private String Name;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private Instant TimestampUtc;
    private String Data;

    public TelemetryFrame(String Name, Instant TimestampUtc, String Data) {
        this.Name = Name;
        this.TimestampUtc = TimestampUtc;
        this.Data = Data;
    }

    public String getName() {
        return Name;
    }

    public Instant getTimestampUtc() {
        return TimestampUtc;
    }

    public String getData() {
        return Data;
    }
}