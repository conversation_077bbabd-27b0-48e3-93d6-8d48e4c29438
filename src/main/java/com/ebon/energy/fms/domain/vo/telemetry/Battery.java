package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class Battery implements Cloneable {
    public Battery() { }

    public Battery(
            Double V,
            Double I,
            Double P,
            Double SoC,
            Double SOH,
            Double Capacity,
            Double DayTotalInputE,
            Double DayTotalOutputE,
            Double DayMaxInputP,
            Long DayMaxInputPEpoch,
            Double DayMaxOutputP,
            Long DayMaxOutputPEpoch,
            BatteryStatusValue Status,
            Double MaxChargeCurrent,
            Double MaxDischargeCurrent,
            Double RemainingCapacity,
            Double RatedCapacity,
            Double MinCellVoltage,
            Double MaxCellVoltage,
            BatteryVoltageType BatteryVoltageType,
            Double HighestMinCellVoltageWhileIdle) {

        this.V = V;
        this.I = I;
        this.P = P;
        this.SoC = SoC;
        this.RemainingCapacity = RemainingCapacity;
        this.SOH = SOH;
        this.Capacity = Capacity;
        this.RatedCapacity = RatedCapacity;
        this.DayTotalInputE = DayTotalInputE;
        this.DayTotalOutputE = DayTotalOutputE;
        this.DayMaxInputP = DayMaxInputP;
        this.DayMaxInputPEpoch = DayMaxInputPEpoch;
        this.DayMaxOutputP = DayMaxOutputP;
        this.DayMaxOutputPEpoch = DayMaxOutputPEpoch;
        this.Status = Status;
        this.MaxChargeCurrent = MaxChargeCurrent;
        this.MaxDischargeCurrent = MaxDischargeCurrent;
        this.MinCellVoltage = MinCellVoltage;
        this.MaxCellVoltage = MaxCellVoltage;
        this.BatteryVoltageType = BatteryVoltageType;
        this.HighestMinCellVoltageWhileIdle = HighestMinCellVoltageWhileIdle;
    }

    // @SystemStatus(minValue = 35, maxValue = 65, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "V")
    // @Display(name = "Voltage", description = "Battery voltage")
    private Double V;

    // @SystemStatus(minValue = -150, maxValue = 150, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "A")
    // @Display(name = "Current", description = "Battery current")
    private Double I;

    // @SystemStatus(minValue = -10, maxValue = 10, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "W")
    // @Display(name = "Power", description = "Power flow in or out of the battery (negative is charging)")
    private Double P;

    // @SystemStatus(minValue = 0, maxValue = 100, logLevel = LogLevels.LEVEL1,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "%")
    // @Display(name = "State of charge", description = "100% indicates a full battery")
    private Double SoC;

    // @SystemStatus(minValue = 0, maxValue = 100000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "Wh")
    // @Display(name = "Remaining Capacity", description = "Remaining charaged capacity, that the battery can discharge")
    private Double RemainingCapacity;

    // @SystemStatus(minValue = 0, maxValue = 100, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "%")
    // @Display(name = "State of health", description = "100% indicates a new battery")
    private Double SOH;

    // @SystemStatus(minValue = 0, maxValue = 5000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "Ah")
    // @Display(name = "Capacity", description = "Total capacity of the battery")
    private Double Capacity;

    // @SystemStatus(minValue = 0, maxValue = 100000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.USER, visibility = Visibilities.DETAILED, units = "Wh")
    // @Display(name = "Rated Capacity", description = "Total capacity the battery is rated for")
    private Double RatedCapacity;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.SYSTEM_ADMIN,
    //               visibility = Visibilities.NONE, units = "kWh")
    // @Display(name = "Day Total  Charging", description = "Total energy input into the battery this day")
    private Double DayTotalInputE;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.SYSTEM_ADMIN,
    //               visibility = Visibilities.NONE, units = "kWh")
    // @Display(name = "Day Total  Discharging", description = "Total energy output from the battery this day")
    private Double DayTotalOutputE;

    // @SystemStatus(minValue = 0, maxValue = 10000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.SYSTEM_ADMIN, visibility = Visibilities.NONE, units = "W")
    // @Display(name = "Daily maximum input power", description = "Maximum input power so far this day")
    private Double DayMaxInputP;

    // @SystemStatus(minValue = 0, maxValue = 9999999999, logLevel = LogLevels.LEVEL3,
    //               role = Roles.SYSTEM_ADMIN, visibility = Visibilities.NONE, units = "")
    // @Display(name = "Time of maximum input power", description = "The time when the maximum input power was recorded")
    private Long DayMaxInputPEpoch;

    // @SystemStatus(minValue = 0, maxValue = 6000, logLevel = LogLevels.LEVEL2,
    //               role = Roles.SYSTEM_ADMIN, visibility = Visibilities.NONE, units = "W")
    // @Display(name = "Daily maximum output power", description = "Maximum output power so far this day")
    private Double DayMaxOutputP;

    // @SystemStatus(minValue = 0, maxValue = 9999999999, logLevel = LogLevels.LEVEL3,
    //               role = Roles.SYSTEM_ADMIN, visibility = Visibilities.NONE, units = "")
    // @Display(name = "Time of maximum output power", description = "The time when the maximum output power was recorded")
    private Long DayMaxOutputPEpoch;

    // @SystemStatus(logLevel = LogLevels.LEVEL1, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Status of the Battery", description = "What mode the Battery is currently in")
    private BatteryStatusValue Status;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "A")
    // @Display(name = "Maximum Charge Current", description = "The maximum current the battery can charge at")
    private Double MaxChargeCurrent;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "A")
    // @Display(name = "Maximum Discharge Current", description = "The maximum current the battery can discharge at")
    private Double MaxDischargeCurrent;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "V")
    // @Display(name = "Minimum Cell Voltage", description = "The lowest cell voltage currently in the battery")
    private Double MinCellVoltage;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "V")
    // @Display(name = "Maximum Cell Voltage", description = "The highest cell voltage currently in the battery")
    private Double MaxCellVoltage;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Battery Voltage Type", description = "The voltage type of the batteries (High or Low Voltage)")
    private BatteryVoltageType BatteryVoltageType = SystemStatusEnum.BatteryVoltageType.Unknown;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "")
    // @Display(name = "Raw SoC", description = "SoC as reported by the battery before adjustment")
    private Double SoCRaw;

    // @SystemStatus(logLevel = LogLevels.LEVEL2, role = Roles.USER,
    //               visibility = Visibilities.DETAILED, units = "V")
    // @Display(name = "Highest Minimum Cell Voltage While Idle", description = "The highest cell voltage while Idle")
    private Double HighestMinCellVoltageWhileIdle;

    // Getters and Setters with original casing
    public Double getV() { return V; }
    public void setV(Double V) { this.V = V; }

    @JsonProperty("I")
    public Double getI() {
        // 解决-0.0场景
        if (I != null && I == 0.0) {
            return 0.0;
        }

        return this.I;
    }

    public void setI(Double I) {
        this.I = I;
    }

    @JsonProperty("P")
    public Double getP() {
        // 解决-0.0场景
        if (P != null && P == 0.0) {
            return 0.0;
        }

        return this.P;
    }

    public void setP(Double P) { this.P = P; }

    public Double getSoC() { return SoC; }
    public void setSoC(Double SoC) { this.SoC = SoC; }

    public Double getRemainingCapacity() { return RemainingCapacity; }
    public void setRemainingCapacity(Double RemainingCapacity) { this.RemainingCapacity = RemainingCapacity; }

    public Double getSOH() { return SOH; }
    public void setSOH(Double SOH) { this.SOH = SOH; }

    public Double getCapacity() { return Capacity; }
    public void setCapacity(Double Capacity) { this.Capacity = Capacity; }

    public Double getRatedCapacity() { return RatedCapacity; }
    public void setRatedCapacity(Double RatedCapacity) { this.RatedCapacity = RatedCapacity; }

    public Double getDayTotalInputE() { return DayTotalInputE; }
    public void setDayTotalInputE(Double DayTotalInputE) { this.DayTotalInputE = DayTotalInputE; }

    public Double getDayTotalOutputE() { return DayTotalOutputE; }
    public void setDayTotalOutputE(Double DayTotalOutputE) { this.DayTotalOutputE = DayTotalOutputE; }

    public Double getDayMaxInputP() { return DayMaxInputP; }
    public void setDayMaxInputP(Double DayMaxInputP) { this.DayMaxInputP = DayMaxInputP; }

    public Long getDayMaxInputPEpoch() { return DayMaxInputPEpoch; }
    public void setDayMaxInputPEpoch(Long DayMaxInputPEpoch) { this.DayMaxInputPEpoch = DayMaxInputPEpoch; }

    public Double getDayMaxOutputP() { return DayMaxOutputP; }
    public void setDayMaxOutputP(Double DayMaxOutputP) { this.DayMaxOutputP = DayMaxOutputP; }

    public Long getDayMaxOutputPEpoch() { return DayMaxOutputPEpoch; }
    public void setDayMaxOutputPEpoch(Long DayMaxOutputPEpoch) { this.DayMaxOutputPEpoch = DayMaxOutputPEpoch; }

    public BatteryStatusValue getStatus() { return Status; }
    public void setStatus(BatteryStatusValue Status) { this.Status = Status; }

    public Double getMaxChargeCurrent() { return MaxChargeCurrent; }
    public void setMaxChargeCurrent(Double MaxChargeCurrent) { this.MaxChargeCurrent = MaxChargeCurrent; }

    public Double getMaxDischargeCurrent() { return MaxDischargeCurrent; }
    public void setMaxDischargeCurrent(Double MaxDischargeCurrent) { this.MaxDischargeCurrent = MaxDischargeCurrent; }

    public Double getMinCellVoltage() { return MinCellVoltage; }
    public void setMinCellVoltage(Double MinCellVoltage) { this.MinCellVoltage = MinCellVoltage; }

    public Double getMaxCellVoltage() { return MaxCellVoltage; }
    public void setMaxCellVoltage(Double MaxCellVoltage) { this.MaxCellVoltage = MaxCellVoltage; }

    public BatteryVoltageType getBatteryVoltageType() { return BatteryVoltageType; }
    public void setBatteryVoltageType(BatteryVoltageType BatteryVoltageType) { this.BatteryVoltageType = BatteryVoltageType; }

    public Double getSoCRaw() { return SoCRaw; }
    public void setSoCRaw(Double SoCRaw) { this.SoCRaw = SoCRaw; }

    public Double getHighestMinCellVoltageWhileIdle() { return HighestMinCellVoltageWhileIdle; }
    public void setHighestMinCellVoltageWhileIdle(Double HighestMinCellVoltageWhileIdle) {
        this.HighestMinCellVoltageWhileIdle = HighestMinCellVoltageWhileIdle;
    }

    @Override
    public Battery clone() {
        try {
            return (Battery) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // Can't happen
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Battery battery = (Battery) o;
        return Objects.equals(V, battery.V) &&
                Objects.equals(I, battery.I) &&
                Objects.equals(P, battery.P) &&
                Objects.equals(SoC, battery.SoC) &&
                Objects.equals(RemainingCapacity, battery.RemainingCapacity) &&
                Objects.equals(SOH, battery.SOH) &&
                Objects.equals(Capacity, battery.Capacity) &&
                Objects.equals(RatedCapacity, battery.RatedCapacity) &&
                Objects.equals(DayTotalInputE, battery.DayTotalInputE) &&
                Objects.equals(DayTotalOutputE, battery.DayTotalOutputE) &&
                Objects.equals(DayMaxInputP, battery.DayMaxInputP) &&
                Objects.equals(DayMaxInputPEpoch, battery.DayMaxInputPEpoch) &&
                Objects.equals(DayMaxOutputP, battery.DayMaxOutputP) &&
                Objects.equals(DayMaxOutputPEpoch, battery.DayMaxOutputPEpoch) &&
                Status == battery.Status &&
                Objects.equals(MaxChargeCurrent, battery.MaxChargeCurrent) &&
                Objects.equals(MaxDischargeCurrent, battery.MaxDischargeCurrent) &&
                Objects.equals(MinCellVoltage, battery.MinCellVoltage) &&
                Objects.equals(MaxCellVoltage, battery.MaxCellVoltage) &&
                BatteryVoltageType == battery.BatteryVoltageType &&
                Objects.equals(SoCRaw, battery.SoCRaw) &&
                Objects.equals(HighestMinCellVoltageWhileIdle, battery.HighestMinCellVoltageWhileIdle);
    }

    @Override
    public int hashCode() {
        return Objects.hash(V, I, P, SoC, RemainingCapacity, SOH, Capacity, RatedCapacity,
                DayTotalInputE, DayTotalOutputE, DayMaxInputP, DayMaxInputPEpoch,
                DayMaxOutputP, DayMaxOutputPEpoch, Status, MaxChargeCurrent,
                MaxDischargeCurrent, MinCellVoltage, MaxCellVoltage,
                BatteryVoltageType, SoCRaw, HighestMinCellVoltageWhileIdle);
    }

    @Override
    public String toString() {
        return "Battery{" +
                "V=" + V +
                ", I=" + I +
                ", P=" + P +
                ", SoC=" + SoC +
                ", RemainingCapacity=" + RemainingCapacity +
                ", SOH=" + SOH +
                ", Capacity=" + Capacity +
                ", RatedCapacity=" + RatedCapacity +
                ", DayTotalInputE=" + DayTotalInputE +
                ", DayTotalOutputE=" + DayTotalOutputE +
                ", DayMaxInputP=" + DayMaxInputP +
                ", DayMaxInputPEpoch=" + DayMaxInputPEpoch +
                ", DayMaxOutputP=" + DayMaxOutputP +
                ", DayMaxOutputPEpoch=" + DayMaxOutputPEpoch +
                ", Status=" + Status +
                ", MaxChargeCurrent=" + MaxChargeCurrent +
                ", MaxDischargeCurrent=" + MaxDischargeCurrent +
                ", MinCellVoltage=" + MinCellVoltage +
                ", MaxCellVoltage=" + MaxCellVoltage +
                ", BatteryVoltageType=" + BatteryVoltageType +
                ", SoCRaw=" + SoCRaw +
                ", HighestMinCellVoltageWhileIdle=" + HighestMinCellVoltageWhileIdle +
                '}';
    }
}