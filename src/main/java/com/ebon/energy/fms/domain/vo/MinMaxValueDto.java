// MinMaxValueDto.java
package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MinMaxValueDto {

    @JsonProperty("Min")
    private int min;

    @JsonProperty("Max")
    private int max;

    @JsonProperty("Value")
    private Integer value;
}
