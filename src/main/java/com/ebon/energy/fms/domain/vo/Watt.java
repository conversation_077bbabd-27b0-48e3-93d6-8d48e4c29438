package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonValue;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class Watt implements IUnit {

    public static final String SYMBOL = "W";

    private BigDecimal value;

    public BigDecimal getValue() {
        return value;
    }

    @JsonValue
    public Double getDoubleValue() {
        if (value == null) {
            return null;
        }

        BigDecimal bigDecimal = value.setScale(3, RoundingMode.HALF_UP);
        return bigDecimal.doubleValue();
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getSymbol() {
        return SYMBOL;
    }

    public static final Watt Unit = new Watt(new BigDecimal("1.0"));
    public static final Watt Zero = new Watt(new BigDecimal("0.0"));
    public static final Watt Tera = new Watt(new BigDecimal("1000000000000"));
    public static final Watt Giga = new Watt(new BigDecimal("1000000000"));
    public static final Watt Mega = new Watt(new BigDecimal("1000000"));
    public static final Watt Kilo = new Watt(new BigDecimal("1000"));
    public static final Watt Hecto = new Watt(new BigDecimal("100"));
    public static final Watt Deca = new Watt(new BigDecimal("10"));
    public static final Watt Deci = new Watt(new BigDecimal("0.1"));
    public static final Watt Centi = new Watt(new BigDecimal("0.01"));
    public static final Watt Milli = new Watt(new BigDecimal("0.001"));

    public Watt() {
    }

    public Watt(BigDecimal value) {
        this.value = value;
    }

    public Watt(String value) {
        this.value = new BigDecimal(value);
    }


    public Watt(Double value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Watt(Integer value) {
        this.value = value != null ? new BigDecimal(String.valueOf(value)) : null;
    }

    public Watt abs() {
        return new Watt(value.abs());
    }

    public Watt subtract(Watt other) {
        return new Watt(this.value.subtract(other.value));
    }

    public Watt add(Watt other) {
        return new Watt(this.value.add(other.value));
    }

    public static Watt add(Watt a, Watt b) {
        return new Watt(a.value.add(b.value));
    }

    public static Watt subtract(Watt a, Watt b) {
        return new Watt(a.value.subtract(b.value));
    }

    public boolean greaterThan(Watt other) {
        return this.value.compareTo(other.value) > 0;
    }

    public boolean lessThan(Watt other) {
        return this.value.compareTo(other.value) < 0;
    }

    public boolean greaterThanOrEqual(Watt other) {
        return this.value.compareTo(other.value) >= 0;
    }

    public boolean lessThanOrEqual(Watt other) {
        return this.value.compareTo(other.value) <= 0;
    }

    public static Watt getValueOrZero(Watt watt) {
        return watt == null || watt.getValue() == null ? Watt.Zero : watt;
    }

    // 重载一元 + 运算符
    public static Watt operatorPlus(Watt a) {
        return a;
    }

    // 重载一元 - 运算符
    public static Watt operatorMinus(Watt a) {
        return new Watt(a.value.negate());
    }

    // 重载二元 + 运算符
    public static Watt operatorAdd(Watt a, Watt b) {
        return new Watt(a.value.add(b.value));
    }

    // 重载二元 - 运算符
    public static Watt operatorSubtract(Watt a, Watt b) {
        return new Watt(a.value.subtract(b.value));
    }

    // 重载二元 / 运算符，返回 BigDecimal
    public static BigDecimal operatorDivide(Watt a, Watt b) {
        return a.value.divide(b.value);
    }

    // 重载 Watt 除以 BigDecimal 的 / 运算符
    public static Watt operatorDivide(Watt a, BigDecimal b) {
        return new Watt(a.value.divide(b));
    }

    // 重载 Watt 乘以 BigDecimal 的 * 运算符
    public static Watt operatorMultiply(Watt a, BigDecimal value) {
        return new Watt(a.value.multiply(value));
    }

    // 重载 BigDecimal 乘以 Watt 的 * 运算符
    public static Watt operatorMultiply(BigDecimal value, Watt a) {
        return new Watt(value.multiply(a.value));
    }

    // 重载 == 运算符
    public static boolean operatorEqual(Watt a, Watt b) {
        return a.value.equals(b.value);
    }

    // 重载 != 运算符
    public static boolean operatorNotEqual(Watt a, Watt b) {
        return !a.value.equals(b.value);
    }

    // 重载 < 运算符
    public static boolean operatorLessThan(Watt a, Watt b) {
        return a.value.compareTo(b.value) < 0;
    }

    // 重载 <= 运算符
    public static boolean operatorLessThanOrEqual(Watt a, Watt b) {
        return a.value.compareTo(b.value) <= 0;
    }

    // 重载 > 运算符
    public static boolean operatorGreaterThan(Watt a, Watt b) {
        return a.value.compareTo(b.value) > 0;
    }

    // 重载 >= 运算符
    public static boolean operatorGreaterThanOrEqual(Watt a, Watt b) {
        return a.value.compareTo(b.value) >= 0;
    }

    // 从 BigDecimal 隐式转换为 Watt
    public static Watt fromBigDecimal(BigDecimal d) {
        return new Watt(d);
    }

    public BigDecimal asDecimal() {
        return asDecimal(Unit);
    }

    public double asDouble() {
        return asDouble(Unit);
    }

    public BigDecimal asDecimal(int precision) {
        return asDecimal(Unit, precision);
    }

    public double asDouble(int precision) {
        return asDouble(Unit, precision);
    }

    public long asLong() {
        return asLong(Unit);
    }

    public BigDecimal asDecimal(Watt unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP);
    }

    public double asDouble(Watt unit, int precision) {
        return value.divide(unit.value, precision, RoundingMode.HALF_UP).doubleValue();
    }

    public BigDecimal asDecimal(Watt unit) {
        return value.divide(unit.value);
    }

    public double asDouble(Watt unit) {
        return value.divide(unit.value).doubleValue();
    }

    public long asLong(Watt unit) {
        return value.divide(unit.value).longValue();
    }


    public int asInt() {
        return value.divide(Unit.value).intValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Watt other = (Watt) obj;
        return value.compareTo(other.value) == 0;
    }

    public int compareTo(Watt other) {
        return value.compareTo(other.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return value.toPlainString() + SYMBOL;
    }
}
