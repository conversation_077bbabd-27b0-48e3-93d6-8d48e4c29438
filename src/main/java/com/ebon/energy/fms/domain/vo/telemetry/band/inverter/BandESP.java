package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;





 





public class BandESP extends DataBackedBand
{
	public BandESP()
	{
		super(BandForge.<BandESP>getMetadataFor(BandESP.class));
	}



	public BandESP(byte[] bytes)
	{
		super(bytes, BandForge.<BandESP>getMetadataFor(BandESP.class));
	}

	public BandESP(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandESP>getMetadataFor(BandESP.class));
	}


	
	public final WattHour getActiveETotalSellR()
	{
		return GetU48(0, new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellS()
	{
		return GetU48(6, new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellT()
	{
		return GetU48(12,new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalSellTotal()
	{
		return GetU48(18, new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyR()
	{
		return GetU48(24, new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyS()
	{
		return GetU48(30, new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyT()
	{
		return GetU48(36, new WattHour(0.01).multiply(WattHour.Kilo));
	}

	
	public final WattHour getActiveETotalBuyTotal()
	{
		return GetU48(42, new WattHour(0.01).multiply(WattHour.Kilo));
	}
}
