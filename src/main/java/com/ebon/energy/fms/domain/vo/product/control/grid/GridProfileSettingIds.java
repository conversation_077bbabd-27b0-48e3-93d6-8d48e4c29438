package com.ebon.energy.fms.domain.vo.product.control.grid;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
public class GridProfileSettingIds {

    @JsonProperty("VoltageDisturbanceBandWithstand")
    private final VoltageDisturbanceBandWithstandSettingIds voltageDisturbanceBandWithstand;

    // 静态工厂方法
    public static GridProfileSettingIds gen3GridProfileSettingIds() {
        return new GridProfileSettingIds(
            new VoltageDisturbanceBandWithstandSettingIds(
                null,
                "57cfc74f-80cb-4cd3-9fdc-f13a9661ea60"
            )
        );
    }

    public static GridProfileSettingIds siGridProfileSettingIds() {
        return new GridProfileSettingIds(
            new VoltageDisturbanceBandWithstandSettingIds(
                "5a76ce5b-0b82-4593-81b8-ba8d5aa0bdcd",
                null
            )
        );
    }

    public static GridProfileSettingIds rossGridProfileSettingIds() {
        return new GridProfileSettingIds(
            new VoltageDisturbanceBandWithstandSettingIds(
                null,
                "1c93ef12-3773-423c-9b36-4683259ef559"
            )
        );
    }
}

