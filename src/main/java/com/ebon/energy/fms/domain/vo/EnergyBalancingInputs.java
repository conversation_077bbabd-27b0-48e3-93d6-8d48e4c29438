package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.BalancingMethods;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@Data
public class EnergyBalancingInputs {
    private final WattHour usageWh;
    private final WattHour soldWh;
    private final WattHour boughtWh;
    private final WattHour generationWh;
    private final WattHour batteryChargedWh;
    private final WattHour batteryDischargedWh;
    private final WattHour maximumPossiblePVOutput;
    private final BalancingMethods balancingMethod;
    private final WattHour acceptableImbalance;
    private final Boolean supportsLoadContributors;

    public EnergyBalancingInputs(
            WattHour usageWh,
            WattHour soldWh,
            WattHour boughtWh,
            WattHour generationWh,
            WattHour batteryChargedWh,
            WattHour batteryDischargedWh,
            WattHour maximumPossiblePVOutput,
            BalancingMethods balancingMethod,
            WattHour acceptableImbalance,
            boolean supportsLoadContributors) {

        this.usageWh = usageWh;
        this.soldWh = soldWh;
        this.boughtWh = boughtWh;
        this.generationWh = generationWh;
        this.batteryChargedWh = batteryChargedWh;
        this.batteryDischargedWh = batteryDischargedWh;
        this.maximumPossiblePVOutput = maximumPossiblePVOutput;
        this.balancingMethod = balancingMethod;
        this.acceptableImbalance = acceptableImbalance;
        this.supportsLoadContributors = supportsLoadContributors;
    }

}