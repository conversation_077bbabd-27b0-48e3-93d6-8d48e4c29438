package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;




 





public class BandSGInverterAnalytics3P extends DataBackedBand
{
	public BandSGInverterAnalytics3P()
	{
		super(BandForge.<BandSGInverterAnalytics3P>getMetadataFor(BandSGInverterAnalytics3P.class));
	}



	public BandSGInverterAnalytics3P(byte[] bytes)
	{
		super(bytes, BandForge.<BandSGInverterAnalytics3P>getMetadataFor(BandSGInverterAnalytics3P.class));
	}

	public BandSGInverterAnalytics3P(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandSGInverterAnalytics3P>getMetadataFor(BandSGInverterAnalytics3P.class));
	}



	public final TimeSpan getInverterUptime()
	{
		return GetU32(0, TimeSpan.fromSeconds(1));
	}


	public final Volt getPV1VoltageMax()
	{
		return GetU16(4, Volt.Deci);
	}


	public final Volt getPV1VoltageMin()
	{
		return GetU16(6, Volt.Deci);
	}


	public final Volt getPV1VoltageAvg()
	{
		return GetU16(8, Volt.Deci);
	}


	public final Watt getPV1PowerMax()
	{
		return GetU32(10, Watt.Deci);
	}


	public final Watt getPV1PowerMin()
	{
		return GetU32(14, Watt.Deci);
	}


	public final Watt getPV1PowerAvg()
	{
		return GetU32(18, Watt.Deci);
	}


	public final Volt getPV2VoltageMax()
	{
		return GetU16(22, Volt.Deci);
	}


	public final Volt getPV2VoltageMin()
	{
		return GetU16(24, Volt.Deci);
	}


	public final Volt getPV2VoltageAvg()
	{
		return GetU16(26, Volt.Deci);
	}


	public final Watt getPV2PowerMax()
	{
		return GetU32(28, Watt.Deci);
	}


	public final Watt getPV2PowerMin()
	{
		return GetU32(32, Watt.Deci);
	}


	public final Watt getPV2PowerAvg()
	{
		return GetU32(36, Watt.Deci);
	}


	public final Watt getActivePowerMax()
	{
		return GetU32(40, Watt.Deci);
	}


	public final Watt getActivePowerMin()
	{
		return GetU32(44, Watt.Deci);
	}


	public final Watt getActivePowerAvg()
	{
		return GetU32(48, Watt.Deci);
	}


	public final VoltAmpsReactive getReactivePowerMax()
	{
		return GetS32(52, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerMin()
	{
		return GetS32(56, VoltAmpsReactive.Deci);
	}


	public final VoltAmpsReactive getReactivePowerAvg()
	{
		return GetS32(60, VoltAmpsReactive.Deci);
	}


	public final BigDecimal getPowerFactorMax()
	{
		return new BigDecimal(GetS16(64)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorMin()
	{
		return new BigDecimal(GetS16(66)).multiply(Percentage.Tenth);
	}


	public final BigDecimal getPowerFactorAvg()
	{
		return new BigDecimal(GetS16(68)).multiply(Percentage.Tenth);
	}


	public final Watt getPhaseL1LoadMax()
	{
		return GetS32(70, Watt.Deci);
	}


	public final Watt getPhaseL1LoadMin()
	{
		return GetS32(74, Watt.Deci);
	}


	public final Watt getPhaseL1LoadAvg()
	{
		return GetS32(78, Watt.Deci);
	}


	public final Volt getGridVoltageAverage()
	{
		return GetU16(82, Volt.Deci);
	}


	public final Ampere getPV1CurrentMax()
	{
		return GetU16(84, Ampere.Centi);
	}


	public final Ampere getPV1CurrentMin()
	{
		return GetU16(86, Ampere.Centi);
	}


	public final Ampere getPV1CurrentAvg()
	{
		return GetU16(88, Ampere.Centi);
	}


	public final Ampere getPV2CurrentMax()
	{
		return GetU16(90, Ampere.Centi);
	}


	public final Ampere getPV2CurrentMin()
	{
		return GetU16(92, Ampere.Centi);
	}


	public final Ampere getPV2CurrentAvg()
	{
		return GetU16(94, Ampere.Centi);
	}


	public final Watt getPhaseL2LoadMax()
	{
		return GetS32(96, Watt.Deci);
	}


	public final Watt getPhaseL2LoadMin()
	{
		return GetS32(100, Watt.Deci);
	}


	public final Watt getPhaseL2LoadAvg()
	{
		return GetS32(104, Watt.Deci);
	}


	public final Watt getPhaseL3LoadMax()
	{
		return GetS32(108, Watt.Deci);
	}


	public final Watt getPhaseL3LoadMin()
	{
		return GetS32(112, Watt.Deci);
	}


	public final Watt getPhaseL3LoadAvg()
	{
		return GetS32(116, Watt.Deci);
	}
}
