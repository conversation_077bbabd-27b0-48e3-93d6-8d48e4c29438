package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BatteryCabinetStatus implements Cloneable {
    private Double Temperature;
    private Status FanState;

    public BatteryCabinetStatus() {
    }

    public BatteryCabinetStatus(Double Temperature, Status FanState) {
        this.Temperature = Temperature;
        this.FanState = FanState;
    }

    public Double getTemperature() {
        return Temperature;
    }

    public void setTemperature(Double Temperature) {
        this.Temperature = Temperature;
    }

    public Status getFanState() {
        return FanState;
    }

    public void setFanState(Status FanState) {
        this.FanState = FanState;
    }

    @Override
    public BatteryCabinetStatus clone() {
        try {
            return (BatteryCabinetStatus) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}