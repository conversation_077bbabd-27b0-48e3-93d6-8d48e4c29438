package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.LEDState;
import com.ebon.energy.fms.common.enums.MeterConnectStatus;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandBHH19 extends DataBackedBand implements IBandBHH
{
	public BandBHH19()
	{
		super(BandForge.<BandBHH19>getMetadataFor(BandBHH19.class));
	}



	public BandBHH19(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHH19>getMetadataFor(BandBHH19.class));
	}

	public BandBHH19(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHH19>getMetadataFor(BandBHH19.class));
	}


	


	public final int getAppModeIndex() { return GetU16(0); }

	
	public final Object getMeterCheckValueL1()
	{
		return MeterConnectStatus.parse(GetMaskedU16(2, (short) 3, (short) 0));
	}

	
	public final Object getMeterCheckValueL2()
	{
		return MeterConnectStatus.parse(GetMaskedU16(2, (short) 48, (short) 4));
	}

	
	public final Object getMeterCheckValueL3()
	{
		return MeterConnectStatus.parse(GetMaskedU16(2, (short) 768, (short) 8));
	}

	
	public final boolean getMeterConnectCheckFlag()
	{
		return GetBool((int) GetU16(4), 1, 0, false);
	}

	
	public final Watt getSimulateMeterPower()
	{
		return GetU16(6, Watt.Unit);
	}

	
	public final boolean getBreezeOnOff()
	{
		return GetBool((int) GetU16(8), 1, 0, false);
	}

	
	public final boolean getLogDataEnable()
	{
		return GetBool((int) GetU16(10), 1, 0, false);
	}

	
	public final TimeSpan getDataSendInterval()
	{
		return GetU16(12, TimeSpan.fromSeconds(1));
	}

	


	public final int getDREDcmd() { return GetU16(14); }

	
	public final boolean getLedtestflag()
	{
		return GetBool((int) GetU16(16), 1, 0, false);
	}

	


	public final int getWiFiOrLANSwitch() { return GetU16(18); }

	
	public final boolean getDredOffGridCheck()
	{
		return GetBool((int) GetU16(20), 1, 0, true);
	}

	
	public final TimeSpan getCommsTimeout()
	{
		return GetU16(24, TimeSpan.fromSeconds(1));
	}

	
	public final Object getWiFiLEDState()
	{
		return LEDState.parse(GetU16(26));
	}

	
	public final Object getComLEDState()
	{
		return LEDState.parse(GetU16(28));
	}

	
	public final boolean getMeterCT1ReverseEnable()
	{
		return GetBool((int) GetU16(30), 1, 0, true);
	}

	


	public final int getErrorLogReadPage() { return GetU16(32); }

	


	public final int getModbusTCPWithoutInternet() { return GetU16(34); }

	
	public final Object getBackupLED()
	{
		return LEDState.parse(GetU16(36));
	}

	
	public final Object getGridLED()
	{
		return LEDState.parse(GetU16(38));
	}

	
	public final Object getSOCLED1()
	{
		return LEDState.parse(GetU16(40));
	}

	
	public final Object getSOCLED2()
	{
		return LEDState.parse(GetU16(42));
	}

	
	public final Object getSOCLED3()
	{
		return LEDState.parse(GetU16(44));
	}

	
	public final Object getSOCLED4()
	{
		return LEDState.parse(GetU16(46));
	}

	
	public final Object getBatteryLED()
	{
		return LEDState.parse(GetU16(48));
	}

	
	public final Object getSystemLED()
	{
		return LEDState.parse(GetU16(50));
	}

	
	public final Object getFaultLED()
	{
		return LEDState.parse(GetU16(52));
	}

	
	public final Object getEnergyLED()
	{
		return LEDState.parse(GetU16(54));
	}

	
	public final Object getLEDExternalControl()
	{
		return LEDState.parse(GetU16(56));
	}

	
	public final boolean getCtOnLoadFlag()
	{
		return GetBool((int) GetU16(66), 1, 0, true);
	}

	
	public final short getOtherInvActivePower()
	{
		return GetS16(68);
	}

	
	public final short getOtherInvReactivePower()
	{
		return GetS16(70);
	}

	
	public final boolean getDredAusCheckEnable()
	{
		return GetBool((int) GetU16(72), 1, 0, true);
	}

	
	public final boolean getDred0toStopMode()
	{
		return GetBool((int) GetU16(74), 1, 0, true);
	}

	
	public final boolean getStopModeSaveEnable()
	{
		return GetBool((int) GetU16(76), 1, 0, true);
	}

	
	public final boolean getPVextendEnable()
	{
		return GetBool((int) GetU16(78), 1, 0, true);
	}
}
