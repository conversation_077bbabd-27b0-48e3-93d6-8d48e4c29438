package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.ZonedDateTime;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class OuijaBoard implements Cloneable {
    private Boolean BMSComms;
    private Boolean BatteryCabinetComms;
    private Boolean CTComms;
    private Boolean InverterComms;
    private Boolean RelayComms;
    private OuijaBoardHardware HardwareVersion;
    private String SoftwareVersion;
    private String OSVersion;
    private String SN;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private ZonedDateTime WindowsTime;
    private String TimeZone;
    private Boolean HasTimeSynced;
    private String ScriptSystemVersion;

    public OuijaBoard() {
        this.HardwareVersion = OuijaBoardHardware.Unknown;
    }

    public OuijaBoard(Boolean BMSComms, Boolean BatteryCabinetComms,
                      Boolean CTComms, Boolean InverterComms,
                      Boolean RelayComms, OuijaBoardHardware HardwareVersion,
                      String SoftwareVersion, String OSVersion,
                      String SN, ZonedDateTime WindowsTime,
                      String TimeZone, Boolean HasTimeSynced,
                      String ScriptSystemVersion) {
        this.BMSComms = BMSComms;
        this.BatteryCabinetComms = BatteryCabinetComms;
        this.CTComms = CTComms;
        this.InverterComms = InverterComms;
        this.RelayComms = RelayComms;
        this.HardwareVersion = Objects.requireNonNullElse(HardwareVersion, OuijaBoardHardware.Unknown);
        this.SoftwareVersion = SoftwareVersion;
        this.OSVersion = OSVersion;
        this.SN = SN;
        this.WindowsTime = WindowsTime;
        this.TimeZone = TimeZone;
        this.HasTimeSynced = HasTimeSynced;
        this.ScriptSystemVersion = ScriptSystemVersion;
    }

    public Boolean getBMSComms() {
        return BMSComms;
    }

    public void setBMSComms(Boolean BMSComms) {
        this.BMSComms = BMSComms;
    }

    public Boolean getBatteryCabinetComms() {
        return BatteryCabinetComms;
    }

    public void setBatteryCabinetComms(Boolean batteryCabinetComms) {
        BatteryCabinetComms = batteryCabinetComms;
    }

    public Boolean getCTComms() {
        return CTComms;
    }

    public void setCTComms(Boolean CTComms) {
        this.CTComms = CTComms;
    }

    public Boolean getInverterComms() {
        return InverterComms;
    }

    public void setInverterComms(Boolean inverterComms) {
        InverterComms = inverterComms;
    }

    public Boolean getRelayComms() {
        return RelayComms;
    }

    public void setRelayComms(Boolean relayComms) {
        RelayComms = relayComms;
    }

    public OuijaBoardHardware getHardwareVersion() {
        return HardwareVersion;
    }

    public void setHardwareVersion(OuijaBoardHardware hardwareVersion) {
        HardwareVersion = hardwareVersion;
    }

    public String getSoftwareVersion() {
        return SoftwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        SoftwareVersion = softwareVersion;
    }

    public String getOSVersion() {
        return OSVersion;
    }

    public void setOSVersion(String OSVersion) {
        this.OSVersion = OSVersion;
    }

    public String getSN() {
        return SN;
    }

    public void setSN(String SN) {
        this.SN = SN;
    }

    public ZonedDateTime getWindowsTime() {
        return WindowsTime;
    }

    public void setWindowsTime(ZonedDateTime windowsTime) {
        WindowsTime = windowsTime;
    }

    public String getTimeZone() {
        return TimeZone;
    }

    public void setTimeZone(String timeZone) {
        TimeZone = timeZone;
    }

    public Boolean getHasTimeSynced() {
        return HasTimeSynced;
    }

    public void setHasTimeSynced(Boolean hasTimeSynced) {
        HasTimeSynced = hasTimeSynced;
    }

    public String getScriptSystemVersion() {
        return ScriptSystemVersion;
    }

    public void setScriptSystemVersion(String scriptSystemVersion) {
        ScriptSystemVersion = scriptSystemVersion;
    }

    @Override
    public OuijaBoard clone() {
        try {
            OuijaBoard clone = (OuijaBoard) super.clone();
            if (this.WindowsTime != null) {
                clone.WindowsTime = ZonedDateTime.from(this.WindowsTime);
            }
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}