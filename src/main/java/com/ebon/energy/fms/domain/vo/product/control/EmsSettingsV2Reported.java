package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class EmsSettingsV2Reported {

    @JsonProperty("WifiSSID")
    private String wifiSsid;

    private String cellularControl;

    private String gridProfileId;

    private String gridProfileCorrelationId;

    private Boolean relay1Installed;

    private String relay1Name;

    private String emsSerialNumber;

    private String emsFirmwareVersion;

    private String emsHardwareVersion;
}
