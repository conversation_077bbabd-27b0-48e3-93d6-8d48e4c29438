package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Map;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
@JsonIgnoreProperties({"Title"})
public class DataWithLinks<T> {
    private AtAGlance<T> AtAGlance;

    private String Title;
    private Object Data;

    public DataWithLinks(String title, AtAGlance<T> atAGlance, Object data) {
        this.Title = title;
        this.AtAGlance = atAGlance;
        this.Data = data;
    }

    public AtAGlance<T> getAtAGlance() {
        return AtAGlance;
    }

    public String getTitle() {
        return Title;
    }

    public Object getData() {
        return Data;
    }

    public void setData(Object data) {
        this.Data = data;
    }

    public static <T> DataWithLinks<T> getDataWithLinks(
            String serialNumber,
            Object data,
            AtAGlance<T> basics) {
        return new DataWithLinks<T>(
                serialNumber,
                basics,
                data
        );
    }

}

class Metadata {
    private final String latest;
    private String permalink;
    private final Map<String, String> back;
    private final Map<String, String> forward;

    public Metadata(String latest, String permalink, Map<String, String> back, Map<String, String> forward) {
        this.latest = latest;
        this.permalink = permalink;
        this.back = back;
        this.forward = forward;
    }

    public String getLatest() {
        return latest;
    }

    public String getPermalink() {
        return permalink;
    }

    public void setPermalink(String permalink) {
        this.permalink = permalink;
    }

    public Map<String, String> getBack() {
        return back;
    }

    public Map<String, String> getForward() {
        return forward;
    }
}
