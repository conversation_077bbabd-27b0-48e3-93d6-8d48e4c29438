package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;










public class BandModuleAlarmInfo extends DataBackedBand
{
	public BandModuleAlarmInfo()
	{
		super(BandForge.<BandModuleAlarmInfo>getMetadataFor(BandModuleAlarmInfo.class));
	}



	public BandModuleAlarmInfo(byte[] bytes)
	{
		super(bytes, BandForge.<BandModuleAlarmInfo>getMetadataFor(BandModuleAlarmInfo.class));
	}

	public BandModuleAlarmInfo(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandModuleAlarmInfo>getMetadataFor(BandModuleAlarmInfo.class));
	}


	public final Object getInfoFlag44()
	{
		return PylonInfoFlagState.parse(GetU8(0));
	}


	public final byte getCommandValue44()
	{
		return GetU8(1);
	}


	public final byte getNumberOfCells44()
	{
		return GetU8(2);
	}

	
	public final Object getCell1VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(3));
	}

	
	public final Object getCell2VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(4));
	}

	
	public final Object getCell3VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(5));
	}

	
	public final Object getCell4VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(6));
	}

	
	public final Object getCell5VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(7));
	}

	
	public final Object getCell6VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(8));
	}

	
	public final Object getCell7VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(9));
	}

	
	public final Object getCell8VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(10));
	}

	
	public final Object getCell9VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(11));
	}

	
	public final Object getCell10VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(12));
	}

	
	public final Object getCell11VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(13));
	}

	
	public final Object getCell12VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(14));
	}

	
	public final Object getCell13VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(15));
	}

	
	public final Object getCell14VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(16));
	}

	
	public final Object getCell15VoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(17));
	}

	
	public final Object getCell16VoltageStatus()
	{
		return getNumberOfCells44() == 16 ? PylonAlarmState.parse(GetU8(18)) : null;
	}

	


	public final byte getNumberOfTempSensors44()
	{
		return GetU8(3 + getNumberOfCells44());
	}

	
	public final Object getTempBMSBoardStatus()
	{
		return getNumberOfTempSensors44() >= 1 ? PylonAlarmState.parse(GetU8(4 + getNumberOfCells44())) : null;
	}

	
	public final Object getCellTempAvgGroup1Status()
	{
		return getNumberOfTempSensors44() >= 3 ? PylonAlarmState.parse(GetU8(5 + getNumberOfCells44())) : null;
	}

	
	public final Object getCellTempAvgGroup2Status()
	{
		return getNumberOfTempSensors44() >= 4 ? PylonAlarmState.parse(GetU8(6 + getNumberOfCells44())) : null;
	}

	
	public final Object getCellTempAvgGroup3Status()
	{
		return getNumberOfTempSensors44() >= 5 ? PylonAlarmState.parse(GetU8(7 + getNumberOfCells44())) : null;
	}

	
	public final Object getMOSFETTempStatus()
	{
		return getNumberOfTempSensors44() >= 2 ? PylonAlarmState.parse(GetU8(8 + getNumberOfCells44())) : null;
	}

	public final Object getCellTempAvgGroup4Status()
	{
		return getNumberOfTempSensors44() >= 6 ? PylonAlarmState.parse(GetU8(9 + getNumberOfCells44())) : null;
	}

	
	public final Object getModuleChargeCurrentStatus()
	{
		return PylonAlarmState.parse(GetU8(4 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getModuleVoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(5 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getModuleDischargeCurrentStatus()
	{
		return PylonAlarmState.parse(GetU8(6 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getStatus1()
	{
		return PylonUSPackStatus1.parse(GetU8(7 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getStatus2()
	{
		return PylonPackStatus2.parse(GetU8(8 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getStatus3()
	{
		return PylonUSPackStatus3.parse(GetU8(9 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getStatus4()
	{
		return PylonPackStatus4.parse(GetU8(10 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}

	
	public final Object getStatus5()
	{
		return PylonPackStatus5.parse(GetU8(11 + getNumberOfTempSensors44() + getNumberOfCells44()));
	}


	public final Short getStatus6()
	{


		return (short)GetU8(12 + getNumberOfTempSensors44() + getNumberOfCells44());
	}
}
