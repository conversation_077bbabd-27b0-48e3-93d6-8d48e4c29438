package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.ITelemetryBand;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

import java.math.*;

@JsonAutoDetect(
		fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
		getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
		setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterBandFTelemetry implements ITelemetryBand
{
	public InverterBandFTelemetry(Volt vac1, Frequency fac1, Watt pacL, ReadWorkMode readWorkMode, InverterErrorMode inverterErrorMode, Volt line1AvgFaultValue, TimeSpan line1AvgFaultTime, Volt line1VHighfaultValue, TimeSpan line1VHighfaultTime, Volt line1VLowfaultValue, TimeSpan line1VLowfaultTime, Frequency line1FHighfaultValueCom, TimeSpan line1FhighfaultTimeCom, Frequency line1FlowfaultValueCom, TimeSpan line1FlowfaultTimeCom, Frequency line1FHighfaultValue, TimeSpan line1FHighfaultTime, Frequency line1FLowfaultValue, TimeSpan line1FLowfaultTime, Volt simVoltage, Frequency simFrequency, TestResult testResult, short selfTestStep, boolean startTest)
	{
		setVac1(vac1);
		setFac1(fac1);
		setPacL(pacL);
		setReadWorkMode(readWorkMode);
		setInverterErrorMode(inverterErrorMode);
		setLine1AvgFaultValue(line1AvgFaultValue);
		setLine1AvgFaultTime(line1AvgFaultTime);
		setLine1VHighfaultValue(line1VHighfaultValue);
		setLine1VHighfaultTime(line1VHighfaultTime);
		setLine1VLowfaultValue(line1VLowfaultValue);
		setLine1VLowfaultTime(line1VLowfaultTime);
		setLine1FHighfaultValueCom(line1FHighfaultValueCom);
		setLine1FhighfaultTimeCom(line1FhighfaultTimeCom);
		setLine1FlowfaultValueCom(line1FlowfaultValueCom);
		setLine1FlowfaultTimeCom(line1FlowfaultTimeCom);
		setLine1FHighfaultValue(line1FHighfaultValue);
		setLine1FHighfaultTime(line1FHighfaultTime);
		setLine1FLowfaultValue(line1FLowfaultValue);
		setLine1FLowfaultTime(line1FLowfaultTime);
		setSimVoltage(simVoltage);
		setSimFrequency(simFrequency);
		setTestResult(testResult);
		setSelfTestStep(selfTestStep);
		setStartTest(startTest);
	}

	private Volt Vac1;
	public final Volt getVac1()
	{
		return Vac1;
	}
	private void setVac1(Volt value)
	{
		Vac1 = value;
	}

	private Frequency Fac1;
	public final Frequency getFac1()
	{
		return Fac1;
	}
	private void setFac1(Frequency value)
	{
		Fac1 = value;
	}

	private Watt PacL;
	public final Watt getPacL()
	{
		return PacL;
	}
	private void setPacL(Watt value)
	{
		PacL = value;
	}

	private ReadWorkMode ReadWorkMode;
	public final ReadWorkMode getReadWorkMode()
	{
		return ReadWorkMode;
	}
	private void setReadWorkMode(ReadWorkMode value)
	{
		ReadWorkMode = value;
	}

	private InverterErrorMode InverterErrorMode;
	public final InverterErrorMode getInverterErrorMode()
	{
		return InverterErrorMode;
	}
	private void setInverterErrorMode(InverterErrorMode value)
	{
		InverterErrorMode = value;
	}

	private Volt Line1AvgFaultValue;
	public final Volt getLine1AvgFaultValue()
	{
		return Line1AvgFaultValue;
	}
	private void setLine1AvgFaultValue(Volt value)
	{
		Line1AvgFaultValue = value;
	}

	private TimeSpan Line1AvgFaultTime;
	public final TimeSpan getLine1AvgFaultTime()
	{
		return Line1AvgFaultTime;
	}
	private void setLine1AvgFaultTime(TimeSpan value)
	{
		Line1AvgFaultTime = value;
	}

	private Volt Line1VHighfaultValue;
	public final Volt getLine1VHighfaultValue()
	{
		return Line1VHighfaultValue;
	}
	private void setLine1VHighfaultValue(Volt value)
	{
		Line1VHighfaultValue = value;
	}

	private TimeSpan Line1VHighfaultTime;
	public final TimeSpan getLine1VHighfaultTime()
	{
		return Line1VHighfaultTime;
	}
	private void setLine1VHighfaultTime(TimeSpan value)
	{
		Line1VHighfaultTime = value;
	}

	private Volt Line1VLowfaultValue;
	public final Volt getLine1VLowfaultValue()
	{
		return Line1VLowfaultValue;
	}
	private void setLine1VLowfaultValue(Volt value)
	{
		Line1VLowfaultValue = value;
	}

	private TimeSpan Line1VLowfaultTime;
	public final TimeSpan getLine1VLowfaultTime()
	{
		return Line1VLowfaultTime;
	}
	private void setLine1VLowfaultTime(TimeSpan value)
	{
		Line1VLowfaultTime = value;
	}

	private Frequency Line1FHighfaultValueCom;
	public final Frequency getLine1FHighfaultValueCom()
	{
		return Line1FHighfaultValueCom;
	}
	private void setLine1FHighfaultValueCom(Frequency value)
	{
		Line1FHighfaultValueCom = value;
	}

	private TimeSpan Line1FhighfaultTimeCom;
	public final TimeSpan getLine1FhighfaultTimeCom()
	{
		return Line1FhighfaultTimeCom;
	}
	private void setLine1FhighfaultTimeCom(TimeSpan value)
	{
		Line1FhighfaultTimeCom = value;
	}

	private Frequency Line1FlowfaultValueCom;
	public final Frequency getLine1FlowfaultValueCom()
	{
		return Line1FlowfaultValueCom;
	}
	private void setLine1FlowfaultValueCom(Frequency value)
	{
		Line1FlowfaultValueCom = value;
	}

	private TimeSpan Line1FlowfaultTimeCom;
	public final TimeSpan getLine1FlowfaultTimeCom()
	{
		return Line1FlowfaultTimeCom;
	}
	private void setLine1FlowfaultTimeCom(TimeSpan value)
	{
		Line1FlowfaultTimeCom = value;
	}

	private Frequency Line1FHighfaultValue;
	public final Frequency getLine1FHighfaultValue()
	{
		return Line1FHighfaultValue;
	}
	private void setLine1FHighfaultValue(Frequency value)
	{
		Line1FHighfaultValue = value;
	}

	private TimeSpan Line1FHighfaultTime;
	public final TimeSpan getLine1FHighfaultTime()
	{
		return Line1FHighfaultTime;
	}
	private void setLine1FHighfaultTime(TimeSpan value)
	{
		Line1FHighfaultTime = value;
	}

	private Frequency Line1FLowfaultValue;
	public final Frequency getLine1FLowfaultValue()
	{
		return Line1FLowfaultValue;
	}
	private void setLine1FLowfaultValue(Frequency value)
	{
		Line1FLowfaultValue = value;
	}

	private TimeSpan Line1FLowfaultTime;
	public final TimeSpan getLine1FLowfaultTime()
	{
		return Line1FLowfaultTime;
	}
	private void setLine1FLowfaultTime(TimeSpan value)
	{
		Line1FLowfaultTime = value;
	}

	private Volt SimVoltage;
	public final Volt getSimVoltage()
	{
		return SimVoltage;
	}
	private void setSimVoltage(Volt value)
	{
		SimVoltage = value;
	}

	private Frequency SimFrequency;
	public final Frequency getSimFrequency()
	{
		return SimFrequency;
	}
	private void setSimFrequency(Frequency value)
	{
		SimFrequency = value;
	}

	private TestResult TestResult;
	public final TestResult getTestResult()
	{
		return TestResult;
	}
	private void setTestResult(TestResult value)
	{
		TestResult = value;
	}

	private short SelfTestStep;
	public final short getSelfTestStep()
	{
		return SelfTestStep;
	}
	private void setSelfTestStep(short value)
	{
		SelfTestStep = value;
	}

	private boolean StartTest;
	public final boolean getStartTest()
	{
		return StartTest;
	}
	private void setStartTest(boolean value)
	{
		StartTest = value;
	}
}
