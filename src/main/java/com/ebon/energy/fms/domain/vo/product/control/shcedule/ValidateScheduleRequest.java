package com.ebon.energy.fms.domain.vo.product.control.shcedule;

import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.product.control.invert.GenericScheduleItemViewModel;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
public class ValidateScheduleRequest {

    @Valid
    @NotNull
    private GenericScheduleItemViewModel schedule;

    private List<GenericScheduleItemViewModel> schedules = new ArrayList<>();


    public void validate() {
        schedule.validate();
        schedules.forEach(GenericScheduleItemViewModel::validate);
        if (schedules.stream().anyMatch(a -> GenericScheduleItemViewModel.doesOverlapWith(a, schedule))) {
            throw new BizException("Date range overlaps with existing date range.");
        }
    }
}
