package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.InverterOperationVO;

import java.time.LocalDateTime;


/**
 * Interface for mapping between device control entities and view models
 */
public interface DeviceControlMapper {


    InverterOperationViewModel mapToView(InverterOperationVO model, LocalDateTime ouijaBoardWindowsTime, String inverterTimeZone);

    DeviceControl mapToEntity(ProductControlViewModel model, DeviceControl existingDeviceControl);
}
