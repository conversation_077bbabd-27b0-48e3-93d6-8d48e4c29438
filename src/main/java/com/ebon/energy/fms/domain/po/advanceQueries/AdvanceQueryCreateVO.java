package com.ebon.energy.fms.domain.po.advanceQueries;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class AdvanceQueryCreateVO {

    @NotBlank(message = "查询名称不能为空")
    private String queryName;
    
    @NotBlank(message = "查询内容不能为空")
    private String query;
    
    private List<String> tagsId;
    
    private List<String> resultColumns;

    private Boolean favorite;

    private Boolean dashboard;

}