package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.*;
import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class BackupLoadPhase {
    private Double V;
    private Double I;
    private Double P;
    private Double F;
    private LoadMode Mode;

    public BackupLoadPhase(Double v, Double i, Double p, Double f, LoadMode mode) {
        this.V = v;
        this.I = i;
        this.P = p;
        this.F = f;
        this.Mode = mode;
    }

    public Double getV() {
        return V;
    }

    public void setV(Double v) {
        V = v;
    }

    public Double getI() {
        return I;
    }

    public void setI(Double i) {
        I = i;
    }

    public Double getP() {
        return P;
    }

    public void setP(Double p) {
        P = p;
    }

    public Double getF() {
        return F;
    }

    public void setF(Double f) {
        F = f;
    }

    public LoadMode getMode() {
        return Mode;
    }

    public void setMode(LoadMode mode) {
        Mode = mode;
    }
}