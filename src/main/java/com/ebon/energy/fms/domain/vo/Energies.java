package com.ebon.energy.fms.domain.vo;

import lombok.Data;

@Data
public class Energies {
    private EnergyBalancingInputs raw;
    private WattHour usageWh;
    private WattHour soldWh;
    private WattHour boughtWh;
    private WattHour generationWh;
    private WattHour batteryChargedWh;
    private WattHour batteryDischargedWh;
    private WattHour solarContributionToLoadsWh;
    private WattHour gridContributionToLoadsWh;
    private WattHour batteryContributionToLoadsWh;

    public Energies(
            EnergyBalancingInputs raw,
            WattHour usageWh,
            WattHour soldWh,
            WattHour boughtWh,
            WattHour generationWh,
            WattHour batteryChargedWh,
            WattHour batteryDischargedWh,
            WattHour solarContributionToLoadsWh,
            WattHour gridContributionToLoadsWh,
            WattHour batteryContributionToLoadsWh) {

        this.raw = raw;
        this.usageWh = usageWh;
        this.soldWh = soldWh;
        this.boughtWh = boughtWh;
        this.generationWh = generationWh;
        this.batteryChargedWh = batteryChargedWh;
        this.batteryDischargedWh = batteryDischargedWh;
        this.solarContributionToLoadsWh = solarContributionToLoadsWh;
        this.gridContributionToLoadsWh = gridContributionToLoadsWh;
        this.batteryContributionToLoadsWh = batteryContributionToLoadsWh;
    }

}