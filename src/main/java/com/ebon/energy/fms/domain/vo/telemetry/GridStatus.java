package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.SystemStatusEnum.GridStatusValue;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class GridStatus implements Cloneable {
    public GridStatus() { }

    /**
     * Please consider using the constructor with typed units instead.
     */
    public GridStatus(
            Double V,
            Double I,
            Double P,
            Double F,
            Double PowerFactor,
            GridStatusValue Status,
            Double FreqFault,
            Long FreqFaultEpoch,
            Double VFault,
            Long VFaultEpoch,
            Double DayTotalImportE,
            Double AllTimeTotalImportE,
            Double DayTotalExportE,
            Double AllTimeTotalExportE,
            Double DayMaxInputP,
            Long DayMaxInputPEpoch,
            Double DayMaxOutputP,
            Long DayMaxOutputPEpoch,
            List<GridPhase> Phases,
            BigDecimal AllTimeTotalE,
            ZonedDateTime AllTimeExportEUtc,
            ZonedDateTime AllTimeExportELocal,
            Boolean MeterAvailable,
            BigDecimal Q,
            BigDecimal S) {

        this.V = V;
        this.I = I;
        this.P = P;
        this.F = F;
        this.PowerFactor = PowerFactor;
        this.Status = Status;
        this.FreqFault = FreqFault;
        this.FreqFaultEpoch = FreqFaultEpoch;
        this.VFault = VFault;
        this.VFaultEpoch = VFaultEpoch;
        this.DayTotalImportE = DayTotalImportE;
        this.AllTimeTotalImportE = AllTimeTotalImportE;
        this.DayTotalExportE = DayTotalExportE;
        this.AllTimeTotalExportE = AllTimeTotalExportE;
        this.DayMaxInputP = DayMaxInputP;
        this.DayMaxInputPEpoch = DayMaxInputPEpoch;
        this.DayMaxOutputP = DayMaxOutputP;
        this.DayMaxOutputPEpoch = DayMaxOutputPEpoch;
        this.Phases = Phases != null ? new ArrayList<>(Phases) : null;
        this.AllTimeTotalE = AllTimeTotalE;
        this.AllTimeExportEUtc = AllTimeExportEUtc;
        this.AllTimeExportELocal = AllTimeExportELocal;
        this.MeterAvailable = MeterAvailable;
        this.Q = Q;
        this.S = S;
    }

    /*public GridStatus(
            Volt V,
            Ampere I,
            Watt P,
            Frequency F,
            Double PowerFactor,
            GridStatusValue Status,
            Double FreqFault,
            Long FreqFaultEpoch,
            Double VFault,
            Long VFaultEpoch,
            WattHour DayTotalImportE,
            WattHour AllTimeTotalImportE,
            WattHour DayTotalExportE,
            WattHour AllTimeTotalExportE,
            Watt DayMaxInputP,
            Long DayMaxInputPEpoch,
            Watt DayMaxOutputP,
            Long DayMaxOutputPEpoch,
            List<GridPhase> Phases,
            WattHour AllTimeTotalE,
            ZonedDateTime AllTimeExportEUtc,
            ZonedDateTime AllTimeExportELocal,
            Boolean MeterAvailable,
            VoltAmpsReactive Q,
            VoltAmps S) {

        this.V = V != null ? V.asDouble(SystemStatus.VOLT_PRECISION) : null;
        this.I = I != null ? I.asDouble(SystemStatus.AMPERE_PRECISION) : null;
        this.P = P != null ? P.asDouble(SystemStatus.WATT_PRECISION) : null;
        this.F = F != null ? F.asDouble(SystemStatus.FREQUENCY_PRECISION) : null;
        this.PowerFactor = PowerFactor;
        this.Status = Status;
        this.FreqFault = FreqFault;
        this.FreqFaultEpoch = FreqFaultEpoch;
        this.VFault = VFault;
        this.VFaultEpoch = VFaultEpoch;
        this.DayTotalImportE = DayTotalImportE != null ?
                DayTotalImportE.asDouble(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) : null;
        this.AllTimeTotalImportE = AllTimeTotalImportE != null ?
                AllTimeTotalImportE.asDouble(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) : null;
        this.DayTotalExportE = DayTotalExportE != null ?
                DayTotalExportE.asDouble(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) : null;
        this.AllTimeTotalExportE = AllTimeTotalExportE != null ?
                AllTimeTotalExportE.asDouble(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) : null;
        this.DayMaxInputP = DayMaxInputP != null ?
                DayMaxInputP.asDouble(Watt.UNIT, SystemStatus.KILO_WATT_PRECISION) : null;
        this.DayMaxInputPEpoch = DayMaxInputPEpoch;
        this.DayMaxOutputP = DayMaxOutputP != null ?
                DayMaxOutputP.asDouble(Watt.UNIT, SystemStatus.KILO_WATT_PRECISION) : null;
        this.DayMaxOutputPEpoch = DayMaxOutputPEpoch;
        this.Phases = Phases != null ? new ArrayList<>(Phases) : null;
        this.AllTimeTotalE = AllTimeTotalE != null ?
                AllTimeTotalE.asDecimal(WattHour.KILO, SystemStatus.KILO_WATT_PRECISION) : null;
        this.AllTimeExportEUtc = AllTimeExportEUtc;
        this.AllTimeExportELocal = AllTimeExportELocal;
        this.MeterAvailable = MeterAvailable;
        this.Q = Q != null ? Q.asDecimal(SystemStatus.VOLT_AMPS_PRECISION) : null;
        this.S = S != null ? S.asDecimal(SystemStatus.VOLT_AMPS_PRECISION) : null;
    }*/

    // Field declarations with original casing
    private Double V;
    private Double I;
    private Double P;
    private Double F;
    private Double PowerFactor;
    private GridStatusValue Status;
    private Double FreqFault;
    private Long FreqFaultEpoch;
    private Double VFault;
    private Long VFaultEpoch;
    private Double DayTotalImportE;
    private Double AllTimeTotalImportE;
    private Double DayTotalExportE;
    private Double AllTimeTotalExportE;
    private Double DayMaxInputP;
    private Long DayMaxInputPEpoch;
    private Double DayMaxOutputP;
    private Long DayMaxOutputPEpoch;
    private List<GridPhase> Phases;
    private BigDecimal AllTimeTotalE;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    private ZonedDateTime AllTimeExportEUtc;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private ZonedDateTime AllTimeExportELocal;
    private Boolean MeterAvailable;
    private BigDecimal Q;
    private BigDecimal S;

    // Getters and Setters with original casing
    public Double getV() { return V; }
    public void setV(Double V) { this.V = V; }

    public Double getI() { return I; }
    public void setI(Double I) { this.I = I; }

    public Double getP() { return P; }
    public void setP(Double P) { this.P = P; }

    public Double getF() { return F; }
    public void setF(Double F) { this.F = F; }

    public Double getPowerFactor() { return PowerFactor; }
    public void setPowerFactor(Double PowerFactor) { this.PowerFactor = PowerFactor; }

    public GridStatusValue getStatus() { return Status; }
    public void setStatus(GridStatusValue Status) { this.Status = Status; }

    public Double getFreqFault() { return FreqFault; }
    public void setFreqFault(Double FreqFault) { this.FreqFault = FreqFault; }

    public Long getFreqFaultEpoch() { return FreqFaultEpoch; }
    public void setFreqFaultEpoch(Long FreqFaultEpoch) { this.FreqFaultEpoch = FreqFaultEpoch; }

    public Double getVFault() { return VFault; }
    public void setVFault(Double VFault) { this.VFault = VFault; }

    public Long getVFaultEpoch() { return VFaultEpoch; }
    public void setVFaultEpoch(Long VFaultEpoch) { this.VFaultEpoch = VFaultEpoch; }

    public Double getDayTotalImportE() { return DayTotalImportE; }
    public void setDayTotalImportE(Double DayTotalImportE) { this.DayTotalImportE = DayTotalImportE; }

    public Double getAllTimeTotalImportE() { return AllTimeTotalImportE; }
    public void setAllTimeTotalImportE(Double AllTimeTotalImportE) { this.AllTimeTotalImportE = AllTimeTotalImportE; }

    public Double getDayTotalExportE() { return DayTotalExportE; }
    public void setDayTotalExportE(Double DayTotalExportE) { this.DayTotalExportE = DayTotalExportE; }

    public Double getAllTimeTotalExportE() { return AllTimeTotalExportE; }
    public void setAllTimeTotalExportE(Double AllTimeTotalExportE) { this.AllTimeTotalExportE = AllTimeTotalExportE; }

    public Double getDayMaxInputP() { return DayMaxInputP; }
    public void setDayMaxInputP(Double DayMaxInputP) { this.DayMaxInputP = DayMaxInputP; }

    public Long getDayMaxInputPEpoch() { return DayMaxInputPEpoch; }
    public void setDayMaxInputPEpoch(Long DayMaxInputPEpoch) { this.DayMaxInputPEpoch = DayMaxInputPEpoch; }

    public Double getDayMaxOutputP() { return DayMaxOutputP; }
    public void setDayMaxOutputP(Double DayMaxOutputP) { this.DayMaxOutputP = DayMaxOutputP; }

    public Long getDayMaxOutputPEpoch() { return DayMaxOutputPEpoch; }
    public void setDayMaxOutputPEpoch(Long DayMaxOutputPEpoch) { this.DayMaxOutputPEpoch = DayMaxOutputPEpoch; }

    public List<GridPhase> getPhases() { return Phases != null ? new ArrayList<>(Phases) : null; }
    public void setPhases(List<GridPhase> Phases) { this.Phases = Phases != null ? new ArrayList<>(Phases) : null; }

    public BigDecimal getAllTimeTotalE() { return AllTimeTotalE; }
    public void setAllTimeTotalE(BigDecimal AllTimeTotalE) { this.AllTimeTotalE = AllTimeTotalE; }

    public ZonedDateTime getAllTimeExportEUtc() { return AllTimeExportEUtc; }
    public void setAllTimeExportEUtc(ZonedDateTime AllTimeExportEUtc) { this.AllTimeExportEUtc = AllTimeExportEUtc; }

    public ZonedDateTime getAllTimeExportELocal() { return AllTimeExportELocal; }
    public void setAllTimeExportELocal(ZonedDateTime AllTimeExportELocal) { this.AllTimeExportELocal = AllTimeExportELocal; }

    public Boolean getMeterAvailable() { return MeterAvailable; }
    public void setMeterAvailable(Boolean MeterAvailable) { this.MeterAvailable = MeterAvailable; }

    public BigDecimal getQ() { return Q; }
    public void setQ(BigDecimal Q) { this.Q = Q; }

    public BigDecimal getS() { return S; }
    public void setS(BigDecimal S) { this.S = S; }

    @Override
    public GridStatus clone() {
        try {
            GridStatus clone = (GridStatus) super.clone();
            clone.Phases = getPhases();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError(); // Can't happen
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GridStatus that = (GridStatus) o;
        return Objects.equals(V, that.V) &&
                Objects.equals(I, that.I) &&
                Objects.equals(P, that.P) &&
                Objects.equals(F, that.F) &&
                Objects.equals(PowerFactor, that.PowerFactor) &&
                Status == that.Status &&
                Objects.equals(FreqFault, that.FreqFault) &&
                Objects.equals(FreqFaultEpoch, that.FreqFaultEpoch) &&
                Objects.equals(VFault, that.VFault) &&
                Objects.equals(VFaultEpoch, that.VFaultEpoch) &&
                Objects.equals(DayTotalImportE, that.DayTotalImportE) &&
                Objects.equals(AllTimeTotalImportE, that.AllTimeTotalImportE) &&
                Objects.equals(DayTotalExportE, that.DayTotalExportE) &&
                Objects.equals(AllTimeTotalExportE, that.AllTimeTotalExportE) &&
                Objects.equals(DayMaxInputP, that.DayMaxInputP) &&
                Objects.equals(DayMaxInputPEpoch, that.DayMaxInputPEpoch) &&
                Objects.equals(DayMaxOutputP, that.DayMaxOutputP) &&
                Objects.equals(DayMaxOutputPEpoch, that.DayMaxOutputPEpoch) &&
                Objects.equals(Phases, that.Phases) &&
                Objects.equals(AllTimeTotalE, that.AllTimeTotalE) &&
                Objects.equals(AllTimeExportEUtc, that.AllTimeExportEUtc) &&
                Objects.equals(AllTimeExportELocal, that.AllTimeExportELocal) &&
                Objects.equals(MeterAvailable, that.MeterAvailable) &&
                Objects.equals(Q, that.Q) &&
                Objects.equals(S, that.S);
    }

    @Override
    public int hashCode() {
        return Objects.hash(V, I, P, F, PowerFactor, Status, FreqFault, FreqFaultEpoch,
                VFault, VFaultEpoch, DayTotalImportE, AllTimeTotalImportE,
                DayTotalExportE, AllTimeTotalExportE, DayMaxInputP, DayMaxInputPEpoch,
                DayMaxOutputP, DayMaxOutputPEpoch, Phases, AllTimeTotalE,
                AllTimeExportEUtc, AllTimeExportELocal, MeterAvailable, Q, S);
    }

    @Override
    public String toString() {
        return "GridStatus{" +
                "V=" + V +
                ", I=" + I +
                ", P=" + P +
                ", F=" + F +
                ", PowerFactor=" + PowerFactor +
                ", Status=" + Status +
                ", FreqFault=" + FreqFault +
                ", FreqFaultEpoch=" + FreqFaultEpoch +
                ", VFault=" + VFault +
                ", VFaultEpoch=" + VFaultEpoch +
                ", DayTotalImportE=" + DayTotalImportE +
                ", AllTimeTotalImportE=" + AllTimeTotalImportE +
                ", DayTotalExportE=" + DayTotalExportE +
                ", AllTimeTotalExportE=" + AllTimeTotalExportE +
                ", DayMaxInputP=" + DayMaxInputP +
                ", DayMaxInputPEpoch=" + DayMaxInputPEpoch +
                ", DayMaxOutputP=" + DayMaxOutputP +
                ", DayMaxOutputPEpoch=" + DayMaxOutputPEpoch +
                ", Phases=" + Phases +
                ", AllTimeTotalE=" + AllTimeTotalE +
                ", AllTimeExportEUtc=" + AllTimeExportEUtc +
                ", AllTimeExportELocal=" + AllTimeExportELocal +
                ", MeterAvailable=" + MeterAvailable +
                ", Q=" + Q +
                ", S=" + S +
                '}';
    }
}