package com.ebon.energy.fms.domain.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class SiteInverterInfoDO {

    private String serialNumber;
    private String model;
    private String currentRole;
    private String status;
    private String rossVersion;
    private String firmwareVersion;
    private String meter;
    private String selectedRole;
    private boolean isInWarranty;
    private boolean offComms;
    private boolean isRegistered;
    private boolean isPending;
    private boolean isOnline;
    private String publicSiteId;
    private Timestamp latestTelemetryUtc;
    private String computerName;
}
