package com.ebon.energy.fms.domain.vo.telemetry;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class InverterRawBandsTelemetry {
    public InverterRawBandsTelemetry() {
    }

    public InverterRawBandsTelemetry(TelemetryBandFrame<byte[]> bandA, TelemetryBandFrame<byte[]> bandB, TelemetryBandFrame<byte[]> bandC, TelemetryBandFrame<byte[]> bandD, TelemetryBandFrame<byte[]> bandE, TelemetryBandFrame<byte[]> bandF, TelemetryBandFrame<byte[]> bandG, TelemetryBandFrame<byte[]> bandH, TelemetryBandFrame<byte[]> bandI, TelemetryBandFrame<byte[]> bandJ) {
        setBandA(bandA);
        setBandB(bandB);
        setBandC(bandC);
        setBandD(bandD);
        setBandE(bandE);
        setBandF(bandF);
        setBandG(bandG);
        setBandH(bandH);
        setBandI(bandI);
        setBandJ(bandJ);
    }

    private TelemetryBandFrame<byte[]> BandA;

    public final TelemetryBandFrame<byte[]> getBandA() {
        return BandA;
    }

    private void setBandA(TelemetryBandFrame<byte[]> value) {
        BandA = value;
    }

    private TelemetryBandFrame<byte[]> BandB;

    public final TelemetryBandFrame<byte[]> getBandB() {
        return BandB;
    }

    private void setBandB(TelemetryBandFrame<byte[]> value) {
        BandB = value;
    }

    private TelemetryBandFrame<byte[]> BandC;

    public final TelemetryBandFrame<byte[]> getBandC() {
        return BandC;
    }

    private void setBandC(TelemetryBandFrame<byte[]> value) {
        BandC = value;
    }

    private TelemetryBandFrame<byte[]> BandD;

    public final TelemetryBandFrame<byte[]> getBandD() {
        return BandD;
    }

    private void setBandD(TelemetryBandFrame<byte[]> value) {
        BandD = value;
    }

    private TelemetryBandFrame<byte[]> BandE;

    public final TelemetryBandFrame<byte[]> getBandE() {
        return BandE;
    }

    private void setBandE(TelemetryBandFrame<byte[]> value) {
        BandE = value;
    }

    private TelemetryBandFrame<byte[]> BandF;

    public final TelemetryBandFrame<byte[]> getBandF() {
        return BandF;
    }

    private void setBandF(TelemetryBandFrame<byte[]> value) {
        BandF = value;
    }

    private TelemetryBandFrame<byte[]> BandG;

    public final TelemetryBandFrame<byte[]> getBandG() {
        return BandG;
    }

    private void setBandG(TelemetryBandFrame<byte[]> value) {
        BandG = value;
    }

    private TelemetryBandFrame<byte[]> BandH;

    public final TelemetryBandFrame<byte[]> getBandH() {
        return BandH;
    }

    private void setBandH(TelemetryBandFrame<byte[]> value) {
        BandH = value;
    }

    private TelemetryBandFrame<byte[]> BandI;

    public final TelemetryBandFrame<byte[]> getBandI() {
        return BandI;
    }

    private void setBandI(TelemetryBandFrame<byte[]> value) {
        BandI = value;
    }

    private TelemetryBandFrame<byte[]> BandJ;

    public final TelemetryBandFrame<byte[]> getBandJ() {
        return BandJ;
    }

    private void setBandJ(TelemetryBandFrame<byte[]> value) {
        BandJ = value;
    }
}

