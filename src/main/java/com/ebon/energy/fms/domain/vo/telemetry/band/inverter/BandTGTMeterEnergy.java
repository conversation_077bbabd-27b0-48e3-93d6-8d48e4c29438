package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;











public class BandTGTMeterEnergy extends DataBackedBand
{
	public BandTGTMeterEnergy()
	{
		super(BandForge.<BandTGTMeterEnergy>getMetadataFor(BandTGTMeterEnergy.class));
	}



	public BandTGTMeterEnergy(byte[] bytes)
	{
		super(bytes, BandForge.<BandTGTMeterEnergy>getMetadataFor(BandTGTMeterEnergy.class));
	}

	public BandTGTMeterEnergy(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandTGTMeterEnergy>getMetadataFor(BandTGTMeterEnergy.class));
	}


	
	public final WattHour getActiveEnergySellL1Tp()
	{
		return GetU32(0, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyL1Tp()
	{
		return GetU32(4, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergySellL2Tp()
	{
		return GetU32(8, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyL2Tp()
	{
		return GetU32(12, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergySellL3Tp()
	{
		return GetU32(16, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyL3Tp()
	{
		return GetU32(20, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergySellTotalTp()
	{
		return GetU32(24, WattHour.Hecto);
	}

	
	public final WattHour getActiveEnergyBuyTotalTp()
	{
		return GetU32(28, WattHour.Hecto);
	}
}
