package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.PylonInfoFlagState;
import com.ebon.energy.fms.common.enums.PylonUserDefined;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;

public class BandPylon42_3000 extends DataBackedBand
{
	public BandPylon42_3000()
	{
		super(BandForge.<BandPylon42_3000>getMetadataFor(BandPylon42_3000.class));
	}



	public BandPylon42_3000(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon42_3000>getMetadataFor(BandPylon42_3000.class));
	}

	public BandPylon42_3000(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon42_3000>getMetadataFor(BandPylon42_3000.class));
	}


	
	public final Object getStatus()
	{
		return PylonInfoFlagState.parse(GetU8(0));
	}

	


	public final byte getPackIndexUnadjusted()
	{
		return GetU8(1);
	}

	


	public final byte getCellCount()
	{
		return GetU8(2);
	}

	
	public final Volt getCellVoltage1()
	{
		return GetU16(3, Volt.Milli);
	}

	
	public final Volt getCellVoltage2()
	{
		return GetU16(5, Volt.Milli);
	}

	
	public final Volt getCellVoltage3()
	{
		return GetU16(7, Volt.Milli);
	}

	
	public final Volt getCellVoltage4()
	{
		return GetU16(9, Volt.Milli);
	}

	
	public final Volt getCellVoltage5()
	{
		return GetU16(11, Volt.Milli);
	}

	
	public final Volt getCellVoltage6()
	{
		return GetU16(13, Volt.Milli);
	}

	
	public final Volt getCellVoltage7()
	{
		return GetU16(15, Volt.Milli);
	}

	
	public final Volt getCellVoltage8()
	{
		return GetU16(17, Volt.Milli);
	}

	
	public final Volt getCellVoltage9()
	{
		return GetU16(19, Volt.Milli);
	}

	
	public final Volt getCellVoltage10()
	{
		return GetU16(21, Volt.Milli);
	}

	
	public final Volt getCellVoltage11()
	{
		return GetU16(23, Volt.Milli);
	}

	
	public final Volt getCellVoltage12()
	{
		return GetU16(25, Volt.Milli);
	}

	
	public final Volt getCellVoltage13()
	{
		return GetU16(27, Volt.Milli);
	}

	public final Volt getCellVoltage14()
	{
		return GetU16(29, Volt.Milli);
	}

	public final Volt getCellVoltage15()
	{
		return GetU16(31, Volt.Milli);
	}



	public final byte getTempCount()
	{
		return GetU8(33);
	}

	public final Kelvin getTempReading1()
	{
		return GetU16(34, Kelvin.Deci);
	}

	public final Kelvin getTempReading2()
	{
		return GetU16(36, Kelvin.Deci);
	}

	
	public final Kelvin getTempReading3()
	{
		return GetU16(38, Kelvin.Deci);
	}

	
	public final Kelvin getTempReading4()
	{
		return GetU16(40, Kelvin.Deci);
	}

	
	public final Kelvin getTempReading5()
	{
		return GetU16(42, Kelvin.Deci);
	}

	
	public final Ampere getPackCurrent()
	{
		return GetS16(44, Ampere.Deci);
	}

	
	public final Volt getPackVoltage()
	{
		return GetU16(46, Volt.Milli);
	}

	
	public final Capacity getPackRemainsOld()
	{
		return GetU16(48, Capacity.Milli);
	}

	
	public final PylonUserDefined getUserDefined()
	{
		return PylonUserDefined.fromValue(GetU8(50));
	}

	
	public final Capacity getPackTotalOld()
	{
		return GetU16(51, Capacity.Milli);
	}

	


	public final int getCycle() { return GetU16(53); }

	
	public final Capacity getPackRemains()
	{
		return GetU24(55, Capacity.Milli);
	}

	
	public final Capacity getPackTotal()
	{
		return GetU24(58, Capacity.Milli);
	}


	public int getPackIndex()
	{
		return getPackIndexUnadjusted() - 2;
	}



	/*public Volt[] getCellVoltages()
	{
		return new Volt[] {getCellVoltage1(), getCellVoltage2(), getCellVoltage3(), getCellVoltage4(), getCellVoltage5(), getCellVoltage6(), getCellVoltage7(), getCellVoltage8(), getCellVoltage9(), getCellVoltage10(), getCellVoltage11(), getCellVoltage12(), getCellVoltage13(), getCellVoltage14(), getCellVoltage15()};
	}


	public Kelvin[] getTempReadings()
	{
		return new Kelvin[] {getTempReading1(), getTempReading2(), getTempReading3(), getTempReading4(), getTempReading5()};
	}*/

	public Watt getPackPower()
	{
		return new Watt(getPackCurrent().getValue().multiply(getPackVoltage().getValue()));
	}
}
