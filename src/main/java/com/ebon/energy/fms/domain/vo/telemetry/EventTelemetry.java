package com.ebon.energy.fms.domain.vo.telemetry;

import com.ebon.energy.fms.common.enums.EventCode;

import java.time.LocalDateTime;
import java.util.Arrays;

public class EventTelemetry {

    public Integer Code;
    public String RequestId;
    public String Message;
    public LocalDateTime WhenUtc;

    /**
     * Default constructor for JSON deserialization.
     */
    public EventTelemetry() {
    }

    public EventTelemetry(String Message, LocalDateTime WhenUtc) {
        this(null, null, Message, WhenUtc);
    }

    public EventTelemetry(EventCode Code, String RequestId, String Message, LocalDateTime WhenUtc) {
        this.Code = (Code != null) ? Code.getValue() : null;
        this.RequestId = RequestId;
        this.Message = Message;
        this.WhenUtc = WhenUtc;
    }

    public Integer getCode() {
        return Code;
    }

    public void setCode(Integer Code) {
        this.Code = Code;
    }

    public String getRequestId() {
        return RequestId;
    }

    public void setRequestId(String RequestId) {
        this.RequestId = RequestId;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String Message) {
        this.Message = Message;
    }

    public LocalDateTime getWhenUtc() {
        return WhenUtc;
    }

    public void setWhenUtc(LocalDateTime WhenUtc) {
        this.WhenUtc = WhenUtc;
    }

    public EventCode GetTypedCode() {
        if (Code == null) {
            return null;
        }
        return Arrays.stream(EventCode.values())
                .filter(e -> e.getValue() == Code)
                .findFirst()
                .orElse(null);
    }

}