package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Data
public class SevenDayHistoryVO {
    private String id;
    private BigDecimal pastSevenDaysWatts;
    private BigDecimal percentageComparisonPastSevenDays;
    private List<DayPowerPairVO> data;
    private boolean applyChartSafeDataFeature;

    // 构造函数
    public SevenDayHistoryVO(
            String id,
            BigDecimal pastSevenDaysWatts,
            BigDecimal percentageComparisonPastSevenDays,
            List<DayPowerPairVO> data,
            boolean applyChartSafeDataFeature) {
        this.id = Objects.requireNonNull(id, "id must not be null");
        this.pastSevenDaysWatts = pastSevenDaysWatts;
        this.percentageComparisonPastSevenDays = percentageComparisonPastSevenDays;
        this.data = Objects.requireNonNull(data, "data must not be null");
        this.applyChartSafeDataFeature = applyChartSafeDataFeature;
    }

}