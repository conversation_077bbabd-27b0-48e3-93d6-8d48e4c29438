package com.ebon.energy.fms.domain.vo.product.control;

import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.domain.vo.BatterySettingsDto;
import com.ebon.energy.fms.domain.vo.GetInverterModeSettingsDto;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.NotImplementedException;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class SGSettingsReader implements ICommonSettingsReader{


   private DeviceInfoAndSettings deviceSettingsDto;


    @Override
    public GetInverterModeSettingsDto getDesiredPowerModeSchedulesForPortal()
        {
            throw new NotImplementedException("Handling Smart Inverter inverter modes were mode implemented the in Nexus");
        }

    @Override
    public ACCoupledSettingsDto getACCoupledSettings(UniversalSettingSource source) {
        return new ACCoupledSettingsDto(false);
    }

    @Override
    public List<UniversalSettingId> getSupportedUniveralSettings(Integer version) {
            List<UniversalSettingId> supportedUniversalSettings = new ArrayList<>();

            supportedUniversalSettings.add(UniversalSettingId.SHADOW_SCAN);
            supportedUniversalSettings.add(UniversalSettingId.CT_FLIP);
            supportedUniversalSettings.add(UniversalSettingId.AS4777_2_GENERATION_AND_EXPORT_LIMITS);
            supportedUniversalSettings.add(UniversalSettingId.AS4777_2_GENERATION_LIMITS);
            supportedUniversalSettings.add(UniversalSettingId.AS4777_2_EXPORT_LIMITS);

            return supportedUniversalSettings;
    }

    @Override
    public BatterySettingsDto getBatterySettings(UniversalSettingSource source) {
        return null;
    }
}
