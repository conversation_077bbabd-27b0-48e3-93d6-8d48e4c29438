package com.ebon.energy.fms.domain.vo;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class DailyEnergyTotalsVO {
    private final WattHour solarDayTotalE;
    private final WattHour solarRedbackPvDayTotalE;
    private final WattHour solarThirdPartyDayTotalE;
    private final WattHour loadDayTotalAcE;
    private final WattHour loadDayTotalBackupE;
    private final WattHour gridDayTotalImportE;
    private final WattHour gridDayTotalExportE;
    private final WattHour batteryDayTotalImportE;
    private final WattHour batteryDayTotalExportE;

    public DailyEnergyTotalsVO(WattHour solarDayTotalE, WattHour solarRedbackPvDayTotalE,
                             WattHour solarThirdPartyDayTotalE, WattHour loadDayTotalAcE,
                             WattHour loadDayTotalBackupE, WattHour gridDayTotalImportE,
                             WattHour gridDayTotalExportE, WattHour batteryDayTotalImportE,
                             WattHour batteryDayTotalExportE) {
        this.solarDayTotalE = solarDayTotalE;
        this.solarRedbackPvDayTotalE = solarRedbackPvDayTotalE;
        this.solarThirdPartyDayTotalE = solarThirdPartyDayTotalE;
        this.loadDayTotalAcE = loadDayTotalAcE;
        this.loadDayTotalBackupE = loadDayTotalBackupE;
        this.gridDayTotalImportE = gridDayTotalImportE;
        this.gridDayTotalExportE = gridDayTotalExportE;
        this.batteryDayTotalImportE = batteryDayTotalImportE;
        this.batteryDayTotalExportE = batteryDayTotalExportE;
    }

    public WattHour getSolarDayTotalE() {
        return solarDayTotalE;
    }

    public WattHour getSolarRedbackPvDayTotalE() {
        return solarRedbackPvDayTotalE;
    }

    public WattHour getSolarThirdPartyDayTotalE() {
        return solarThirdPartyDayTotalE;
    }

    public WattHour getLoadDayTotalAcE() {
        return loadDayTotalAcE;
    }

    public WattHour getLoadDayTotalBackupE() {
        return loadDayTotalBackupE;
    }

    public WattHour getGridDayTotalImportE() {
        return gridDayTotalImportE;
    }

    public WattHour getGridDayTotalExportE() {
        return gridDayTotalExportE;
    }

    public WattHour getBatteryDayTotalImportE() {
        return batteryDayTotalImportE;
    }

    public WattHour getBatteryDayTotalExportE() {
        return batteryDayTotalExportE;
    }
}