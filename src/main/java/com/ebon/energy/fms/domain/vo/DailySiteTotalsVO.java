package com.ebon.energy.fms.domain.vo;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DailySiteTotalsVO {
    private LocalDate date;
    private EnergyVal dailyUsage;
    private EnergyVal dailySold;
    private EnergyVal dailyBought;
    private EnergyVal dailyGeneration;
    private EnergyVal dailyBatteryCharged;
    private EnergyVal dailyBatteryDischarged;

    // 字符串日期构造函数
    public DailySiteTotalsVO(
            String date,
            EnergyVal dailyUsage,
            EnergyVal dailySold,
            EnergyVal dailyBought,
            EnergyVal dailyGeneration,
            EnergyVal dailyBatteryCharged,
            EnergyVal dailyBatteryDischarged
    ) {
        this(
                LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE),
                dailyUsage,
                dailySold,
                dailyBought,
                dailyGeneration,
                dailyBatteryCharged,
                dailyBatteryDischarged
        );
    }

    // LocalDate构造函数
    public DailySiteTotalsVO(
            LocalDate date,
            EnergyVal dailyUsage,
            EnergyVal dailySold,
            EnergyVal dailyBought,
            EnergyVal dailyGeneration,
            EnergyVal dailyBatteryCharged,
            EnergyVal dailyBatteryDischarged
    ) {
        this.date = date;
        this.dailyUsage = dailyUsage;
        this.dailySold = dailySold;
        this.dailyBought = dailyBought;
        this.dailyGeneration = dailyGeneration;
        this.dailyBatteryCharged = dailyBatteryCharged;
        this.dailyBatteryDischarged = dailyBatteryDischarged;
    }

    // Getter方法
    public LocalDate getDate() {
        return date;
    }

    public EnergyVal getDailyUsage() {
        return dailyUsage;
    }

    public EnergyVal getDailySold() {
        return dailySold;
    }

    public EnergyVal getDailyBought() {
        return dailyBought;
    }

    public EnergyVal getDailyGeneration() {
        return dailyGeneration;
    }

    public EnergyVal getDailyBatteryCharged() {
        return dailyBatteryCharged;
    }

    public EnergyVal getDailyBatteryDischarged() {
        return dailyBatteryDischarged;
    }

    // 创建空数据实例
    public static DailySiteTotalsVO empty(LocalDate date) {
        return new DailySiteTotalsVO(
                date,
                new EnergyVal(0, true),
                new EnergyVal(0, true),
                new EnergyVal(0, true),
                new EnergyVal(0, true),
                null,
                null
        );
    }
}