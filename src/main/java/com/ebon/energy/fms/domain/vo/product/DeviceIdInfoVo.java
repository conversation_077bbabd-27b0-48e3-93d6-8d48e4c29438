package com.ebon.energy.fms.domain.vo.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Value object for device ID information
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceIdInfoVo {
    
    /**
     * Device ID (primary key)
     */
    private String id;
    
    /**
     * Device identifier
     */
    private String deviceId;
    
    /**
     * Cloud platform name
     */
    private String cloudPlatformName;
}
