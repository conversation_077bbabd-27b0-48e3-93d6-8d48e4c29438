package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("Device")
public class DeviceDO {

    @TableField(value = "Id")
    private String id;

    @TableField(value = "EdgeId")
    private String edgeId;

    @TableField(value = "DeviceId")
    private String deviceId;

    @TableField(value = "SerialNumber")
    private String serialNumber;

    @TableField(value = "ModifiedDateUtc")
    private Timestamp modifiedDateUtc;

    @TableField(value = "DeviceTwinId")
    private String deviceTwinId;

    @TableField(value = "StartTime")
    private Timestamp startTime;

    @TableField(value = "EndTime")
    private Timestamp endTime;

    @TableField(value = "ApplicationName")
    private String applicationName;

    @TableField(value = "ReferenceTableProcessedTimestampUtc")
    private Timestamp referenceTableProcessedTimestampUtc;

    @TableField(value = "PendingDeviceEvents")
    private Integer pendingDeviceEvents;

    @TableField(value = "CloudPlatformName")
    private String cloudPlatformName;

}