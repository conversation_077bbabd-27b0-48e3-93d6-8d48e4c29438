package com.ebon.energy.fms.domain.vo.product.control;

import java.util.List;
import java.util.Map;

import com.ebon.energy.fms.domain.entity.ModelInfoDO;
import com.ebon.energy.fms.domain.entity.ProductWithInstallationDO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * ViewModel for product control configuration
 */
@Data
public class ProductControlViewModel {

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("InverterOperation")
    private InverterOperationViewModel inverterOperation;

    @JsonProperty("ProductDefaults")
    private InstallationSpecification productDefaults;
}
