package com.ebon.energy.fms.domain.vo.product.control;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InternalDeviceInfoAndSettings extends ExplicitSettingsModel {

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("ModelName")
    private String modelName;

    @JsonProperty("FirmwareVersion")
    private String firmwareVersion;

    @JsonProperty("SoftwareVersion")
    private String softwareVersion;

    @JsonProperty("HardwareConfig")
    private String hardwareConfig;

    @JsonProperty("Desired")
    private String desired;

    @JsonProperty("Reported")
    private String reported;

    @JsonProperty("Intent")
    private String intent;
}
