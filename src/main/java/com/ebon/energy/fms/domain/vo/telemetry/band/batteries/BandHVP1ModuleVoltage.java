package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;









public class BandHVP1ModuleVoltage extends DataBackedBand
{
	public BandHVP1ModuleVoltage()
	{
		super(BandForge.<BandHVP1ModuleVoltage>getMetadataFor(BandHVP1ModuleVoltage.class));
	}



	public BandHVP1ModuleVoltage(byte[] bytes)
	{
		super(bytes, BandForge.<BandHVP1ModuleVoltage>getMetadataFor(BandHVP1ModuleVoltage.class));
	}

	public BandHVP1ModuleVoltage(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandHVP1ModuleVoltage>getMetadataFor(BandHVP1ModuleVoltage.class));
	}



	public final Volt getVoltageOfModuleNumber00()
	{
		return GetU16(0, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber01()
	{
		return GetU16(2, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber02()
	{
		return GetU16(4, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber03()
	{
		return GetU16(6, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber04()
	{
		return GetU16(8, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber05()
	{
		return GetU16(10, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber06()
	{
		return GetU16(12, Volt.Centi);
	}


	public final Volt getVoltageOfModuleNumber07()
	{
		return GetU16(14, Volt.Centi);
	}
}
