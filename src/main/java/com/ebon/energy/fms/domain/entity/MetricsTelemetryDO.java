package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("MetricsTelemetry")
public class MetricsTelemetryDO {

    @TableId(value = "Id")
    private String id;

    @TableField("SerialNumber")
    private String serialNumber;

    @TableField("LastTelemetryUtc")
    private Timestamp lastTelemetryUtc;

    @TableField(value = "MinutesSinceLastTelemetry")
    private Integer minutesSinceLastTelemetry;

    @TableField("IncidentsForRb")
    private Integer incidentsForRb;

    @TableField("IncidentsForInstaller")
    private Integer incidentsForInstaller;

    @TableField("IncidentsForHomeUser")
    private Integer incidentsForHomeUser;

    @TableField("LastModifiedById")
    private String lastModifiedById;

    @TableField("LastModifiedOnUtc")
    private Timestamp lastModifiedOnUtc;

    @TableField("CreatedById")
    private String createdById;

    @TableField("CreatedOnUtc")
    private Timestamp createdOnUtc;

    @TableField("StartTime")
    private Timestamp startTime;

    @TableField("EndTime")
    private Timestamp endTime;

    @TableField("UnhealthyAtUtcRb")
    private Timestamp unhealthyAtUtcRb;

    @TableField("UnhealthyAtUtcInstaller")
    private Timestamp unhealthyAtUtcInstaller;

    @TableField("UnhealthyAtUtcHomeUser")
    private Timestamp unhealthyAtUtcHomeUser;

}