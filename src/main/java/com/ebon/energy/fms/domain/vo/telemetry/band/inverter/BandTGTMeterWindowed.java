package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;

public class BandTGTMeterWindowed extends DataBackedBand {
    public BandTGTMeterWindowed() {
        super(BandForge.<BandTGTMeterWindowed>getMetadataFor(BandTGTMeterWindowed.class));
    }


    public BandTGTMeterWindowed(byte[] bytes) {
        super(bytes, BandForge.<BandTGTMeterWindowed>getMetadataFor(BandTGTMeterWindowed.class));
    }

    public BandTGTMeterWindowed(String encodedBytes) {
        super(encodedBytes, BandForge.<BandTGTMeterWindowed>getMetadataFor(BandTGTMeterWindowed.class));
    }


    public final Volt getVoltageL1MaxTp() {
        return GetU16(0, Volt.Deci);
    }


    public final Volt getVoltageL1MinTp() {
        return GetU16(2, Volt.Deci);
    }


    public final Volt getVoltageL1AvgTp() {
        return GetU16(4, Volt.Deci);
    }


    public final Ampere getCurrentL1MaxTp() {
        return GetS32(6, Ampere.Centi);
    }


    public final Ampere getCurrentL1MinTp() {
        return GetS32(10, Ampere.Centi);
    }


    public final Ampere getCurrentL1AvgTp() {
        return GetS32(14, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL1MaxTp() {
        return new BigDecimal(GetS16(18)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL1MinTp() {
        return new BigDecimal(GetS16(20)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL1AvgTp() {
        return new BigDecimal(GetS16(22)).multiply(Percentage.Tenth);
    }


    public final Watt getActivePowerL1MaxTp() {
        return GetS32(24, Watt.Deci);
    }


    public final Watt getActivePowerL1MinTp() {
        return GetS32(28, Watt.Deci);
    }


    public final Watt getActivePowerL1AvgTp() {
        return GetS32(32, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL1MaxTp() {
        return GetU32(36, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL1MinTp() {
        return GetU32(40, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL1AvgTp() {
        return GetU32(44, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL1MaxTp() {
        return GetS32(48, VoltAmpsReactive.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL1MinTp() {
        return GetS32(52, VoltAmpsReactive.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL1AvgTp() {
        return GetS32(56, VoltAmpsReactive.Deci);
    }


    public final Volt getVoltageL2MaxTp() {
        return GetU16(60, Volt.Deci);
    }


    public final Volt getVoltageL2MinTp() {
        return GetU16(62, Volt.Deci);
    }


    public final Volt getVoltageL2AvgTp() {
        return GetU16(64, Volt.Deci);
    }


    public final Ampere getCurrentL2MaxTp() {
        return GetS32(66, Ampere.Centi);
    }


    public final Ampere getCurrentL2MinTp() {
        return GetS32(70, Ampere.Centi);
    }


    public final Ampere getCurrentL2AvgTp() {
        return GetS32(74, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL2MaxTp() {
        return new BigDecimal(GetS16(78)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2MinTp() {
        return new BigDecimal(GetS16(80)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL2AvgTp() {
        return new BigDecimal(GetS16(82)).multiply(Percentage.Tenth);
    }


    public final Watt getActivePowerL2MaxTp() {
        return GetS32(84, Watt.Deci);
    }


    public final Watt getActivePowerL2MinTp() {
        return GetS32(88, Watt.Deci);
    }


    public final Watt getActivePowerL2AvgTp() {
        return GetS32(92, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL2MaxTp() {
        return GetU32(96, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2MinTp() {
        return GetU32(100, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL2AvgTp() {
        return GetU32(104, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL2MaxTp() {
        return GetS32(108, VoltAmpsReactive.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL2MinTp() {
        return GetS32(112, VoltAmpsReactive.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL2AvgTp() {
        return GetS32(116, VoltAmpsReactive.Deci);
    }


    public final Volt getVoltageL3MaxTp() {
        return GetU16(120, Volt.Deci);
    }


    public final Volt getVoltageL3MinTp() {
        return GetU16(122, Volt.Deci);
    }


    public final Volt getVoltageL3AvgTp() {
        return GetU16(124, Volt.Deci);
    }


    public final Ampere getCurrentL3MaxTp() {
        return GetS32(126, Ampere.Centi);
    }


    public final Ampere getCurrentL3MinTp() {
        return GetS32(130, Ampere.Centi);
    }


    public final Ampere getCurrentL3AvgTp() {
        return GetS32(134, Ampere.Centi);
    }


    public final BigDecimal getPowerFactorL3MaxTp() {
        return new BigDecimal(GetS16(138)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL3MinTp() {
        return new BigDecimal(GetS16(140)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getPowerFactorL3AvgTp() {
        return new BigDecimal(GetS16(142)).multiply(Percentage.Tenth);
    }


    public final Watt getActivePowerL3MaxTp() {
        return GetS32(144, Watt.Deci);
    }


    public final Watt getActivePowerL3MinTp() {
        return GetS32(148, Watt.Deci);
    }


    public final Watt getActivePowerL3AvgTp() {
        return GetS32(152, Watt.Deci);
    }


    public final VoltAmps getApparentPowerL3MaxTp() {
        return GetU32(156, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL3MinTp() {
        return GetU32(160, VoltAmps.Deci);
    }


    public final VoltAmps getApparentPowerL3AvgTp() {
        return GetU32(164, VoltAmps.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL3MaxTp() {
        return GetS32(168, VoltAmpsReactive.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL3MinTp() {
        return GetS32(172, VoltAmpsReactive.Deci);
    }


    public final VoltAmpsReactive getReactivePowerL3AvgTp() {
        return GetS32(176, VoltAmpsReactive.Deci);
    }


    public final Frequency getFrequencyL1MaxTp() {
        return GetU16(180, Frequency.Centi);
    }


    public final Frequency getFrequencyL1MinTp() {
        return GetU16(182, Frequency.Centi);
    }


    public final Frequency getFrequencyL1AvgTp() {
        return GetU16(184, Frequency.Centi);
    }


    public final BigDecimal getTotalHarmonicDistortionVoltageL1Tp() {
        return new BigDecimal(GetU16(186)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getTotalHarmonicDistortionVoltageL2Tp() {
        return new BigDecimal(GetU16(188)).multiply(Percentage.Tenth);
    }


    public final BigDecimal getTotalHarmonicDistortionVoltageL3Tp() {
        return new BigDecimal(GetU16(190)).multiply(Percentage.Tenth);
    }
}
