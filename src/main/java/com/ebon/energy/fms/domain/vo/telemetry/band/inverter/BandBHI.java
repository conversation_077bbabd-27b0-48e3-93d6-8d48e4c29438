package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.common.enums.BMSProtocolCode;
import com.ebon.energy.fms.common.enums.FloatBattType;
import com.ebon.energy.fms.common.enums.InverterETPowerMode;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.Percentage;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


import java.math.*;


public class BandBHI extends DataBackedBand
{
	public BandBHI()
	{
		super(BandForge.<BandBHI>getMetadataFor(BandBHI.class));
	}



	public BandBHI(byte[] bytes)
	{
		super(bytes, BandForge.<BandBHI>getMetadataFor(BandBHI.class));
	}

	public BandBHI(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandBHI>getMetadataFor(BandBHI.class));
	}


	
	public final boolean getStopSocProtect()
	{
		return GetBool((int) GetU16(0), 1, 0, false);
	}

	
	public final Volt getBattFloatVolt()
	{
		return GetU16(2, Volt.Deci);
	}

	
	public final Ampere getBattFloatCurr()
	{
		return GetU16(4, Ampere.Deci);
	}

	
	public final TimeSpan getBattToFloatTime()
	{
		return GetU16(6, TimeSpan.fromMinutes(1));
	}

	
	public final Object getBattTypeIndex()
	{
		return FloatBattType.parse(GetU16(8));
	}

	
	public final boolean getDCVoltOutputKeepBattVoltOn()
	{
		return GetBool((int) GetU16(12), 1, 0, false);
	}

	
	public final Volt getBattAvgChgVolt()
	{
		return GetU16(14, Volt.Deci);
	}

	
	public final TimeSpan getBatAvgChgHours()
	{
		return GetU16(16, TimeSpan.fromHours(1));
	}

	
	public final boolean getFeedPowerEnable()
	{
		return GetBool((int) GetU16(18), 1, 0, false);
	}

	
	public final Watt getFeedPowerPara()
	{
		return GetS16(20, Watt.Unit);
	}

	
	public final Object getEMSPowerMode()
	{
		return InverterETPowerMode.parse((byte) GetU16(22));
	}

	
	public final Watt getEMSPowerSet()
	{
		return GetU16(24, Watt.Unit);
	}

	
	public final BigDecimal getBatBMSCurrLmtCoff()
	{
		return new BigDecimal(GetU16(26)).multiply(Percentage._1);
	}

	
	public final Object getBMSProtocolCode()
	{
		return BMSProtocolCode.parse(GetU16(28));
	}

	


	public final int getStartTime1() { return GetU16(30); }

	


	public final int getEndTime1() { return GetU16(32); }

	
	public final BigDecimal getBatPowerPercent1()
	{
		return new BigDecimal(GetS16(34)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek1() { return GetU16(36); }

	


	public final int getStartTime2() { return GetU16(38); }

	


	public final int getEndTime2() { return GetU16(40); }

	
	public final BigDecimal getBatPowerPercent2()
	{
		return new BigDecimal(GetS16(42)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek2() { return GetU16(44); }

	


	public final int getStartTime3() { return GetU16(46); }

	


	public final int getEndTime3() { return GetU16(48); }

	
	public final BigDecimal getBatPowerPercent3()
	{
		return new BigDecimal(GetS16(50)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek3() { return GetU16(52); }

	


	public final int getStartTime4() { return GetU16(54); }

	


	public final int getEndTime4() { return GetU16(56); }

	
	public final BigDecimal getBatPowerPercent4()
	{
		return new BigDecimal(GetS16(58)).multiply(Percentage._1);
	}

	


	public final int getWorkWeek4() { return GetU16(60); }

	
	public final BigDecimal getBattStartChgSoc()
	{
		return new BigDecimal(GetU16(62)).multiply(Percentage._01);
	}

	
	public final BigDecimal getBattStopChgSoc()
	{
		return new BigDecimal(GetU16(64)).multiply(Percentage._01);
	}

	
	public final boolean getClearAllEconomicData()
	{
		return GetBool((int) GetU16(66), 1, 0, false);
	}

	
	public final boolean getFeedPowerEnable3Phase()
	{
		return GetBool((int) GetU16(68), 1, 0, false);
	}

	
	public final Watt getRPhaseFeedPowerPara()
	{
		return GetS16(70, Watt.Unit);
	}

	
	public final Watt getSPhaseFeedPowerPara()
	{
		return GetS16(72, Watt.Unit);
	}

	
	public final Watt getTPhaseFeedPowerPara()
	{
		return GetS16(74, Watt.Unit);
	}
}
