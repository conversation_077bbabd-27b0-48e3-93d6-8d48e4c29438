package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringHelper;
import com.ebon.energy.fms.util.StringProcessors;


// Copyright (c) Redback Technologies. All Rights Reserved.





public class BandPylon51 extends DataBackedBand
{
	public BandPylon51()
	{
		super(BandForge.<BandPylon51>getMetadataFor(BandPylon51.class));
	}



	public BandPylon51(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon51>getMetadataFor(BandPylon51.class));
	}

	public BandPylon51(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon51>getMetadataFor(BandPylon51.class));
	}


	
	public final String getDeviceNameRaw()
	{
		return GetBufS(0, 10, StringProcessors.GoodweDecode);
	}



	


	public final byte getVersionMajor()
	{
		return GetU8(10);
	}

	


	public final byte getVersionMinor()
	{
		return GetU8(11);
	}

	
	public final String getManufacturerRaw()
	{
		return GetBufS(12, 20, StringProcessors.GoodweDecode);
	}


	public String getDeviceName()
	{
		return StringHelper.trimEnd(getDeviceNameRaw(), (char)0, ' ', '-');
	}

	public String getManufacturer()
	{
		return StringHelper.trimEnd(getManufacturerRaw(), (char)0, ' ', '-');
	}
}
