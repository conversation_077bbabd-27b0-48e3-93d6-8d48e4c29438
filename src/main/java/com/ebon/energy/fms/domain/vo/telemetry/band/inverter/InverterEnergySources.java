package com.ebon.energy.fms.domain.vo.telemetry.band.inverter;

import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;



// Copyright (c) Redback Technologies. All Rights Reserved.


public class InverterEnergySources
{
	public static final InverterEnergySources None = new InverterEnergySources(0);
	public static final InverterEnergySources AC = new InverterEnergySources(1);
	public static final InverterEnergySources Battery = new InverterEnergySources(2);
	public static final InverterEnergySources PV = new InverterEnergySources(4);

	public static final int SIZE = Integer.SIZE;

	private int intValue;
	private static java.util.HashMap<Integer, InverterEnergySources> mappings;
	private static java.util.HashMap<Integer, InverterEnergySources> getMappings()
	{
		if (mappings == null)
		{
			synchronized (InverterEnergySources.class)
			{
				if (mappings == null)
				{
					mappings = new java.util.HashMap<Integer, InverterEnergySources>();
				}
			}
		}
		return mappings;
	}

	private InverterEnergySources(int value)
	{
		intValue = value;
		synchronized (InverterEnergySources.class)
		{
			getMappings().put(value, this);
		}
	}

	public int getValue()
	{
		return intValue;
	}

	public static InverterEnergySources forValue(int value)
	{
		synchronized (InverterEnergySources.class)
		{
			InverterEnergySources enumObj = getMappings().get(value);
			if (enumObj == null)
			{
				return new InverterEnergySources(value);
			}
			else
			{
				return enumObj;
			}
		}
	}
}
