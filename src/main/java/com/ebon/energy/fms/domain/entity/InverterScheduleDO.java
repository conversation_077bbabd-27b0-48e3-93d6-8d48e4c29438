package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Time;
import java.sql.Timestamp;
import java.util.UUID;

/**
 * Entity class for InverterSchedule table
 */
@Data
@TableName("InverterSchedule")
public class InverterScheduleDO {

    @TableField("SerialNumber")
    private String serialNumber;

    @TableField("ScheduleId")
    private String scheduleId;

    @TableField("UserNotes")
    private String userNotes;

    @TableField("CreatedById")
    private String createdById;

    @TableField("CreatedOnUtc")
    private Timestamp createdOnUtc;

    @TableField("CreatedChannel")
    private String createdChannel;

    @TableField("DeletedById")
    private String deletedById;

    @TableField("DeletedOnUtc")
    private Timestamp deletedOnUtc;

    @TableField("DeletedChannel")
    private String deletedChannel;

    @TableField("StartAtUtc")
    private Timestamp startAtUtc;

    @TableField("EndAtUtc")
    private Timestamp endAtUtc;

    @TableField("DailyStartTime")
    private Time dailyStartTime;

    @TableField("Duration")
    private Time duration;

    @TableField("DaysOfWeek")
    private Integer daysOfWeek;

    @TableField("ActionName")
    private String actionName;

    @TableField("ActionParameter")
    private Integer actionParameter;

    @TableField("Priority")
    private Integer priority;

    @TableField("IanaTimeZoneId")
    private String ianaTimeZoneId;
}
