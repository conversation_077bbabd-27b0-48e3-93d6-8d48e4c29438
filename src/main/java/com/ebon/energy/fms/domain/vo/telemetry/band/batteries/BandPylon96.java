package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;









public class BandPylon96 extends DataBackedBand
{
	public BandPylon96()
	{
		super(BandForge.<BandPylon96>getMetadataFor(BandPylon96.class));
	}



	public BandPylon96(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon96>getMetadataFor(BandPylon96.class));
	}

	public BandPylon96(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon96>getMetadataFor(BandPylon96.class));
	}


	


	public final byte getPackIndexUnadjusted()
	{
		return GetU8(0);
	}

	


	public final int getManufacturerVerson() { return GetU16(1); }

	


	public final int getMainlineVersion()
	{
		return GetU24(3);
	}
}
