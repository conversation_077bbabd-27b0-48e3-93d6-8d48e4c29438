package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.BatteryIndex;

import java.util.Objects;

public class BattFloatSettingsVO implements Cloneable {
    public double getVoltageValue() {
        return Voltage != null ? Voltage : 0d;
    }

    public double getCurrentValue() {
        return Current != null ? Current : 0d;
    }

    public int getTimeValue() {
        return Time != null ? Time : 0;
    }

    // range between 50 - 60 default 54
    public Double Voltage;
    // range between 0 - 50 default 3
    public Double Current;
    // minutes between 0 and 60000
    public Integer Time;

    public BatteryIndex index;

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            // 由于实现了 Cloneable 接口，这里不会抛出异常
            throw new AssertionError();
        }
    }

    @Override
    public int hashCode() {
        return 17 * (int) getVoltageValue() + 31 * (int) getCurrentValue() + 41 * getTimeValue() + 83 * index.ordinal();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof BattFloatSettingsVO)) {
            return false;
        }
        BattFloatSettingsVO bcs = (BattFloatSettingsVO) obj;
        return Objects.equals(bcs.Voltage, Voltage) && Objects.equals(bcs.Current, Current)
                && Objects.equals(bcs.Time, Time) && bcs.index == index;
    }
}