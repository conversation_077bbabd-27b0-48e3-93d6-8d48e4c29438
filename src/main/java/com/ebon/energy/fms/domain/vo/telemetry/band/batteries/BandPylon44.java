package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.common.enums.*;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;


public class BandPylon44 extends DataBackedBand
{
	public BandPylon44()
	{
		super(BandForge.<BandPylon44>getMetadataFor(BandPylon44.class));
	}



	public BandPylon44(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon44>getMetadataFor(BandPylon44.class));
	}

	public BandPylon44(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon44>getMetadataFor(BandPylon44.class));
	}


	
	public final Object getStatus()
	{
		return PylonInfoFlagState.parse(GetU8(0));
	}

	


	public final byte getPackIndexUnadjusted()
	{
		return GetU8(1);
	}

	


	public final byte getCellCount()
	{
		return GetU8(2);
	}

	
	public final Object getCellVoltageStatus1()
	{
		return PylonAlarmState.parse(GetU8(3));
	}

	
	public final Object getCellVoltageStatus2()
	{
		return PylonAlarmState.parse(GetU8(4));
	}

	
	public final Object getCellVoltageStatus3()
	{
		return PylonAlarmState.parse(GetU8(5));
	}

	
	public final Object getCellVoltageStatus4()
	{
		return PylonAlarmState.parse(GetU8(6));
	}

	
	public final Object getCellVoltageStatus5()
	{
		return PylonAlarmState.parse(GetU8(7));
	}

	
	public final Object getCellVoltageStatus6()
	{
		return PylonAlarmState.parse(GetU8(8));
	}

	
	public final Object getCellVoltageStatus7()
	{
		return PylonAlarmState.parse(GetU8(9));
	}

	
	public final Object getCellVoltageStatus8()
	{
		return PylonAlarmState.parse(GetU8(10));
	}

	
	public final Object getCellVoltageStatus9()
	{
		return PylonAlarmState.parse(GetU8(11));
	}

	
	public final Object getCellVoltageStatus10()
	{
		return PylonAlarmState.parse(GetU8(12));
	}

	
	public final Object getCellVoltageStatus11()
	{
		return PylonAlarmState.parse(GetU8(13));
	}

	
	public final Object getCellVoltageStatus12()
	{
		return PylonAlarmState.parse(GetU8(14));
	}

	
	public final Object getCellVoltageStatus13()
	{
		return PylonAlarmState.parse(GetU8(15));
	}

	
	public final Object getCellVoltageStatus14()
	{
		return PylonAlarmState.parse(GetU8(16));
	}

	
	public final Object getCellVoltageStatus15()
	{
		return PylonAlarmState.parse(GetU8(17));
	}

	


	public final byte getTempSensorCount()
	{
		return GetU8(18);
	}

	
	public final Object getTempSensorStatus1()
	{
		return PylonAlarmState.parse(GetU8(19));
	}

	
	public final Object getTempSensorStatus2()
	{
		return PylonAlarmState.parse(GetU8(20));
	}

	
	public final Object getTempSensorStatus3()
	{
		return PylonAlarmState.parse(GetU8(21));
	}

	
	public final Object getTempSensorStatus4()
	{
		return PylonAlarmState.parse(GetU8(22));
	}

	
	public final Object getTempSensorStatus5()
	{
		return PylonAlarmState.parse(GetU8(23));
	}

	
	public final Object getChargeCurrentStatus()
	{
		return PylonAlarmState.parse(GetU8(24));
	}

	
	public final Object getPackVoltageStatus()
	{
		return PylonAlarmState.parse(GetU8(25));
	}

	
	public final Object getDischargeCurrentStatus()
	{
		return PylonAlarmState.parse(GetU8(26));
	}

	
	public final Object getStatus1()
	{
		return PylonPackStatus1.parse(GetU8(27));
	}

	
	public final Object getStatus2()
	{
		return PylonPackStatus2.parse(GetU8(28));
	}

	
	public final Object getStatus3()
	{
		return PylonPackStatus3.parse(GetU8(29));
	}

	
	public final Object getStatus4()
	{
		return PylonPackStatus4.parse(GetU8(30));
	}

	
	public final Object getStatus5()
	{
		return PylonPackStatus5.parse(GetU8(31));
	}


	public int getPackIndex()
	{
		return getPackIndexUnadjusted() - 2;
	}



	/*public PylonAlarmState[] getCellVoltageStatuses()
	{
		return new PylonAlarmState[] {getCellVoltageStatus1(), getCellVoltageStatus2(), getCellVoltageStatus3(), getCellVoltageStatus4(), getCellVoltageStatus5(), getCellVoltageStatus6(), getCellVoltageStatus7(), getCellVoltageStatus8(), getCellVoltageStatus9(), getCellVoltageStatus10(), getCellVoltageStatus11(), getCellVoltageStatus12(), getCellVoltageStatus13(), getCellVoltageStatus14(), getCellVoltageStatus15()};
	}

	public PylonAlarmState[] getTempSensorStatuses()
	{
		return new PylonAlarmState[] {getTempSensorStatus1(), getTempSensorStatus2(), getTempSensorStatus3(), getTempSensorStatus4(), getTempSensorStatus5()};
	}*/
}
