package com.ebon.energy.fms.domain.vo.telemetry.band.batteries;


import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.band.BandForge;
import com.ebon.energy.fms.domain.vo.telemetry.band.DataBackedBand;
import com.ebon.energy.fms.util.StringProcessors;


// Copyright (c) Redback Technologies. All Rights Reserved.





public class BandPylon93 extends DataBackedBand implements IBandPylonPackIndex
{
	public BandPylon93()
	{
		super(BandForge.<BandPylon93>getMetadataFor(BandPylon93.class));
	}



	public BandPylon93(byte[] bytes)
	{
		super(bytes, BandForge.<BandPylon93>getMetadataFor(BandPylon93.class));
	}

	public BandPylon93(String encodedBytes)
	{
		super(encodedBytes, BandForge.<BandPylon93>getMetadataFor(BandPylon93.class));
	}


	


	public final byte getPackIndexUnadjusted()
	{
		return GetU8(0);
	}

	
	public final String getSerial()
	{
		return GetBufS(1, 16, StringProcessors.GoodweDecode);
	}


	public int getPackIndex()
	{
		return getPackIndexUnadjusted() - 2;
	}
}
