package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum ExtInvDetailedStatus {
    NoStatus(0),
    SafetyOverFreqCurveFlag(1L << 0),
    SafetyUnderFreqCurveFlag(1L << 1),
    SafetyFreqRecoverCurveFlag(1L << 2),
    SafetyPUCurveOVFlag(1L << 3),
    SafetyPUCurveUVFlag(1L << 4),
    SafetyQUCurveFlag(1L << 5),
    SafetyPFCurveFlag(1L << 6),
    FixedPFSettingFlag(1L << 7),
    FixedQSettingFlag(1L << 8),
    InvOverTempFlag(1L << 9),
    DREDSellPowerLimitFlag(1L << 10),
    DREDBuyPowerLimitFlag(1L << 11),
    ActivePowerSettingFlag(1L << 12),
    GeDratePowerFlag(1L << 13),
    AutoTestEnableFlag(1L << 14),
    GridVoltSt1DerateFlag(1L << 15),
    ForceOffGridFlag(1L << 16),
    ForceStopModeFlag(1L << 17),
    OffGridMpptChgUpsOffFLag(1L << 18),
    SafetyQUCurveOVFlag(1L << 19),
    SafetyQUCurveUVFlag(1L << 20),
    Bit21(1L << 21),
    Bit22(1L << 22),
    Bit23(1L << 23),
    Bit24(1L << 24),
    Bit25(1L << 25),
    Bit26(1L << 26),
    Bit27(1L << 27),
    Bit28(1L << 28),
    Bit29(1L << 29),
    Bit30(1L << 30),
    Bit31(1L << 31),
    Bit32(1L << 32),
    Bit33(1L << 33),
    Bit34(1L << 34),
    Bit35(1L << 35),
    Bit36(1L << 36),
    Bit37(1L << 37),
    Bit38(1L << 38),
    Bit39(1L << 39),
    Bit40(1L << 40),
    Bit41(1L << 41),
    Bit42(1L << 42),
    Bit43(1L << 43),
    Bit44(1L << 44),
    Bit45(1L << 45),
    Bit46(1L << 46),
    Bit47(1L << 47),
    Bit48(1L << 48),
    Bit49(1L << 49),
    Bit50(1L << 50),
    Bit51(1L << 51),
    Bit52(1L << 52),
    Bit53(1L << 53),
    Bit54(1L << 54),
    Bit55(1L << 55),
    Bit56(1L << 56),
    Bit57(1L << 57),
    Bit58(1L << 58),
    Bit59(1L << 59),
    Bit60(1L << 60),
    Bit61(1L << 61),
    Bit62(1L << 62),
    Bit63(1L << 63);

    private long longValue;

    private ExtInvDetailedStatus(long value) {
        longValue = value;
    }

    public long getValue() {
        return longValue;
    }

    public static ExtInvDetailedStatus fromValue(long value) {
        ExtInvDetailedStatus[] values = ExtInvDetailedStatus.values();
        for (ExtInvDetailedStatus e : ExtInvDetailedStatus.values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(long value) {
        ExtInvDetailedStatus o = fromValue(value);
        if (o != null) {
            return o;
        }

        Set<ExtInvDetailedStatus> result = EnumSet.noneOf(ExtInvDetailedStatus.class);
        ExtInvDetailedStatus[] values = ExtInvDetailedStatus.values();
        for (ExtInvDetailedStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }

}