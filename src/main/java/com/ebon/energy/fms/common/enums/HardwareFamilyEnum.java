package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum HardwareFamilyEnum {
    /**
     * Unknown hardware family.
     */
    @JsonProperty("Unknown")
    Unknown(0, "Unknown", null, null, null),

    /**
     * SH - Smart Hybrid.
     */
    @JsonProperty("Inverter_Goodwe_ES")
    Inverter_Goodwe_ES(1, "Hybrid", "Smart Hybrid", "ES", null),

    /**
     * ST - Smart Three Phase Hybrid.
     */
    @JsonProperty("Inverter_Goodwe_ET")
    Inverter_Goodwe_ET(2, "Hybrid", "Smart Three Phase Hybrid", "ET", null),

    /**
     * SB - Smart Battery.
     */
    @JsonProperty("Inverter_Goodwe_BH")
    Inverter_Goodwe_BH(3, "AC Coupled", "Smart Battery", "SB", null),

    /**
     * SI - Smart Inverter.
     */
    @JsonProperty("Inverter_Senergy_SG")
    Inverter_Senergy_SG(51, "Grid-Tie", "Smart Inverter", "SI", null),

    @JsonProperty("Battery_LG")
    Battery_LG(100, null, null, null, null),

    @JsonProperty("Battery_Pylon_LV")
    Battery_Pylon_LV(101, null, null, null, null),

    @JsonProperty("Battery_Pylon_HV")
    Battery_Pylon_HV(102, null, null, null, null),

    @JsonProperty("Battery_Redback")
    Battery_Redback(103, null, null, null, null);

    private final int code;
    private final String systemType;
    private final String displayName;
    private final String shortName;
    private final String description;

    @JSONCreator
    public static HardwareFamilyEnum fromValue(int value) {
        for (HardwareFamilyEnum e : values()) {
            if (e.getCode() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching hardware family for value: " + value);
    }
}
