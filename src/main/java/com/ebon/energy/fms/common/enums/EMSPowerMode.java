package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum EMSPowerMode
{
    None(0),
    UPSMode(1 << 0),
    SpontaneousSelfUseMode(1 << 1),
    TimedChargeDischargeMode(1 << 2),
    FeedPriorityMode(1 << 3),
    FeedNetworkPriorityTimingChargeDischargeMode(1 << 4),
    BatteryRetentionMode(1 << 5),
    OffGridMode(1 << 6),
    RemoteControlOfBatteryChargingDischarge(1 << 7),
    PassiveMode(1 << 8); //9,10,11,12,13,14,15Reserved

    private int value;


    private EMSPowerMode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static EMSPowerMode forValue(int value)
    {
        for (EMSPowerMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        EMSPowerMode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<EMSPowerMode> result = EnumSet.noneOf(EMSPowerMode.class);
        EMSPowerMode[] values = EMSPowerMode.values();
        for (EMSPowerMode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}