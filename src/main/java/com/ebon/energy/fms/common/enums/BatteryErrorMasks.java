package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BatteryErrorMasks {
    NoError(0),
    OverTemperature(1 << 0),
    UnderTemperature(1 << 1),
    CellVDifference(1 << 2),
    OverTotalVoltage(1 << 3),
    DischargeOverCurrent(1 << 4),
    ChargeOverCurrent(1 << 5),
    UnderSoC(1 << 6),
    UnderTotalVoltage(1 << 7),
    UnderTotalV(1 << 7), // backwards compat with 2.3
    CommFail(1 << 8),
    OutputShort(1 << 9),
    BMSSoCTooHigh(1 << 10),
    BMSModuleDefault(1 << 11),
    BMSSystemDefault(1 << 12),
    BMSInternalFault(1 << 13);

    private final int value;

    BatteryErrorMasks(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BatteryErrorMasks fromValue(int value) {
        for (BatteryErrorMasks result : BatteryErrorMasks.values()) {
            if (result.getValue() == value) {
                return result;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BatteryErrorMasks o = fromValue(value);
        if (o != null) {
            return o;
        }

        Set<BatteryErrorMasks> result = EnumSet.noneOf(BatteryErrorMasks.class);
        BatteryErrorMasks[] values = BatteryErrorMasks.values();
        for (BatteryErrorMasks e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}