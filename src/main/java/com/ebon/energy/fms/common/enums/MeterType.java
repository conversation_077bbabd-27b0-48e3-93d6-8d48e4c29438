package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum MeterType
{
    SinglePhase(0),
    ThreePhaseThreeWire(1),
    ThreePhaseFourWire(2),
    HomeKit(3),
    TwoCTs(4);

    private int value;


    MeterType(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static MeterType forValue(int value)
    {
        for (MeterType e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        MeterType o = forValue(value);
        if (o != null) {
            return o;
        }
        
       return value;
    }
}
