package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum DredMode {
    NoMode(0),
    DRM0(1 << 0),
    DRM1(1 << 1),
    DRM2(1 << 2),
    DRM3(1 << 3),
    DRM4(1 << 4),
    DRM5(1 << 5),
    DRM6(1 << 6),
    DRM7(1 << 7),
    DRM8(1 << 8);

    private final int value;

    DredMode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static DredMode forValue(int value)
    {
        for (DredMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        DredMode o = forValue(value);
        if (o != null) {
            return o;
        }

        Set<DredMode> result = EnumSet.noneOf(DredMode.class);
        DredMode[] values = DredMode.values();
        for (DredMode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}