package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterstState
{
    NormalOperation(0),
    Waiting(2),
    Initialization(3),
    ControlledOffGrid(4100),
    GridOvervoltage(4112),
    GridUndervoltage(4113),
    GridOverfrequency(4114),
    GridUnderfrequency(4115),
    GridCountercurrent(4116),
    NoPowerGrid(4117),
    GridImbalance(4118),
    GridFrequencyJitter(4119),
    GridOvercurrent(4120),
    GridCurrentTrackingFault(4121),
    DCOvervoltage(4128),
    DCBusOvervoltage(4129),
    UnevenDCBusVoltage(4130),
    DCBusUndervoltage(4131),
    DCBusUnevenVoltage2(4132),
    DirectCurrentAPassingCurrent(4133),
    DirectCurrentBPassingCurrent(4134),
    DCInputDisturbance(4135),
    DCReverseConnectionFault(4136),
    PCMidpointGroundFault(4137),
    PowerGridDisturbance(4144),
    DSPInitializationFaultProtection(4145),
    OverTemperatureProtection(4146),
    GroundingProtectionPVInsulationFault(4147),
    LeakageCurrentProtection01(4148),
    RelayFaultDetectionProtection(4149),
    DSP_BCommunicationAnomaly(4150),
    DCComponentIsTooLarge(4151),
    UndervoltageFaultProtection12V(4152),
    LeakageCurrentSelfTestProtection(4153),
    UnderTemperatureProtection(4154),
    ArcSelfTestProtection(4160),
    ArcFaultProtection(4161),
    DSPOnChipSRAMException(4162),
    DSPOnChipFlashException(4163),
    DSPOnChipPCPointerException(4164),
    DSPCriticalRegisterException(4165),
    GridDisturbance02(4166),
    GridCurrentSamplingAnomaly(4167),
    IGBTOvercurrent(4168),
    GridSideInstantaneousOvercurrent(4176),
    BatteryOvervoltageHardwareFault(4177),
    LLCHardwareOvercurrent(4178),
    BatteryOvervoltageDetection(4179),
    BatteryUndervoltageDetection(4180),
    BatteryNotConnected(4181),
    BypassOvervoltageFault(4182),
    BypassOverloadFault(4183),
    DSPSelfTestException(4184),
    DSPDetectsBatteryOvercurrent(4187),
    AbnormalLossOfSlaveSynchronizationSignal(4192),
    HostSynchronizationSignalLossException(4193),
    SlaveSynchronizationSignalPeriodException(4194),
    HostSynchronizationSignalCycleException(4195),
    PhysicalAddressConflict(4196),
    HeartbeatLoss(4197),
    DCANRegisterException(4198),
    MultipleHostError(4199),
    SlaveMasterOnGridOffGridModesNotSynchronized(4200),
    HostOffGridSlaveVoltageNotSynchronized(4201),
    OtherMachinesHaveMalfunctioned(4202),
    BatteryOvervoltageHardwareFailure02(4208),
    BatteryOvercurrentHardwareFailure(4209),
    OffGridBackupUndervoltageFault(4210),
    BusMidpointOvercurrentHardwareFailure(4211),
    BatteryStartupFailureFailure(4212),
    DC3WayAverageOvercurrent(4213),
    DC4ChannelAverageOvercurrent(4214),
    SoftStartTimeoutFault(4215),
    GridToOffGridTimeoutFault(4216),
    FailSafe(8208),
    MeterCommunicationFailure(8209),
    BatteryCommunicationFailure(8210),
    DSPCommunicationFailure(8212),
    BMSWarningFailure(8213),
    InconsistentBatterySelection(8214),
    Alarm2BMS(8215),
    DRMConnectionFailed(8216),
    MeterSelectionAnomaly(8217),
    LeadAcidBatteryAmbientTemperatureHigh(8224),
    LeadAcidBatteryAmbientTemperatureLow(8225),
    GridConnectedBackupOverload(8240),
    EPMHardLimitProtection(8256),
    AFCIBoardCommunicationAnomaly(8257),
    AFCIBoardCtModuleHardwareAbnormality(8258),
    G100OvercurrentProtection(8259),
    MultiHostSettingsException(8260),
    BatteryUndervoltageFaultProtection(8264),
    GridSurge(61456),
    InternalFanFailure(61457),
    ExternalFanFailure(61461);

    private int value;


    InverterstState(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterstState forValue(int value)
    {
        for (InverterstState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InverterstState o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
