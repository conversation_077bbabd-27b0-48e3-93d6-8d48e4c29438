package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum SmartRelayMode {
    Schedule(0, "Schedule"),
    On(1, "On"),
    Off(2, "Off"),
    Auto(3, "Smart Load Control");

    private final int value;
    private final String displayName;

    SmartRelayMode(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public int getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JSONCreator
    public static SmartRelayMode fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (SmartRelayMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching SmartRelayMode for value: " + value);
    }
}