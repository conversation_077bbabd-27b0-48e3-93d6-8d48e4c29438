package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonUSPackStatus1
{
    None(0),
    ModuleOverVoltage(1 << 0),
    CellUnderVoltage(1 << 1),
    ChargeOverCurrent(1 << 2),

    DischargeOverCurrent(1 << 4),
    DischargeOverTemperature(1 << 5),
    ChargeOverTemperature(1 << 6),
    ModuleUnderVoltage(1 << 7);

    private int value;
    PylonUSPackStatus1(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonUSPackStatus1 fromValue(int value) {
        for (PylonUSPackStatus1 state : PylonUSPackStatus1.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }
        
        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonUSPackStatus1 o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonUSPackStatus1 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}
