package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum ModelTypeEnum {
    EMSFirmware(0),
    Firmware(1)
    ;

    private final int value;

    ModelTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static ModelTypeEnum fromValue(int value) {
        for (ModelTypeEnum e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        return EMSFirmware;
    }
}
