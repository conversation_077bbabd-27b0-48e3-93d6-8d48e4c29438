package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum AFCIfaultNum
{
    Unknown(0),
    FirstChannelFailure(1),
    SecondChannelFailure(2);

    private int value;

    AFCIfaultNum(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static AFCIfaultNum forValue(int value)
    {
        for (AFCIfaultNum e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        AFCIfaultNum o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<AFCIfaultNum> result = EnumSet.noneOf(AFCIfaultNum.class);
        AFCIfaultNum[] values = AFCIfaultNum.values();
        for (AFCIfaultNum e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
