package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BMSAlarmCodeET
{
    NoAlarm(0),
    BatteryOverVoltageCharge2(1 << 0),
    BatteryUnderVoltageDischarge2(1 << 1),
    BatteryCellHighTemperature2(1 << 2),
    BatteryCellLowTemperature2(1 << 3),
    BatteryOverCurrentCharge2(1 << 4),
    BatteryOvercurrentDischarge2(1 << 5),
    BatteryFaultPrecharge(1 << 6),
    BatteryFaultDCBus(1 << 7),
    BatteryBreak(1 << 8),
    BatteryLock(1 << 9),
    BatteryFaultDischargeCircuit(1 << 10),
    BatteryFaultChargeCircuit(1 << 11),
    BatteryFaultCommunication2(1 << 12),
    BatteryCellHighTemperature3(1 << 13),
    BatteryUnderVoltageDischarge3(1 << 14),
    BatteryOverVoltageCharge3(1 << 15);

    private int value;

    BMSAlarmCodeET(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BMSAlarmCodeET forValue(int value)
    {
        for (BMSAlarmCodeET e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BMSAlarmCodeET o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BMSAlarmCodeET> result = EnumSet.noneOf(BMSAlarmCodeET.class);
        BMSAlarmCodeET[] values = BMSAlarmCodeET.values();
        for (BMSAlarmCodeET e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
