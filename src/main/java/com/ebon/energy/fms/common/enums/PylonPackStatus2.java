package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonPackStatus2 {
    
    None(0),
    PreFET(1 << 0),
    CFET(1 << 1),
    DFET(1 << 2),
    UsePackPower(1 << 3);

    private final int value;

    PylonPackStatus2(int value) {
        this.value = (byte)value;
    }

    public int getValue() {
        return value;
    }

    public static PylonPackStatus2 fromValue(int value) {
        for (PylonPackStatus2 state : PylonPackStatus2.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonPackStatus2 o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonPackStatus2 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}