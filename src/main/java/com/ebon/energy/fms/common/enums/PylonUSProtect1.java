package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonUSProtect1
{
    None(0),
    Unknown0(1 << 0),
    MOSFETOverTemp(1 << 1),
    CellUnderTemp(1 << 2),
    CellOverTemp(1 << 3),
    CellUnderVoltage(1 << 4),
    CellOverVoltage(1 << 5),
    ModuleUnderVoltage(1 << 6),
    ModuleOverVoltage(1 << 7);

    private int value;

    PylonUSProtect1(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonUSProtect1 forValue(int value)
    {
        for (PylonUSProtect1 state : PylonUSProtect1.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonUSProtect1 o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonUSProtect1 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}
