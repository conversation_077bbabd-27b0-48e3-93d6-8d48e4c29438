package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode04
{
    None(0),
    DCOvervoltage(1 << 0),
    DCBusOvervoltage(1 << 1),
    UnevenDCBusVoltage(1 << 2),
    DCBusUndervoltage(1 << 3),
    DCBusUnevenVoltage2(1 << 4),
    DirectCurrentAPassingVurrent(1 << 5),
    DirectCurrentBPassingCurrent(1 << 6),
    DCInputDisturbance(1 << 7),
    GridOvercurrent(1 << 8),
    IGBTOvercurrent(1 << 9),
    GridDisturbance02(1 << 10),
    ArcSelfTestProtection(1 << 11),
    ArcFaultReservation(1 << 12),
    GridCurrentSamplingAnomaly(1 << 13),
    DSPSelfTestException(1 << 14),
    BatteryDischargeOvercurrent(1 << 15);

    private int value;

    FaultCode04(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode04 forValue(int value)
    {
        for (FaultCode04 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode04 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FaultCode04> result = EnumSet.noneOf(FaultCode04.class);
        FaultCode04[] values = FaultCode04.values();
        for (FaultCode04 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
