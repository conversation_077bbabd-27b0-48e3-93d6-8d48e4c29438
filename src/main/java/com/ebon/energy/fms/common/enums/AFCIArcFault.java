package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum AFCIArcFault
{
    None(0),
    MPPT0_1ChannelFault(1 << 0),
    MPPT2_3ChannelFault(1 << 1),
    MPPT4_5ChannelFault(1 << 2),
    MPPT6_7ChannelFault(1 << 3),
    MPPT8_9ChannelFault(1 << 4);

    private int value;

    AFCIArcFault(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static AFCIArcFault forValue(int value)
    {
        for (AFCIArcFault e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        AFCIArcFault o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<AFCIArcFault> result = EnumSet.noneOf(AFCIArcFault.class);
        AFCIArcFault[] values = AFCIArcFault.values();
        for (AFCIArcFault e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}