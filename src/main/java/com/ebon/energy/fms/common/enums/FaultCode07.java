package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode07
{
    None(0),
    DCReverseConnectionFault(1 << 0),
    BatteryOvervoltageHardwareFailure2(1 << 1),
    BatteryOvercurrentHardwareFailure(1 << 2),
    BusMidpointOvercurrentHardwareFailure(1 << 3),
    BatteryStartupFailure(1 << 4),
    DC3WayAverageOvercurrent(1 << 5),
    DC4ChannelAverageOvercurrent(1 << 6),
    SoftStartTimeoutFault(1 << 7),
    DepartureTimeoutFault(1 << 8),
    DRMNotConnected(1 << 9),
    ACBK_N2PE_ERR(1 << 10),
    DSPHardwareMismatch(1 << 11),
    AFCICommsException(1 << 12),
    AFCI_CTModuleAbnormality(1 << 13),
    MulMasterSetErr(1 << 14),
    SlvCommErr(1 << 15);

    private int value;


    FaultCode07(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode07 forValue(int value)
    {
        for (FaultCode07 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode07 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FaultCode07> result = EnumSet.noneOf(FaultCode07.class);
        FaultCode07[] values = FaultCode07.values();
        for (FaultCode07 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}