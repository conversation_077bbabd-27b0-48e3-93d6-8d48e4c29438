package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

@JSONType(deserializer = GoodWeSiteGenerationLimitType.GoodWeSiteGenerationLimitTypeDeserializer.class)
public enum GoodWeSiteGenerationLimitType {

    NoLimit(0),
    SoftLimit(1),
    HardLimit(2);

    private final int value;

    GoodWeSiteGenerationLimitType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static GoodWeSiteGenerationLimitType fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (GoodWeSiteGenerationLimitType e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching GoodWeSiteGenerationLimitType for value: " + value);
    }

    class GoodWeSiteGenerationLimitTypeDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                int intValue = (Integer) value;
                for (GoodWeSiteGenerationLimitType item : GoodWeSiteGenerationLimitType.values()) {
                    if (item.value == intValue) {
                        return (T) item;
                    }
                }
            } else if (value instanceof String) {
                String strValue = (String) value;
                for (GoodWeSiteGenerationLimitType item : GoodWeSiteGenerationLimitType.values()) {
                    if (item.name().equals(strValue)) {
                        return (T) item;
                    }
                }
            }
            throw new JSONException("无法解析枚举 GoodWeSiteGenerationLimitType: " + value);
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }
}
