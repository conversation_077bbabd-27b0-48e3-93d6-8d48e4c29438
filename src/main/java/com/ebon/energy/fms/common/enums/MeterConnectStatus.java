package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum MeterConnectStatus
{
    NG(0),
    OK(1),
    <PERSON>erse(2),
    CT<PERSON>rror(3);

    private int value;


    MeterConnectStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static MeterConnectStatus forValue(int value)
    {
        for (MeterConnectStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        MeterConnectStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
