package com.ebon.energy.fms.common.enums;

import java.util.ArrayList;
import java.util.List;

public enum PvMode
{
    NoPV(0x00), // inverter disconnects to PV
    StandBy(0x01), // standby, does not output power
    Working(0x02); // output power

    private int value;


    PvMode(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PvMode forValue(int value)
    {
        for (PvMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PvMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PvMode mode : values()) {
            if (mode.value != 0 && (remaining & mode.value) == mode.value) {
                activeFlags.add(mode.name());
                remaining &= ~mode.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}
