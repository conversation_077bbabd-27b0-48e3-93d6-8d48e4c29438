package com.ebon.energy.fms.common.utils;


import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.SettingsType;

public class ModelInfo {

    private final HardwareModelEnum hardwareModel;

    private final int armVersion;

    public ModelInfo(String inverterModelString, String firmwareString) {
        if (inverterModelString == null) {
            throw new IllegalArgumentException("inverterModelString cannot be null");
        }
        if (firmwareString == null) {
            throw new IllegalArgumentException("firmwareString cannot be null");
        }
        this.hardwareModel = HardwareModelHelpers.parseModelName(inverterModelString);
        this.armVersion = extractArmVersion(firmwareString);
    }

    public ModelInfo(HardwareModelEnum hardwareModel, String firmwareString) {
        this.hardwareModel = hardwareModel;
        this.armVersion = extractArmVersion(firmwareString);
    }

    public ModelInfo(HardwareModelEnum hardwareModel, int armVersion) {
        this.hardwareModel = hardwareModel;
        this.armVersion = armVersion;
    }

    public static ModelInfo mostConservative() {
        return new ModelInfo(HardwareModelEnum.SH4600, 1);
    }

    public static ModelInfo mostConservativeSH4600() {
        return new ModelInfo(HardwareModelEnum.SH4600, 1);
    }

    public static ModelInfo SH4600v2() {
        return new ModelInfo(HardwareModelEnum.SH4600, 7);
    }

    public static ModelInfo mostConservativeSH5000() {
        return new ModelInfo(HardwareModelEnum.SH5000, 1);
    }

    public HardwareModelEnum getHardwareModel() {
        return hardwareModel;
    }

    public int getArmVersion() {
        return armVersion;
    }

    public boolean isSettingsV2() {
        SettingsType settingsType = hardwareModel.getSettingsTypeEnum();
        if (settingsType == null) {
            throw new RuntimeException("SettingsType missing for " + hardwareModel);
        }
        return settingsType.isSettingsV2();
    }

    public String forLogs() {
        return hardwareModel + "@ARM" + armVersion;
    }

    /**
     * 仅供 BatteryEnergyUtility 使用的辅助方法。
     */
    public static ModelInfo forBatteryEnergyUtilityOnly(String inverterModelString, String firmwareString) {
        return new ModelInfo(
                HardwareModelHelpers.parseModelName(inverterModelString),
                extractArmVersion(firmwareString)
        );
    }

    /**
     * 仅供嵌入式网络和 VPP 使用的辅助方法。
     */
    public static ModelInfo forGroupCodeOnly(String inverterModelString, String firmwareString) {
        return new ModelInfo(
                HardwareModelHelpers.parseModelName(inverterModelString),
                extractArmVersion(firmwareString)
        );
    }

    public static ModelInfo fromHardwareModelForTesting(HardwareModelEnum model) {
        switch (model) {
            case SH4600:
                return mostConservativeSH4600();
            case SH5000:
            case Unknown:
                return mostConservativeSH5000();
            default:
                return new ModelInfo(model, 0);
        }
    }

    /**
     * 解析固件版本以提取 ARM 版本。
     */
    public static int extractArmVersion(String firmwareVersion) {
        return ArmAndDspVersions.getOrZeros(firmwareVersion).getArmVersion();
    }
}
