package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonPackStatus1 {
    None(0),
    OverVoltage(1 << 0),
    CellLowerLimitVoltage(1 << 2),
    ChargeOvercurrent(1 << 3),
    DischargeOvercurrent(1 << 4),
    DischargeTempProtection(1 << 5),
    ChargeTempProtection(1 << 6),
    UnderVoltage(1 << 7),
    PackUnderVoltage(0x80);

    private final int value;

    PylonPackStatus1(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PylonPackStatus1 fromValue(int value) {
        for (PylonPackStatus1 state : PylonPackStatus1.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonPackStatus1 o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonPackStatus1 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}