package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterOperatingStatus
{
    Stopped(0),
    OpenLoop(1),
    SoftStart(2),
    GridConnected(3),
    OffGrid(4),
    DepartureandMerger(5),
    BackupBypass(6),
    Generator(7);

    private int value;


    InverterOperatingStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterOperatingStatus forValue(int value)
    {
        for (InverterOperatingStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InverterOperatingStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
