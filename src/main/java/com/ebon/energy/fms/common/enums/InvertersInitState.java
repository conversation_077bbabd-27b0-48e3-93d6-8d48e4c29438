package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InvertersInitState {
    None(0),
    ModelSettingsComplete(1 << 0),
    NationalStandardComplete(1 << 1),
    PowerCurveSet(1 << 2),
    ModuleIdentificationMark(1 << 3),
    FanSupport(1 << 4),
    FCASEnabled(1 << 5),
    AFCISelfCheck(1 << 6),
    AFCISelfTest(1 << 7),
    DSPChipIDHighBit(1 << 8),
    DSPChipIDLowBit(1 << 9),
    IGBTFilteringComplete(1 << 10), //11,12,13 Reserved
    DSPWaveformDataMark(1 << 14);

    private int value;


    InvertersInitState(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static InvertersInitState forValue(int value) {
        for (InvertersInitState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InvertersInitState o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<InvertersInitState> result = EnumSet.noneOf(InvertersInitState.class);
        InvertersInitState[] values = InvertersInitState.values();
        for (InvertersInitState e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}