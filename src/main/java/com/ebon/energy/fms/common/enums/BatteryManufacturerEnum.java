package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum BatteryManufacturerEnum {
    None(0),
    Unknown(1),
    Custom(2),
    LG(300),
    Pylon(400),
    LeadAcid(500),
    SimpliPHi(600),
    Redflow(700),
    Aquion(800);

    private final int value;

    BatteryManufacturerEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static BatteryManufacturerEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (BatteryManufacturerEnum manufacturer : BatteryManufacturerEnum.values()) {
            if (manufacturer.getValue() == value) {
                return manufacturer;
            }
        }
        throw new IllegalArgumentException("No matching BatteryManufacturer for value: " + value);
    }
}