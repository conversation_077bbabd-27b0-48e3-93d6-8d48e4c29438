package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum AFCIModuleFault
{
    None(0),
    <PERSON>dule1(1 << 0),
    <PERSON>dule2(1 << 1),
    <PERSON>dule3(1 << 2),
    Module4(1 << 3),
    Module5(1 << 4);

    private int value;

    AFCIModuleFault(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static AFCIModuleFault forValue(int value)
    {
        for (AFCIModuleFault e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        AFCIModuleFault o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<AFCIModuleFault> result = EnumSet.noneOf(AFCIModuleFault.class);
        AFCIModuleFault[] values = AFCIModuleFault.values();
        for (AFCIModuleFault e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
