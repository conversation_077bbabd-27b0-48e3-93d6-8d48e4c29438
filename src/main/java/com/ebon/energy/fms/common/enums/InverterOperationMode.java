package com.ebon.energy.fms.common.enums;

public enum InverterOperationMode {
    NoMode(0, "NoMode"),
    Auto(1, "Auto"),
    ChargeBattery(2, "Charge Battery"),
    DischargeBattery(3, "Discharge Battery"),
    ImportPower(4, "Import Power"),
    ExportPower(5, "Export Power"),
    Conserve(6, "Conserve"),
    Offgrid(7, "Off Grid"),
    Hibernate(8, "Hibernate"),
    BuyPower(9, "Buy Power"),
    SellPower(10, "Sell Power"),
    ForceChargeBattery(11, "Force Charge Battery"),
    ForceDischargeBattery(12, "Force Discharge Battery"),
    Stop(255, "Stop");

    private final int value;
    private final String displayName;

    InverterOperationMode(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public int getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static InverterOperationMode fromValue(int value) {
        for (InverterOperationMode mode : InverterOperationMode.values()) {
            if (mode.value == value) {
                return mode;
            }
        }
        throw new IllegalArgumentException("No enum InverterOperationMode with value: " + value);
    }
}