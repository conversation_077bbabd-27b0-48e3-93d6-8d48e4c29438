package com.ebon.energy.fms.common.enums;

public enum DerateFlag
{
    Normal(0),
    OverFrequency(1 << 0),
    UnderFrequency(1 << 1),
    OverVoltage(1 << 2);

    private int value;

    DerateFlag(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static DerateFlag forValue(int value)
    {
        for (DerateFlag e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        DerateFlag o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
