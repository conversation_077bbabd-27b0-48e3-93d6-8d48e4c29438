package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum AS477Parameters
{
    None(0),
    QUCurve(1 << 0),
    PUCurve(1 << 1),
    PFreqCurve(1 << 2),

    FixedQ(1 << 12),
    FixedPF(1 << 13),
    PQCurve(1 << 14),
    PFNull(1 << 15);

    private int value;

    AS477Parameters(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static AS477Parameters forValue(int value)
    {
        for (AS477Parameters e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        AS477Parameters o = forValue(value);
        if (o != null) {
            return o;
        }

        Set<AS477Parameters> result = EnumSet.noneOf(AS477Parameters.class);
        AS477Parameters[] values = AS477Parameters.values();
        for (AS477Parameters e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}