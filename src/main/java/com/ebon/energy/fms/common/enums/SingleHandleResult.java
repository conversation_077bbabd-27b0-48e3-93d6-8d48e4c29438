package com.ebon.energy.fms.common.enums;

public enum SingleHandleResult {
    Invalid(0),
    BadTestingConditions(1),
    Testing(2),
    Normal(3);

    private final int value;

    SingleHandleResult(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static SingleHandleResult fromValue(int value) {
        for (SingleHandleResult result : SingleHandleResult.values()) {
            if (result.getValue() == value) {
                return result;
            }
        }

        return null;
    }


    public static Object parse(int value) {
        SingleHandleResult o = fromValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}