package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum BatteryIndex {
    Lithium_Ion(0x00);

    private final int value;

    BatteryIndex(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static BatteryIndex fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (BatteryIndex index : BatteryIndex.values()) {
            if (index.getValue() == value) {
                return index;
            }
        }

        return null;
    }
}
