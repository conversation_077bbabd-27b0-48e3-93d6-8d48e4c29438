package com.ebon.energy.fms.common.enums;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.IOException;

@JsonSerialize(using = PylonUserDefined.PylonUserDefinedSerializer.class)
public class PylonUserDefined {
    private final int value;
    private final String name;

    private PylonUserDefined(int value, String name) {
        this.value = value;
        this.name = name;
    }

    // 预定义的枚举值
    public static final PylonUserDefined LessThan65Ah = new PylonUserDefined(2, "LessThan65Ah");
    public static final PylonUserDefined MoreThan65Ah = new PylonUserDefined(4, "MoreThan65Ah");

    public static PylonUserDefined fromValue(int value) {
        switch (value) {
            case 2:
                return LessThan65Ah;
            case 4:
                return MoreThan65Ah;
            default:
                return new PylonUserDefined(value, String.valueOf(value));
        }
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static class PylonUserDefinedSerializer extends JsonSerializer<PylonUserDefined> {
        @Override
        public void serialize(PylonUserDefined value, JsonGenerator gen, SerializerProvider serializers)
                throws IOException {
            // 如果是预定义的枚举值，输出name；否则输出原始值
            if (value.getName().equals(String.valueOf(value.getValue()))) {
                gen.writeNumber(value.getValue()); // 输出原始数字值
            } else {
                gen.writeString(value.getName()); // 输出枚举名称
            }
        }
    }
}