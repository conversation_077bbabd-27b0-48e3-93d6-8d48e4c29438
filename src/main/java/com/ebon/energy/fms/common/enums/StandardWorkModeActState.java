package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum StandardWorkModeActState
{
    None(0),
    VoltageToActivePower(1 << 0),
    VoltageToReactivePower(1 << 1),
    FixedPowerFactor(1 << 2),
    FixedReactivePower(1 << 3),
    ActiveVsPowerFactor(1 << 4);

    private int value;


    StandardWorkModeActState(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static StandardWorkModeActState forValue(int value)
    {
        for (StandardWorkModeActState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        StandardWorkModeActState o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<StandardWorkModeActState> result = EnumSet.noneOf(StandardWorkModeActState.class);
        StandardWorkModeActState[] values = StandardWorkModeActState.values();
        for (StandardWorkModeActState e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}