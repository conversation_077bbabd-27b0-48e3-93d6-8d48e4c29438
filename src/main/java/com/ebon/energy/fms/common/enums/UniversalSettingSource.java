package com.ebon.energy.fms.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;

public enum UniversalSettingSource {
    @JsonProperty("Desired")
    DESIRED("Desired"),
    @JsonProperty("Reported")
    REPORTED("Reported"),
    @JsonProperty("Intent")
    INTENT("Intent");

    private final String value;

    UniversalSettingSource(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    @JsonCreator
    public static UniversalSettingSource fromValue(String value) {
        for (UniversalSettingSource source : values()) {
            if (source.value.equalsIgnoreCase(value)) {
                return source;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}
