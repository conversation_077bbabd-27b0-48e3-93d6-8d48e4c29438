package com.ebon.energy.fms.common.enums;

public enum BattType {
    None,               //selectable      // There has never been a battery detected
    Unknown,            // A battery was detected, but the BMS is somehow not responding
    Lead_Acid_Flooded,  // selectable
    Lead_Acid_Sealed,   // selectable
    SimpliPHi_2_6,          // selecteble (former Lithium Ion No BMS)
    Pylon_US2000A,
    Pylon_US2000B,
    Pylon_US2000BPlus,
    Pylon_US3000A,
    Pylon_US3000B,
    _Pylon_V2,
    LG_Resu_V1,
    _LG_V2,
    Redflow_V1,
    AquionS20P,         // selectable
    AquionS30,          // selectable
    AquionAspen48S2_2,  // selectable
    Custom,              // selectable
    SimpliPHi_3_4;
}
