package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterWarningCodeESUpper
{
    PVBoostHardwareOC(101),
    LoadOverload(105),
    GridVoltageSamplingTooHigh(106),
    InvHardwareOrSoftwareSamplingx8(107),
    PVBoostSoftwareOC(108),
    BUSVoltageTooHigh(109),
    OffGridOutputVoltageTooHigh(120),
    OffGridOutputVoltageTooLow(121),
    BUSVoltageTooLow(122),
    PVVoltageToLow(123),
    OffGridBatteryVoltageLowShutdownProtection(125),
    TriggeringInvHardwareSingleOC(126),
    SelfTestBUSSoftStartFail(128),
    NoPVGridConnModeBattDisconShutdownProtection(129),
    GridBackflow(130);

    private int value;

    InverterWarningCodeESUpper(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterWarningCodeESUpper forValue(int value)
    {
        for (InverterWarningCodeESUpper e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InverterWarningCodeESUpper o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
