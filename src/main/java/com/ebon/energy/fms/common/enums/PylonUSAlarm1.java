package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonUSAlarm1
{
    None(0),
    HighCellVoltageDifference(1 << 0),
    MOSFETHighTemp(1 << 1),
    CellLowTemp(1 << 2),
    CellHighTemp(1 << 3),
    CellLowVoltage(1 << 4),
    CellHighVoltage(1 << 5),
    ModuleLowVoltage(1 << 6),
    ModuleHighVoltage(1 << 7);

    private int value;

    PylonUSAlarm1(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonUSAlarm1 forValue(int value)
    {
        for (PylonUSAlarm1 state : PylonUSAlarm1.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonUSAlarm1 o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonUSAlarm1 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}
