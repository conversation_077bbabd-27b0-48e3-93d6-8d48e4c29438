package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum ConfigurationType {
    Electrical(0),
    Network(1),
    DeviceControl(2),
    SystemGeneratedDeviceControl(3),
    Monitor(4);

    private final int value;

    ConfigurationType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static ConfigurationType fromValue(int value) {
        for (ConfigurationType type : ConfigurationType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching ConfigurationType for value: " + value);
    }
}