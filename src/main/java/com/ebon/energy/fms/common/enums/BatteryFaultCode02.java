package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BatteryFaultCode02
{
    None(0),
    ChargingOvercurrent(1 << 0),
    BMSInternalProtection(1 << 3),
    BatteryModuleImbalance(1 << 4),
    FullChargeRequest(1 << 6),
    ForcedChargeRequest(1 << 7);

    private int value;

    BatteryFaultCode02(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BatteryFaultCode02 forValue(int value)
    {
        for (BatteryFaultCode02 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BatteryFaultCode02 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BatteryFaultCode02> result = EnumSet.noneOf(BatteryFaultCode02.class);
        BatteryFaultCode02[] values = BatteryFaultCode02.values();
        for (BatteryFaultCode02 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
