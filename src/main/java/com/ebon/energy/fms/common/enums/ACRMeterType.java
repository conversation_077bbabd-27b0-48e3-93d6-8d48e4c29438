package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum ACRMeterType
{
    Unknown(0),
    ACR1Phase(1),
    ACR3Phase(3);

    private int value;

    private ACRMeterType(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static ACRMeterType forValue(int value)
    {
        for (ACRMeterType e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        ACRMeterType o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}