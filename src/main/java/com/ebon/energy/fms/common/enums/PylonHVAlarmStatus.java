package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonHVAlarmStatus
{
    BatteryCellLowVoltageAlarm(1 << 0),
    BatteryCellHighVoltageAlarm(1 << 1),
    PileLowVoltageAlarm(1 << 2),
    PileHighVoltageAlarm(1 << 3),
    ChargeLowTemperatureAlarm(1 << 4),
    ChargeHighTemperatureAlarm(1 << 5),
    DischargeLowTemperatureAlarm(1 << 6),
    DischargeHighTemperatureAlarm(1 << 7),
    ChargeOverCurrentAlarm(1 << 8),
    DischargeOverCurrentAlarm(1 << 9),

    BMSHighTemperatureAlarm(1 << 11),
    ModuleHighTempertureAlarm(1 << 12),
    ModuleLowVoltageAlarm(1 << 13),
    ModuleHighVoltageAlarm(1 << 14);

    private int value;


    PylonHVAlarmStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonHVAlarmStatus forValue(int value)
    {
        for (PylonHVAlarmStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        PylonHVAlarmStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<PylonHVAlarmStatus> result = EnumSet.noneOf(PylonHVAlarmStatus.class);
        PylonHVAlarmStatus[] values = PylonHVAlarmStatus.values();
        for (PylonHVAlarmStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}