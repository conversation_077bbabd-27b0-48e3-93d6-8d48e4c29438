package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SGMeterStatus
{
    CommsFailed(0),
    CommsOk(1),
    Scanning(2),
    NotFound(3);

    private int value;


    private SGMeterStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static SGMeterStatus forValue(int value)
    {
        for (SGMeterStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SGMeterStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
