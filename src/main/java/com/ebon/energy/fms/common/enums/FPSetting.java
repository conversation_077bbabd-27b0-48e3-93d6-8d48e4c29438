package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FPSetting
{
    OFSlopePowerHold(1 << 0),
    UFSlopePowerHold(1 << 1),
    OFFixSlopePn(1 << 2),
    UFFixSlopePn(1 << 3),
    RecoverFixPower(1 << 4),
    StopModeOFChargeEn(1 << 5),
    StopModeUFDischargeEn(1 << 6),
    OFRecoverPrate(1 << 7),
    UFRecoverPrate(1 << 8),
    OFFixSlopePm(1 << 9),
    UFFixSlopePm(1 << 10);

    private int value;


    FPSetting(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FPSetting forValue(int value)
    {
        for (FPSetting e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FPSetting o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FPSetting> result = EnumSet.noneOf(FPSetting.class);
        FPSetting[] values = FPSetting.values();
        for (FPSetting e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }

    public static void main(String[] args) {
        System.out.println(parse(99));
        System.out.println(parse(499));
    }

}

