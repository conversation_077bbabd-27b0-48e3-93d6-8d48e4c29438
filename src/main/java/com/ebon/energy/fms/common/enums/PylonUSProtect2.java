package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonUSProtect2
{
    None(0),
    Unknown0(1 << 0),
    Unknown1(1 << 1),
    Unknown2(1 << 2),
    BMSError(1 << 3),
    Unknown3(1 << 4),
    DischargeOverCurrent(1 << 5),
    ChargeOverCurrent(1 << 6),
    Unknown7(1 << 7);

    private int value;

    PylonUSProtect2(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonUSProtect2 forValue(int value)
    {
        for (PylonUSProtect2 state : PylonUSProtect2.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonUSProtect2 o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonUSProtect2 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}