package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum H2MasterCtrlFaultMsg2
{
    None(0),
    RelayFault(1 << 0),
    MemoryFault(1 << 1),
    HighTemperature(1 << 2),
    LowTemperature(1 << 3),
    InternalCommsFailure(1 << 4),
    GroundFaultDetectionEquipmentFault(1 << 5),
    DCLeakageDetectionEquipmentFault(1 << 6),
    CurrentDetectionEquipmentFault(1 << 7),
    GridOverVoltageL1(1 << 8),
    GridUnderVoltageL1(1 << 9),
    GridOverVoltageL2(1 << 10),
    GridUnderVoltageL2(1 << 11),
    GridOverVoltageL3(1 << 12),
    GridUnderVoltageL3(1 << 13),
    GridSustainedOverVoltage(1 << 14),
    OffgridOutputUnderVoltage(1 << 15),
    ReservedBit49(1 << 16),
    GridOverFrequency(1 << 17),
    GridUnderFrequency(1 << 18),
    ReservedBit52(1 << 19),
    HighDCVoltageL1(1 << 20),
    HighDCVoltageL2(1 << 21),
    HighDCVoltageL3(1 << 22),
    GridOutage(1 << 23),
    ReservedBit57(1 << 24),
    ReservedBit58(1 << 25),
    GroundLeakageFault(1 << 26),
    HighDCCurrentL1(1 << 27),
    HighDCCurrentL2(1 << 28),
    HighDCCurrentL3(1 << 29),
    IsolationFault(1 << 30),
    BusVoltageUnbalanced(1 << 31);

    private int intValue;


    private H2MasterCtrlFaultMsg2(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static H2MasterCtrlFaultMsg2 forValue(int value)
    {
        for (H2MasterCtrlFaultMsg2 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        H2MasterCtrlFaultMsg2 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<H2MasterCtrlFaultMsg2> result = EnumSet.noneOf(H2MasterCtrlFaultMsg2.class);
        H2MasterCtrlFaultMsg2[] values = H2MasterCtrlFaultMsg2.values();
        for (H2MasterCtrlFaultMsg2 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}