package com.ebon.energy.fms.common.enums;

public enum PowerModeSource
{
    // * Ross does NOT have 'Unspecified' and 'Defaults' is 0
    // * EMS has 'Unspecified' and 'Defaults' is 1
    // This setup works because enums are serialized as strings
    Unspecified,
    Defaults,
    DesiredSettings,
    Schedules,
    Dred,
    AutoCharge,
    SiteCoordinator,
    FullChargeRequest,
    BMSForceChargeRequest,
    BalanceChargeRequest
}
