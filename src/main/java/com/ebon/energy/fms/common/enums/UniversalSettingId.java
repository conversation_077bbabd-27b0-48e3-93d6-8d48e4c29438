// UniversalSettingId.java

package com.ebon.energy.fms.common.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * Java enum equivalent of the C# UniversalSettingId enum.
 * Enum names are in UPPER_SNAKE_CASE as per Java conventions.
 */
@Getter
public enum UniversalSettingId {
    @JsonProperty("ShadowScan")
    SHADOW_SCAN,

    @JsonProperty("GridProfile")
    GRID_PROFILE,

    @JsonProperty("SiteExportLimit")
    SITE_EXPORT_LIMIT,

    @JsonProperty("PowerFactor")
    POWER_FACTOR,

    @JsonProperty("RelaySettings")
    RELAY_SETTINGS,

    @JsonProperty("BatterySettings")
    BATTERY_SETTINGS,

    @JsonProperty("TelemetryPeriod")
    TELEMETRY_PERIOD,

    @JsonProperty("CtFlip")
    CT_FLIP,

    @JsonProperty("ACCoupled")
    AC_COUPLED,

    @JsonProperty("ThirdPartyCtElection")
    THIRD_PARTY_CT_ELECTION,

    @JsonProperty("AS4777_2_GenerationAndExportLimits")
    AS4777_2_GENERATION_AND_EXPORT_LIMITS,

    @JsonProperty("AS4777_2_GenerationLimits")
    AS4777_2_GENERATION_LIMITS,

    @JsonProperty("AS4777_2_ExportLimits")
    AS4777_2_EXPORT_LIMITS,

    @JsonProperty("AS4777_2_GoodWeExportLimit")
    AS4777_2_GOOD_WE_EXPORT_LIMIT,

    /**
     * TODO: At some point we should deprecate this and change references over to SmartLoadControl.
     */
    @JsonProperty("SmartRelaySettings")
    SMART_RELAY_SETTINGS,

    @JsonProperty("TimeZoneAliasSettings")
    TIME_ZONE_ALIAS_SETTINGS,

    @JsonProperty("DredSettings")
    DRED_SETTINGS,

    @JsonProperty("SiteCoordinator")
    SITE_COORDINATOR,

    /**
     * TODO: At some point we should change the rest of the SmartRelaySettings references over to this.
     */
    @JsonProperty("SmartLoadControl")
    SMART_LOAD_CONTROL
}
