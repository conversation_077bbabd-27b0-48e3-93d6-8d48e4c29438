package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.BalancingMethods;
import com.ebon.energy.fms.domain.vo.Energies;
import com.ebon.energy.fms.domain.vo.EnergyBalancingInputs;
import com.ebon.energy.fms.domain.vo.WattHour;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

public class EnergyBalancer {
    
    private static final double BATTERY_REDUCTION_PERCENTAGE = 0.1;

    public static Energies getEnergies(EnergyBalancingInputs inputs) {
        Objects.requireNonNull(inputs, "inputs must not be null");

        return inputs.getSupportsLoadContributors()
                ? applyBalancerMethod(inputs)
                : new Energies(
                inputs,
                inputs.getUsageWh(),
                inputs.getSoldWh(),
                inputs.getBoughtWh(),
                inputs.getGenerationWh(),
                inputs.getBatteryChargedWh(),
                inputs.getBatteryDischargedWh(),
                null,
                null,
                null
        );
    }

    private static Energies applyBalancerMethod(EnergyBalancingInputs inputs) {
        if (inputs.getBalancingMethod() == BalancingMethods.NaiveMethod) {
            return balanceEnergyNaiveMethod(inputs);
        } else if (inputs.getBalancingMethod() == BalancingMethods.FixedGrid) {
            return balanceEnergyFixedGrid(inputs);
        } else if (inputs.getBalancingMethod() == BalancingMethods.FixedConsumption) {
            return balanceEnergyFixedConsumption(inputs);
        }
        return balanceEnergyNaiveMethod(inputs);
    }

    private static Energies balanceEnergyNaiveMethod(EnergyBalancingInputs inputs) {
        WattHour solarWh = Optional.ofNullable(inputs.getGenerationWh())
                .map(wh -> wh.subtract(inputs.getSoldWh()))
                .map(wh -> inputs.getBatteryChargedWh() != null ? wh.subtract(inputs.getBatteryChargedWh()) : wh)
                .orElse(null);

        return new Energies(
                inputs,
                inputs.getUsageWh(),
                inputs.getSoldWh(),
                inputs.getBoughtWh(),
                inputs.getGenerationWh(),
                inputs.getBatteryChargedWh(),
                inputs.getBatteryDischargedWh(),
                solarWh,
                inputs.getBoughtWh(),
                inputs.getBatteryDischargedWh()
        );
    }

    private static Energies balanceEnergyFixedGrid(EnergyBalancingInputs inputs) {
        Energies energies = new Energies(
                inputs,
                inputs.getUsageWh(),
                inputs.getSoldWh(),
                inputs.getBoughtWh(),
                inputs.getGenerationWh(),
                inputs.getBatteryChargedWh(),
                inputs.getBatteryDischargedWh(),
                getSolarContribution(inputs),
                inputs.getBoughtWh(),
                inputs.getBatteryDischargedWh()
        );

        BigDecimal imbalance = getImbalanceValue(energies);

        if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) > 0) {
            if (imbalance.compareTo(BigDecimal.ZERO) > 0) {
                // 场景A：消耗大于供应
                BigDecimal newUsage = energies.getUsageWh().getValue().subtract(imbalance);
                energies.setUsageWh(new WattHour(newUsage.max(BigDecimal.ZERO)));
            } else {
                // 场景B：供应大于消耗
                if (energies.getBatteryContributionToLoadsWh() != null &&
                        energies.getBatteryContributionToLoadsWh().getValue().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal reduction = energies.getBatteryContributionToLoadsWh().getValue()
                            .multiply(BigDecimal.valueOf(BATTERY_REDUCTION_PERCENTAGE))
                            .min(imbalance.abs());
                    energies.setBatteryContributionToLoadsWh(
                            new WattHour(energies.getBatteryContributionToLoadsWh().getValue().subtract(reduction)));
                    imbalance = getImbalanceValue(energies);
                    if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) <= 0) return energies;
                }

                if (energies.getSolarContributionToLoadsWh() != null &&
                        energies.getSolarContributionToLoadsWh().getValue().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal reduction = energies.getSolarContributionToLoadsWh().getValue()
                            .min(imbalance.abs());
                    energies.setSolarContributionToLoadsWh(
                            new WattHour(energies.getSolarContributionToLoadsWh().getValue().subtract(reduction)));
                    imbalance = getImbalanceValue(energies);
                    if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) <= 0) return energies;
                }

                if (energies.getBatteryContributionToLoadsWh() != null &&
                        energies.getBatteryContributionToLoadsWh().getValue().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal reduction = energies.getBatteryContributionToLoadsWh().getValue()
                            .min(imbalance.abs());
                    energies.setBatteryContributionToLoadsWh(
                            new WattHour(energies.getBatteryContributionToLoadsWh().getValue().subtract(reduction)));
                    imbalance = getImbalanceValue(energies);
                    if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) <= 0) return energies;
                }

                energies.setUsageWh(new WattHour(energies.getUsageWh().getValue().add(imbalance.abs())));
            }
        }

        return energies;
    }

    private static Energies balanceEnergyFixedConsumption(EnergyBalancingInputs inputs) {
        Energies energies = new Energies(
                inputs,
                inputs.getUsageWh(),
                inputs.getSoldWh(),
                inputs.getBoughtWh(),
                inputs.getGenerationWh(),
                inputs.getBatteryChargedWh(),
                inputs.getBatteryDischargedWh(),
                getSolarContribution(inputs),
                inputs.getBoughtWh(),
                inputs.getBatteryDischargedWh()
        );

        BigDecimal imbalance = getImbalanceValue(energies);

        if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) > 0) {
            if (imbalance.compareTo(BigDecimal.ZERO) > 0) {
                // 场景A：消耗大于供应，增加电网贡献
                energies.setGridContributionToLoadsWh(
                        new WattHour(energies.getGridContributionToLoadsWh().getValue().add(imbalance)));
            } else {
                // 场景B：供应大于消耗
                if (energies.getBatteryContributionToLoadsWh() != null &&
                        energies.getBatteryContributionToLoadsWh().getValue().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal reduction = energies.getBatteryContributionToLoadsWh().getValue()
                            .multiply(BigDecimal.valueOf(BATTERY_REDUCTION_PERCENTAGE))
                            .min(imbalance.abs());
                    energies.setBatteryContributionToLoadsWh(
                            new WattHour(energies.getBatteryContributionToLoadsWh().getValue().subtract(reduction)));
                    imbalance = getImbalanceValue(energies);
                    if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) <= 0) return energies;
                }

                if (energies.getSolarContributionToLoadsWh() != null &&
                        energies.getSolarContributionToLoadsWh().getValue().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal reduction = energies.getSolarContributionToLoadsWh().getValue()
                            .min(imbalance.abs());
                    energies.setSolarContributionToLoadsWh(
                            new WattHour(energies.getSolarContributionToLoadsWh().getValue().subtract(reduction)));
                    imbalance = getImbalanceValue(energies);
                    if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) <= 0) return energies;
                }

                if (energies.getBatteryContributionToLoadsWh() != null &&
                        energies.getBatteryContributionToLoadsWh().getValue().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal reduction = energies.getBatteryContributionToLoadsWh().getValue()
                            .min(imbalance.abs());
                    energies.setBatteryContributionToLoadsWh(
                            new WattHour(energies.getBatteryContributionToLoadsWh().getValue().subtract(reduction)));
                    imbalance = getImbalanceValue(energies);
                    if (imbalance.abs().compareTo(inputs.getAcceptableImbalance().getValue()) <= 0) return energies;
                }

                energies.setGridContributionToLoadsWh(
                        new WattHour(energies.getGridContributionToLoadsWh().getValue().subtract(imbalance.abs())));
            }
        }

        return energies;
    }

    private static BigDecimal getImbalanceValue(Energies energies) {
        BigDecimal totalSupply = BigDecimal.ZERO;
        totalSupply = totalSupply.add(Optional.ofNullable(energies.getSolarContributionToLoadsWh()).map(WattHour::getValue).orElse(BigDecimal.ZERO));
        totalSupply = totalSupply.add(Optional.ofNullable(energies.getBatteryContributionToLoadsWh()).map(WattHour::getValue).orElse(BigDecimal.ZERO));
        totalSupply = totalSupply.add(Optional.ofNullable(energies.getGridContributionToLoadsWh()).map(WattHour::getValue).orElse(BigDecimal.ZERO));

        return energies.getUsageWh().getValue().subtract(totalSupply);
    }

    private static WattHour getSolarContribution(EnergyBalancingInputs inputs) {
        if (!Optional.ofNullable(inputs.getGenerationWh()).isPresent()) {
            return null;
        }

        BigDecimal solarContribution = inputs.getGenerationWh().getValue()
                .min(inputs.getMaximumPossiblePVOutput().getValue())
                .subtract(inputs.getSoldWh().getValue());

        if (inputs.getBatteryChargedWh() != null) {
            solarContribution = solarContribution.subtract(inputs.getBatteryChargedWh().getValue());
        }

        return solarContribution.compareTo(BigDecimal.ZERO) >= 0 ? new WattHour(solarContribution) : null;
    }
}
