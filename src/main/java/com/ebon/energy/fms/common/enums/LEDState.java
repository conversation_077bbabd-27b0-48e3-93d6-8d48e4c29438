package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum LEDState
{
    Off(1),
    On(2),
    Flash1x(3),
    Flash2x(4),
    Flash4x(5);

    private int value;

    LEDState(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static LEDState forValue(int value)
    {
        for (LEDState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        LEDState o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}