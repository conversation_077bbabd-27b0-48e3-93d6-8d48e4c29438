package com.ebon.energy.fms.common.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 自定义 ZonedDateTime 序列化器，将 ZonedDateTime 序列化为 "yyyy-MM-dd HH:mm:ssZ" 格式，并始终使用UTC时区（Z）
 */
public class CustomZonedDateTimeSerializer extends JsonSerializer<ZonedDateTime> {
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss'Z'");

    @Override
    public void serialize(ZonedDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        
        // 将任何时区的ZonedDateTime转换为UTC时区
        ZonedDateTime utcValue = value.withZoneSameInstant(ZoneOffset.UTC);
        String formattedDate = utcValue.format(FORMATTER);
        gen.writeString(formattedDate);
    }
}
