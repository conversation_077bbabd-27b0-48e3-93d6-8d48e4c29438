package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterWorkModeH2
{
    Initialise(0),
    Wait(1),
    Standby(2),
    OffGrid(3),
    Ongrid(4),
    Fault(5),
    Upgrade(6),
    Debug(7),
    Check(8),
    Reset(9);

    private int value;

    InverterWorkModeH2(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterWorkModeH2 forValue(int value)
    {
        for (InverterWorkModeH2 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InverterWorkModeH2 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
