package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonHVSwitchValueIndicate
{
    DischargeCircuitOn(1 << 0),
    ChargeCircuitOn(1 << 1),
    PreChargeCircuitOn(1 << 2),
    BuzzerOn(1 << 3),
    HeatingFilmOn(1 << 4),
    CurrentLimitingModuleOn(1 << 5),
    FanOn(1 << 6);

    private int value;


    PylonHVSwitchValueIndicate(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonHVSwitchValueIndicate forValue(int value)
    {
        for (PylonHVSwitchValueIndicate e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        PylonHVSwitchValueIndicate o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<PylonHVSwitchValueIndicate> result = EnumSet.noneOf(PylonHVSwitchValueIndicate.class);
        PylonHVSwitchValueIndicate[] values = PylonHVSwitchValueIndicate.values();
        for (PylonHVSwitchValueIndicate e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
