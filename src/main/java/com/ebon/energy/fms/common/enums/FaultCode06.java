package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode06
{
    None(0),
    SlaveLoseErr(1 << 0),
    MasterLoseErr(1 << 1),
    SlavePrdErr(1 << 2),
    MasterPrdErr(1 << 3),
    AddrConflict(1 << 4),
    HeartbeatLose(1 << 5),
    DCanErr(1 << 6),
    MulMasterErr(1 << 7),
    ModeConflict(1 << 8),
    SPlugVoltErr(1 << 9),
    OtherFault(1 << 10),
    CANBusFault(1 << 11),
    ModelMismatch(1 << 12); //13,14,15 Reserved

    private int value;


    FaultCode06(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode06 forValue(int value)
    {
        for (FaultCode06 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode06 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FaultCode06> result = EnumSet.noneOf(FaultCode06.class);
        FaultCode06[] values = FaultCode06.values();
        for (FaultCode06 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
