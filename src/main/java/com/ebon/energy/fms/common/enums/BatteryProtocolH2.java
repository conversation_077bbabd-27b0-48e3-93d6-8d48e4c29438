package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BatteryProtocolH2
{
    NoBattery(0),
    LeadAcidBattery(2),
    D<PERSON>ss(20),
    Pylon(21),
    B2_SAJ(22),
    RED_P1_BMSHV(26);

    private int value;

    BatteryProtocolH2(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BatteryProtocolH2 forValue(int value)
    {
        for (BatteryProtocolH2 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BatteryProtocolH2 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BatteryProtocolH2> result = EnumSet.noneOf(BatteryProtocolH2.class);
        BatteryProtocolH2[] values = BatteryProtocolH2.values();
        for (BatteryProtocolH2 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
