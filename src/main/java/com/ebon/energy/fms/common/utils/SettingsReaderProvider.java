package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.SettingsProtocol;
import com.ebon.energy.fms.common.enums.SettingsType;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import lombok.NonNull;

public class SettingsReaderProvider {

    public static ICommonSettingsReader get(@NonNull DeviceInfoAndSettings deviceSettingsDto) {
        if (deviceSettingsDto.getReported() == null) {
            throw new RuntimeException("Unable to establish type of device");
        }

        ICommonSettingsReader settingsReader = tryGet(deviceSettingsDto);
        if (settingsReader != null) {
            return settingsReader;
        } else {
            throw new RuntimeException("Unable to determine which settings reader to use. Please make sure your device has a valid ROSS version number.");
        }
    }

    public static ICommonSettingsReader tryGet(DeviceInfoAndSettings deviceSettingsDto) {
        if (deviceSettingsDto == null || deviceSettingsDto.getReported() == null) {
            return null;
        }

        SettingsProtocol protocol = deviceSettingsDto.getReported().getProtocol();
        if (protocol == SettingsProtocol.V2) {
            Integer major = deviceSettingsDto.getIdentityCard() != null
                    && deviceSettingsDto.getIdentityCard().getSofterVersion() != null
                    && deviceSettingsDto.getIdentityCard().getSofterVersion().getRossVersionNumber() != null
                    ? deviceSettingsDto.getIdentityCard().getSofterVersion().getRossVersionNumber().getMajor()
                    : null;

            if (major == null) {
                return null;
            }

            switch (major) {
                case 2:
                    return new RossSettingsReader(deviceSettingsDto);
                case 3:
                    return new SGSettingsReader(deviceSettingsDto);
                case 4:
                    ModelInfo model = deviceSettingsDto.getIdentityCard().getModelInfo();
                    if (model == null) {
                        return null;
                    }
                    SettingsType settingsType = model.getHardwareModel().getSettingsTypeEnum();
                    if (settingsType == null || settingsType == SettingsType.None) {
                        return null;
                    }
                    switch (settingsType) {
                        case ESETSG:
                            return new RossSettingsReader(deviceSettingsDto);
                        case SG:
                            return new SGSettingsReader(deviceSettingsDto);
                        case ESG:
                            return new ESGSettingsReader(deviceSettingsDto);
                        case EH1P:
                            return new EH1PSettingsReader(deviceSettingsDto);
                        case HVH3P:
                            return new HVH3PSettingsReader(deviceSettingsDto);
                        default:
                            throw new RuntimeException("No builder for model '" + model + "'");
                    }
                default:
                    return null;
            }
        } else {
            // Default case
            return new RossSettingsReader(deviceSettingsDto);
        }
    }
}
