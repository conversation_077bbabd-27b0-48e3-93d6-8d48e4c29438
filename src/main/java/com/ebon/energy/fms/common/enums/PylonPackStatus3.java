package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonPackStatus3 {
    None(0),
    <PERSON><PERSON>(1 << 0),
    FullyCharged(1 << 1),
    StartupHeater(1 << 2),
    EffDischargeCurrent(1 << 3),
    EffChargeCurrent(1 << 4);

    private final int value;

    PylonPackStatus3(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PylonPackStatus3 fromValue(int value) {
        for (PylonPackStatus3 state : PylonPackStatus3.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }
        
        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonPackStatus3 o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonPackStatus3 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}