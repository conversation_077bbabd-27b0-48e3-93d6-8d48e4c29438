package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum StandardOperatingMode
{
    NoResponseMode(0),
    VoltWattDefault(1),
    VoltVar(2),
    FixedPowerFactor(3),
    FixedReactivePower(4),
    PowerPF(5),
    Rule21VoltWatt(6),
    IEEE1547_2018(12);

    private int value;


    StandardOperatingMode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static StandardOperatingMode forValue(int value)
    {
        for (StandardOperatingMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        StandardOperatingMode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
