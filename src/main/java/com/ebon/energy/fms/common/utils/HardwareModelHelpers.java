package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.enums.HardwareModelEnum;

import java.util.List;
import java.util.regex.Pattern;

public class HardwareModelHelpers {
    private static final Pattern TRAILING_SPACE_PATTERN = Pattern.compile("[^a-zA-Z0-9_]+");
    public static final List<HardwareModelEnum> SIs = List.of(HardwareModelEnum.SI5000, HardwareModelEnum.SI6000, HardwareModelEnum.SI8000, HardwareModelEnum.SI10000 );
    public static HardwareModelEnum parseModelName(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            return HardwareModelEnum.Unknown;
        }
        // 替换非字母数字或下划线为下划线
        String cleanedModelName = TRAILING_SPACE_PATTERN.matcher(modelName.trim().replace(" ", "")).replaceAll("_");
        return HardwareModelEnum.fromName(cleanedModelName);
    }

    // 判断是否为 Gen3（此处仅示例，具体实现需结合实际属性）
    public static boolean isGen3(HardwareModelEnum model) {
        // 这里假设 EMSPro 类型为 Gen3
        return model == HardwareModelEnum.SH5000_G3 ||
               model == HardwareModelEnum.SH6000_G3 ||
               model == HardwareModelEnum.EH1P5K ||
               model == HardwareModelEnum.EH1P6K ||
               model == HardwareModelEnum.RED_H3_T10HV ||
               model == HardwareModelEnum.RED_H3_T15HV ||
               model == HardwareModelEnum.RED_H3_T20HV ||
               model == HardwareModelEnum.RED_H3_T30HV;
    }

    /**
     * 根据序列号解析硬件型号
     */
    public static HardwareModelEnum determineFromSerialNumber(String serialNumber) {
        if (serialNumber == null || serialNumber.trim().length() < 16) {
            return HardwareModelEnum.Unknown;
        }
        String modelDesignation = serialNumber.substring(8, 12);
        int modelVersion;
        try {
            modelVersion = Integer.parseInt(modelDesignation);
        } catch (NumberFormatException e) {
            return HardwareModelEnum.Unknown;
        }
        int code = modelVersion - 100;
        if (code < 100) return HardwareModelEnum.SH4600;
        if (code == 124) return HardwareModelEnum.SH5000_N;
        if (code < 130) return HardwareModelEnum.SH5000;
        if (code < 200) return HardwareModelEnum.SH5000v2;
        if (code == 213) return HardwareModelEnum.ST10000_N;
        if (code < 300) return HardwareModelEnum.ST10000;
        if (code < 400) return HardwareModelEnum.SB7200;
        if (code == 412) return HardwareModelEnum.SI5000_N;
        if (code < 500) return HardwareModelEnum.SI5000;
        if (code == 512) return HardwareModelEnum.SI6000_N;
        if (code < 600) return HardwareModelEnum.SI6000;
        if (code == 612) return HardwareModelEnum.SI8000_N;
        if (code < 700) return HardwareModelEnum.SI8000;
        if (code == 712) return HardwareModelEnum.SI10000_N;
        if (code < 800) return HardwareModelEnum.SI10000;
        if (code < 900) return HardwareModelEnum.SB9600;
        if (code < 1000) return HardwareModelEnum.SB14200;
        if (code < 1100) return HardwareModelEnum.SH5000_G3;
        if (code < 1200) return HardwareModelEnum.EH1P5K;
        if (code < 1300) return HardwareModelEnum.EH1P6K;
        return HardwareModelEnum.Unknown;
    }
}