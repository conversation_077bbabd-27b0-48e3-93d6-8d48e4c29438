package com.ebon.energy.fms.common.enums;

public enum PhaseRole {

    <PERSON><PERSON><PERSON>("Master"),

    Coordinator("Master"),

    Worker("Worker"),

    Incompatible("Incompatible"),

    GridTie("Grid Tie");

    private final String displayName;

    PhaseRole(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static String getDisplayNameFromName(String name) {
        for (PhaseRole e : values()) {
            if (e.name().equalsIgnoreCase(name)) {
                return e.getDisplayName();
            }
        }
        
        return name;
    }

}