package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum H2DisplayFaultMsg
{
    None(0),
    InternalCommsFailureMasterSlave(1 << 0),
    MeterCommsLoss(1 << 1),
    MemoryFault(1 << 2),
    RTCFault(1 << 3),
    BMSFault(1 << 4),
    BMSCommsLost(1 << 5),
    CTEquipmentFault(1 << 6),
    AFCIBoardCommsLoss(1 << 7),
    ReservedBit73(1 << 8),
    ReservedBit74(1 << 9),
    ReservedBit75(1 << 10),
    GridOverVoltageL1(1 << 11),
    GridUnderVoltageL1(1 << 12),
    GridOverVoltageL2(1 << 13),
    GridUnderVoltageL2(1 << 14),
    GridOverVoltageL3(1 << 15),
    GridUnderVoltageL3(1 << 16),
    GridOverFrequency(1 << 17),
    GridUnderFrequency(1 << 18),
    ReservedBit84(1 << 19),
    ReservedBit85(1 << 20),
    ReservedBit86(1 << 21),
    ReservedBit87(1 << 22),
    GridOutage(1 << 23),
    PVInputModeFault(1 << 24),
    PVHardwareOverCurrent(1 << 25),
    SlavePVOverVoltage(1 << 26),
    BusHardwareOverVoltage(1 << 27),
    ReservedBit93(1 << 28),
    ReservedBit94(1 << 29),
    ReservedBit95(1 << 30),
    ReservedBit96(1 << 31);

    private int intValue;


    private H2DisplayFaultMsg(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static H2DisplayFaultMsg forValue(int value)
    {
        for (H2DisplayFaultMsg e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        H2DisplayFaultMsg o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<H2DisplayFaultMsg> result = EnumSet.noneOf(H2DisplayFaultMsg.class);
        H2DisplayFaultMsg[] values = H2DisplayFaultMsg.values();
        for (H2DisplayFaultMsg e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
