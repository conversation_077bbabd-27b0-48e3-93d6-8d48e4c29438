package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum NationalStandards
{
    G83_2_G59_3(0),
    UL_240V(1),
    VDE0126(2),
    AS4777(3),
    AS4777_NQ(4),
    CQC(5),
    ENEL(6),
    UL_208V(7),
    MEX_CFE(8),
    Custom(9),
    VDE4105(10),
    EN50438DK(11),
    EN50438IE(12),
    EN50438NL(13),
    EN50438T(14),
    EN50438L(15),
    UL_240V_A(16),
    UL_208V_A(17),
    BRAZIL(18),
    AUS_Q_0_9(19), //use '_' instead of '.';AUS_Q_0.9
    AUS_Q_0_8(20), //use '_' instead of '.'; AUS_Q_0.8
    G83_1(21),
    RD1699B(22),
    IEC61727(23),
    G59_3(24),
    UL_HECO(25),
    NewZeal(26),
    Barbados(27),
    Chile(28),
    France(29),
    CQCB_GNB(30),
    CQCC_GNC(31),
    Philippin(32);

    private int value;


    NationalStandards(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static NationalStandards forValue(int value)
    {
        for (NationalStandards e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        NationalStandards o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}