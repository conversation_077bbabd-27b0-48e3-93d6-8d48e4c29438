package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum ExtInvDetailedErr
{
    NoError(0),
    BattLLCHardOCErr(1L << 0),
    BattBoostHardwareOCErr(1L << 1),
    BattBoostSoftwareOCErr(1L << 2),
    BattBMSFaultErr(1L << 3),
    BattBMSDischgDisableErr(1L << 4),
    BattRmsOCErr(1L << 5),
    OffgridBmsCurrLimitErr(1L << 6),
    BusSoftStartFailedErr(1L << 7),
    BusVoltTooLowErr(1L << 8),
    BusSampVoltTooHighErr(1L << 9),
    InvHardwareOCErr(1L << 10),
    InvSoftwareOCErr(1L << 11),
    PvBoostHardwareOCErr(1L << 12),
    PvBoostSoftwareOCErr(1L << 13),
    GridBackflowErr(1L << 14),
    OffgridBattVoltLowErr(1L << 15),
    OffgridUpsVoltHighErr(1L << 16),
    OffgridUpsVoltLowErr(1L << 17),
    UpsOverLoadErr(1L << 18),
    OffGridZeroLossErr(1L << 19),
    PowerFastRetrackErr(1L << 20),
    BypassRelaySwErr(1L << 21),
    LoadRelaySwErr(1L << 22),
    Bit23(1L << 23),
    Bit24(1L << 24),
    Bit25(1L << 25),
    Bit26(1L << 26),
    Bit27(1L << 27),
    Bit28(1L << 28),
    Bit29(1L << 29),
    Bit30(1L << 30),
    Bit31(1L << 31),
    Bit32(1L << 32),
    Bit33(1L << 33),
    Bit34(1L << 34),
    Bit35(1L << 35),
    Bit36(1L << 36),
    Bit37(1L << 37),
    Bit38(1L << 38),
    Bit39(1L << 39),
    Bit40(1L << 40),
    Bit41(1L << 41),
    Bit42(1L << 42),
    Bit43(1L << 43),
    Bit44(1L << 44),
    Bit45(1L << 45),
    Bit46(1L << 46),
    Bit47(1L << 47),
    Bit48(1L << 48),
    Bit49(1L << 49),
    Bit50(1L << 50),
    Bit51(1L << 51),
    Bit52(1L << 52),
    Bit53(1L << 53),
    Bit54(1L << 54),
    Bit55(1L << 55),
    Bit56(1L << 56),
    Bit57(1L << 57),
    Bit58(1L << 58),
    Bit59(1L << 59),
    Bit60(1L << 60),
    Bit61(1L << 61),
    Bit62(1L << 62),
    Bit63(1L << 63);

    private long longValue;

    private ExtInvDetailedErr(long value)
    {
        longValue = value;
    }

    public long getValue()
    {
        return longValue;
    }

    public static ExtInvDetailedErr fromValue(long value) {
        for (ExtInvDetailedErr aa55 : ExtInvDetailedErr.values()) {
            if (aa55.getValue() == value) {
                return aa55;
            }
        }

        return null;
    }

    public static Object parse(long value) {
        ExtInvDetailedErr o = fromValue(value);
        if (o != null) {
            return o;
        }

        Set<ExtInvDetailedErr> result = EnumSet.noneOf(ExtInvDetailedErr.class);
        ExtInvDetailedErr[] values = ExtInvDetailedErr.values();
        for (ExtInvDetailedErr e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}