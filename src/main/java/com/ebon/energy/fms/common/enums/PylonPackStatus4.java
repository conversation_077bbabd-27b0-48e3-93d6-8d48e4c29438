package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonPackStatus4 {
    None(0),
    Check<PERSON>ell1(1 << 0),
    CheckCell2(1 << 1),
    CheckCell3(1 << 2),
    CheckCell4(1 << 3),
    CheckCell5(1 << 4),
    CheckCell6(1 << 5),
    CheckCell7(1 << 6),
    CheckCell8(1 << 7);

    private final int value;

    PylonPackStatus4(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PylonPackStatus4 fromValue(int value) {
        for (PylonPackStatus4 state : PylonPackStatus4.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonPackStatus4 o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonPackStatus4 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}