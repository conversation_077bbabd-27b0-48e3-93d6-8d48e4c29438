package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum CPLDWarningCode {
    None(0),
    PV1_OverCurrent_HW(1),
    PV2_OverCurrent_HW(2),
    BatteryOverCurrent_HW(3),
    BusOverVoltage_HW(4),
    L1_InvOverCurr_HW(5),
    L2_InvOverCurr_HW(6),
    L3_InvOverCurr_HW(7),
    BatteryRelayFailure(8);

    private int value;

    CPLDWarningCode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static CPLDWarningCode forValue(int value) {
        for (CPLDWarningCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        CPLDWarningCode o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}
