package com.ebon.energy.fms.common.enums;


public enum InverterCommsStatus 
{
	/** 
	 No inverter communications has been attempted yet
	*/
	Untested((byte)0),

	/** 
	 Communications via the current inverter adapter failed to produce a valid response.
	*/
	Failed((byte)2),

	/** 
	 Communications via the current inverter adapter provided a fallback "something is wrong" response.
	*/
	Degraded((byte)3),

	/** 
	 Communications via the current inverter adapter completed successfully.
	*/
	Success((byte)4),

	/** 
	 No communications possible in the current context.
	*/
	None((byte)5),

	/** 
	 No information available to determine inverter communication status.
	*/
	Unknown((byte)6);

	private byte byteValue;


	InverterCommsStatus(byte value)
	{
		byteValue = value;
	}

	public byte getValue()
	{
		return byteValue;
	}

	public static InverterCommsStatus forValue(byte value)
	{
		for (InverterCommsStatus e : values()) {
			if (e.getValue() == value) {
				return e;
			}
		}

		return null;
	}
}
