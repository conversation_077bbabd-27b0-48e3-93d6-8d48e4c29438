package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterWorkMode
{
    NoMode(0),
    WaitMode(0x01), // cut off all the connection to inverter, manual set
    OnlineMode(0x02), // PV input to inverter, inverter input to grid, auto mode
    Offline(0x04), // PV inputs to inverter (first),  Battery input to inverter (seconds), nverter works as AC source
    Fault(0x10); // something wrong

    private int value;

    InverterWorkMode(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterWorkMode forValue(int value)
    {
        for (InverterWorkMode e : InverterWorkMode.values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        int unsignedByte = Byte.toUnsignedInt((byte) value);
        InverterWorkMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (InverterWorkMode mode : values()) {
            if (mode.value != 0 && (remaining & mode.value) == mode.value) {
                activeFlags.add(mode.name());
                remaining &= ~mode.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }

    // 测试代码
    public static void main(String[] args) {
        test(0);    // NoMode
        test(1);    // WaitMode
        test(2);    // OnlineMode
        test(3);    // WaitMode, OnlineMode
        test(4);    // Offline
        test(7);    // WaitMode, OnlineMode, Offline
        test(8);    // 8
        test(16);   // Fault
        test(19);   // WaitMode, OnlineMode, Fault
        test(32);    // 32
        test(255);  // 255
        test(65535); //255
    }

    private static void test(int value) {
        System.out.printf("%3d (0x%02X) -> %s%n",
                value, value, parse(value));
    }

}