package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * LVControlMethod 枚举，表示低压控制方法。
 */
@Getter
public enum LVControlMethod {
    /**
     * 默认方法：
     * SOC = (CurrentCapacity / TotalCapacity) 的平均值。
     * charge limit = chargLimitMin * 电池数量
     * discharge limit = dischargelimitmin * 电池数量
     */
    OLD_METHOD(0),

    /**
     * 求和方法：
     * SOC = 所有剩余容量之和 / 所有额定容量之和
     * charge limit = 所有电池的充电上限之和
     * discharge limit = 所有电池的放电上限之和
     */
    SUMMED_METHOD(1);

    private final int value;

    LVControlMethod(int value) {
        this.value = value;
    }
}
