package com.ebon.energy.fms.common.enums;

public enum SGConnectionState {
    Unknown(0),
    None(1),
    LocalAccess(2),
    ConstrainedInternetAccess(3),
    InternetAccess(4);

    private final int value;

    SGConnectionState(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static SGConnectionState fromValue(int value) {
        for (SGConnectionState state : SGConnectionState.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }
       
        return null;
    }

    public static Object parse(int value) {
        SGConnectionState o = fromValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}