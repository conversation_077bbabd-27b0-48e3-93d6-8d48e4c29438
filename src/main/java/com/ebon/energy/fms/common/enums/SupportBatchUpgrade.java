package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SupportBatchUpgrade
{
    NotSupportedDSP(0),
    SupportedDSP(1),
    Load(2),
    Neutral(3);

    private int value;

    SupportBatchUpgrade(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static SupportBatchUpgrade forValue(int value)
    {
        for (SupportBatchUpgrade e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SupportBatchUpgrade o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}