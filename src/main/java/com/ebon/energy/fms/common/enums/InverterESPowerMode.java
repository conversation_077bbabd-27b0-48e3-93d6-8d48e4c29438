package com.ebon.energy.fms.common.enums;

public enum InverterESPowerMode
{
    NoMode(0),
    Auto(1),
    ChargeBattery(2), // Watt
    DischargeBattery(3), // Watt
    ImportPower(4), // Watt
    ExportPower(5), // <PERSON>
    Conserve(6),
    Offgrid(7),
    // ???? = 8,
    Hibernate(9),
    ChargePlusPV(10),
    BuyPower(11),
    SellPower(12),
    Stop(255);

    private int value;


    InverterESPowerMode(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterESPowerMode forValue(int value)
    {
        for (InverterESPowerMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        InverterESPowerMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}
