package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

@JSONType(deserializer = GoodWeSiteExportLimitType.GoodWeSiteExportLimitTypeDeserializer.class)
public enum GoodWeSiteExportLimitType {

    NoLimit(0),
    SoftLimit(1),
    HardLimit(2),
    GoodWeSpecial(3),
    FlexibleSoftLimit(4);

    private final int value;

    GoodWeSiteExportLimitType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    class GoodWeSiteExportLimitTypeDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                int intValue = (Integer) value;
                for (GoodWeSiteExportLimitType item : GoodWeSiteExportLimitType.values()) {
                    if (item.value == intValue) {
                        return (T) item;
                    }
                }
            } else if (value instanceof String) {
                String strValue = (String) value;
                for (GoodWeSiteExportLimitType item : GoodWeSiteExportLimitType.values()) {
                    if (item.name().equals(strValue)) {
                        return (T) item;
                    }
                }
            }
            throw new JSONException("无法解析枚举 GoodWeSiteExportLimitType: " + value);
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

}
