package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum EnergyFlowIssue {
    LowValuesMayBeInacurrate(1),
    BigAdjustment(2),
    OnGridButNoCt(3),
    NoBatteriesOffgridPVLoadMismatch(4),
    IncoherentGridData(5),
    ConfiguredWithBatteriesFalseButBatteryDataPresent(6),
    ConfiguredWithBatteriesTrueButNoBatteryData(7),
    BatteryDataWrongDirection(8),
    SoCOutOfBounds(9),
    NegativeLoad(10),
    NegativePV(11),
    Negative3rdParty(12),
    BatteryDataMissing(13),
    SupportsConnectedPVFalseButPVP(14),
    LowBatteryPowerMayBeInacurrate(15),
    UnknownBatteryStatus(16),
    BatteryPowerButStatusDisconnected(17),
    BatteryPowerButStatusIdle(18),
    NegativeMainLoad(19),
    NegativeBackupLoad(20),
    LowMainLoadValueMayBeInacurrate(21),
    LowBackupLoadValueMayBeInacurrate(22),
    LowPVValueMayBeInacurrate(23),
    LowThirdPartyWValueMayBeInacurrate(24),
    LowGridValueMayBeInacurrate(25),
    LowBatteryValueMayBeInacurrate(26),
    BalancingAdjustmentToPV(27),
    BalancingAdjustmentToThirdPartyPV(28),
    BalancingAdjustmentToACLoad(29),
    BalancingAdjustmentToBackupLoad(30),
    BalancingAdjustmentToBatteryPower(31),
    BalancingAdjustmentToGridPower(32),
    BalancingAdjustmentInsufficient(33);

    private final int value;

    EnergyFlowIssue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    // 通过值转化枚举的方法
    public static EnergyFlowIssue fromValue(int value) {
        for (EnergyFlowIssue issue : EnergyFlowIssue.values()) {
            if (issue.value == value) {
                return issue;
            }
        }
        throw new IllegalArgumentException("No EnergyFlowIssue found with value: " + value);
    }
}
