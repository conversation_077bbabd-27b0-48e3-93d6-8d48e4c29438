package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SGRebootCode {
    None(0),
    Unknown(1),
    PowerOutage(2),
    FirmwareUpdate(3),
    Error(4);

    private int value;


    SGRebootCode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static SGRebootCode forValue(int value) {
        for (SGRebootCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SGRebootCode o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}
