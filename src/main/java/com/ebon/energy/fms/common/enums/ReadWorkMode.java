package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum ReadWorkMode
{
    Wait(0),
    Normal(1),
    Fault(2),
    PermanentFault(3);

    private int value;


    private ReadWorkMode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static ReadWorkMode forValue(int value)
    {
        for (ReadWorkMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        ReadWorkMode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}