package com.ebon.energy.fms.common.enums;

public enum GridWaveQualityLevelCheck
{
    /**
     Stringent check on grid frequency
     */
    High(0),

    /**
     Relaxed check of grid frequency
     */
    Low(1),

    /**
     Do not check, used for generators
     */
    Close(2);

    private int value;


    private GridWaveQualityLevelCheck(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static GridWaveQualityLevelCheck forValue(int value)
    {
        for (GridWaveQualityLevelCheck e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        GridWaveQualityLevelCheck o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}