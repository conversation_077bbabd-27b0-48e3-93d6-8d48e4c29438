package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum EquipmentClassFaultCode
{
    None((short)0),
    GridVoltageSamplingError(1 << 0);

    private int value;


    private EquipmentClassFaultCode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static EquipmentClassFaultCode forValue(int value)
    {
        for (EquipmentClassFaultCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        EquipmentClassFaultCode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<EquipmentClassFaultCode> result = EnumSet.noneOf(EquipmentClassFaultCode.class);
        EquipmentClassFaultCode[] values = EquipmentClassFaultCode.values();
        for (EquipmentClassFaultCode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
