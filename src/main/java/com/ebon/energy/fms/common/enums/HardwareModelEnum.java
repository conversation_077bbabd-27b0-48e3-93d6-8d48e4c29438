package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.annotation.JSONCreator;
import lombok.Getter;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

@JSONType(deserializer = HardwareModelEnum.HardwareModelDeserializer.class)
public enum HardwareModelEnum {
    Unknown(0, BoardType.Ouija, SettingsType.None),

    Unsupported(1, BoardType.Ouija, SettingsType.None),

    None(5, BoardType.Ouija, SettingsType.None),

    SH4600(10, BoardType.O<PERSON>ja, SettingsType.ESETSG),

    SH5000(11, BoardType.Ouija, SettingsType.ESETSG),

    SH5000v2(12, BoardType.Ouija, SettingsType.ESETSG),

    SH5000_N(13, BoardType.Ouija, SettingsType.ESETSG),

    SH5000_G3(14, BoardType.EMSPro, SettingsType.ESG),

    SH6000_G3(15, BoardType.EMSPro, SettingsType.ESG),

    GW10K_ET(20, BoardType.Ouija, SettingsType.ESETSG),

    GW10K_BT(21, BoardType.Ouija, SettingsType.ESETSG),

    ST10000(22, BoardType.Ouija, SettingsType.ESETSG),

    GW10KL_ET(23, BoardType.Ouija, SettingsType.ESETSG),

    GW10KL_BT(24, BoardType.Ouija, SettingsType.ESETSG),

    GW10K_ET_N(25, BoardType.Ouija, SettingsType.ESETSG),

    ST10000_N(27, BoardType.Ouija, SettingsType.ESETSG),

    GW10KL_ET_N(28, BoardType.Ouija, SettingsType.ESETSG),

    SB7200(31, BoardType.Ouija, SettingsType.ESETSG),

    GW5000_BH(32, BoardType.Ouija, SettingsType.ESETSG),

    GW6000_BH(33, BoardType.Ouija, SettingsType.ESETSG),

    SB9600(34, BoardType.Ouija, SettingsType.ESETSG),

    SB14200(35, BoardType.Ouija, SettingsType.ESETSG),

    SI5000(51, BoardType.EMS, SettingsType.SG),

    SI6000(52, BoardType.EMS, SettingsType.SG),

    SI8000(53, BoardType.EMS, SettingsType.SG),

    SI10000(54, BoardType.EMS, SettingsType.SG),

    RED_I3_T10(56, BoardType.EMS, SettingsType.SG),

    RED_I3_T15(57, BoardType.EMS, SettingsType.SG),

    SI5000_N(61, BoardType.EMS, SettingsType.SG),

    SI6000_N(62, BoardType.EMS, SettingsType.SG),

    SI8000_N(63, BoardType.EMS, SettingsType.SG),

    SI10000_N(64, BoardType.EMS, SettingsType.SG),

    EH1P5K(200, BoardType.EMSPro, SettingsType.EH1P),

    EH1P6K(201, BoardType.EMSPro, SettingsType.EH1P),

    RED_H3_T10HV(210, BoardType.EMSPro, SettingsType.HVH3P),

    RED_H3_T15HV(211, BoardType.EMSPro, SettingsType.HVH3P),

    RED_H3_T20HV(212, BoardType.EMSPro, SettingsType.HVH3P),

    RED_H3_T30HV(213, BoardType.EMSPro, SettingsType.HVH3P),

    Pylon2000(100, null, SettingsType.None),

    Pylon2000A(101, null, SettingsType.None),

    Pylon2000B(102, null, SettingsType.None),

    Pylon3000B(103, null, SettingsType.None),

    LG_RESU_3_3(104, null, SettingsType.None),

    LG_RESU_6_5(105, null, SettingsType.None),

    LG_RESU_10(106, null, SettingsType.None),

    LGM48_3_3(107, null, SettingsType.None),

    Pylon5000(108, null, SettingsType.None),

    PylonLV(109, null, SettingsType.None),

    PylonUS(110, null, SettingsType.None),

    PylonUS5000(111, null, SettingsType.None),

    US5000(112, null, SettingsType.None),

    US3000C(113, null, SettingsType.None),

    RED_R1_5000LV(130, null, SettingsType.None),

    PylonHV(1000, null, SettingsType.None),

    Pylon3000C(1004, null, SettingsType.None);

    @Getter
    private final int value;
    @Getter
    private final BoardType boardType;
    private final SettingsType settingsType;

    HardwareModelEnum(int value, BoardType boardType, SettingsType SettingsType) {
        this.value = value;
        this.boardType = boardType;
        this.settingsType = SettingsType;
    }

    public SettingsType getSettingsTypeEnum() {
        return settingsType;
    }

    public static HardwareModelEnum fromValue(int value) {
        for (HardwareModelEnum e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching hardware model for value: " + value);
    }

    // 通过名称查找
    public static HardwareModelEnum fromName(String name) {
        for (HardwareModelEnum model : HardwareModelEnum.values()) {
            if (model.name().equalsIgnoreCase(name)) {
                return model;
            }
        }
        return Unknown;
    }

    public static class HardwareModelDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                int intValue = (Integer) value;
                for (HardwareModelEnum item : HardwareModelEnum.values()) {
                    if (item.value == intValue) {
                        return (T) item;
                    }
                }
            } else if (value instanceof String) {
                String strValue = (String) value;
                for (HardwareModelEnum item : HardwareModelEnum.values()) {
                    if (item.name().equals(strValue)) {
                        return (T) item;
                    }
                }
            }
            throw new JSONException("无法解析枚举 HardwareModel: " + value);
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }
}