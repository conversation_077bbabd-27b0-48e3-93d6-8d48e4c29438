package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BMSStatusCodeET
{
    NoStatus(0),
    ForceCharge(1 << 0),
    StopCharge(1 << 1),
    StopDischarge(1 << 2);

    private int value;

    BMSStatusCodeET(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BMSStatusCodeET forValue(int value)
    {
        for (BMSStatusCodeET e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BMSStatusCodeET o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BMSStatusCodeET> result = EnumSet.noneOf(BMSStatusCodeET.class);
        BMSStatusCodeET[] values = BMSStatusCodeET.values();
        for (BMSStatusCodeET e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}