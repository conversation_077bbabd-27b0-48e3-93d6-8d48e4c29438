package com.ebon.energy.fms.common.enums;

public enum SGInverterMode
{
    Initial(0x00),
    Standby(0x01),
    Check(0x02),
    Online(0x03),
    Fault(0x05),
    Shutdown(0x09);

    private int value;

    SGInverterMode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static SGInverterMode forValue(int value)
    {
        for (SGInverterMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SGInverterMode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
