package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonPackStatus5 {
    None(0),
    CheckCell9(1 << 0),
    CheckCell10(1 << 1),
    CheckCell11(1 << 2),
    CheckCell12(1 << 3),
    Check<PERSON>ell13(1 << 4),
    Check<PERSON>ell14(1 << 5),
    CheckCell15(1 << 6),
    CheckCell16(1 << 7);

    private final int value;

    PylonPackStatus5(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PylonPackStatus5 fromValue(int value) {
        for (PylonPackStatus5 state : PylonPackStatus5.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }
        
        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonPackStatus5 o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonPackStatus5 state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }

}