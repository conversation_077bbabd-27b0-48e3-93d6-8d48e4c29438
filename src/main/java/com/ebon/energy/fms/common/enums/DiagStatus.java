package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum DiagStatus
{
    None(0),
    BatteryVoltLow(1 << 0),
    BatterySOCLow(1 << 1),
    BatterySOCInBack(1 << 2),
    BMSDischargeDisable(1 << 3),
    DischargeTimeOn(1 << 4),
    ChargeTimeOn(1 << 5),
    DischargeDriveOn(1 << 6),
    BMSDischgCurrentLow(1 << 7),
    DischargeCurrentLow(1 << 8),
    MeterCommLoss(1 << 9),
    MeterConnectReverse(1 << 10),
    SelfUseLoadLight(1 << 11),
    EMSDischargeIZero(1 << 12),
    DischargeBUSHigh(1 << 13),
    BatteryDisconnect(1 << 14),
    BatteryOvercharge(1 << 15),
    BMSOverTemperature(1 << 16),
    BMSOvercharge(1 << 17),
    BMSChargeDisable(1 << 18),
    SelfUseOff(1 << 19),
    SOCDeltaOverRange(1 << 20),
    BatterySelfDischarge(1 << 21),
    OffgridSOCLow(1 << 22),
    GridWaveUnstable(1 << 23),
    FeedPowerLimit(1 << 24),
    PFValueSet(1 << 25),
    RealPowerLimit(1 << 26),
    DCOutputOn(1 << 27),
    SOCProtectOff(1 << 28),
    DischargeModeForBP(1 << 29);

    private int intValue;

    DiagStatus(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static DiagStatus forValue(int value)
    {
        for (DiagStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        DiagStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<DiagStatus> result = EnumSet.noneOf(DiagStatus.class);
        DiagStatus[] values = DiagStatus.values();
        for (DiagStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
