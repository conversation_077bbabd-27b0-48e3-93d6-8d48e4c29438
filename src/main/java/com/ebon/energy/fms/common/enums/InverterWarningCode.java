package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterWarningCode
{
    None(0),
    InvCurrUnbalance(0x0001),
    CPLDWarning(0x0002),
    PvVoltLow(0x0004),
    PLLWarning(0x0008),
    BusUnbalance(0x0010),
    InvOutOfControl(0x0020),
    InvCurrOutRangeSW(0x0040),
    BusVoltLow(0x0080),
    SoftCtrlShutdown(0x0100),
    DcCurrOutRangeSW(0x0200),
    InvCurrOutRangeHW(0x0400),
    SystemFalse(0x0800),
    InvCurrFalse(0x1000),
    InvPwmFalse(0x2000),
    IslandingOccur(0x4000),
    OffGridPhaseFalse(0x8000);

    private int value;


    InverterWarningCode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterWarningCode forValue(int value)
    {
        for (InverterWarningCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InverterWarningCode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<InverterWarningCode> result = EnumSet.noneOf(InverterWarningCode.class);
        InverterWarningCode[] values = InverterWarningCode.values();
        for (InverterWarningCode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}