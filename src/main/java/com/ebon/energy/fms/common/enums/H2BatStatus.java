package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum H2BatStatus
{
    None(0),
    BatteryLowVoltage(1 << 0),
    BatteryFeed(1 << 1),
    BatteryFullyCharged(1 << 2),
    LowOffGridSOC(1 << 3),
    LowOnGridSOC(1 << 4),
    SOCFull(1 << 5),
    DischargeOverCurrent(1 << 6),
    ChargeOverCurrent(1 << 7),
    BatteryForceCharge(1 << 8),
    BatteryChargeInhibit(1 << 9),
    BatteryDischargeInhibit(1 << 10),
    BatteryDOD(1 << 11),
    BatteryWakeUp(1 << 12),
    ReservedBit13(1 << 13),
    ReservedBit14(1 << 14),
    BatteryHighVoltage(1 << 15);

    private int value;


    H2BatStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static H2BatStatus forValue(int value)
    {
        for (H2BatStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        H2BatStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<H2BatStatus> result = EnumSet.noneOf(H2BatStatus.class);
        H2BatStatus[] values = H2BatStatus.values();
        for (H2BatStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}