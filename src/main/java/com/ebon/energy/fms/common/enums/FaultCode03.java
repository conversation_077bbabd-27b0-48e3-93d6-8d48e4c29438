package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode03
{
    None(0),
    BatteryNotConnected(1 << 0),
    BatteryOvervoltageDetection(1 << 1),
    BatteryUndervoltageDetection(1 << 2),
    BatteryBMSAlarm(1 << 3),
    AbnormalBatteryName(1 << 4),
    LowTemperatureLeadAcidBattery(1 << 5),
    HighTemperatureLeadAcidBattery(1 << 6);

    private int value;


    FaultCode03(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode03 forValue(int value)
    {
        for (FaultCode03 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode03 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FaultCode03> result = EnumSet.noneOf(FaultCode03.class);
        FaultCode03[] values = FaultCode03.values();
        for (FaultCode03 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}