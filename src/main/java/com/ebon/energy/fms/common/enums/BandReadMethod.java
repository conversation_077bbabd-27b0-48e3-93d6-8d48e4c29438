package com.ebon.energy.fms.common.enums;

public enum BandReadMethod {
    /**
     * Read occurs via something that is not covered below.
     * This is normally used by things that perform reads via some side method
     * (rather than the usual read/write queries)
     */
    Other(0x00),

    /**
     * The information is in a Register (as part of a modbus spec)
     */
    ModbusRegister(0x01),

    /**
     * The information is in a File (as part of a modbus spec)
     */
    ModbusFile(0x02);

    private final int value;

    BandReadMethod(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}