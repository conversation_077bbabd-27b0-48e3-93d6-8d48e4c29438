package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterWarningCodeESLower {
    LLCHardwareOC(1 << 0),
    LLCBoostHardwareOC(1 << 1),
    LLCBoostSoftwareOC_OR_BMSOCFault(1 << 2),
    BatteryCurrentRMSOC(1 << 3),
    OffGridBatteryCurrentExceedsBMSCurrentLimit(1 << 4);

    private int value;


    private InverterWarningCodeESLower(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static InverterWarningCodeESLower forValue(int value) {
        for (InverterWarningCodeESLower e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        InverterWarningCodeESLower o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<InverterWarningCodeESLower> result = EnumSet.noneOf(InverterWarningCodeESLower.class);
        InverterWarningCodeESLower[] values = InverterWarningCodeESLower.values();
        for (InverterWarningCodeESLower e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
