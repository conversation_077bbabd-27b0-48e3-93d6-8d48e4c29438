package com.ebon.energy.fms.common.enums;

public enum Endianness {
    /**
     * Most significant (bigger) bit first
     */
    Big(0),

    /**
     * As above
     */
    BigEndian(0),

    /**
     * Least significant (smaller) bit first
     */
    Little(1),

    /**
     * As above
     */
    LittleEndian(1);

    private final int value;

    Endianness(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}