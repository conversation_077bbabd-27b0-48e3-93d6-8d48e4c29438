package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FunctionStatus
{
    None(0),
    AntiIslanding(1 << 0),
    LVRT(1 << 1),
    BurnInMode(1 << 2),
    PowerLimitFunction(1 << 3),

    MPPTShadow(1 << 7),
    Meter(1 << 8),
    AutoMode(1 << 9),
    EMSMode(1 << 10),
    FeedingEnable(1 << 11),
    BatteryActive(1 << 12),
    GroundFaultFlag(1 << 13),
    HighImpedance(1 << 15);

    private int value;


    FunctionStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FunctionStatus forValue(int value)
    {
        for (FunctionStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FunctionStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FunctionStatus> result = EnumSet.noneOf(FunctionStatus.class);
        FunctionStatus[] values = FunctionStatus.values();
        for (FunctionStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
