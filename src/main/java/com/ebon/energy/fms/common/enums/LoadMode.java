package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum LoadMode {
    Disconnected(0x00), // OFF, inverter disconnects to Load
    Connected(0x01); // ON,inverter connects to Load

    private final int value;

    LoadMode(int value) {
        this.value = (byte) value;
    }

    public int getValue() {
        return value;
    }

    public static LoadMode fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (LoadMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        LoadMode o = fromValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}