package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BMSWarningCodeET
{
    NoWarning(0),
    BatteryOverVoltageCharge1(1 << 0),
    BatteryUnderVoltageDischarge1(1 << 1),
    BatteryCellHighTemperature1(1 << 2),
    BatteryCellLowTemperature1(1 << 3),
    BatteryOverCurrentCharge1(1 << 4),
    BatteryOvercurrentDischarge1(1 << 5),
    BatteryFaultCommunication(1 << 6),
    BatterySystemReboot(1 << 7),
    BatteryCellImbalance(1 << 8),
    BatterySystemLowTemperature1(1 << 9),
    BatterySystemLowTemperature2(1 << 10),
    BatterySystemHighTemperature(1 << 11);

    private int value;

    BMSWarningCodeET(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BMSWarningCodeET forValue(int value)
    {
        for (BMSWarningCodeET e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BMSWarningCodeET o = forValue(value);
        if (o != null) {
            return o;
        }

        Set<BMSWarningCodeET> result = EnumSet.noneOf(BMSWarningCodeET.class);
        BMSWarningCodeET[] values = BMSWarningCodeET.values();
        for (BMSWarningCodeET e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}