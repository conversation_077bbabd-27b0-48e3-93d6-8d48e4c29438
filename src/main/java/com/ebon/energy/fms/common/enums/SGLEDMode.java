package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SGLEDMode
{
    RedOn(0),
    <PERSON>Flash(1 << 1),
    <PERSON>On(1 << 2),
    <PERSON>Flash(1 << 3),
    <PERSON>On(1 << 4),
    BlueFlash(1 << 5);

    private int intValue;


    private SGLEDMode(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static SGLEDMode forValue(int value)
    {
        for (SGLEDMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SGLEDMode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<SGLEDMode> result = EnumSet.noneOf(SGLEDMode.class);
        SGLEDMode[] values = SGLEDMode.values();
        for (SGLEDMode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
