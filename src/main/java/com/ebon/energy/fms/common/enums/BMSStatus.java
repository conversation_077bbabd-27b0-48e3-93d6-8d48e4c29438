package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BMSStatus
{
    None(0),
    Charge((1 << 0)),
    Forbidden((1 << 1)),
    ForceCharge((1 << 2));

    private int value;

    BMSStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BMSStatus forValue(int value)
    {
        for (BMSStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BMSStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BMSStatus> result = EnumSet.noneOf(BMSStatus.class);
        BMSStatus[] values = BMSStatus.values();
        for (BMSStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
