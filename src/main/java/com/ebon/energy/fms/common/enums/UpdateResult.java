package com.ebon.energy.fms.common.enums;

public enum UpdateResult
{
    Normal(0x00),
    BinFileOK(0x01),
    UpdateSuccess(0x02),
    BinFileNotGood(0x14),
    UpdateFail(0x15);

    private int value;

    UpdateResult(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static UpdateResult forValue(int value)
    {
        for (UpdateResult e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        UpdateResult o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}
