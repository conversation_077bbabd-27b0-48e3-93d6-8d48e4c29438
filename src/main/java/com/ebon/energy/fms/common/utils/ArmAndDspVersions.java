package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.domain.vo.telemetry.band.inverter.InverterAdapterHelper;

import java.util.Locale;
import java.util.Objects;

public class ArmAndDspVersions {
    private final int armVersion;
    private final int dspVersion;

    public ArmAndDspVersions(int armVersion, int dspVersion) {
        this.armVersion = armVersion;
        this.dspVersion = dspVersion;
    }

    public int getArmVersion() {
        return armVersion;
    }

    public int getDspVersion() {
        return dspVersion;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof ArmAndDspVersions)) return false;
        ArmAndDspVersions other = (ArmAndDspVersions) obj;
        return armVersion == other.armVersion && dspVersion == other.dspVersion;
    }

    @Override
    public int hashCode() {
        return Objects.hash(armVersion, dspVersion);
    }

    public static ArmAndDspVersions getOrThrow(String firmwareVersion) {
        InverterAdapterHelper.VersionPair versions = InverterAdapterHelper.extractArmAndDsp(firmwareVersion);
        String arm = versions.arm;
        String dsp = versions.dsp;

        if (arm == null || dsp == null) {
            throw new IllegalStateException(
                    String.format("Failed to extract Arm and Dsp versions from goodwe firmware version '%s', arm: %s, dsp: %s",
                            firmwareVersion, arm, dsp));
        }

        try {
            int armVersion = Integer.parseUnsignedInt(arm);
            int dspVersion = Integer.parseUnsignedInt(dsp);
            return new ArmAndDspVersions(armVersion, dspVersion);
        } catch (Exception e) {
            throw new IllegalStateException(
                    String.format("Failed to parse goodwe firmware version '%s' arm: %s, dsp: %s",
                            firmwareVersion, arm, dsp), e);
        }
    }

    public static boolean tryGet(String firmwareVersion, Wrapper<ArmAndDspVersions> out) {
        try {
            out.value = getOrThrow(firmwareVersion);
            return true;
        } catch (Exception e) {
            out.value = null;
            return false;
        }
    }

    public static ArmAndDspVersions getOrZeros(String firmwareVersion) {
        Wrapper<ArmAndDspVersions> out = new Wrapper<>();
        if (!tryGet(firmwareVersion, out)) {
            return new ArmAndDspVersions(0, 0);
        }
        return out.value;
    }

    // Helper class to simulate out parameter
    public static class Wrapper<T> {
        public T value;
    }
}
