package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode01
{
    None(0),
    NoPowerGrid(1 << 0),
    GridOvervoltage(1 << 1),
    GridUndervoltage(1 << 2),
    GridOverfrequency(1 << 3),
    GridUnderfrequency(1 << 4),
    GridImbalance(1 << 5),
    GridFrequencyJitter(1 << 6),
    GridCountercurrent(1 << 7),
    GridCurrentTrackingFault(1 << 8),
    MeterCommunicationFailed(1 << 9),
    FailSafe(1 << 10),
    MeterSelectionAnomaly(1 << 11),
    EPMHardLimitProtection(1 << 12),
    G100CurrentOverlimit(1 << 13),
    G100VoltageFault(1 << 14),
    GridPhaseAnomaly(1 << 15);

    private int value;


    FaultCode01(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode01 forValue(int value)
    {
        for (FaultCode01 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode01 o = forValue(value);
        if (o != null) {
            return o;
        }

        Set<FaultCode01> result = EnumSet.noneOf(FaultCode01.class);
        FaultCode01[] values = FaultCode01.values();
        for (FaultCode01 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
