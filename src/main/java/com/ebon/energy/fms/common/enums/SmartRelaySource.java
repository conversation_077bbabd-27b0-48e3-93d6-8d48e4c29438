package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum SmartRelaySource {
    PV(0, "Solar Generation"),
    Export(1, "Export Power");

    private final int value;
    private final String displayName;

    SmartRelaySource(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public int getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    @JSONCreator
    public static SmartRelaySource fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (SmartRelaySource e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching SmartRelaySource for value: " + value);
    }
}