package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SGErrorCode
{
    NoError(0),
    GridOverVoltage(1 << 0),
    GridUnderVoltage(1 << 1),
    GridMissing(1 << 2),
    GridOverFreq(1 << 3),
    GridUnderFreq(1 << 4),
    PVOverVoltage(1 << 5),
    PVInsulationAbnormal(1 << 6),
    LeakageCurrentAbnormal(1 << 7),
    GridVoltageHigherThanBus(1 << 8),
    ControlPowerLow(1 << 9),
    UnknownRegulationCode(1 << 10),
    PowerHardLimitExceeded(1 << 11),
    AnyPhasePVIFailure(1 << 12),
    Reserved(1 << 13),
    ArcFaultDetection(1 << 14),
    GFCI(1 << 15),
    OutputDCOverCurrent(1 << 16),
    InverterRelayAbnormal(1 << 17),
    DRM0Active(1 << 18),
    InverterOverTemperature(1 << 19),
    GFCIDetectorFault(1 << 20),
    PVStringReverse(1 << 21),
    SystemTypeError(1 << 22),
    FanLock(1 << 23),
    BusUnderVoltage(1 << 24),
    BusOverVoltage(1 << 25),
    InternalCommunicationError(1 << 26),
    FirmwareIncompatability(1 << 27),
    EEPROMError(1 << 28),
    ConsistentWarning(1 << 29),
    InverterAbnormal(1 << 30),
    BoostAbnormal(1 << 31);

    private long intValue;


    private SGErrorCode(int value)
    {
        intValue = value;
    }

    public long getValue()
    {
        return intValue;
    }

    public static SGErrorCode forValue(long value)
    {
        for (SGErrorCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(long value) {
        SGErrorCode o = forValue(value);
        if (o != null) {
            return o;
        }

        Set<SGErrorCode> result = EnumSet.noneOf(SGErrorCode.class);
        SGErrorCode[] values = SGErrorCode.values();
        for (SGErrorCode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}