package com.ebon.energy.fms.common.enums;

public enum EventCode {
    GeneralEvent(1),
    SetMeterCheckSentSuccess(2),
    SetMeterCheckSentFailed(3);

    private final int value;

    EventCode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    // 通过 value 获取对应的枚举值
    public static EventCode fromValue(int value) {
        for (EventCode e : EventCode.values()) {
            if (e.value == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No EventCode with value: " + value);
    }
}
