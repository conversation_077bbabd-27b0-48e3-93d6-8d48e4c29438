package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum VRTMode
{
    Disabled(0),
    OnlyLowVoltageRideThrough(1),
    OnlyHighVoltageRideThrough(2),
    EnableBoth(3);

    private int value;

    VRTMode(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static VRTMode forValue(int value)
    {
        for (VRTMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        VRTMode o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}
