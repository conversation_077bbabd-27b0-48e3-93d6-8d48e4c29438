package com.ebon.energy.fms.common.enums;

/**
 * Auto load control states for smart relays
 */
public enum AutoLoadControlState {
    /**
     * @deprecated None is not used in Smart Load Control
     */
    @Deprecated
    None,

    /**
     * Starts delay timer when power reaches OFF trigger threshold
     */
    DelayOff,

    /**
     * Requests relay to turn off
     */
    RequestOff,

    /**
     * <PERSON><PERSON> accepted load control request to turn off
     */
    Off,

    /**
     * Starts delay timer when power reaches ON trigger threshold
     */
    DelayOn,

    /**
     * Requests relay to turn on
     */
    RequestOn,

    /**
     * <PERSON><PERSON> accepted load control request to turn on
     */
    On,

    /**
     * Neutral state happens when power is between ON and OFF trigger thresholds
     */
    Neutral,

    /**
     * Forces relay to be ON
     */
    OnForDailyRuntime,

    /**
     * Forces relay to be OFF
     */
    DailyRuntimeComplete
}