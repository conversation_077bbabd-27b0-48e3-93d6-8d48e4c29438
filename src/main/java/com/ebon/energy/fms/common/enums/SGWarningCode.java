package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SGWarningCode
{
    LoggerEEPROMFail(1 << 1),
    ArcFaultDetection(1 << 2),
    SingleTrackerDetect(1 << 3),
    OutputPowerTooLow(1 << 4),
    DataLoggerLost(1 << 5),
    MeterLost(1 << 6),
    InverterLost(1 << 7);

    private int intValue;

    private SGWarningCode(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static SGWarningCode forValue(int value)
    {
        for (SGWarningCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SGWarningCode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<SGWarningCode> result = EnumSet.noneOf(SGWarningCode.class);
        SGWarningCode[] values = SGWarningCode.values();
        for (SGWarningCode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }

}