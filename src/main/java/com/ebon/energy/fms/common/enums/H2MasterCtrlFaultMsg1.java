package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum H2MasterCtrlFaultMsg1
{
    None(0),
    BusOverVoltage(1 << 0),
    BusUnderVoltage(1 << 1),
    GridPhaseError(1 << 2),
    PVOverVoltage(1 << 3),
    IslandingError(1 << 4),
    ArcEquipmentFault(1 << 5),
    PVInputError(1 << 6),
    MeterCommsLoss(1 << 7),
    BusHardwareOverVoltage(1 << 8),
    PVHardwareOverCurrent(1 << 9),
    ReservedBit11(1 << 10),
    InverterHardwareOverCurrent(1 << 11),
    ReservedBit13(1 << 12),
    ReservedBit14(1 << 13),
    NGroundVoltageFault(1 << 14),
    DRM0Fault(1 << 15),
    Fan1Fault(1 << 16),
    Fan2Fault(1 << 17),
    Fan3Fault(1 << 18),
    Fan4Fault(1 << 19),
    ArcFault(1 << 20),
    SoftwarePVOverCurrent(1 << 21),
    BatteryOverVoltage(1 << 22),
    BatteryOverCurrent(1 << 23),
    BatteryChargeCurrentHigh(1 << 24),
    BatteryOverload(1 << 25),
    BatterySoftConnectionTimeout(1 << 26),
    OutputOverload(1 << 27),
    BatteryOpenCircuit(1 << 28),
    BatteryDischargeVoltageLow(1 << 29),
    AuthorisationExpired(1 << 30),
    CommsLossInternalDisplayBoard(1 << 31);

    private int intValue;


    private H2MasterCtrlFaultMsg1(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static H2MasterCtrlFaultMsg1 forValue(int value)
    {
        for (H2MasterCtrlFaultMsg1 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        H2MasterCtrlFaultMsg1 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<H2MasterCtrlFaultMsg1> result = EnumSet.noneOf(H2MasterCtrlFaultMsg1.class);
        H2MasterCtrlFaultMsg1[] values = H2MasterCtrlFaultMsg1.values();
        for (H2MasterCtrlFaultMsg1 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
