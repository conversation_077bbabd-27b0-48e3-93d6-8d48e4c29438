package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonHVProtectionStatus
{
    BatteryCellUnderVoltageProtection(1 << 0),
    BatteryCellOverVoltageProtection(1 << 1),
    PileUnderVoltageProtection(1 << 2),
    PileOverVoltageProtection(1 << 3),
    ChargeUnderTempProtection(1 << 4),
    ChargeOverTempProtection(1 << 5),
    DischargeUnderTempProtection(1 << 6),
    DischargeOverTempProtection(1 << 7),
    ChargeOverCurrentProtection(1 << 8),
    DischargeOverCurrentProtection(1 << 9),
    ShortCircuitProtection(1 << 10),

    ModuleOverTempProtection(1 << 12),
    ModuleUnderVoltageProtection(1 << 13),
    ModuleOverVoltageProtection(1 << 14);

    private int value;


    PylonHVProtectionStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonHVProtectionStatus forValue(int value)
    {
        for (PylonHVProtectionStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        PylonHVProtectionStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<PylonHVProtectionStatus> result = EnumSet.noneOf(PylonHVProtectionStatus.class);
        PylonHVProtectionStatus[] values = PylonHVProtectionStatus.values();
        for (PylonHVProtectionStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
