package com.ebon.energy.fms.common.enums;

public enum LocalTimeSource {
    /**
     * Used when the time source is unknown or unpopulated.
     */
    Unknown,

    /**
     * The time was generated by taking the timezone for the installation address
     * and the device's UTC timestamp
     */
    AddressAndDeviceUtc,

    /**
     * The time was generated by taking the device's Windows Time
     */
    DeviceLocal,

    /**
     * The time was generated by taking the device's UTC Time and converting to AEST
     */
    BrisbaneAndDeviceUtc,

    /**
     * The time was generated by taking the UTC timestamp when the data was recieved
     * and converting to AEST.
     */
    BrisbaneAndServerUtc
}