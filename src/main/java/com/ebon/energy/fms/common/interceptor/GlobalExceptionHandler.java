package com.ebon.energy.fms.common.interceptor;

import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 对restcontroller抛出的异常进行处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public JsonResult exception(Exception e, HttpServletRequest request) {
        log.error("internal error", e);
        return JsonResult.buildError(CommonErrorCodeEnum.ERROR);
    }

    @ExceptionHandler(BizException.class)
    @ResponseBody
    public JsonResult bizException(BizException e, HttpServletRequest request, HttpServletResponse response) {
        return JsonResult.buildError(e.getErrorCode(), e.getErrorMsg());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public JsonResult methodArgumentNotValidException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        BindingResult result = ex.getBindingResult();
        List<FieldError> fieldErrorList = result.getFieldErrors();
        FieldError fieldError = fieldErrorList.get(0);
        String errorMsg = fieldError.getDefaultMessage();
        errorMsg = errorMsg.startsWith(fieldError.getField()) ? errorMsg : fieldError.getField() + " " + errorMsg;
        return JsonResult.buildError(CommonErrorCodeEnum.ERR_REQUEST_PARAMETER.getErrorCode(), errorMsg);
    }

}
