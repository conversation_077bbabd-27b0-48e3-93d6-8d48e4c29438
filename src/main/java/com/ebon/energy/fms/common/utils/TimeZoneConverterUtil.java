package com.ebon.energy.fms.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.time.DateTimeException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Map; // Not strictly needed for the direct translation but good for understanding
import java.util.TimeZone;

public class TimeZoneConverterUtil {

    /**
     * Converts a BCL (Windows) or IANA time zone ID string to a java.time.ZoneId.
     *
     * @param bclOrIanaTimeZoneId The string identifier for the time zone, which can be
     * either a Windows (BCL) ID or an IANA ID.
     * @return The corresponding ZoneId if found; otherwise, null.
     */
    public static ZoneId convertBclOrIanaToDateTimeZone(String bclOrIanaTimeZoneId) {
        if (bclOrIanaTimeZoneId == null || bclOrIanaTimeZoneId.isEmpty()) {
            return null;
        }

        // Attempt 1: Treat as a standard IANA time zone ID or a directly resolvable alias
        // ZoneId.of() can resolve standard IANA IDs like "Europe/London"
        // and also some aliases, which might include some Windows zones
        // depending on the Java runtime's timezone data (tzdb version).
        try {
            return ZoneId.of(bclOrIanaTimeZoneId);
        } catch (DateTimeException e) {
            // This means bclOrIanaTimeZoneId is not a recognized IANA ID by ZoneId.of()
            // in its primary lookup, or it's not an alias ZoneId.of() directly resolves.
            // Now, we proceed to check if it's a Windows ID that can be mapped.
        }

        var zoneIdForWindowsId = WindowsToZoneIdConverter.getZoneIdForWindowsId(bclOrIanaTimeZoneId);
        if (zoneIdForWindowsId !=null){
            return zoneIdForWindowsId;
        }
        // Attempt 2: Check if it's a Windows ID (or other short ID) mapped in ZoneId.SHORT_IDS
        // This is a common way to map Windows time zone names to IANA IDs in Java.
        // ZoneId.SHORT_IDS provides a map like {"EST": "America/New_York", "MST": "America/Denver", ...}
        // It also often includes mappings for full Windows display names.
        String mappedIanaId = ZoneId.SHORT_IDS.get(bclOrIanaTimeZoneId);
        if (mappedIanaId != null) {
            try {
                // Validate and return the ZoneId for the mapped IANA ID
                return ZoneId.of(mappedIanaId);
            } catch (DateTimeException ex) {
                // This would be unusual if the mapping in SHORT_IDS is valid,
                // but it's a good practice to handle it.
                // It could happen if SHORT_IDS contains an outdated or incorrect mapping.
                System.err.println("Warning: Mapped IANA ID '" + mappedIanaId + "' from input '" + bclOrIanaTimeZoneId + "' is invalid.");
                return null;
            }
        }

        // If neither direct resolution nor mapping via SHORT_IDS worked,
        // we cannot resolve it with built-in java.time mechanisms.
        return null;
    }

    public static LocalDate getLocalDateFromBclTimeZone(Instant dateTime, String bclTimeZoneId) {
        if (StringUtils.isBlank(bclTimeZoneId)) {
            throw new IllegalArgumentException("No time zone configured for given site");
        }

        try {
            ZoneId zoneId = convertBclOrIanaToDateTimeZone(bclTimeZoneId);

            // 将Instant转换为指定时区的日期时间
            ZonedDateTime zonedDateTime = dateTime.atZone(zoneId);

            // 返回本地日期
            return zonedDateTime.toLocalDate();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid time zone configured for given site: " + bclTimeZoneId, e);
        }
    }

    public static void main(String[] args) {
        System.out.println("--- Testing IANA IDs ---");
        System.out.println("UTC: " + convertBclOrIanaToDateTimeZone("UTC"));
        System.out.println("Europe/Paris: " + convertBclOrIanaToDateTimeZone("Europe/Paris"));
        System.out.println("America/New_York: " + convertBclOrIanaToDateTimeZone("America/New_York"));

        System.out.println("\n--- Testing Windows (BCL) IDs ---");
        // Note: The exact Windows IDs recognized can depend on the JDK's timezone data version.
        // Common mappings:
        // "Eastern Standard Time" -> "America/New_York"
        // "Central Standard Time" -> "America/Chicago"
        // "Pacific Standard Time" -> "America/Los_Angeles"
        // "GMT Standard Time"     -> "Europe/London" (for UK, distinct from "Greenwich Standard Time" which is Etc/GMT)
        System.out.println("Eastern Standard Time (Windows): " + convertBclOrIanaToDateTimeZone("Eastern Standard Time"));
        System.out.println("Central Standard Time (Windows): " + convertBclOrIanaToDateTimeZone("Central Standard Time"));
        System.out.println("Pacific Standard Time (Windows): " + convertBclOrIanaToDateTimeZone("Pacific Standard Time"));
        System.out.println("GMT Standard Time (Windows): " + convertBclOrIanaToDateTimeZone("GMT Standard Time"));
        System.out.println("W. Europe Standard Time (Windows): " + convertBclOrIanaToDateTimeZone("W. Europe Standard Time")); // e.g., Europe/Berlin

        System.out.println("\n--- Testing Edge Cases ---");
        System.out.println("Invalid Zone: " + convertBclOrIanaToDateTimeZone("Invalid/Zone"));
        System.out.println("Empty String: " + convertBclOrIanaToDateTimeZone(""));
        System.out.println("Null String: " + convertBclOrIanaToDateTimeZone(null));

        // Example of a short ID that might be in ZoneId.SHORT_IDS
        System.out.println("PST (Short ID): " + convertBclOrIanaToDateTimeZone("PST")); // May resolve to America/Los_Angeles

        // To see available short IDs (for inspection, not for production logic generally)
        // System.out.println("\n--- Available Short IDs in ZoneId.SHORT_IDS ---");
        // ZoneId.SHORT_IDS.forEach((key, value) -> {
        //     if (key.toLowerCase().contains("standard time") || key.toLowerCase().contains("eastern")) { // Filter for examples
        //         System.out.println(key + " -> " + value);
        //     }
        // });
    }
}