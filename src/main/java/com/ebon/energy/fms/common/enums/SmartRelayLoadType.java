package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum SmartRelayLoadType {
    Unknown(0, ""),
    HotWaterSystem(1, "Hot Water System"),
    HeatPump(2, "Heat Pump"),
    AirConditioner(3, "Air Conditioner"),
    PoolPump(4, "Pool Pump"),
    EVCharger(5, "EV Charger");

    private final int value;
    private final String displayName;

    SmartRelayLoadType(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public int getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static SmartRelayLoadType fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (SmartRelayLoadType e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching SmartRelayLoadType for value: " + value);
    }
}