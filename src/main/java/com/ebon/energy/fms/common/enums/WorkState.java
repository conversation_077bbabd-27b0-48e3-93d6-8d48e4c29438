package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum WorkState
{
    None(0),
    NormalOperation(1 << 0),
    InitialStandby(1 << 1),
    ControlledShutdown(1 << 2),
    FaultShutdown(1 << 3),
    Standby(1 << 4),
    DeratingOperation(1 << 5),
    LimitRunning(1 << 6),
    BypassOverload(1 << 7),
    LoadFailure(1 << 8),
    PowerGridFailure(1 << 9),
    BatteryFailure(1 << 10),
    Reserved(1 << 11),
    GridSurgeWarning(1 << 12),
    FanFailureWarning(1 << 13),
    ExternalFanFailure(1 << 14),
    FailureShutdown(1 << 15);

    private int value;

    WorkState(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static WorkState forValue(int value)
    {
        for (WorkState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        WorkState o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<WorkState> result = EnumSet.noneOf(WorkState.class);
        WorkState[] values = WorkState.values();
        for (WorkState e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}