package com.ebon.energy.fms.common.enums;

import java.util.ArrayList;
import java.util.List;

public enum PylonAlarmState {
    Normal(0),
    BelowLimit(0x01),
    AboveLimit(0x02),
    OtherError(0xF0);

    private final int value;

    PylonAlarmState(int value) {
        this.value = (byte)value;
    }

    public int getValue() {
        return value;
    }

    public static PylonAlarmState fromValue(int value) {
        for (PylonAlarmState state : PylonAlarmState.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonAlarmState o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}