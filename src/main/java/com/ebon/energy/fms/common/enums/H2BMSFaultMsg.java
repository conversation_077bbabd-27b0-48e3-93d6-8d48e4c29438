package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum H2BMSFaultMsg
{
    None(0),
    BMSInternalCommsLoss(1 << 0),
    BatterySequenceError(1 << 1),
    DischargeOverCurrentProtect(1 << 2),
    ChargingOverCurrentProtect(1 << 3),
    TotalVoltageLowerLimitProtect(1 << 4),
    TotalVoltageUpperLimitProtect(1 << 5),
    CellUnderVoltageProtect(1 << 6),
    CellOverVoltageProtect(1 << 7),
    BMSHardwareError(1 << 8),
    ChargeCellUnderTemperatureProtect(1 << 9),
    ChargeCellOverTemperatureProtect(1 << 10),
    DischargeCellUnderTemperatureProtect(1 << 11),
    DischargeCellOverTemperatureProtect(1 << 12),
    RelayError(1 << 13),
    PreChargeError(1 << 14),
    InsulationError(1 << 15),
    BMSSupplierIncompatible(1 << 16),
    BatteryCellSupplierIncompatible(1 << 17),
    BatteryCellImcompatibility(1 << 18),
    VoltageInconsistency(1 << 19),
    CircuitBreakerOpen(1 << 20),
    TemperatureImbalance(1 << 21),
    CellImbalanceLevel1(1 << 22),
    CellImbalanceLevel2(1 << 23),
    BMSOverTemperatureProtect(1 << 24),
    ShortCircuitProtect(1 << 25),
    TotalPressureMatchingFailed(1 << 26),
    SystemLockout(1 << 27),
    FuseFaultProtect(1 << 28),
    ChargingPortHighVoltageFaultProtect(1 << 29),
    ReservedBit126(1 << 30),
    ReservedBit127(1 << 31);

    private int intValue;


    H2BMSFaultMsg(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static H2BMSFaultMsg forValue(int value)
    {
        for (H2BMSFaultMsg e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        H2BMSFaultMsg o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<H2BMSFaultMsg> result = EnumSet.noneOf(H2BMSFaultMsg.class);
        H2BMSFaultMsg[] values = H2BMSFaultMsg.values();
        for (H2BMSFaultMsg e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
