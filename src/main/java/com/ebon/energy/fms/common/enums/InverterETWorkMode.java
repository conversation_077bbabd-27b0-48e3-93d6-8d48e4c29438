package com.ebon.energy.fms.common.enums;

public enum InverterETWorkMode
{
    Wait(0),
    Ongrid(1),
    Offgrid(2),
    <PERSON>ault(3),
    <PERSON>(4),
    <PERSON>(5);

    private int value;

    InverterETWorkMode(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterETWorkMode forValue(int value)
    {
        for (InverterETWorkMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        InverterETWorkMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}