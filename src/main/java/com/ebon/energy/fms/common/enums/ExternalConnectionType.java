package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.fasterxml.jackson.annotation.JsonValue;

import java.lang.reflect.Type;

@JSONType(deserializer = ExternalConnectionType.ExternalConnectionTypeDeserializer.class)
public class ExternalConnectionType {
    private int value;
    private String name;

    ExternalConnectionType() {
    }

    ExternalConnectionType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    // 预定义的枚举值
    public static final ExternalConnectionType UNKNOWN = new ExternalConnectionType(0, "UNKNOWN");
    public static final ExternalConnectionType MOBILE = new ExternalConnectionType(1, "MOBILE");
    public static final ExternalConnectionType WIFI = new ExternalConnectionType(2, "WIFI");
    public static final ExternalConnectionType ACCESS_POINT = new ExternalConnectionType(3, "ACCESS_POINT");
    public static final ExternalConnectionType ETHERNET = new ExternalConnectionType(4, "ETHERNET");

    public static ExternalConnectionType fromValue(int value) {
        switch (value) {
            case 0:
                return UNKNOWN;
            case 1:
                return MOBILE;
            case 2:
                return WIFI;
            case 3:
                return ACCESS_POINT;
            case 4:
                return ETHERNET;
            default:
                return new ExternalConnectionType(value, String.valueOf(value));
        }
    }

    public int getValue() {
        return value;
    }

    @JsonValue
    public String getName() {
        return name;
    }

    public static class ExternalConnectionTypeDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            return (T) ExternalConnectionType.fromValue((int) value);
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }
}