package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BMSStatusMasks
{
    Nothing(0),
    StandBy(1 << 0),
    Working(1 << 1),
    <PERSON><PERSON><PERSON>(1 << 2),
    Maintain(1 << 3), // seems to be: Has Error flag
    ChargeEnable(1 << 4),
    ChargeImmediately(1 << 5),
    DischargeEnable(1 << 6),
    Charging(1 << 7),
    Discharging(1 << 8);

    private int value;

    BMSStatusMasks(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BMSStatusMasks forValue(int value)
    {
        for (BMSStatusMasks e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BMSStatusMasks o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BMSStatusMasks> result = EnumSet.noneOf(BMSStatusMasks.class);
        BMSStatusMasks[] values = BMSStatusMasks.values();
        for (BMSStatusMasks e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
