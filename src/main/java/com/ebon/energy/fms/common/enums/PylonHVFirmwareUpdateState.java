package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonHVFirmwareUpdateState
{
    MasterToSlave(0x000C),
    MasterUpgrading(0x000D),
    MasterToSlaveFail(0x000E),
    SlaveUpgradeFail(0x000F);

    private int value;


    PylonHVFirmwareUpdateState(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonHVFirmwareUpdateState forValue(int value)
    {
        for (PylonHVFirmwareUpdateState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        PylonHVFirmwareUpdateState o = forValue(value);
        if (o != null) {
            return o;
        }
        
       return value;
    }
}