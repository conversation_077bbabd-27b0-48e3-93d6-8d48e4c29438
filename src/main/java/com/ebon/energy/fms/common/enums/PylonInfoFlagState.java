package com.ebon.energy.fms.common.enums;

import java.util.ArrayList;
import java.util.List;

public enum PylonInfoFlagState {
    Normal(0x00),
    UnreadAlarm(1 << 0),
    UnreadSwitch(1 << 4),
    AllUnread(0x11);

    private final int value;

    PylonInfoFlagState(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PylonInfoFlagState fromValue(int value) {
        for (PylonInfoFlagState state : PylonInfoFlagState.values()) {
            if (state.getValue() == value) {
                return state;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        PylonInfoFlagState o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        List<String> activeFlags = new ArrayList<>();
        int remaining = unsignedByte;

        // 检查所有非零的已定义标志位
        for (PylonInfoFlagState state : values()) {
            if (state.value != 0 && (remaining & state.value) == state.value) {
                activeFlags.add(state.name());
                remaining &= ~state.value;
            }
        }

        // 如果还有未处理的位，返回原始数字
        return remaining != 0 ? unsignedByte :
                String.join(", ", activeFlags);
    }
}