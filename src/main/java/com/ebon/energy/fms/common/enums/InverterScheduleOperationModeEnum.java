package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * Enum for inverter schedule operation modes
 */
@Getter
public enum InverterScheduleOperationModeEnum {
    Auto("Auto"),
    ChargeBattery("Charge Battery"),
    DischargeBattery("Discharge Battery"),
    ImportPower("Import Power"),
    ExportPower("Export Power"),
    Conserve("Conserve"),
    BuyPower("Buy Power"),
    ForceChargeBattery("Force Charge Battery"),
    ForceDischargeBattery("Force Discharge Battery"),
    Offgrid("Off Grid"),
    SellPower("Sell Power"),
    Hibernate("Hibernate"),
    Stop("Stop");

    private final String displayName;

    InverterScheduleOperationModeEnum(String displayName) {
        this.displayName = displayName;
    }


    public static InverterScheduleOperationModeEnum fromString(String value) {
        for (InverterScheduleOperationModeEnum b : InverterScheduleOperationModeEnum.values()) {
            if (b.name().equals(value)) {
                return b;
            }
        }
        return null;
    }

}
