package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FunctionOperationStatus2 {
    None(0),
    ConstantVoltOrCurrentCharging(1 << 0),
    FloatCharging(1 << 1);

    private final int value;

    FunctionOperationStatus2(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static FunctionOperationStatus2 fromValue(int value) {
        for (FunctionOperationStatus2 status : FunctionOperationStatus2.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FunctionOperationStatus2 o = fromValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FunctionOperationStatus2> result = EnumSet.noneOf(FunctionOperationStatus2.class);
        FunctionOperationStatus2[] values = FunctionOperationStatus2.values();
        for (FunctionOperationStatus2 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
