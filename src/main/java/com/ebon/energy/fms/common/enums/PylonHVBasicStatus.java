package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonHVBasicStatus
{
    Charge(1 << 0),
    Discharge(1 << 1),
    ReservedForState(1 << 2),

    SystemErrorProtection(1 << 3),
    CurrentProtection(1 << 4),
    VoltageProtection(1 << 5),
    TemperatureProtection(1 << 6),
    CurrentAlarm(1 << 7),
    VoltageAlarm(1 << 8),
    TemperatureAlarm(1 << 9),
    PileSystemIdleStatus(1 << 10),
    PileSystemChargingStatus(1 << 11),
    PileSystemDischargingStatus(1 << 12),
    PileSystemSleepStatus(1 << 13);

    private int value;


    PylonHVBasicStatus(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static PylonHVBasicStatus forValue(int value)
    {
        for (PylonHVBasicStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        PylonHVBasicStatus o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<PylonHVBasicStatus> result = EnumSet.noneOf(PylonHVBasicStatus.class);
        PylonHVBasicStatus[] values = PylonHVBasicStatus.values();
        for (PylonHVBasicStatus e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
