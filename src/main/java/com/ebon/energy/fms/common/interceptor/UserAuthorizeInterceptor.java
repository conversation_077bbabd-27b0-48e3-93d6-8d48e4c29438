package com.ebon.energy.fms.common.interceptor;

import com.ebon.energy.fms.common.annotation.NoLoginRequired;
import com.ebon.energy.fms.common.constants.AuthorizeConstants;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.ebon.energy.fms.service.JwtService;
import com.ebon.energy.fms.service.UserService;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;


@Slf4j
public class UserAuthorizeInterceptor implements HandlerInterceptor, InitializingBean {

    @Resource
    private JwtService jwtTokenService;

    @Resource
    private UserService userService;

    public UserAuthorizeInterceptor() {
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        NoLoginRequired noLoginRequired = handlerMethod.getMethodAnnotation(NoLoginRequired.class);
        if (noLoginRequired == null) {
            noLoginRequired = handlerMethod.getBeanType().getAnnotation(NoLoginRequired.class);
        }
        if (noLoginRequired != null) {
            return true;
        }

        String jwtToken = RequestUtil.getValueFromRequest(request, AuthorizeConstants.COOKIE_TOKEN_KEY);
        String email = jwtTokenService.parseSubject(jwtToken);
        if (StringUtils.isEmpty(email)) {
            throw new BizException(CommonErrorCodeEnum.LOGIN_TOKEN_ERROR);
        }

        UserVO userVO = userService.getUserByEmail(email);
        if (Objects.isNull(userVO)) {
            throw new BizException(CommonErrorCodeEnum.LOGIN_TOKEN_ERROR);
        }

        request.setAttribute(AuthorizeConstants.ATTRIBUTE_USER_KEY, userVO);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }
}
