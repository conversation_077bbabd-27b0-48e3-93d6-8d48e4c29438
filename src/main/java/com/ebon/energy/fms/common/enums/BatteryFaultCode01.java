package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BatteryFaultCode01
{
    None(0),
    Reserve(1 << 0),
    BatteryHighVoltage(1 << 1),
    BatteryLowVoltage(1 << 2),
    HighBatteryTemperature(1 << 3),
    LowBatteryTemperature(1 << 4),
    OverTemperatureCharging(1 << 5),
    UnderTemperatureCharging(1 << 6),
    DischargeOvercurrent(1 << 7);

    private int value;

    BatteryFaultCode01(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BatteryFaultCode01 forValue(int value) {
        for (BatteryFaultCode01 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BatteryFaultCode01 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BatteryFaultCode01> result = EnumSet.noneOf(BatteryFaultCode01.class);
        BatteryFaultCode01[] values = BatteryFaultCode01.values();
        for (BatteryFaultCode01 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
