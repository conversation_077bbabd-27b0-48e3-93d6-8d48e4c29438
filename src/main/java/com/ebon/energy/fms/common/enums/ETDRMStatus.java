package com.ebon.energy.fms.common.enums;

public enum ETDRMStatus {
    DRM0(0),
    DRM1(1),
    DRM2(2),
    DRM3(3),
    DRM4(4),
    DRM5(5),
    DRM6(6),
    DRM7(7),
    DRM8(8),
    OK(255);

    private int value;


    private ETDRMStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ETDRMStatus forValue(int value) {
        for (ETDRMStatus e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        ETDRMStatus o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}
