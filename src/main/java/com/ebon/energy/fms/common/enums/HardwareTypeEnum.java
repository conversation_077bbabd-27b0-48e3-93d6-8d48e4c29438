package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum HardwareTypeEnum {
    Unknown(0),
    Inverter(1),
    Batteries(2);

    private final int value;

    HardwareTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static HardwareTypeEnum fromValue(int value) {
        for (HardwareTypeEnum e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        throw new IllegalArgumentException("No matching hardware type for value: " + value);
    }
}
