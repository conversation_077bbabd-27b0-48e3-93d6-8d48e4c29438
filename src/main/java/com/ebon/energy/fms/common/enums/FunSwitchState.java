package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FunSwitchState
{
    None(0),
    DRMFunction(1 << 0),
    SiteManagerEnable(1 << 1),
    SlaveOrMaster(1 << 2),
    BalancedOrUnbalancedPhase(1 << 3),
    GeneratorStartupCondition(1 << 4),
    GeneratorStartedCorrect(1 << 5),
    BatteriesIndependentOrParallel(1 << 6),
    AFCIBoardExistsFlag(1 << 7),
    AFCIBoardSelfCheckStatus(1 << 8),
    InverterGridConnectedState(1 << 9),
    DualBackupEnabled(1 << 10),
    FastShutdownSwitchState(1 << 11),
    EmergencyStopSwitchStatus(1 << 12);

    private int value;


    FunSwitchState(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FunSwitchState forValue(int value)
    {
        for (FunSwitchState e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FunSwitchState o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FunSwitchState> result = EnumSet.noneOf(FunSwitchState.class);
        FunSwitchState[] values = FunSwitchState.values();
        for (FunSwitchState e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
