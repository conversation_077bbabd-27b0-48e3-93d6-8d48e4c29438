package com.ebon.energy.fms.common.utils;


import java.time.DateTimeException;
import java.time.ZoneId;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.function.BiConsumer; // Import BiConsumer

public class WindowsToZoneIdConverter {

    // 1. 将 Map 声明为 private static final
    private static final Map<String, ZoneId> WINDOWS_TO_ZONE_ID_MAP;

    // 2. 使用静态初始化块来填充 Map
    static {
        Map<String, ZoneId> tempMap = new HashMap<>();

        // Helper to safely add to map, defined locally or as a static method
        BiConsumer<String, String> addMapping = (windowsId, ianaIdStr) -> {
            try {
                tempMap.put(windowsId, ZoneId.of(ianaIdStr));
            } catch (DateTimeException e) {
                System.err.println("Error creating ZoneId for IANA ID '" + ianaIdStr + "' (Windows ID: '" + windowsId + "'): " + e.getMessage());
            }
        };

        addMapping.accept("Dateline Standard Time", "Etc/GMT+12");
        addMapping.accept("UTC-11", "Etc/GMT+11");
        addMapping.accept("Aleutian Standard Time", "America/Adak");
        addMapping.accept("Hawaiian Standard Time", "Pacific/Honolulu");
        addMapping.accept("Marquesas Standard Time", "Pacific/Marquesas");
        addMapping.accept("Alaskan Standard Time", "America/Anchorage");
        addMapping.accept("UTC-09", "Etc/GMT+9");
        addMapping.accept("Pacific Standard Time (Mexico)", "America/Tijuana");
        addMapping.accept("UTC-08", "Etc/GMT+8");
        addMapping.accept("Pacific Standard Time", "America/Los_Angeles");
        addMapping.accept("US Mountain Standard Time", "America/Phoenix");
        addMapping.accept("Mountain Standard Time (Mexico)", "America/Chihuahua");
        addMapping.accept("Mountain Standard Time", "America/Denver");
        addMapping.accept("Yukon Standard Time", "America/Whitehorse");
        addMapping.accept("Central America Standard Time", "America/Guatemala");
        addMapping.accept("Central Standard Time", "America/Chicago");
        addMapping.accept("Easter Island Standard Time", "Pacific/Easter");
        addMapping.accept("Central Standard Time (Mexico)", "America/Mexico_City");
        addMapping.accept("Canada Central Standard Time", "America/Regina");
        addMapping.accept("SA Pacific Standard Time", "America/Bogota");
        addMapping.accept("Eastern Standard Time (Mexico)", "America/Cancun");
        addMapping.accept("Eastern Standard Time", "America/New_York");
        addMapping.accept("Haiti Standard Time", "America/Port-au-Prince");
        addMapping.accept("Cuba Standard Time", "America/Havana");
        addMapping.accept("US Eastern Standard Time", "America/Indianapolis");
        addMapping.accept("Turks And Caicos Standard Time", "America/Grand_Turk");
        addMapping.accept("Paraguay Standard Time", "America/Asuncion");
        addMapping.accept("Atlantic Standard Time", "America/Halifax");
        addMapping.accept("Venezuela Standard Time", "America/Caracas");
        addMapping.accept("Central Brazilian Standard Time", "America/Cuiaba");
        addMapping.accept("SA Western Standard Time", "America/La_Paz");
        addMapping.accept("Pacific SA Standard Time", "America/Santiago");
        addMapping.accept("Newfoundland Standard Time", "America/St_Johns");
        addMapping.accept("Tocantins Standard Time", "America/Araguaina");
        addMapping.accept("E. South America Standard Time", "America/Sao_Paulo");
        addMapping.accept("SA Eastern Standard Time", "America/Cayenne");
        addMapping.accept("Argentina Standard Time", "America/Argentina/Buenos_Aires");
        addMapping.accept("Greenland Standard Time", "America/Godthab");
        addMapping.accept("Montevideo Standard Time", "America/Montevideo");
        addMapping.accept("Magallanes Standard Time", "America/Punta_Arenas");
        addMapping.accept("Saint Pierre Standard Time", "America/Miquelon");
        addMapping.accept("Bahia Standard Time", "America/Bahia");
        addMapping.accept("UTC-02", "Etc/GMT+2");
        addMapping.accept("Azores Standard Time", "Atlantic/Azores");
        addMapping.accept("Cape Verde Standard Time", "Atlantic/Cape_Verde");
        addMapping.accept("UTC", "Etc/UTC");
        addMapping.accept("GMT Standard Time", "Europe/London");
        addMapping.accept("Greenwich Standard Time", "Atlantic/Reykjavik");
        addMapping.accept("Sao Tome Standard Time", "Africa/Sao_Tome");
        addMapping.accept("Morocco Standard Time", "Africa/Casablanca");
        addMapping.accept("W. Europe Standard Time", "Europe/Berlin");
        addMapping.accept("Central Europe Standard Time", "Europe/Budapest");
        addMapping.accept("Romance Standard Time", "Europe/Paris");
        addMapping.accept("Central European Standard Time", "Europe/Warsaw");
        addMapping.accept("W. Central Africa Standard Time", "Africa/Lagos");
        addMapping.accept("Jordan Standard Time", "Asia/Amman");
        addMapping.accept("GTB Standard Time", "Europe/Bucharest");
        addMapping.accept("Middle East Standard Time", "Asia/Beirut");
        addMapping.accept("Egypt Standard Time", "Africa/Cairo");
        addMapping.accept("E. Europe Standard Time", "Europe/Chisinau");
        addMapping.accept("Syria Standard Time", "Asia/Damascus");
        addMapping.accept("West Bank Standard Time", "Asia/Hebron");
        addMapping.accept("South Africa Standard Time", "Africa/Johannesburg");
        addMapping.accept("FLE Standard Time", "Europe/Kiev");
        addMapping.accept("Israel Standard Time", "Asia/Jerusalem");
        addMapping.accept("Kaliningrad Standard Time", "Europe/Kaliningrad");
        addMapping.accept("Sudan Standard Time", "Africa/Khartoum");
        addMapping.accept("Libya Standard Time", "Africa/Tripoli");
        addMapping.accept("Namibia Standard Time", "Africa/Windhoek");
        addMapping.accept("Arabic Standard Time", "Asia/Baghdad");
        addMapping.accept("Turkey Standard Time", "Europe/Istanbul");
        addMapping.accept("Arab Standard Time", "Asia/Riyadh");
        addMapping.accept("Belarus Standard Time", "Europe/Minsk");
        addMapping.accept("Russian Standard Time", "Europe/Moscow");
        addMapping.accept("E. Africa Standard Time", "Africa/Nairobi");
        addMapping.accept("Iran Standard Time", "Asia/Tehran");
        addMapping.accept("Arabian Standard Time", "Asia/Dubai");
        addMapping.accept("Astrakhan Standard Time", "Europe/Astrakhan");
        addMapping.accept("Azerbaijan Standard Time", "Asia/Baku");
        addMapping.accept("Russia Time Zone 3", "Europe/Samara");
        addMapping.accept("Mauritius Standard Time", "Indian/Mauritius");
        addMapping.accept("Saratov Standard Time", "Europe/Saratov");
        addMapping.accept("Georgian Standard Time", "Asia/Tbilisi");
        addMapping.accept("Volgograd Standard Time", "Europe/Volgograd");
        addMapping.accept("Caucasus Standard Time", "Asia/Yerevan");
        addMapping.accept("Afghanistan Standard Time", "Asia/Kabul");
        addMapping.accept("West Asia Standard Time", "Asia/Tashkent");
        addMapping.accept("Ekaterinburg Standard Time", "Asia/Yekaterinburg");
        addMapping.accept("Pakistan Standard Time", "Asia/Karachi");
        addMapping.accept("Qyzylorda Standard Time", "Asia/Qyzylorda");
        addMapping.accept("India Standard Time", "Asia/Kolkata");
        addMapping.accept("Sri Lanka Standard Time", "Asia/Colombo");
        addMapping.accept("Nepal Standard Time", "Asia/Kathmandu");
        addMapping.accept("Central Asia Standard Time", "Asia/Almaty");
        addMapping.accept("Bangladesh Standard Time", "Asia/Dhaka");
        addMapping.accept("Omsk Standard Time", "Asia/Omsk");
        addMapping.accept("Myanmar Standard Time", "Asia/Yangon");
        addMapping.accept("SE Asia Standard Time", "Asia/Bangkok");
        addMapping.accept("Altai Standard Time", "Asia/Barnaul");
        addMapping.accept("W. Mongolia Standard Time", "Asia/Hovd");
        addMapping.accept("North Asia Standard Time", "Asia/Krasnoyarsk");
        addMapping.accept("N. Central Asia Standard Time", "Asia/Novosibirsk");
        addMapping.accept("Tomsk Standard Time", "Asia/Tomsk");
        addMapping.accept("China Standard Time", "Asia/Shanghai");
        addMapping.accept("North Asia East Standard Time", "Asia/Irkutsk");
        addMapping.accept("Singapore Standard Time", "Asia/Singapore");
        addMapping.accept("W. Australia Standard Time", "Australia/Perth");
        addMapping.accept("Taipei Standard Time", "Asia/Taipei");
        addMapping.accept("Ulaanbaatar Standard Time", "Asia/Ulaanbaatar");
        addMapping.accept("Aus Central W. Standard Time", "Australia/Eucla");
        addMapping.accept("Transbaikal Standard Time", "Asia/Chita");
        addMapping.accept("Tokyo Standard Time", "Asia/Tokyo");
        addMapping.accept("North Korea Standard Time", "Asia/Pyongyang");
        addMapping.accept("Korea Standard Time", "Asia/Seoul");
        addMapping.accept("Yakutsk Standard Time", "Asia/Yakutsk");
        addMapping.accept("Cen. Australia Standard Time", "Australia/Adelaide");
        addMapping.accept("AUS Central Standard Time", "Australia/Darwin");
        addMapping.accept("E. Australia Standard Time", "Australia/Brisbane");
        addMapping.accept("AUS Eastern Standard Time", "Australia/Sydney");
        addMapping.accept("West Pacific Standard Time", "Pacific/Port_Moresby");
        addMapping.accept("Tasmania Standard Time", "Australia/Hobart");
        addMapping.accept("Vladivostok Standard Time", "Asia/Vladivostok");
        addMapping.accept("Lord Howe Standard Time", "Australia/Lord_Howe");
        addMapping.accept("Bougainville Standard Time", "Pacific/Bougainville");
        addMapping.accept("Russia Time Zone 10", "Asia/Srednekolymsk");
        addMapping.accept("Magadan Standard Time", "Asia/Magadan");
        addMapping.accept("Norfolk Standard Time", "Pacific/Norfolk");
        addMapping.accept("Sakhalin Standard Time", "Asia/Sakhalin");
        addMapping.accept("Central Pacific Standard Time", "Pacific/Guadalcanal");
        addMapping.accept("Russia Time Zone 11", "Asia/Kamchatka");
        addMapping.accept("New Zealand Standard Time", "Pacific/Auckland");
        addMapping.accept("UTC+12", "Etc/GMT-12");
        addMapping.accept("Fiji Standard Time", "Pacific/Fiji");
        addMapping.accept("Chatham Islands Standard Time", "Pacific/Chatham");
        addMapping.accept("UTC+13", "Etc/GMT-13");
        addMapping.accept("Tonga Standard Time", "Pacific/Tongatapu");
        addMapping.accept("Samoa Standard Time", "Pacific/Apia");
        addMapping.accept("Line Islands Standard Time", "Pacific/Kiritimati");
        // ... (所有 addMapping.accept 调用)

        // 3. 将其设为不可修改的 Map，以确保线程安全和不变性
        WINDOWS_TO_ZONE_ID_MAP = Collections.unmodifiableMap(tempMap);
    }

    /**
     * Returns an unmodifiable view of the Windows to ZoneId map.
     * This method is optional if you only access the map internally
     * via getZoneIdForWindowsId.
     * @return An unmodifiable map of Windows Time Zone IDs to ZoneId objects.
     */
    public static Map<String, ZoneId> getWindowsToZoneIdMapView() {
        return WINDOWS_TO_ZONE_ID_MAP; // Already unmodifiable
    }

    public static ZoneId getZoneIdForWindowsId(String windowsTimeZoneId) {
        // 4. 直接从静态 Map 中获取
        ZoneId zoneId = WINDOWS_TO_ZONE_ID_MAP.get(windowsTimeZoneId);

        if (zoneId == null) {
            try {
                zoneId = ZoneId.of(windowsTimeZoneId, ZoneId.SHORT_IDS);
            } catch (DateTimeException | NullPointerException e) { // Catch NullPointerException for robustness
                // Fallback or further handling if needed
            }
        }

        if (zoneId == null) {
            try {
                TimeZone utilTimeZone = TimeZone.getTimeZone(windowsTimeZoneId);
                if (utilTimeZone != null && !utilTimeZone.getID().equals("GMT") && !utilTimeZone.getID().equals(windowsTimeZoneId)) {
                    zoneId = ZoneId.of(utilTimeZone.getID());
                } else if (utilTimeZone != null && utilTimeZone.getID().equals(windowsTimeZoneId)) {
                    try {
                        zoneId = ZoneId.of(windowsTimeZoneId);
                    } catch (DateTimeException ignored) {
                        // Not a valid IANA ID
                    }
                }
            } catch (Exception e) {
                // Ignore
            }
        }

        return zoneId;
    }


    public static void main(String[] args) {
        // Map<String, ZoneId> map = getWindowsToZoneIdMapView(); // Get the view if needed

        System.out.println("Windows to ZoneId Mappings (Sample from Map):");
        WINDOWS_TO_ZONE_ID_MAP.entrySet().stream().limit(10).forEach(entry ->
                System.out.println("\"" + entry.getKey() + "\" -> ZoneId: \"" + entry.getValue().getId() + "\"")
        );
        System.out.println("...\nTotal mappings in provided map: " + WINDOWS_TO_ZONE_ID_MAP.size());

        System.out.println("\n--- Individual Lookups ---");
        String windowsZone1 = "Pacific Standard Time";
        ZoneId zoneId1 = getZoneIdForWindowsId(windowsZone1);
        System.out.println("Windows: \"" + windowsZone1 + "\" -> ZoneId: " + (zoneId1 != null ? zoneId1.getId() : "Not found"));

        String windowsZone2 = "E. South America Standard Time";
        ZoneId zoneId2 = getZoneIdForWindowsId(windowsZone2);
        System.out.println("Windows: \"" + windowsZone2 + "\" -> ZoneId: " + (zoneId2 != null ? zoneId2.getId() : "Not found"));

        String windowsZoneNonExistent = "Non Existent Time Zone";
        ZoneId zoneIdNonExistent = getZoneIdForWindowsId(windowsZoneNonExistent);
        System.out.println("Windows: \"" + windowsZoneNonExistent + "\" -> ZoneId: " + (zoneIdNonExistent != null ? zoneIdNonExistent.getId() : "Not found"));
    }
}