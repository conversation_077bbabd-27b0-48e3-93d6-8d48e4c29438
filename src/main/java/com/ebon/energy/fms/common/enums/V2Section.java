package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum V2Section {
    Inverter(1),
    <PERSON><PERSON>(2),
    BatteryManager(3),
    BatteryStack(4),
    InverterControl(5),
    Site(6),
    Constraints(7);

    private final int value;

    V2Section(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static V2Section fromValue(int value) {
        for (V2Section section : V2Section.values()) {
            if (section.getValue() == value) {
                return section;
            }
        }
        throw new IllegalArgumentException("No matching V2Section for value: " + value);
    }
}
