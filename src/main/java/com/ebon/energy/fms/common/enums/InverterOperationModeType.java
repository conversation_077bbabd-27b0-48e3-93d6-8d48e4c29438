package com.ebon.energy.fms.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum InverterOperationModeType {
    Set(1),
    Schedule(2);

    // 用于存储每个枚举常量对应的顺序值
    private final int order;
    // 用于缓存枚举常量和其对应的顺序值的映射关系
    private static final Map<InverterOperationModeType, Integer> ORDER_MAP = new HashMap<>();

    static {
        // 初始化 ORDER_MAP，将每个枚举常量和其对应的顺序值存入映射中
        for (InverterOperationModeType modeType : InverterOperationModeType.values()) {
            ORDER_MAP.put(modeType, modeType.order);
        }
    }

    // 枚举的构造函数，接收一个整数参数表示顺序
    InverterOperationModeType(int order) {
        this.order = order;
    }

    // 获取枚举常量对应的顺序值的方法
    public int getOrder() {
        return order;
    }

    // 根据顺序值获取对应的枚举常量的方法
    public static InverterOperationModeType getByOrder(int order) {
        for (InverterOperationModeType modeType : InverterOperationModeType.values()) {
            if (modeType.getOrder() == order) {
                return modeType;
            }
        }
        throw new IllegalArgumentException("No InverterOperationModeType with order: " + order);
    }

    // 获取所有枚举常量和其对应的顺序值的映射关系的方法
    public static Map<InverterOperationModeType, Integer> getOrderMap() {
        return ORDER_MAP;
    }
}
