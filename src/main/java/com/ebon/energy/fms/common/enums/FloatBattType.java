package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FloatBattType {
    LeadAcidSettings(0x00),

    LiIon_BBox2_5(0x01),
    LiIon_BBox5_0(0x02),
    LiIon_BBox7_5(0x03),
    LiIon_BBox10(0x04),
    LiIon_BBox12_5(0x05),
    LiIon_LGResu6_4Ex(0x06),
    LiIon_LGResu10(0x07),
    LiIon_LGResu6_5(0x08),
    LiIon_PylonUSB2000A(0x0A),
    LiIon_PylonUSB2000B_x1(0x0B),
    LiIon_PylonUSB2000B_x2(0x0C),
    LiIon_PylonUSB2000B_x3(0x0D),
    LiIon_PylonUSB2000B_x4(0x0E),
    VRLAB_Hoppecke_SunPPack11(0x0F),
    LiIon_GCL_5_6Kwh(0x11),
    LiIon_FactoryBatt_x1(0x12),
    LiIon_FactoryBatt_x2(0x13),
    VRLAB_Default(0x14),
    LiIon_GCL_5_6Kwh_x2(0x1F),
    LiIon_GCL_5_6Kwh_x3(0x20),
    LiIon_GCL_5_6Kwh_x4(0x21),

    BMSBattery(0xFC),

    EMSBattery(0x1F9);

    private int value;


    FloatBattType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static FloatBattType forValue(int value) {
        for (FloatBattType e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FloatBattType o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}
