package com.ebon.energy.fms.common.enums;

import java.util.ArrayList;
import java.util.List;

public enum BattMode
{
    NoBattery((byte)0), // no battery, inverter disconnected
    StandBy((byte)1), // no charging or discharging
    Discharging((byte)2),
    Charging((byte)3),
    WaitCharge((byte)4), // what is that?
    WaitDischarge((byte)5);

    private byte value;

    BattMode(byte value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BattMode forValue(int value)
    {
        for (BattMode e : BattMode.values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        BattMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}
