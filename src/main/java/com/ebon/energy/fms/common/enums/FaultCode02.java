package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode02
{
    None(0),
    BackupOvervoltageFault(1 << 0),
    BackupOverloadFailure(1 << 1),
    GridConnectedBackupOverload(1 << 2),
    OffGridBackupUndervoltageFault(1 << 3);

    private int value;


    FaultCode02(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode02 forValue(int value)
    {
        for (FaultCode02 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode02 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FaultCode02> result = EnumSet.noneOf(FaultCode02.class);
        FaultCode02[] values = FaultCode02.values();
        for (FaultCode02 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
