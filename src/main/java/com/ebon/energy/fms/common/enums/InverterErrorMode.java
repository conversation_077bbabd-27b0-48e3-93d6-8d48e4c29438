package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum InverterErrorMode
{
    NoError(0),
    InternalCommunicationFailure(1 << 31),
    EEPROM_RW_Fail(1 << 30),
    FacFailure(1 << 29),
    DSPCommunicationFailure(1 << 28),
    PhaseAngleFailure(1 << 27),
    RelayCheckFailure(1 << 25),
    VacConsistencyFailure(1 << 23),
    FacConsistencyFailure(1 << 22),
    BackupOverLoad(1 << 20),
    DCInjectionHigh(1 << 19),
    IsolationFailure(1 << 18),
    VacFailure(1 << 17),
    ExternalFanFailure(1 << 16),
    PVOverVoltage(1 << 15),
    UtilityPhaseFailure(1 << 14), // Previously "AutoTestFailure" but <PERSON> confirmed it is not used in the ES.
    OverTemperature(1 << 13),
    InternalFanFailure(1 << 12),
    DCBusHigh(1 << 11),
    GroundIFailure(1 << 10),
    UtilityLoss(1 << 9),
    AC_HCT_Failure(1 << 8),
    RelayDeviceFailure(1 << 7),
    GFCI_DeviceFailure(1 << 6),
    GFCI_ConsistencyFailure(1 << 4),
    DCI_ConsistencyFailure(1 << 3),
    AC_HCT_CheckFailure(1 << 1),
    GFCI_DeviceCheckFailure(1 << 0);

    private long value;


    InverterErrorMode(long value)
    {
        this.value = value;
    }

    public long getValue()
    {
        return value;
    }

    public static InverterErrorMode forValue(long value)
    {
        for (InverterErrorMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(long value) {
        InverterErrorMode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<InverterErrorMode> result = EnumSet.noneOf(InverterErrorMode.class);
        InverterErrorMode[] values = InverterErrorMode.values();
        for (InverterErrorMode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}