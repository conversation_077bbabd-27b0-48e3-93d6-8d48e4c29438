package com.ebon.energy.fms.common.enums;

public enum InverterETPowerMode
{
    NoMode(0),
    Auto(1),
    ChargePlusPV(2), // Watt
    DischargeDeratePV(3), // Watt
    ImportPower(4), // Watt
    ExportPower(5), // <PERSON>
    Conserve(6),
    Offgrid(7),
    Hibernate(8),
    BuyPower(9),
    SellPower(10),
    ChargeBattery(11),
    DischargeBattery(12),
    Stop(255);

    private int value;


    InverterETPowerMode(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static InverterETPowerMode forValue(int value)
    {
        for (InverterETPowerMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        InverterETPowerMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}
