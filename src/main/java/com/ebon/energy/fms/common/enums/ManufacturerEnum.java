package com.ebon.energy.fms.common.enums;

public enum ManufacturerEnum {
    // No Batteries
    None(0),
    // Any Pylontech batteries
    Pylon(1),
    // Used for ageing tests and allow logic separation from None
    Custom(2),
    // Any Redbacktech batteries
    Redback(3);

    private final int value;

    ManufacturerEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ManufacturerEnum fromValue(int value) {
        for (ManufacturerEnum manufacturer : ManufacturerEnum.values()) {
            if (manufacturer.getValue() == value) {
                return manufacturer;
            }
        }
        throw new IllegalArgumentException("No matching Manufacturer for value: " + value);
    }
}
