package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum FaultCode05
{
    None(0),
    PowerGridDisturbance(1 << 0),
    DCComponentTooLarge(1 << 1),
    OverTemperatureProtection(1 << 2),
    RelayDetectionProtection(1 << 3),
    UnderTemperatureProtection(1 << 4),
    PVInsulationFailure(1 << 5),
    _12VUndervoltageProtection(1 << 6), //Add '_' for first letter;12VUndervoltageProtection
    LeakageCurrentProtection(1 << 7),
    LeakageCurrentSelfProtection(1 << 8),
    DSPInitializationProtection(1 << 9),
    DSP_BProtection(1 << 10),
    BatteryOvervoltageHardwareFailure(1 << 11),
    LLCHardwareOvercurrent(1 << 12),
    GridInstantaneousOvercurrent(1 << 13),
    CANCommunicationFailed(1 << 14),
    DSPCommunicationFailed(1 << 15);

    private int value;


    FaultCode05(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static FaultCode05 forValue(int value)
    {
        for (FaultCode05 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        FaultCode05 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<FaultCode05> result = EnumSet.noneOf(FaultCode05.class);
        FaultCode05[] values = FaultCode05.values();
        for (FaultCode05 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
