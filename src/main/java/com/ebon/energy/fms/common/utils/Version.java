package com.ebon.energy.fms.common.utils;

public final class Version implements Comparable<Version>, Cloneable {
    private final int major;
    private final int minor;
    private final int build;
    private final int revision;

    public Version() {
        this(0, 0, -1, -1);
    }

    public Version(int major, int minor) {
        this(major, minor, -1, -1);
    }

    public Version(int major, int minor, int build) {
        this(major, minor, build, -1);
    }

    public Version(int major, int minor, int build, int revision) {
        if (major < 0 || minor < 0 || build < -1 || revision < -1)
            throw new IllegalArgumentException("Version components must be non-negative (or -1 for undefined build/revision)");
        this.major = major;
        this.minor = minor;
        this.build = build;
        this.revision = revision;
    }

    public Version(String version) {
        String[] parts = version.split("\\.");
        if (parts.length < 2 || parts.length > 4)
            throw new IllegalArgumentException("Version string must have 2 to 4 components");
        this.major = Integer.parseInt(parts[0]);
        this.minor = Integer.parseInt(parts[1]);
        this.build = parts.length > 2 ? Integer.parseInt(parts[2]) : -1;
        this.revision = parts.length > 3 ? Integer.parseInt(parts[3]) : -1;
    }

    public int getMajor() { return major; }
    public int getMinor() { return minor; }
    public int getBuild() { return build; }
    public int getRevision() { return revision; }

    public int getMajorRevision() { return revision == -1 ? -1 : (revision >> 16) & 0xFFFF; }
    public int getMinorRevision() { return revision == -1 ? -1 : revision & 0xFFFF; }

    @Override
    public Object clone() {
        return new Version(major, minor, build, revision);
    }

    @Override
    public int compareTo(Version other) {
        if (other == null) return 1;
        if (major != other.major) return Integer.compare(major, other.major);
        if (minor != other.minor) return Integer.compare(minor, other.minor);
        if (build != other.build) return Integer.compare(build, other.build);
        return Integer.compare(revision, other.revision);
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof Version)) return false;
        Version other = (Version) obj;
        return major == other.major && minor == other.minor &&
               build == other.build && revision == other.revision;
    }

    @Override
    public int hashCode() {
        int result = major;
        result = 31 * result + minor;
        result = 31 * result + build;
        result = 31 * result + revision;
        return result;
    }

    @Override
    public String toString() {
        if (build == -1) return major + "." + minor;
        if (revision == -1) return major + "." + minor + "." + build;
        return major + "." + minor + "." + build + "." + revision;
    }

    public String toString(int fieldCount) {
        switch (fieldCount) {
            case 0: return "";
            case 1: return String.valueOf(major);
            case 2: return major + "." + minor;
            case 3:
                if (build == -1) throw new IllegalArgumentException("Build is undefined");
                return major + "." + minor + "." + build;
            case 4:
                if (build == -1 || revision == -1) throw new IllegalArgumentException("Build or revision is undefined");
                return major + "." + minor + "." + build + "." + revision;
            default:
                throw new IllegalArgumentException("fieldCount must be 0-4");
        }
    }

    public static Version parse(String input) {
        return new Version(input);
    }

    public static boolean tryParse(String input, Version[] result) {
        try {
            result[0] = new Version(input);
            return true;
        } catch (Exception e) {
            result[0] = null;
            return false;
        }
    }
}
