package com.ebon.energy.fms.common.enums;

public enum GridMode
{
    Loss(0x00), // inverter disconnected
    OK(0x01), // inverter connected
    Fault(0x02); // something wrong

    private int value;


    GridMode(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static GridMode forValue(int value)
    {
        for (GridMode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        GridMode o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}