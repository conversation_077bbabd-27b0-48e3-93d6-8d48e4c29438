package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum BMSProtocolCode {
    ATSDevice(0x00),
    AlphaBMS(0x01),
    ATLBMS(0x02),
    PYLONBMS_I(0x03),
    PYLONMS_II(0x04),
    //Reserved = 0x05,
    LishenBMS(0x06),
    BYDBMS(0x07),
    LGBMS(0x08),
    BYDBMU(0x09),
    //Reserved = 0x0A,
    GCLBMS(0x0B),
    PYLONBMS_Plus(0x0C),
    LishenJP(0x0D),
    //Reserved = 0x0E ~ 0x1D
    GoodWeProtocol(0x1E),
    ProtoWareHouse(0x1F),
    LeadAcidBattery(0x20),

    PylonTech(0x101),

    // as per email on 21/01/2019, trying to fix BMS
    BMSBattery(0x21),

    // The only BMS Protocol Code supported by the ET Three Phase inverter
    EMSBattery(0x11F);

    private int value;

    BMSProtocolCode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BMSProtocolCode forValue(int value) {
        for (BMSProtocolCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BMSProtocolCode o = forValue(value);
        if (o != null) {
            return o;
        }

        return value;
    }
}
