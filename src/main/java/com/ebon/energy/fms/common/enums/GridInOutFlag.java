package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum GridInOutFlag {
    Idle(0x00),    // Inverter neither send power to Grid, nor get power from Grid.
    ToGrid(0x01),  // Inverter sends power to Grid
    FromGrid(0x02); // Inverter gets power from Grid.

    private final int value;

    GridInOutFlag(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static GridInOutFlag fromValue(int value) {
        for (GridInOutFlag flag : GridInOutFlag.values()) {
            if (flag.value == value) {
                return flag;
            }
        }
        
        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        GridInOutFlag o = fromValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}