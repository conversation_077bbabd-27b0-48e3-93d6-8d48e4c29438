package com.ebon.energy.fms.common.enums;

public enum Country
{
    Italy(0x00),
    Czech(0x01),
    Germany(0x02),
    Spain(0x03),
    GreeceMainland(0x04),
    Denmark(0x05),
    Belgium(0x06),
    RomaniaSpecial(0x07),
    G83Spec(0x08),
    Australia(0x09),
    France(0x0a),
    China(0x0b),
    America(0x0c),
    Poland(0x0d),
    SouthAfrica(0x0e),
    Australia_L(0x0f),
    Brazil(0x10),
    ThailandM(0x11),
    ThailandP(0x12),
    Mauritius(0x13),
    Holland(0x14),
    Northern_Ireland(0x15),
    China_High_Pressure(0x16),
    France_50KhZ(0x17),
    France_60KhZ(0x18),
    Australia_Ergon(0x19),
    Australia_Energex(0x1a),
    Holland_16_or_20_Amp(0x1b),
    Korea(0x1c),
    China_Station(0x1d),
    Austria(0x1e),
    India(0x1f),
    Grid_Default_50Hz(0x20),
    Warehouse(0x21),
    Phillipines(0x22),
    Ireland(0x23),
    Taiwan(0x24),
    Bulgaria(0x25),
    Barbados(0x26),
    China_Max_Pressure(0x27),
    G59_3(0x28),
    Sweden(0x29),
    Chile(0x2a),
    Brazil_Low_Voltage(0x2b),
    New_Zealand(0x2c),
    IEEE1547_208Vac(0x2D),
    IEEE1547_220Vac(0x2E),
    IEEE1547_240Vac(0x2F),
    Default_60HzLowVolt(0x30),
    Default_50HzLowVolt(0x31),

    /**
     Please note that this has different meaning in firmware 171709 and 151507

     In 151507 it's AustraliaWestern and in 171709 it's Western Power
     */
    AustraliaWestern(0x32),

    /**
     Usable from 171709 + ROSS2.2
     */
    AUMicroGrid(0x33),
    Japan50Hz(0x34),
    Japan60Hz(0x35),
    IndiaHigher(0x36),
    DewaLowVolt(0x37),
    DewaMediumVolt(0x38),
    Slovakia(0x39),
    GreenGrid(0x3A),

    /**
     Usable from 171709 + ROSS2.2
     */
    AUHorizon(0x44),
    Unknown(0xff); // Error message in the inverter


    private int value;

    Country(int value)
    {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static Country forValue(int value)
    {
        for (Country e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(byte value) {
        int unsignedByte = Byte.toUnsignedInt(value);
        Country o = forValue(unsignedByte);
        if (o != null) {
            return o;
        }

        return unsignedByte;
    }
}
