package com.ebon.energy.fms.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.OperateLogDO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.mapper.primary.OperateLogMapper;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.sql.Timestamp;

@Slf4j
@Aspect
@Component
public class LogAop {
    @Resource
    private OperateLogMapper operateLogMapper;

    @Pointcut(value = "@annotation(org.springframework.web.bind.annotation.RequestMapping)")
    public void cutService() {
    }

    @Pointcut(value = "@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public void cutService2() {
    }

    @Pointcut(value = "@annotation(org.springframework.web.bind.annotation.GetMapping)")
    public void cutService3() {
    }

    @AfterReturning(pointcut = "cutService() || cutService2() || cutService3()", returning = "retVal")
    public void afterReturningAdvice(JoinPoint point, Object retVal) throws Exception {
        try {
            handle(point, retVal);
        } catch (Exception e) {
            try {
                log.error("handle admin user action aspect error, req:{}", getRequestInfo(point), e);
            } catch (Exception ex) {

            }
        }
    }

    @AfterThrowing(pointcut = "cutService() || cutService2() || cutService3()", throwing = "ex")
    public void afterThrowingAdvice(JoinPoint point, Throwable ex) {
        try {
            handle(point, ex);
        } catch (Exception e) {
            try {
                log.error("handle admin user action aspect error, req:{}", getRequestInfo(point), e);
            } catch (Exception exc) {

            }
        }
    }

    private JSONObject getRequestInfo(JoinPoint point) throws Exception {
        Method currentMethod = getCurrentMethod(point);
        Object[] args = point.getArgs();
        Annotation[][] annotations = currentMethod.getParameterAnnotations();
        for (int i = 0; i < annotations.length; i++) {
            Annotation[] arr = annotations[i];
            if (arr.length == 0) {
                continue;
            }
            for (Annotation annotation : arr) {
                if (!(annotation instanceof RequestBody)) {
                    continue;
                }
                JSONObject jo = JSON.parseObject(args[i] != null ? JSON.toJSONString(args[i]) : "{}");
                return jo;
            }
        }
        return new JSONObject();
    }

    private Method getCurrentMethod(JoinPoint point) throws Exception {
        //获取拦截的方法名
        Signature sig = point.getSignature();
        MethodSignature msig = (MethodSignature) sig;
        Object target = point.getTarget();
        Method currentMethod = target.getClass().getMethod(msig.getName(), msig.getParameterTypes());
        return currentMethod;
    }

    private void handle(JoinPoint point, Object result) throws Exception {
        Method currentMethod = getCurrentMethod(point);
        RequestMapping rmapping = currentMethod.getAnnotation(RequestMapping.class);
        GetMapping gmapping = currentMethod.getAnnotation(GetMapping.class);
        PostMapping pmapping = currentMethod.getAnnotation(PostMapping.class);
        if (rmapping == null && pmapping == null && gmapping == null) {
            return;
        }

        OperateLogAnnotation annotation = currentMethod.getAnnotation(OperateLogAnnotation.class);
        if (annotation == null) {
            return;
        }

        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            JSONObject requestJSON = getRequestInfo(point);
            String module = annotation.name();
            String username = "";
            if (module.equals("Login")) {
                //登录从请求信息中取用户名
                username = requestJSON.getString("email");
            } else {
                username = RequestUtil.getLoginUserEmail();
            }

            OperateLogDO logDO = new OperateLogDO();
            logDO.setModule(module);
            if (!module.equals("Login")) {
                logDO.setContent(requestJSON.toJSONString());
            }
            logDO.setUserName(username == null ? "" : username);
            logDO.setIpAddress(RequestUtil.getRemoteIp(request));
            logDO.setUrlAccessed(request.getServletPath());
            logDO.setTimeAccessed(new Timestamp(System.currentTimeMillis()));
            logDO.setMethod(request.getMethod());

            if (result instanceof JsonResult) {
                JsonResult jsonResult = (JsonResult) result;
                logDO.setResultCode(jsonResult.getCode());
                if (jsonResult.onSuccess() && jsonResult.getData() != null) {
                    logDO.setResultMsg(JSONObject.toJSONString(jsonResult.getData()));
                } else {
                    logDO.setResultMsg(jsonResult.getMessage());
                }
            } else if (result instanceof BizException) {
                BizException e = (BizException) result;
                logDO.setResultCode(e.getErrorCode());
                logDO.setResultMsg(e.getErrorMsg());
            } else if (result instanceof Exception) {
                logDO.setResultCode(CommonErrorCodeEnum.SYSTEM_UNKNOWN_ERROR.getErrorCode());
                logDO.setResultMsg(CommonErrorCodeEnum.SYSTEM_UNKNOWN_ERROR.getErrorMsg());
            }

            operateLogMapper.insert(logDO);
        } catch (Exception e) {
            log.error("saveLog error", e);
        }
    }

}

