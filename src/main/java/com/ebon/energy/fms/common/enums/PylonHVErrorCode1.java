package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum PylonHVErrorCode1
{
    VoltageSensorError(1 << 0),
    TemperatureSensorError(1 << 1),
    InternalCommunicationsError(1 << 2),
    InputOverVoltageError(1 << 3),
    InputReversePolarityError(1 << 4),
    RelayError(1 << 5),
    BatteryDamageError(1 << 6),
    SwitchOffCircuitError(1 << 7),
    BMICError(1 << 8),
    InternalBusError(1 << 9),
    PowerOnSelfTestError(1 << 10),
    AddChipErrorMark(1 << 11);

    private int intValue;


    private PylonHVErrorCode1(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static PylonHVErrorCode1 forValue(int value)
    {
        for (PylonHVErrorCode1 e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        PylonHVErrorCode1 o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<PylonHVErrorCode1> result = EnumSet.noneOf(PylonHVErrorCode1.class);
        PylonHVErrorCode1[] values = PylonHVErrorCode1.values();
        for (PylonHVErrorCode1 e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}