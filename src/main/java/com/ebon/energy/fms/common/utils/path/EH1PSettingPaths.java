package com.ebon.energy.fms.common.utils.path;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.ebon.energy.fms.common.enums.V2Section;
import com.ebon.energy.fms.domain.vo.product.control.V2Setting;
import com.ebon.energy.fms.domain.vo.setting.provider.SettingPaths;
import com.ebon.energy.fms.domain.vo.Ampere;
import com.ebon.energy.fms.domain.vo.Volt;

import java.math.BigDecimal;

public class EH1PSettingPaths extends SettingPaths {

    public EH1PSettingPaths() {
        super(
                null,
                null,
                "v2.ems.GridProfileId",
                "v2.ems.GridProfileCorrelationId",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "v2.ems.Relay{0}Name",
                "v2.ems.Relay{0}Installed",
                "v2.inverter.Relay{0}Active",
                "v2.site"
        );
    }

    public static V2Setting BatteryProtocol = new V2Setting(V2Section.BatteryManager, "protocol");

    public static V2Setting InverterMode = new V2Setting(V2Section.BatteryManager, "BatteryType");

    public static V2Setting BatteryType = new V2Setting(V2Section.BatteryManager, "BatteryType");

    public static V2Setting BatteryArchitecture = new V2Setting(V2Section.BatteryManager, "BatteryArchitecture");

    public static V2Setting BattertyMaxChargeCurren = new V2Setting(V2Section.BatteryManager, "MaxChargeCurrentLimitOverride");

    public static V2Setting BatteryCount = new V2Setting(V2Section.BatteryManager, "BatteryCount");

    public static V2Setting BatteryMaxChargeCurrent = new V2Setting(V2Section.BatteryManager, "MaxChargeCurrentLimitOverride");

    public static V2Setting BatteryMaxChargeVoltage = new V2Setting(V2Section.BatteryManager, "MaxChargeVoltageLimitOverride");

    public static V2Setting BatteryMaxChargePower = new V2Setting(V2Section.BatteryManager, "MaxChargePower");

    public static V2Setting BatteryMaxDischargeCurrent = new V2Setting(V2Section.BatteryManager, "MaxDischargeCurrentLimitOverride");

    public static V2Setting BatteryMaxDischargePower = new V2Setting(V2Section.BatteryManager, "MaxDischargePower");

    public static V2Setting BatteryMinSoc0to1 = new V2Setting(V2Section.BatteryManager, "MinSOC");

    public static V2Setting BatteryMinOffgridSoc0to1 = new V2Setting(V2Section.BatteryManager, "MinSOCOffgrid");

    public static V2Setting BatteryModuleRatedMaxChargeCurrent = new V2Setting(V2Section.BatteryManager, "ModuleRatedMaxChargeCurrent");

    public static V2Setting BatteryModuleRatedMaxDischargeCurrent = new V2Setting(V2Section.BatteryManager, "ModuleRatedMaxDischargeCurrent");

    public static V2Setting BatteryModuleRatedMaxChargeVoltage = new V2Setting(V2Section.BatteryManager, "ModuleRatedMaxChargeVoltage");

    public static V2Setting BatteryModuleRatedMinDischargeVoltage = new V2Setting(V2Section.BatteryManager, "ModuleRatedMinDischargeVoltage");

    public static V2Setting BatteryModuleRatedMinDischargeVoltageOffgrid = new V2Setting(V2Section.BatteryManager, "ModuleRatedMinDischargeVoltageOffgrid");

    public static V2Setting BatteryModuleRatedMinSOC = new V2Setting(V2Section.BatteryManager, "ModuleRatedMinSOC");

    public static V2Setting BatteryModuleRatedMinSOCOffgrid = new V2Setting(V2Section.BatteryManager, "ModuleRatedMinSOCOffgrid");

    public static V2Setting BatteryInverterRatedMaxChargeCurrent = new V2Setting(V2Section.BatteryManager, "InverterRatedMaxChargeCurrent");

    public static V2Setting BatteryInverterRatedMaxDischargeCurrent = new V2Setting(V2Section.BatteryManager, "InverterRatedMaxDischargeCurrent");

    public static V2Setting BatteryWakeTriggerVoltage = new V2Setting(V2Section.BatteryManager, "BatteryWakeTriggerVoltage");

    // 默认值方法
    public static Ampere getBatteryModuleRatedMaxChargeCurrentDefault() {
        return new Ampere(80);
    }

    public static Ampere getBatteryModuleRatedMaxDischargeCurrentDefault() {
        return new Ampere(80);
    }

    public static Volt getBatteryModuleRatedMaxChargeVoltageDefault() {
        return new Volt(new BigDecimal("53.2"));
    }

    public static Volt getBatteryModuleRatedMaxChargeVoltageForRedbackDefault() {
        return new Volt(new BigDecimal("54.0"));
    }

    public static Volt getBatteryModuleRatedMinDischargeVoltageDefault() {
        return new Volt(46);
    }

    public static Volt getBatteryModuleRatedMinDischargeVoltageForRedbackDefault() {
        return new Volt(new BigDecimal("44.0"));
    }

    public static Volt getBatteryModuleRatedMinDischargeVoltageOffgridDefault() {
        return new Volt(46);
    }

    public static Volt getBatteryModuleRatedMinDischargeVoltageOffgridForRedbackDefault() {
        return new Volt(new BigDecimal("44.0"));
    }

    public static BigDecimal getBatteryModuleRatedMinSOCDefault() {
        return new BigDecimal("0.1");
    }

    public static BigDecimal getBatteryModuleRatedMinSOCOffgridDefault() {
        return new BigDecimal("0.05");
    }

    public static Ampere getBatteryInverterRatedMaxChargeCurrentDefault() {
        return new Ampere(120);
    }

    public static Ampere getBatteryInverterRatedMaxDischargeCurrentDefault() {
        return new Ampere(120);
    }

    /**
     * 根据制造商获取电池配置信息
     */
    public static class BatteryConfig {
        private final String protocol;
        private final String batteryArchitecture;

        public BatteryConfig(String protocol, String batteryArchitecture) {
            this.protocol = protocol;
            this.batteryArchitecture = batteryArchitecture;
        }

        public String getProtocol() {
            return protocol;
        }

        public String getBatteryArchitecture() {
            return batteryArchitecture;
        }
    }

    public static BatteryConfig fromManufacturer(ManufacturerEnum manufacturer) {
        if (manufacturer == null) {
            return new BatteryConfig("None", null);
        }

        // Comment from the spec as of 2.20:
        // For HV Batteries this will need to be updated.
        if (manufacturer == ManufacturerEnum.None) {
            return new BatteryConfig("None", "Parallel");  // Even for "None" we want to return a valid architecture; User Story 84542: GEN3 Battery Default Values
        }

        // Temporarily disable, wait for device support of 2025.03.11
        // if (manufacturer == ManufacturerEnum.Redback) {
        //     return new BatteryConfig("RED-R1-5000LV", "Redback");
        // }

        return new BatteryConfig("PylonUS", "Parallel");
    }

}
