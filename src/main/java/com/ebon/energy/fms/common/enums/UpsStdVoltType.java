package com.ebon.energy.fms.common.enums;

public enum UpsStdVoltType
{
    Volt_208(0),
    Volt_220(1),
    Volt_240(2),
    Volt_230(3);

    private int value;

    UpsStdVoltType(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static UpsStdVoltType forValue(int value)
    {
        for (UpsStdVoltType e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        UpsStdVoltType o = forValue(value);
        if (o != null) {
            return o;
        }
        
        return value;
    }
}