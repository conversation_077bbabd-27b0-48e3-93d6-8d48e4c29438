package com.ebon.energy.fms.common.enums;

import com.alibaba.fastjson.annotation.JSONCreator;

public enum BatteryModelEnum {
    None(0),
    Unknown(1),
    Custom(2),
    LG_RESU_6_4Ex(301),
    LG_RESU_3_3(302),
    LG_RESU_6_5(303),
    LG_RESU_10(304),
    LG_RESU_Plus(305),
    LG_M48063P3S(306),
    LG_M48126P3S(307),
    Pylon_US2000A(401),
    Pylon_US2000B(402),
    Pylon_US2000BPlus(403),
    Pylon_US3000A(404),
    Pylon_US3000B(405),
    LeadAcid_Flooded(501),
    LeadAcid_Sealed(502),
    SimpliPHi_2_6(601),
    SimpliPHi_3_4(602),
    Redflow_V1(701),
    Aquion_S20P(801),
    Aquion_S30(802),
    Aquion_Aspen_48S_2_2(803);

    private final int value;

    BatteryModelEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    @JSONCreator
    public static BatteryModelEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }

        for (BatteryModelEnum model : BatteryModelEnum.values()) {
            if (model.getValue() == value) {
                return model;
            }
        }
        throw new IllegalArgumentException("No matching BatteryModel for value: " + value);
    }
}
