package com.ebon.energy.fms.common.enums;

/**
 * Enum for inverter operation modes
 */
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public enum InverterOperationModeEnum {
    @JsonProperty("NoMode")
    NoMode(0, "NoMode"),
    @JsonProperty("Auto")
    Auto(1, "Auto"),
    @JsonProperty("ChargeBattery")
    ChargeBattery(2, "Charge Battery"),
    @JsonProperty("DischargeBattery")
    DischargeBattery(3, "Discharge Battery"),
    @JsonProperty("ImportPower")
    ImportPower(4, "Import Power"),
    @JsonProperty("ExportPower")
    ExportPower(5, "Export Power"),
    @JsonProperty("Conserve")
    Conserve(6, "Conserve"),
    @JsonProperty("Offgrid")
    Offgrid(7, "Off Grid"),
    @JsonProperty("Hibernate")
    Hibernate(8, "Hibernate"),
    @JsonProperty("BuyPower")
    BuyPower(9, "Buy Power"),
    @JsonProperty("SellPower")
    SellPower(10, "Sell Power"),
    @JsonProperty("ForceChargeBattery")
    ForceChargeBattery(11, "Force Charge Battery"),
    @JsonProperty("ForceDischargeBattery")
    ForceDischargeBattery(12, "Force Discharge Battery"),
    @JsonProperty("Stop")
    Stop(255, "Stop");

    private final int code;
    private final String displayName;

    InverterOperationModeEnum(int code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }


}
