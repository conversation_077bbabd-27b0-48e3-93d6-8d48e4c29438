package com.ebon.energy.fms.common.enums;

public enum TelemetryErrorCodeEnum {
    // 通用错误
    GeneralError(0),
    ConfigurationMismatch(1),
    UnableToExtractSettingsFromTwinCollection(2),
    UnableToExtractDesiredSettingsFrom<PERSON>son(3),
    UnableToExtractReportedSettingsFromJson(4),
    TimedOutWaitingForNextRoundOfTelemetry(5),
    // 注意拼写错误不要更改
    InveterManagerTaskFailed(6),
    InverterManagmentServiceProcessorFailedFlushing(7),
    TryGetFrameAsyncFailed(8),
    ErrorRetrievingTelemetry(9),
    FailedToApplyInverterSafetyCountry(10),
    FailedToApplyInverterShadowScanChange(11),
    FailedToApplySiteExportLimitPower(12),
    FailedToApplyDoesSiteRequireExportLimit(13),
    BatteryCabintetComms(14),
    FailedToApplyPowerFactor(15),
    IssueWithSiteExportLimitRequest(16),
    IssueWithPowerFactorRequest(17),
    IssueWithShadowScanChangeRequest(18),
    IssueWithSafetyCountryChangeRequest(19),
    TestError(20),
    TwinWithNoSettings(21),
    FailedToSaveAggregates(22),
    MissingAggregateTelemetry(23),
    PylonChargeRequest(24),
    InverterManagerTaskTimeout(25),
    TryGetFrameAsyncOpenPortFailed(26),
    TryGetFrameAsyncFailedTimeout(27),
    InveterManagerTaskOpenPortFailed(28),
    PylonOpenPortFailed(29),
    UnknownVoltageForSafetyCountry(30),
    PCA9534Comms(31),
    UnableToExtractReportedSettingsFromTwin(32),
    NumberOfBatteriesChanged10Times(33),
    BatteryCommsFailuresAbove10Percent(34),
    BatteryCommsWithExtraBytesAbove10Percent(35),
    BatteryCommsExtraBytesAbove1Percent(36),
    IoTHubDesiredUploadFailedNoTwin(37),
    IoTHubDesiredUploadFailedNoTwinDesired(38),
    IoTHubDesiredUploadFailedCannotExtractDesired(39),
    IoTHubDesiredUploadFailedUploadedNotEqualToWhatInCloud(40),
    // 无电池检测到
    NoBatteries(41),
    // 缺少电池
    MissingBatteries(42),
    // 多余电池
    ExtraBatteries(43),
    FailedToSaveSettingViaDirectMessage(44),
    DetectedAlreadyUpdatedFirmware(45),
    SystemIsNotReadyForUpdate(46),
    InverterVersionOutOfSyncAfterUpdate(47),
    FirmwareUpdateCannotReadVersionAfterUpdate(48),
    FirmwareUpdateUnexpectedFailure(49),
    FirmwareUpdateTokenAlreadyUsed(50),
    IssueWithBackupStartDelayMinutesRequest(51),
    FailedToApplyBackupStartDelayMinutesRequest(52),
    IssueWithBattOfflineSOCUnderMinRequest(53),
    FailedToApplyBattOfflineSOCUnderMin(54),
    FailedToApplyMagicSetting(55),
    IssueWithMagicSettingRequest(56),
    IssueWithRecoverTimeEERequest(57),
    FailedToApplyRecoverTimeEE(58),
    UnknownDirectMethod(59),
    FailedToOverrideTelemetry(60),
    FailedToSaveTeletryOverrides(61),
    FailedToQueueBatteryCommandViaDirectMessageEmptyData(62),
    FailedToQueueBatteryCommandViaDirectMessage(63),
    FailedToQueuePylonShutdownCommandViaDirectMessageEmptyData(64),
    FailedToQueuePylonShutdownCommandViaDirectMessage(65),
    FailedToReadDesiredFromFullTwin(66),
    Ross1Error(67),
    Ross1BatteryError(68),
    InvalidTimeZoneFormat(69),
    HasLocalScheduleButTimeZoneNotConfigured(70),
    CustomBatteryNotSupported(71),
    UnknownBatteryManufacturer(72),
    FailedToWriteRelay(73),
    FailedToReadRelay(74),
    InvalidScheduleConfiguration(75),
    ParallelBatteryVoltageSpreadOverThreshold(76),
    ServiceHasNotRunForALongTime(77),
    RiskyActionHasNotFinishedYet(78),
    ServiceHasNotFinishedYet(79),
    BatteryShutdownFailed(80),
    FirmwareUpdateBlockedUntilInverterCommsEstablished(81),
    FirmwareUpdateFailedInvalidVersionForHardwareConfig(82),
    DetectedBrokenOrInvalidDred(83),
    IssueWithPowerModeRequest(84),
    FailedToApplyPowerMode(85),
    BatteryCommsTooOld(86),
    // 不推荐使用
    @Deprecated
    PlateRatingLowerThanBatteriesSuggest(87),
    PylonLVFailedToDecode(88),
    ErrorHandlingDirectMethod(89),
    InappropriateUsageOfGeneralErrorCode(90),
    IssueWithArbitrarySetting(91),
    TelemetryNotSentClientNotInitialised(92),
    GridSellandGridBuyAreZero(93),
    PvRunningTotalIsTooHighAndExceedsThreshold(94),
    TotalLoadRunningTotalIsTooHighAndExceedsThreshold(95),
    TotalGridRunningTotalIsTooHighAndExceedsThreshold(96),
    InvalidBatteriesDetected(97),
    UnsupportedInverterHardware(98),
    UnknownTelemetryBandType(99),
    FailedToReadInverterBand(100),
    FailedToReadInverterFallbackBand(101),
    InverterCommsFailedForHardwareConfig(102),
    FirmwareUpdateFlashAttemptFailed(103),
    FirmwareUpdateAllFlashAttemptsFailed(104),
    FirmwareUpdateLeftoverBytesFoundInPostflight(105),
    FirmwareUpdateFailedToFindFirmware(106),
    FirmwareUpdateUnableToReestablishComms(107),
    FirmwareUpdateNoMatchingFirmwareFound(108),
    FirmwareUpdateEncounteredGoodweBug(109),
    // 丢失电池
    LostBatteries(110),
    NetworkContextUpdateTimedOut(111),
    NetworkContextUpdateEncounteredError(112),
    NetworkContextUpdateFinishedAfterTimeout(113),
    PylonHVFoundExtraBytesInBuffer(114),
    PylonHVReadError(115),
    PylonHVFailedSoftwake(116),
    PylonHVFailedShutdown(117),
    PylonHVAnomalousBandData(118),
    FanControllerConfigMismatch(119),
    ManagedInverterSettingInvalidFormat(120),
    ManagedInverterSettingDuplicate(121),
    ManagedInverterSettingInvalidTargetRegister(122),
    ManagedInverterSettingFailedToProcess(123),
    BatteryHardwareStartupAttemptsExceeded(124),
    BluetoothReadError(125),
    BluetoothWriteError(126),
    PylonHVAttemptingSoftwake(127),
    PylonHVAttemptingHardwake(128),
    PylonHVFirmwareUpdateFailure(129),
    PylonHVFirmwareUpdateRejected(130),
    PylonHVFirmwareUpdateSlaveUpdateFailed(131),
    PylonHVFirmwareUpdateSlaveTransmitFailed(132),
    PylonHVFirmwareUpdateStateReadFailed(133),
    BatteryUpdateMissingParameters(134),
    BatteryDoesNotSupportFirmwareUpdate(135),
    BatteryUpdateFirmwareNotFound(136),
    ArbitrarySettingFailedToDecodeFromDesired(137),
    ArbitrarySettingFailedToApply(138),
    SerialPortMoreThanTenPercentOfBytesUnused(139),
    SerialPortNoResponseToWrites(140),
    SerialPortProcessorReadThreadError(141),
    SerialPortProcessorReadThreadCanceled(142),
    SerialPortProcessorWriteThreadError(143),
    SerialPortProcessorWriteThreadCanceled(144),
    PylonLVMalformedResponse(145),
    PylonHVMalformedResponse(146),
    PylonLVTimeoutWaitingForResponse(147),
    PylonHVTimeoutWaitingForResponse(148),
    PylonHVAttemptingChargeCommand(149),
    PylonHVFailedChargeCommand(150),
    BatterySoCOutsideAcceptableRange(151),
    BatteryOverridesActive(152),
    BMSProcessorDisabled(153),
    BluetoothStartError(154),
    BluetoothStopError(155),
    BluetoothAdvertisingFailed(156),
    IotClientDeviceTwinError(157),
    IotClientFailedToSendEvent(158),
    IotClientUpdateReportedFailed(159),
    IotClientBlobUpdateFailed(160),
    FailedToConvertGridTieVersion(161),
    // 有效作为转换错误
    MissingInverterFirmwareVersion(162),
    GridTieConversion(163),
    BatteryChargeTotalIsZero(164),
    BatteryDischargeTotalIsZero(165),
    TotalBatteryChargeRunningTotalIsTooHighAndExceedsThreshold(166),
    TotalBatteryDischargeRunningTotalIsTooHighAndExceedsThreshold(167),
    MissingGridReferenceCT(168),
    IssueWithAS4777SiteExportLimits(169),
    PylonHVAttemptingDischargeCommand(170),
    PylonHVFailedDischargeCommand(171),
    ArbitraryReaderFailedToDecodeFromDesired(172),
    ArbitraryReaderFailedToApply(173),
    SmartLoadControlFailedToDecodeFromDesired(174),
    SmartLoadControlMissingSomeRequiredFields(175),
    SmartLoadControlFailedDuringExecution(176),
    ConstraintStarted(177),
    ConstraintEnded(178),
    MaxSiteImportPowerWChanged(179),
    MaxSiteExportPowerWChanged(180),
    MaxChargePowerWChanged(181),
    MaxDischargePowerWChanged(182),
    MaxSiteGenerationPowerVAChanged(183),
    MaxNetworkExportPowerWChanged(184),
    MaxNetworkGenerationPowerVAChanged(185),
    // 186 - 191 错误不被 ROSS 发送
    // 有效作为转换错误
    MissingModelName(186),
    // 有效作为转换错误
    MissingBatchReferenceDataItem(187),
    // 有效作为转换错误
    ESGConversion(188),
    // 有效作为转换错误
    UnknownHardwareFirmwareConfiguration(189),
    BatteryAlarm(190),
    BalanceChargeFailedToFixCellImbalance(191),
    // EMS 区域
    // 缺少错误编号
    MissingErrorNumber(4998),
    // 错误编号超出范围
    OutOfRangeErrorNumber(4999),
    // 未知错误
    Unknown(5000),
    // 全部启动
    AllStarted(5001),
    // 处理期望设置
    ProcessingDesired(5002),
    // 处理完期望设置
    ProcessedDesired(5003),
    // 存储的设置无效
    StoredSettingsInvalid(5004),
    // 没有存储的设置
    NoStoredSettings(5005),
    // 期望设置缺少版本号
    DesiredMissingVersion(5006),
    // 期望的良好 EMS 设置
    DesiredGoodEmsSetting(5007),
    // 期望的不良 EMS 设置
    DesiredBadEmsSetting(5008),
    // 期望的未知 EMS 设置
    DesiredUnknownEmsSetting(5009),
    // 期望的良好 EMS 固件
    DesiredGoodEmsFirmware(5010),
    // 期望的不良 EMS 固件
    DesiredBadEmsFirmware(5011),
    // Azure DPS 注册失败
    AzureDPSRegistrationFailed(5012),
    // Azure DPS 注册无法保存
    AzureDPSRegistrationCouldNotSave(5013),
    // Azure DPS 注册成功
    AzureDPSRegistrationSuccess(5014),
    // Azure DPS 注册已保存
    AzureDPSRegistrationSaved(5015),
    // Azure 消息确认成功
    AzureMessageConfirmationSuccess(5016),
    // Azure 消息确认失败
    AzureMessageConfirmationFailed(5017),
    // Azure 消息发送成功
    AzureMessageSendSuccess(5018),
    // Azure 消息发送失败
    AzureMessageSendFailed(5019),
    // 收到 Azure 直接命令
    AzureDirectCommandReceived(5020),
    // 收到无主体的 Azure 直接命令
    AzureDirectCommandReceivedNoBody(5021),
    // Azure 直接命令有效负载无效
    AzureDirectCommandInvalidPayload(5022),
    // Azure 直接命令完成
    AzureDirectCommandFinished(5023),
    // 收到完整的 Azure 孪生数据
    AzureTwinFullReceived(5024),
    // 完整的 Azure 孪生数据无效
    AzureTwinFullInvalid(5025),
    // 完整的 Azure 孪生数据缺少报告部分
    AzureTwinFullMissingReported(5026),
    // 完整的 Azure 孪生数据报告部分无法分配内存
    AzureTwinFullReportedCouldNotAlloc(5027),
    // 完整的 Azure 孪生数据报告部分无法发送
    AzureTwinFullReportedCouldNotSend(5028),
    // 收到 Azure 孪生数据补丁
    AzureTwinPatchReceived(5029),
    // 收到未知的 Azure 孪生数据
    AzureTwinUnknownReceived(5030),
    // Azure 报告成功上传
    AzureReportedSuccessfulUpload(5031),
    // Azure 报告上传失败
    AzureReportedFailedUpload(5032),
    // Azure 开发人员错误
    AzureDeveloperError(5033),
    // 报告跟踪不匹配
    ReportedTraceMismatch(5034),
    // 报告无法分配补丁
    ReportedCouldNotAllocPatch(5035),
    // 报告成功
    ReportedSuccessful(5036),
    // 报告失败
    ReportedFailed(5037),
    // 报告超时
    ReportedTimedOut(5038),
    // 报告重复请求
    ReportedDuplicateRequest(5039),
    // 报告完整同步超时
    ReportedFullSyncTimedOut(5040),
    // 报告逆变器未知地址
    ReportedInverterUnknownAddress(5041),
    // 报告逆变器源数据大小错误
    ReportedInverterBadSourceSize(5042),
    // 报告逆变器无可用数据
    ReportedInverterNoDataAvailable(5043),
    // 期望的逆变器条目类型错误
    DesiredInverterEntryBadType(5044),
    // 期望的逆变器节点类型错误
    DesiredInverterNodeWrongType(5045),
    // 期望的有效逆变器序列号节点类型错误
    DesiredValidInverterSerialNodeWrongType(5046),
    // 跳过空遥测数据
    SkippedEmptyTelemetry(5047),
    // 将遥测数据从积压中推出
    PushedTelemetryOutOfBacklog(5048),
    // BLE 开始广播
    BLEStartAdvertising(5049),
    // BLE 停止广播
    BLEStopAdvertising(5050),
    // I2C 驱动安装失败
    I2CDriverInstallFailed(5051),
    // Io 扩展器 pca9536 配置 IO 设置
    IoExpanderpca9536ConfigIOsetup(5052),
    // Io 扩展器 pca9536 配置极性
    IoExpanderpca9536ConfigPolarity(5053),
    // Io 扩展器 pca9536 设置输出失败
    IoExpanderpca9536FailedSetOutputs(5054),
    // Io 扩展器 pca9536 读取输入失败
    IoExpanderpca9536FailedToReadInput(5055),
    // 逆变器设置未知键
    InverterSettingsUnknownKey(5056),
    // 逆变器设置非法值
    InverterSettingsIllegalValue(5057),
    // 逆变器设置无组
    InverterSettingsNoGroup(5058),
    // 逆变器设置源数据大小错误
    InverterSettingsBadSourceSize(5059),
    // 逆变器设置无可用数据
    InverterSettingsNoDataAvailable(5060),
    // 逆变器设置请求不可能
    InverterSettingsRequestImpossible(5061),
    // 逆变器设置请求已生成
    InverterSettingsRequestGenerated(5062),
    // 逆变器设置请求缓冲区已满
    InverterSettingsRequestBufferFull(5063),
    // 逆变器未知地址
    InverterUnknownAddress(5064),
    // 逆变器源数据大小错误
    InverterBadSourceSize(5065),
    // 逆变器无可用数据
    InverterNoDataAvailable(5066),
    // Modbus 读取无响应
    ModbusReadNoResponse(5067),
    // Modbus 读取成功
    ModbusReadSuccess(5068),
    // Modbus 读取响应大小错误
    ModbusReadResponseWrongSize(5069),
    // Modbus 读取错误响应
    ModbusReadErrorResponse(5070),
    // Modbus 读取未知响应
    ModbusReadUnknownResponse(5071),
    // Modbus 写入设置非法长度
    ModbusWriteSettingIllegalLength(5072),
    // Modbus 写入未处理的寄存器
    ModbusWriteWroteUnhandledRegister(5073),
    // Modbus 写入但无逆变器响应
    ModbusWriteWroteButNoInverter(5074),
    // Modbus 写入无响应
    ModbusWriteNoResponse(5075),
    // Modbus 写入成功
    ModbusWriteSuccess(5076),
    // Modbus 写入失败
    ModbusWriteFailed(5077),
    // Modbus 写入未知响应
    ModbusWriteUnknownResponse(5078),
    // 时间从逆变器同步
    TimeSyncedFromInverter(5079),
    // 时间同步到逆变器
    TimeSyncedToInverter(5080),
    // 捕获到逆变器序列号
    CapturedInverterSerial(5081),
    // 捕获到的逆变器序列号已更改
    CapturedInverterSerialChanged(5082),
    // 无法初始化以太网
    CannotInitEth(5083),
    // 开始回滚
    BeginRollback(5084),
    // 开始测试模式
    BeginTestmode(5085),
    // 开始更新
    BeginUpdate(5086),
    // 工厂回滚失败
    FactoryRollbackFailed(5087),
    // 工厂回滚开始
    FactoryRollbackStarting(5088),
    // OTA 开发错误
    OTADevError(5089),
    // OTA 更新下载失败
    OTAUpdateDownloadFailed(5090),
    // OTA 更新下载成功
    OTAUpdateDownloadSuccess(5091),
    // OTA 更新后检查失败
    OTAUpdateFailedPostCheck(5092),
    // OTA 更新开始
    OTAUpdateStarted(5093),
    // OTA 更新后检查成功
    OTAUpdateSuccessPostCheck(5094),
    // OTA 等待云确认
    OTAWaitingForCloudBeforeSignoff(5095),
    // 逆变器同步无期望序列号
    InverterSyncNoExpectedSN(5096),
    // 逆变器同步无当前序列号
    InverterSyncNoCurrentSN(5097),
    // 逆变器同步序列号不匹配
    InverterSyncMismatchSN(5098),
    // 开发错误
    DevError(5099),
    // SNTP 启动
    SNTPStarted(5100),
    // SNTP 时间同步
    SNTPTimeSynced(5101),
    // SNTP 未知事件
    SNTPUnknownEvent(5102),
    // 进入生产测试模式
    EnteringProdTestMode(5103),
    // OTA 进度更新
    OTAProgressUpdate(5104),
    // 事件队列已满
    EventQueueFull(5105),
    // Azure 请求从补丁获取完整设备孪生数据
    AzureRequestingFullDeviceTwinFromPatch(5106),
    // Azure 请求从请求获取完整设备孪生数据
    AzureRequestingFullDeviceTwinFromReq(5107),
    // 遥测频段过期
    TelemetryBandExpired(5108),
    // Azure 直接命令存活
    AzureDirectCommandAlive(5109),
    // Azure 直接命令 BLE 广播
    AzureDirectCommandBleAdvertise(5110),
    // Azure 直接命令 DPS 覆盖
    AzureDirectCommandDPSOverride(5111),
    // Azure 直接命令 EMS 工厂重置
    AzureDirectCommandEMSFactoryReset(5112),
    // Azure 直接命令 EMS 重启
    AzureDirectCommandEMSReboot(5113),
    // Azure 直接命令逆变器清除能源历史
    AzureDirectCommandInverterClearEnergyHistory(5114),
    // Azure 直接命令逆变器工厂重置
    AzureDirectCommandInverterFactoryReset(5115),
    // Azure 直接命令逆变器功率因数测试
    AzureDirectCommandInverterPowerFactorTest(5116),
    // Azure 直接命令逆变器无功功率测试
    AzureDirectCommandInverterReactivePowerTest(5117),
    // Azure 直接命令逆变器重启
    AzureDirectCommandInverterReboot(5118),
    // Azure 直接命令电表清除能源历史
    AzureDirectCommandMeterClearEnergyHistory(5119),
    // Azure 直接命令电表重置最小最大值
    AzureDirectCommandMeterResetMinMax(5120),
    // Azure 直接命令 OTA 更新
    AzureDirectCommandOTAUpdate(5121),
    // Azure 直接命令回滚
    AzureDirectCommandRollback(5122),
    // Azure 直接命令测试模式
    AzureDirectCommandTestMode(5123),
    // Azure 直接命令 Wi-Fi 连接
    AzureDirectCommandWifiConnect(5124),
    // Azure 直接命令重置固件获取器
    AzureDirectCommandResetFirmwareFetcher(5125),
    // Azure 直接命令更新逆变器主程序
    AzureDirectCommandUpdateInverterMaster(5126),
    // Azure 直接命令更新逆变器从程序
    AzureDirectCommandUpdateInverterSlave(5127),
    // Azure 直接命令更新电表
    AzureDirectCommandUpdateMeter(5128),
    // 固件下载器块重置
    FirmwareDownloaderBlockReset(5129),
    // 固件下载器启动
    FirmwareDownloaderStarted(5130),
    // 固件下载器下载失败
    FirmwareDownloaderFailedDownload(5131),
    // 固件下载器哈希错误
    FirmwareDownloaderBadHash(5132),
    // 固件下载器成功
    FirmwareDownloaderSuccess(5133),
    // 固件更新未知电表硬件
    FirmwareUpdateUnknownMeterHardware(5134),
    // 固件更新逆变器主程序失败
    FirmwareUpdateFailedInverterMaster(5135),
    // 固件更新逆变器从程序失败
    FirmwareUpdateFailedInverterSlave(5136),
    // 固件更新 PA 失败
    FirmwareUpdateFailedPA(5137),
    // 固件更新逆变器主程序成功
    FirmwareUpdateSucceededInverterMaster(5138),
    // 固件更新逆变器从程序成功
    FirmwareUpdateSucceededInverterSlave(5139),
    // 固件更新 PA 成功
    FirmwareUpdateSucceededPA(5140),
    // 固件更新逆变器进度
    FirmwareUpdateProgressInverter(5141),
    // 固件更新 PA 进度
    FirmwareUpdateProgressPA(5142),
    // 期望的良好逆变器主程序固件
    DesiredGoodInvMasterFirmware(5143),
    // 期望的不良逆变器主程序固件
    DesiredBadInvMasterFirmware(5144),
    // 期望的良好逆变器从程序固件
    DesiredGoodInvSlaveFirmware(5145),
    // 期望的不良逆变器从程序固件
    DesiredBadInvSlaveFirmware(5146),
    // 期望的良好电表固件
    DesiredGoodMeterFirmware(5147),
    // 期望的不良电表固件
    DesiredBadMeterFirmware(5148),
    // 逆变器主程序引导加载程序
    InverterBootloaderMaster(5149),
    // 逆变器从程序引导加载程序
    InverterBootloaderSlave(5150),
    // 电表引导加载程序
    InverterBootloaderMeter(5151),
    // 未知引导加载程序
    InverterBootloaderUnknown(5152),
    // 固件下载通用错误
    FirmwareDownloadGeneralError(5153),
    // 固件下载状态码错误
    FirmwareDownloadBadStatusCode(5154),
    // 固件下载打开请求错误
    FirmwareDownloadOpenRequestError(5155),
    // Azure 直接命令设置 NFC URL
    AzureDirectCommandSetNfcUrl(5156),
    // NFC 工作触发
    NfcWipTriggered(5157),
    // 逆变器引导加载程序检查开始
    InverterBootloaderCheckStarted(5158),
    // 逆变器引导加载程序检查发现引导加载程序
    InverterBootloaderCheckFoundBootloader(5159),
    // 逆变器引导加载程序检查未发现引导加载程序
    InverterBootloaderCheckNoBootloader(5160),
    // 自行重启
    SelfAdministeredReboot(5161),
    // Azure 直接命令终止 Azure 任务
    AzureDirectCommandKillAzureTask(5162),
    // Azure 直接命令逆变器串行命令
    AzureDirectCommandInverterSerialCommand(5163),
    // 期望的良好继电器设置
    DesiredGoodRelaySettings(5164),
    // 期望的不良继电器设置
    DesiredBadRelaySettings(5165),
    // 无效的时区变量
    InvalidTimezoneVariable(5166),
    // 时区变量未定义
    TimeZoneVariableNotDefined(5167),
    // 期望设置错误
    DesiredSettingsError(5168),
    // 从逆变器同步的时间在过去
    TimeSyncFromInverterInPast(5169),
    // 调度继电器设置错误
    ScheduleBadRelaySetting(5170),
    // 继电器设置错误
    BadRelaySetting(5171),
    // PRO 区域
    // 未知错误
    PRO_Unknown(6000),
    // 全部启动
    PRO_AllStarted(6001),
    // 处理期望设置
    PRO_ProcessingDesired(6002),
    // 处理完期望设置
    PRO_ProcessedDesired(6003),
    // 存储的设置无效
    PRO_StoredSettingsInvalid(6004),
    // 没有存储的设置
    PRO_NoStoredSettings(6005),
    // 期望设置缺少版本号
    PRO_DesiredMissingVersion(6006),
    // 期望的良好 EMS 设置
    PRO_DesiredGoodEmsSetting(6007),
    // 期望的不良 EMS 设置
    PRO_DesiredBadEmsSetting(6008),
    // 期望的未知 EMS 设置
    PRO_DesiredUnknownEmsSetting(6009),
    // 期望的良好 EMS 固件
    PRO_DesiredGoodEmsFirmware(6010),
    // 期望的不良 EMS 固件
    PRO_DesiredBadEmsFirmware(6011),
    // Azure DPS 注册失败
    PRO_AzureDPSRegistrationFailed(6012),
    // Azure DPS 注册无法保存
    PRO_AzureDPSRegistrationCouldNotSave(6013),
    // Azure DPS 注册成功
    PRO_AzureDPSRegistrationSuccess(6014),
    // Azure DPS 注册已保存
    PRO_AzureDPSRegistrationSaved(6015),
    // Azure 消息确认成功
    PRO_AzureMessageConfirmationSuccess(6016),
    // Azure 消息确认失败
    PRO_AzureMessageConfirmationFailed(6017),
    // Azure 消息发送成功
    PRO_AzureMessageSendSuccess(6018),
    // Azure 消息发送失败
    PRO_AzureMessageSendFailed(6019),
    // 收到 Azure 直接命令
    PRO_AzureDirectCommandReceived(6020),
    // 收到无主体的 Azure 直接命令
    PRO_AzureDirectCommandReceivedNoBody(6021),
    // Azure 直接命令有效负载无效
    PRO_AzureDirectCommandInvalidPayload(6022),
    // Azure 直接命令完成
    PRO_AzureDirectCommandFinished(6023),
    // 收到完整的 Azure 孪生数据
    PRO_AzureTwinFullReceived(6024),
    // 完整的 Azure 孪生数据无效
    PRO_AzureTwinFullInvalid(6025),
    // 完整的 Azure 孪生数据缺少报告部分
    PRO_AzureTwinFullMissingReported(6026),
    // 完整的 Azure 孪生数据报告部分无法分配内存
    PRO_AzureTwinFullReportedCouldNotAlloc(6027),
    // 完整的 Azure 孪生数据报告部分无法发送
    PRO_AzureTwinFullReportedCouldNotSend(6028),
    // 收到 Azure 孪生数据补丁
    PRO_AzureTwinPatchReceived(6029),
    // 收到未知的 Azure 孪生数据
    PRO_AzureTwinUnknownReceived(6030),
    // Azure 报告成功上传
    PRO_AzureReportedSuccessfulUpload(6031),
    // Azure 报告上传失败
    PRO_AzureReportedFailedUpload(6032),
    // Azure 开发人员错误
    PRO_AzureDeveloperError(6033),
    // 报告跟踪不匹配
    PRO_ReportedTraceMismatch(6034),
    // 报告无法分配补丁
    PRO_ReportedCouldNotAllocPatch(6035),
    // 报告成功
    PRO_ReportedSuccessful(6036),
    // 报告失败
    PRO_ReportedFailed(6037),
    // 报告超时
    PRO_ReportedTimedOut(6038),
    // 报告重复请求
    PRO_ReportedDuplicateRequest(6039),
    // 报告完整同步超时
    PRO_ReportedFullSyncTimedOut(6040),
    // 报告逆变器未知地址
    PRO_ReportedInverterUnknownAddress(6041),
    // 报告逆变器源数据大小错误
    PRO_ReportedInverterBadSourceSize(6042),
    // 报告逆变器无可用数据
    PRO_ReportedInverterNoDataAvailable(6043),
    // 期望的逆变器条目类型错误
    PRO_DesiredInverterEntryBadType(6044),
    // 期望的逆变器节点类型错误
    PRO_DesiredInverterNodeWrongType(6045),
    // 期望的有效逆变器序列号节点类型错误
    PRO_DesiredValidInverterSerialNodeWrongType(6046),
    // 跳过空遥测数据
    PRO_SkippedEmptyTelemetry(6047),
    // 将遥测数据从积压中推出
    PRO_PushedTelemetryOutOfBacklog(6048),
    // BLE 开始广播
    PRO_BLEStartAdvertising(6049),
    // BLE 停止广播
    PRO_BLEStopAdvertising(6050),
    // I2C 驱动安装失败
    PRO_I2CDriverInstallFailed(6051),
    PRO_InverterSettingsUnknownKey(6052),
    PRO_InverterSettingsIllegalValue(6053),
    PRO_InverterSettingsNoGroup(6054),
    PRO_InverterSettingsBadSourceSize(6055),
    PRO_InverterSettingsNoDataAvailable(6056),
    PRO_InverterSettingsRequestImpossible(6057),
    PRO_InverterSettingsRequestGenerated(6058),
    PRO_InverterSettingsRequestBufferFull(6059),
    PRO_InverterUnknownAddress(6060),
    PRO_InverterBadSourceSize(6061),
    PRO_InverterNoDataAvailable(6062),
    PRO_ModbusReadNoResponse(6063),
    PRO_ModbusReadSuccess(6064),
    PRO_ModbusReadResponseWrongSize(6065),
    PRO_ModbusReadErrorResponse(6066),
    PRO_ModbusReadUnknownResponse(6067),
    PRO_ModbusWriteSettingIllegalLength(6068),
    PRO_ModbusWriteWroteUnhandledRegister(6069),
    PRO_ModbusWriteWroteButNoInverter(6070),
    PRO_ModbusWriteNoResponse(6071),
    PRO_ModbusWriteSuccess(6072),
    PRO_ModbusWriteFailed(6073),
    PRO_ModbusWriteUnknownResponse(6074),
    PRO_TimeSyncedFromInverter(6075),
    PRO_TimeSyncedToInverter(6076),
    PRO_CapturedInverterSerial(6077),
    PRO_CapturedInverterSerialChanged(6078),
    PRO_CannotInitEth(6079),
    PRO_BeginRollback(6080),
    PRO_BeginUpdate(6081),
    PRO_FactoryRollbackFailed(6082),
    PRO_FactoryRollbackStarting(6083),
    PRO_OTADevError(6084),
    PRO_OTAUpdateDownloadFailed(6085),
    PRO_OTAUpdateDownloadSuccess(6086),
    PRO_OTAUpdateFailedPostCheck(6087),
    PRO_OTAUpdateStarted(6088),
    PRO_OTAUpdateSuccessPostCheck(6089),
    PRO_OTAWaitingForCloudBeforeSignoff(6090),
    PRO_InverterSyncNoExpectedSN(6091),
    PRO_InverterSyncNoCurrentSN(6092),
    PRO_InverterSyncMismatchSN(6093),
    PRO_DevError(6094),
    PRO_SNTPStarted(6095),
    PRO_SNTPTimeSynced(6096),
    PRO_SNTPUnknownEvent(6097),
    PRO_OTAProgressReport(6098),
    PRO_EventQueueFull(6099),
    PRO_AzureRequestingFullDeviceTwinFromPatch(6100),
    PRO_AzureRequestingFullDeviceTwinFromReq(6101),
    PRO_TelemetryBandExpired(6102),
    PRO_AzureDirectCommandAlive(6103),
    PRO_AzureDirectCommandBleAdvertise(6104),
    PRO_AzureDirectCommandDPSOverride(6105),
    PRO_AzureDirectCommandEMSFactoryReset(6106),
    PRO_AzureDirectCommandEMSReboot(6107),
    PRO_AzureDirectCommandInverterClearEnergyHistory(6108),
    PRO_AzureDirectCommandInverterFactoryReset(6109),
    PRO_AzureDirectCommandInverterPowerFactorTest(6110),
    PRO_AzureDirectCommandInverterReactivePowerTest(6111),
    PRO_AzureDirectCommandInverterReboot(6112),
    PRO_AzureDirectCommandMeterClearEnergyHistory(6113),
    PRO_AzureDirectCommandMeterResetMinMax(6114),
    PRO_AzureDirectCommandOTAUpdate(6115),
    PRO_AzureDirectCommandRollback(6116),
    PRO_AzureDirectCommandWifiConnect(6117),
    PRO_AzureDirectCommandResetFirmwareFetcher(6118),
    PRO_AzureDirectCommandUpdateInverterARM(6119),
    PRO_AzureDirectCommandUpdateInverterDSP(6120),
    PRO_AzureDirectCommandUpdateMeter(6121),
    PRO_FirmwareDownloaderBlockReset(6122),
    PRO_FirmwareDownloaderStarted(6123),
    PRO_FirmwareDownloaderFailedDownload(6124),
    PRO_FirmwareDownloaderBadHash(6125),
    PRO_FirmwareDownloaderSuccess(6126),
    PRO_FirmwareUpdateUnknownMeterHardware(6127),
    PRO_FirmwareUpdateFailedInverterARM(6128),
    PRO_FirmwareUpdateFailedInverterDSP(6129),
    PRO_FirmwareUpdateFailedPA(6130),
    PRO_FirmwareUpdateSucceededInverterARM(6131),
    PRO_FirmwareUpdateSucceededInverterDSP(6132),
    PRO_FirmwareUpdateSucceededPA(6133),
    PRO_FirmwareUpdateProgressInverter(6134),
    PRO_FirmwareUpdateProgressPA(6135),
    PRO_DesiredGoodInvARMFirmware(6136),
    PRO_DesiredBadInvARMFirmware(6137),
    PRO_DesiredGoodInvDSPFirmware(6138),
    PRO_DesiredBadInvDSPFirmware(6139),
    PRO_DesiredGoodMeterFirmware(6140),
    PRO_DesiredBadMeterFirmware(6141),
    PRO_InverterBootloaderARM(6142),
    PRO_InverterBootloaderDSP(6143),
    PRO_InverterBootloaderMeter(6144),
    PRO_InverterBootloaderUnknown(6145),
    PRO_FirmwareDownloadGeneralError(6146),
    PRO_FirmwareDownloadBadStatusCode(6147),
    PRO_FirmwareDownloadOpenRequestError(6148),
    PRO_InverterBootloaderCheckStarted(6149),
    PRO_InverterBootloaderCheckFoundBootloader(6150),
    PRO_InverterBootloaderCheckNoBootloader(6151),
    PRO_SelfAdministeredReboot(6152),
    PRO_AzureDirectCommandKillAzureTask(6153),
    PRO_AzureDirectCommandInverterSerialCommand(6154),
    PRO_DesiredGoodRelaySettings(6155),
    PRO_DesiredBadRelaySettings(6156),
    PRO_InvalidTimezoneVariable(6157),
    PRO_TimeZoneVariableNotDefined(6158),
    PRO_DesiredSettingsError(6159),
    PRO_TimeSyncFromInverterInPast(6160),
    PRO_ScheduleBadRelaySetting(6161),
    PRO_BadRelaySettings(6162),
    PRO_DesiredBadInverterControlSettings(6163),
    PRO_DesiredGoodInverterControlSettings(6164),
    PRO_DesiredGoodBatteryManagerSettings(6165),
    PRO_DesiredBadBatteryManagerSettings(6166),
    PRO_AzureDirectCommandEMSBatteryStart(6167),
    PRO_StartBatterySignalSent(6168),
    PRO_DesiredBatteryCountMismatchWithActual(6169),
    PRO_NfcWipTriggered(6170),
    PRO_StoredInvSerialMismatchWithActual(6171),
    PRO_ExportLimitIncompleteSettings(6172),
    PRO_ExportLimitConfigured(6173),
    PRO_GenLimitIncompleteSettings(6174),
    PRO_GenLimitConfigured(6175),
    PRO_GenerationHardLimitNotSupported(6176),
    PRO_GenerationHardAndSoftLimitsNotSupported(6177),
    PRO_DesiredBadConstraintSettings(6178),
    PRO_DesiredGoodConstraintSettings(6179),
    PRO_STM32NotResponding(6180),
    PRO_DesiredGoodSiteSettings(6181),
    PRO_DesiredBadSiteSettings(6182),
    PRO_AzureDirectCommandClearReported(6183),
    PRO_FailedToSaveDesiredSettings(6184),
    PRO_SavedDesiredSettings(6185),
    PRO_NFCURLSet(6186),
    PRO_NFCFailedToSetURL(6187),
    PRO_ModbusReadNoResponseMeter(6188),
    PRO_ModbusReadNoResponseInverter(6189),
    PRO_ReadNoResponseBatteries(6190),
    PRO_AzureDirectCommandUploadLog(6191),
    PRO_AzureDirectCommandBatterySerialCommand(6192),
    PRO_AzureDirectCommandMeterGridSerialCommand(6193),
    PRO_AzureDirectCommandMeterAuxSerialCommand(6194),
    PRO_ConstraintStarted(6195),
    PRO_ConstraintEnded(6196),
    PRO_FirmwareUpdateProgressInverterARM(6197),
    PRO_FirmwareUpdateProgressInverterDSP(6198),
    PRO_TaskFreeMemoryAlarm(6199),
    PRO_HeapFreeMemoryAlarm(6200),
    PRO_MaxTempAlarm(6201),
    PRO_VoltageAlarm(6202),
    PRO_CpuUtilisationPctAlarm(6203),
    PRO_LostCommsWithPeer(6204),
    PRO_CannotConnectToPeer(6205),
    PRO_IncompatibleFirmware(6206),
    PRO_MeterNotFound(6207)
    ;

    private int errorCode;

    private TelemetryErrorCodeEnum(int errorCode) {
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public static String getErrorCodeNameOrNull(Integer errorCode) {
        try {
            // Check if the errorCode exists in the enum
            for (TelemetryErrorCodeEnum code : TelemetryErrorCodeEnum.values()) {
                if (code.getErrorCode() == errorCode) {
                    return code.name();
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}