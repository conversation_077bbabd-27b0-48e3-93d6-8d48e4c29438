package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum EnergyControlSwitch
{
    None(0),
    SelfUse(1 << 0),
    OptimizeRevenue(1 << 1),
    EnergyStorageOffGrid(1 << 2),
    BatteryWakeUp(1 << 3),
    BackupBattery(1 << 4),
    ChargeBatteryFromGrid(1 << 5),
    FeedPriorityMode(1 << 6),
    NightBatteryOverDischargeRetentionEnable(1 << 7),
    MainsPowerDynamicAdjustEnableBatteryStronglyCharged(1 << 8),
    BatteryCurrentCorrectionEnable(1 << 9),
    BatteryTherapyMode(1 << 10),
    PeakShavingMode(1 << 11); //12,13,14,15Reserved

    private int value;


    private EnergyControlSwitch(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static EnergyControlSwitch forValue(int value)
    {
        for (EnergyControlSwitch e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        EnergyControlSwitch o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<EnergyControlSwitch> result = EnumSet.noneOf(EnergyControlSwitch.class);
        EnergyControlSwitch[] values = EnergyControlSwitch.values();
        for (EnergyControlSwitch e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
