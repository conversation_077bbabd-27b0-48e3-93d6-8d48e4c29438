package com.ebon.energy.fms.common.constants;

public class Tags {
    public static final String BMS = "BMS";
    public static final String DetectedBatteryMan = "DetectedBatteryMan";
    public static final String FirmwareVersion = "FirmwareVersion";
    public static final String DetectedBatteryModel = "DetectedBatteryModel";
    public static final String IsDailyOnline = "IsDailyOnline";
    public static final String IsInstalled = "IsInstalled";
    public static final String IsHourlyOnline = "IsHourlyOnline";
    public static final String OwnerName = "OwnerName";
    public static final String ROSSVersion = "ROSSVersion";
    public static final String WDVersion = "WDVersion";
    public static final String SCCMHeartbeat = "SCCMHeartbeat";
    public static final String OriginalInstallerEmail = "OriginalInstallerEmail";
    public static final String MaintainingInstallerEmail = "MaintainingInstallerEmail";
    public static final String MaintainingInstallerPhoneNumber = "MaintainingInstallerPhoneNumber";
    public static final String OriginalInstaller = "OriginalInstaller";
    public static final String OwnerPhoneNumber = "OwnerPhoneNumber";
    public static final String ProductOwnerEmail = "ProductOwnerEmail";
    public static final String Timestamp_Brisbane = "Timestamp_Brisbane";
    public static final String SerialNumber = "SerialNumber";
    public static final String IsOffComms = "IsOffComms";
    public static final String IsDailyOnline_WD = "IsDailyOnline_WD";
    public static final String IsHourlyOnline_WD = "IsHourlyOnline_WD";
    public static final String WDHeartbeatTimeStamp_Brisbane = "WDHeartbeatTimeStamp_Brisbane";
    public static final String SoC = "SoC";
    public static final String Postcode = "Postcode";
    public static final String State = "State";
    public static final String FullAddress = "FullAddress";
    public static final String Suburb = "Suburb";
    public static final String Installer = "Installer";
    public static final String MaintainingInstaller = "MaintainingInstaller";
    public static final String Comment = "Comment";
    public static final String WindowsVersion = "WindowsVersion";
    public static final String WindowsVersion_SS = "WindowsVersion_SS";
    public static final String TagValues = "TagValues";
    public static final String Long = "Long";
    public static final String Lat = "Lat";
    public static final String BackupOn = "BackupOn";
    public static final String ACPower = "ACPower";
    public static final String ACTotalToday = "ACTotalToday";
    public static final String AllTimeTotalExport = "AllTimeTotalExport";
    public static final String AllTimeTotalImport = "AllTimeTotalImport";
    public static final String DayTotalExport = "DayTotalExport";
    public static final String DayTotalImport = "DayTotalImport";
    public static final String GridPower = "GridPower";
    public static final String PVPower = "PVPower";
    public static final String PVTotalAllTime = "PVTotalAllTime";
    public static final String PVTotalToday = "PVTotalToday";
    public static final String InverterModelName = "InverterModelName";
    public static final String BatteryCapacity = "BatteryCapacity";
    public static final String BatteryChargeCurrent_BMS = "BatteryChargeCurrent_BMS";
    public static final String BatteryChargeCurrent_Custom = "BatteryChargeCurrent_Custom";
    public static final String BatteryChargeCurrent_Override = "BatteryChargeCurrent_Override";
    public static final String BatteryDisChargeCurrent_BMS = "BatteryDisChargeCurrent_BMS";
    public static final String BatteryDisChargeCurrent_Custom = "BatteryDisChargeCurrent_Custom";
    public static final String BatteryDisChargeCurrent_Override = "BatteryDisChargeCurrent_Override";
    public static final String FanMode = "FanMode";
    public static final String LimitExportPower = "LimitExportPower";
    public static final String LimitExportPowerUserValue = "LimitExportPowerUserValue";
    public static final String IsLimitExportPower = "IsLimitExportPower";
    public static final String MinimumSoC = "MinimumSoC";
    public static final String MinimumSoCOffGrid = "MinimumSoCOffGrid";
    public static final String OffGridCharge = "OffGridCharge";
    public static final String BatteryChargeVoltage_BMS = "BatteryChargeVoltage_BMS";
    public static final String BatteryChargeVoltage_Custom = "BatteryChargeVoltage_Custom";
    public static final String CTComms = "CTComms";
    public static final String GridVoltage = "GridVoltage";
    public static final String InverterTime = "InverterTime";
    public static final String WindowsTime = "WindowsTime";
    public static final String ActiveInstallationDate = "ActiveInstallationDate";
    public static final String BMSVersionNumber = "BMSVersionNumber";
    public static final String BatteryPower = "BatteryPower";
}
