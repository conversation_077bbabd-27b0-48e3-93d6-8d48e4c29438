package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;
public enum BatteryManufacturer
{
    NoBattery(0),
    PYLON_LV(1),
    UserDefined(2),
    B_BOX_LV(3),
    DynessLV(4),
    PureDrive_LV(5),
    LGChem_LV(6),
    AoBo_LV(7),
    JiaWei_LV(8),
    WECO_LV(9),
    FreedomWon_LV(10),
    Soluna_LV(11),
    GSL_LV(12),
    <PERSON>_LV(13),
    RiShengGSEnergySunrise_LV(14),
    Alpha_LV(15),
    CATLUZ_ENERGY_CATL_LV(16),
    ATL_LV(17),
    Zeta_LV(18),
    Highstar_LV(19),
    KODAK_LV(20),
    FOX_LV(21),
    EXIDE_LV(22),
    HD_Energy_LV(23),
    DLGPytes_LV(24),
    ARVIO_LV(25),
    PAND_LV(26),
    WANKE_LV(27),
    Dowell_LV(28),
    ROSEN_ESSRongsheng(29),
    ZRGPZhongruiGreenEnergy_LV(30),
    NaradaSouthernMetropolis(31),
    BST_LV(32),
    Cegasa_LV(33),
    Meterboost(34),
    MOURA(35),
    Tecloman_LV(36),
    UpowerUE_H(37),
    UpowerEU_I(38),
    SUNWODA_LV(39),
    CFE_LV(40),
    DMEGC_Hengdian_Dongci_LV(41),
    J_Pack_LVJAHaibo_LV(42),
    HY4850GuizhouJiaying_LV(43),
    AMPACEXinneng_an(44),
    JohnRay_LV(45),
    GSO(46),
    YOSHOPOEraNebula_LV(47),
    Hubble(48),
    AlpSolarr_LV(49),
    Haier_LV(50),
    NAHUI_LV(51),
    FEB(52),
    Shoto(53),
    KG(54),
    Duracell(55),
    NBZK_LV(56),
    STELTEC_LV(57),
    FLS_LV(58),
    Desay_LV(59),
    Greenway_LV(60),
    ETower(61), //Lowercase to uppercase;eTower
    Reserve062(62),
    Reserve063(63),
    Reserve064(64),
    Reserve065(65),
    Reserve066(66),
    Reserve067(67),
    Reserve068(68),
    Reserve069(69),
    Reserve070(70),
    Reserve071(71),
    Reserve072(72),
    Reserve073(73),
    Reserve074(74),
    Reserve075(75),
    Reserve076(76),
    Reserve077(77),
    Reserve078(78),
    Reserve079(79),
    Reserve080(80),
    Reserve081(81),
    Reserve082_FD(82),
    Reserve083(83),
    Reserve084(84),
    Reserve085(85),
    Reserve086(86),
    Reserve087(87),
    Reserve088(88),
    Reserve089(89),
    Reserve090(90),
    Reserve091(91),
    Reserve092(92),
    Reserve093(93),
    Reserve094(94),
    Reserve095(95),
    Reserve096(96),
    Reserve097(97),
    LithiumBatteryRS485_LV(98),
    GeneralLiBat_LV(99),
    LeadAcidBattery(100),
    _48V_LiBat_LV(101), //Add '_' for first letter;48V_LiBat_LV
    _51_2V_LiBat_LV(102); //Add '_' for first letter and use '_' instead of '.';51.2V_LiBat_LV


    private int value;

    BatteryManufacturer(int value) {
        this.value = value;
    }

    public int getValue()
    {
        return value;
    }

    public static BatteryManufacturer forValue(int value)
    {
        for (BatteryManufacturer e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        BatteryManufacturer o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<BatteryManufacturer> result = EnumSet.noneOf(BatteryManufacturer.class);
        BatteryManufacturer[] values = BatteryManufacturer.values();
        for (BatteryManufacturer e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}