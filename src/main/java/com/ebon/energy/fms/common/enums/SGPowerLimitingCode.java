package com.ebon.energy.fms.common.enums;

import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum SGPowerLimitingCode
{
    HighTemp(1 << 0),
    PVInputVoltage(1 << 1),
    FanFault(1 << 2),
    SoftStart(1 << 3),
    GridHighFreq(1 << 4),
    GridLowVoltage(1 << 5),
    FixedActivePowerLimit(1 << 6),
    FixedReactivePowerLimit(1 << 7),
    MaxOutputCurrent(1 << 8),
    BurnInMode(1 << 9),
    GridHighVoltage(1 << 10),
    SoftShutdown(1 << 11),
    QUMode(1 << 12),
    ExportOrGenerationLimit(1 << 13);

    private int intValue;


    private SGPowerLimitingCode(int value)
    {
        intValue = value;
    }

    public int getValue()
    {
        return intValue;
    }

    public static SGPowerLimitingCode forValue(int value)
    {
        for (SGPowerLimitingCode e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }

        return null;
    }

    public static Object parse(int value) {
        SGPowerLimitingCode o = forValue(value);
        if (o != null) {
            return o;
        }
        
        Set<SGPowerLimitingCode> result = EnumSet.noneOf(SGPowerLimitingCode.class);
        SGPowerLimitingCode[] values = SGPowerLimitingCode.values();
        for (SGPowerLimitingCode e : values) {
            if ((value & e.getValue()) != 0) {
                result.add(e);
            }
        }

        if (CollectionUtils.isEmpty(result)) {
            return value;
        }

        return result.stream().map(e -> String.valueOf(e)).collect(Collectors.joining(", "));
    }
}
