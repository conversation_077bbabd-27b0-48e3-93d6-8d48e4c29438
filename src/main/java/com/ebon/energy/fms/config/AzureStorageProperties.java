package com.ebon.energy.fms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "azure.storage")
public class AzureStorageProperties {
    private String oldAccountName;
    private String oldAccountKey;
    private String telemetryAccountName;
    private String telemetryAccountKey;
    private Boolean enableNewTelemetryStorage = false;
    private String useOnlyNewTelemetryFromDate = "********";
    private String useOnlyOldStoragePriorToDate = "********";
}