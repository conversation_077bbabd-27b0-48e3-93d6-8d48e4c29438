package com.ebon.energy.fms.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;

public class CustomZonedDateTimeDeserializer implements ObjectDeserializer {

    private static final DateTimeFormatter UTC_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss'Z'");
    private static final List<DateTimeFormatter> FORMATTERS = Arrays.asList(
            DateTimeFormatter.ISO_LOCAL_DATE_TIME,
            DateTimeFormatter.ISO_ZONED_DATE_TIME,
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ssXXX"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"), // 格式: 2025-04-29T06:11:26Z
            UTC_FORMATTER,  // 格式: 2025-04-29 06:11:26Z
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'"), //格式： 2017-11-01T14:48:54.020208Z
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),    // 格式: 2025/04/29 06:11:26
            DateTimeFormatter.ofPattern("yyyy/MM/dd'T'HH:mm:ss")
    );

    @Override
    public ZonedDateTime deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        String dateStr = parser.parseObject(String.class);
        if (StringUtils.isBlank(dateStr)){
            return null;
        }

        for (DateTimeFormatter formatter : FORMATTERS) {
            try {
                if (formatter.equals(DateTimeFormatter.ISO_LOCAL_DATE_TIME)) {
                    return LocalDateTime.parse(dateStr, formatter).atZone(ZoneId.systemDefault());
                } else if (formatter.equals(UTC_FORMATTER)) {
                    return ZonedDateTime.parse(dateStr, UTC_FORMATTER.withZone(ZoneOffset.UTC));
                } else {
                    return ZonedDateTime.parse(dateStr, formatter);
                }
            } catch (DateTimeParseException e) {
                // 尝试下一种格式
            }
        }

        throw new RuntimeException("无法解析日期字符串: " + dateStr);
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}