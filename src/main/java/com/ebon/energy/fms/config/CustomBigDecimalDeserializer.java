package com.ebon.energy.fms.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class CustomBigDecimalDeserializer implements ObjectDeserializer {
    @Override
    public BigDecimal deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Object value = parser.parse();
        if (value == null) {
            return null;
        }

        return ((BigDecimal) value).setScale(3, RoundingMode.HALF_UP).stripTrailingZeros();
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}