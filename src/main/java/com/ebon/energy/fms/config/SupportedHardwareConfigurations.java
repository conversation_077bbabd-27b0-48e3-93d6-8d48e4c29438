package com.ebon.energy.fms.config;

import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.HardwareTypeEnum;
import com.ebon.energy.fms.domain.vo.HardwareCapabilityVO;
import com.ebon.energy.fms.domain.vo.HardwareSettingsVO;

import java.math.BigDecimal;
import java.util.*;

public class SupportedHardwareConfigurations {
    private static final Map<String, HardwareSettingsVO> current;

    static {
        Map<String, HardwareSettingsVO> builder = new HashMap<>();

        // SG Configuration
        builder.put("SG", new HardwareSettingsVO(
                "SG",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Senergy_SG,
                new HardwareModelEnum[] {
                        HardwareModelEnum.SI5000, HardwareModelEnum.SI6000, HardwareModelEnum.SI8000, HardwareModelEnum.SI10000,
                        HardwareModelEnum.SI5000_N, HardwareModelEnum.SI6000_N, HardwareModelEnum.SI8000_N, HardwareModelEnum.SI10000_N
                },
                false,
                0,
                65535,
                0x00,
                "0",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                        0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, true, true,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // SG1 Configuration with all model capabilities
        Map<HardwareModelEnum, HardwareCapabilityVO> sg1ModelCapabilities = new HashMap<>();

        // SI5000 model capabilities
        sg1ModelCapabilities.put(HardwareModelEnum.SI5000, new HardwareCapabilityVO(
                0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 8, 0, 4600, 0, 4600, 0, 4600,
                -30000, 30000, 0, 100, 0, 100,
                10, 100, 10, 40,
                false, true, true, false, true, true,
                null, true, false
        ));

        // SI6000 model capabilities
        sg1ModelCapabilities.put(HardwareModelEnum.SI6000, new HardwareCapabilityVO(
                0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 8, 0, 4600, 0, 4600, 0, 4600,
                -30000, 30000, 0, 100, 0, 100,
                10, 100, 10, 40,
                false, true, true, false, true, true,
                null, true, false
        ));

        // SI8000 model capabilities
        sg1ModelCapabilities.put(HardwareModelEnum.SI8000, new HardwareCapabilityVO(
                0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 8, 0, 4600, 0, 4600, 0, 4600,
                -30000, 30000, 0, 100, 0, 100,
                10, 100, 10, 40,
                false, true, true, false, true, true,
                null, true, false
        ));

        // SI10000 model capabilities
        sg1ModelCapabilities.put(HardwareModelEnum.SI10000, new HardwareCapabilityVO(
                0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 8, 0, 4600, 0, 4600, 0, 4600,
                -30000, 30000, 0, 100, 0, 100,
                10, 100, 10, 40,
                false, true, true, false, true, true,
                null, true, false
        ));

        // Create SG1 configuration
        builder.put("SG1", new HardwareSettingsVO(
                "SG1",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Senergy_SG,
                new HardwareModelEnum[] {
                        HardwareModelEnum.SI5000, HardwareModelEnum.SI6000, HardwareModelEnum.SI8000, HardwareModelEnum.SI10000,
                        HardwareModelEnum.SI5000_N, HardwareModelEnum.SI6000_N, HardwareModelEnum.SI8000_N, HardwareModelEnum.SI10000_N
                },
                false,
                1,
                65535,
                0x00,
                "0",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                        0, 10000, 0, 10000, new BigDecimal("0.8"), new BigDecimal("0.8"),
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, true, true,
                        null, true, true
                ),
                sg1ModelCapabilities
        ));

        // TGT Configuration with all model capabilities
        Map<HardwareModelEnum, HardwareCapabilityVO> tgtModelCapabilities = new HashMap<>();

        // RED_I3_T10 model capabilities
        tgtModelCapabilities.put(HardwareModelEnum.RED_I3_T10, new HardwareCapabilityVO(
                0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 8, 0, 4600, 0, 4600, 0, 4600,
                -30000, 30000, 0, 100, 0, 100,
                10, 100, 10, 40,
                false, true, true, false, true, true,
                null, true, false
        ));

        // RED_I3_T15 model capabilities
        tgtModelCapabilities.put(HardwareModelEnum.RED_I3_T15, new HardwareCapabilityVO(
                0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 8, 0, 4600, 0, 4600, 0, 4600,
                -30000, 30000, 0, 100, 0, 100,
                10, 100, 10, 40,
                false, true, true, false, true, true,
                null, true, false
        ));

        // Create TGT configuration
        builder.put("TGT", new HardwareSettingsVO(
                "TGT",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Senergy_SG,
                new HardwareModelEnum[] {
                        HardwareModelEnum.RED_I3_T10,
                        HardwareModelEnum.RED_I3_T15
                },
                false,
                1,
                65535,
                0x00,
                "0",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, -10000, 10000, -10000, 10000,
                        0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, true, true,
                        null, true, true
                ),
                tgtModelCapabilities
        ));

        // Common ES models
        HardwareModelEnum[] esModels = {
                HardwareModelEnum.SH4600, HardwareModelEnum.SH5000,
                HardwareModelEnum.SH5000v2, HardwareModelEnum.SH5000_N
        };

        // ES Configuration
        builder.put("ES", new HardwareSettingsVO(
                "ES",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                esModels,
                true,
                4,
                8,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // ES3 Configuration
        builder.put("ES3", new HardwareSettingsVO(
                "ES3",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                esModels,
                true,
                0,
                3,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 4600, 4600, 4600, 0, 4600, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // ES9 Configuration
        builder.put("ES9", new HardwareSettingsVO(
                "ES9",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                esModels,
                true,
                9,
                9,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4950, 0, 4950, 0, 4950,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // ES10 Configuration
        builder.put("ES10", new HardwareSettingsVO(
                "ES10",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                esModels,
                true,
                10,
                11,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4950, 0, 4950, 0, 4950,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, true, false, false,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // ES12 Configuration
        builder.put("ES12", new HardwareSettingsVO(
                "ES12",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                esModels,
                true,
                12,
                13,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 4950, 5000, 5000, 0, 65535, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4950, 0, 4950, 0, 4950,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, true, false, false,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // ES14 Configuration
        builder.put("ES14", new HardwareSettingsVO(
                "ES14",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                new HardwareModelEnum[] {
                        HardwareModelEnum.SH4600, HardwareModelEnum.SH5000,
                        HardwareModelEnum.SH5000v2, HardwareModelEnum.SH5000_N
                },
                true,
                14,
                65535,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 4950, 4950, 4950, -5000, 30000, -5000, 30000,
                        0, 5000, 0, 5000, new BigDecimal("0.8"), new BigDecimal("0.8"),
                        "", "", "", "", "", "",
                        0, 8, 0, 4950, 0, 4950, 0, 4950,
                        -30000, 30000, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, true, true, true,
                        null, true, true
                ),
                new HashMap<>()
        ));

        builder.put("ESG", new HardwareSettingsVO(
                "ESG",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                new HardwareModelEnum[] { HardwareModelEnum.SH5000_G3, HardwareModelEnum.SH6000_G3 },
                false,
                1,
                4,
                0x00,
                "0001",
                new HardwareCapabilityVO(
                        0, 5000, 5000, 5000, -10000, 10000, -10000, 10000,
                        0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 4, 0, 5000, 0, 5000, 0, 5300,
                        -30000, 30000, 0, 120, 0, 120,
                        5, 100, 5, 40,
                        false, true, true, true, true, true,
                        null, true, true
                ),
                new HashMap<>()
        ));

        // ESG Configuration
        Map<HardwareModelEnum, HardwareCapabilityVO> esgModelCapabilities = new HashMap<>();
        esgModelCapabilities.put(HardwareModelEnum.SH5000_G3, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 5000, 0, 5300,
                -30000, 30000, 0, 120, 0, 120,
                5, 100, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));
        esgModelCapabilities.put(HardwareModelEnum.SH6000_G3, new HardwareCapabilityVO(
                0, 6000, 5000, 5000, -10000, 10000, -10000, 10000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 6000, 0, 6300,
                -30000, 30000, 0, 120, 0, 120,
                5, 100, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));

        // ESG5 Configuration
        builder.put("ESG5", new HardwareSettingsVO(
                "ESG5",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                new HardwareModelEnum[] { HardwareModelEnum.SH5000_G3, HardwareModelEnum.SH6000_G3 },
                false,
                5,
                65535,
                0x00,
                "0001",
                new HardwareCapabilityVO(
                        0, 5000, 5000, 5000, -10000, 10000, -10000, 10000,
                        0, 5000, 0, 5000, new BigDecimal("0.8"), new BigDecimal("0.8"),
                        "", "", "", "", "", "",
                        0, 4, 0, 5000, 0, 5000, 0, 5300,
                        -30000, 30000, 0, 120, 0, 120,
                        5, 100, 5, 40,
                        false, true, true, true, true, true,
                        null, true, true
                ),
                esgModelCapabilities
        ));

        // EH1P Configuration
        Map<HardwareModelEnum, HardwareCapabilityVO> eh1pModelCapabilities = new HashMap<>();
        eh1pModelCapabilities.put(HardwareModelEnum.EH1P5K, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, -10000, 10000, 0, 20000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 5000, 0, 5000,
                -30000, 30000, 1, 100, 1, 100,
                5, 40, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));
        eh1pModelCapabilities.put(HardwareModelEnum.EH1P6K, new HardwareCapabilityVO(
                0, 6000, 5000, 6000, -10000, 10000, 0, 24000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 6000, 0, 6000,
                -30000, 30000, 1, 125, 1, 125,
                5, 40, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));

        builder.put("EH1P", new HardwareSettingsVO(
                "EH1P",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ES,
                new HardwareModelEnum[] { HardwareModelEnum.EH1P5K, HardwareModelEnum.EH1P6K },
                false,
                5,
                65535,
                0x00,
                "0001",
                new HardwareCapabilityVO(
                        0, 5000, 5000, 5000, -10000, 10000, 0, 20000,
                        0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 4, 0, 5000, 0, 5000, 0, 5000,
                        -30000, 30000, 0, 100, 0, 100,
                        5, 40, 5, 40,
                        false, true, true, true, true, true,
                        null, true, true
                ),
                eh1pModelCapabilities
        ));

        // HVH3P Configuration with complete model capabilities
        Map<HardwareModelEnum, HardwareCapabilityVO> hvh3pModelCapabilities = new HashMap<>();

        // RED_H3_T10HV model capabilities
        hvh3pModelCapabilities.put(HardwareModelEnum.RED_H3_T10HV, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, -10000, 10000, 0, 20000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 5000, 0, 5000,
                -30000, 30000, 1, 100, 1, 100,
                5, 40, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));

        // RED_H3_T15HV model capabilities
        hvh3pModelCapabilities.put(HardwareModelEnum.RED_H3_T15HV, new HardwareCapabilityVO(
                0, 6000, 5000, 5000, -10000, 10000, 0, 24000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 6000, 0, 6000,
                -30000, 30000, 1, 125, 1, 125,
                5, 40, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));

        // RED_H3_T20HV model capabilities
        hvh3pModelCapabilities.put(HardwareModelEnum.RED_H3_T20HV, new HardwareCapabilityVO(
                0, 6000, 5000, 5000, -10000, 10000, 0, 24000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 6000, 0, 6000,
                -30000, 30000, 1, 125, 1, 125,
                5, 40, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));

        // RED_H3_T30HV model capabilities
        hvh3pModelCapabilities.put(HardwareModelEnum.RED_H3_T30HV, new HardwareCapabilityVO(
                0, 6000, 5000, 5000, -10000, 10000, 0, 24000,
                0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                0, 4, 0, 5000, 0, 6000, 0, 6000,
                -30000, 30000, 1, 125, 1, 125,
                5, 40, 5, 40,
                false, true, true, true, true, true,
                null, true, true
        ));

        // HVH3P Settings
        builder.put("HVH3P", new HardwareSettingsVO(
                "HVH3P",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ET,
                new HardwareModelEnum[] {
                        HardwareModelEnum.RED_H3_T10HV,
                        HardwareModelEnum.RED_H3_T15HV,
                        HardwareModelEnum.RED_H3_T20HV,
                        HardwareModelEnum.RED_H3_T30HV
                },
                false,
                5,
                65535,
                0x00,
                "0001",
                new HardwareCapabilityVO(
                        0, 5000, 5000, 5000, -10000, 10000, 0, 20000,
                        0, 5000, 0, 5000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 4, 0, 5000, 0, 5000, 0, 5000,
                        -30000, 30000, 0, 100, 0, 100,
                        5, 40, 5, 40,
                        false, true, true, true, true, true,
                        null, true, true
                ),
                hvh3pModelCapabilities
        ));


        // Common ET models
        HardwareModelEnum[] etModels = {
                HardwareModelEnum.GW10K_ET, HardwareModelEnum.ST10000, HardwareModelEnum.GW10KL_ET,
                HardwareModelEnum.GW10K_ET_N, HardwareModelEnum.ST10000_N, HardwareModelEnum.GW10KL_ET_N
        };

        // ET Configuration
        builder.put("ET", new HardwareSettingsVO(
                "ET",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ET,
                etModels,
                true,
                0,
                13,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 10000, 10000, 10000, 0, 10000, 0, 0,
                        0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 10000, 0, 10000, 0, 10000,
                        -30000, 30000, 0, 25, 0, 25,
                        10, 39, 10, 39,
                        true, true, false, true, false, false,
                        null, false, true
                ),
                new HashMap<>()
        ));

        // ET14 Configuration
        builder.put("ET14", new HardwareSettingsVO(
                "ET14",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ET,
                etModels,
                true,
                14,
                18,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 10000, 10000, 10000, 0, 10000, 0, 0,
                        0, 10000, 0, 10000, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 10000, 0, 10000, 0, 10000,
                        -30000, 30000, 0, 25, 0, 25,
                        10, 100, 10, 40,
                        true, true, false, true, false, false,
                        null, false, true
                ),
                new HashMap<>()
        ));

        // ET19 Configuration
        builder.put("ET19", new HardwareSettingsVO(
                "ET19",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_ET,
                etModels,
                true,
                19,
                65535,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 10000, 10000, 10000, -10000, 30000, -10000, 30000,
                        0, 10000, 0, 10000, new BigDecimal("0.8"), new BigDecimal("0.8"),
                        "", "", "", "", "", "",
                        0, 8, 0, 10000, 0, 10000, 0, 10000,
                        -30000, 30000, 0, 25, 0, 25,
                        10, 100, 10, 40,
                        true, true, false, true, true, true,
                        null, false, true
                ),
                new HashMap<>()
        ));

        // Common BH models
        HardwareModelEnum[] bhModels = {
                HardwareModelEnum.GW5000_BH, HardwareModelEnum.GW6000_BH,
                HardwareModelEnum.SB7200, HardwareModelEnum.SB9600, HardwareModelEnum.SB14200
        };

        // BH Configuration
        Map<HardwareModelEnum, HardwareCapabilityVO> bhModelCapabilities = new HashMap<>();
        bhModelCapabilities.put(HardwareModelEnum.SB7200, new HardwareCapabilityVO(
                0, 3600, 3600, 3600, 0, 10000, 0, 0,
                0, 3600, 0, 3600, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                3, 3, 0, 3600, 0, 3600, 0, 3600,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, false, false,
                7200, false, false
        ));
        bhModelCapabilities.put(HardwareModelEnum.SB9600, new HardwareCapabilityVO(
                0, 3600, 3600, 3600, 0, 10000, 0, 0,
                0, 4600, 0, 4600, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                4, 4, 0, 3600, 0, 5000, 0, 5000,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, false, false,
                9600, false, false
        ));
        bhModelCapabilities.put(HardwareModelEnum.SB14200, new HardwareCapabilityVO(
                0, 3600, 3600, 3600, 0, 10000, 0, 0,
                0, 4600, 0, 4600, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                4, 4, 0, 3600, 0, 5000, 0, 5000,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, false, false,
                14200, false, false
        ));

        builder.put("BH", new HardwareSettingsVO(
                "BH",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_BH,
                bhModels,
                true,
                0,
                13,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 3600, 3600, 3600, 0, 10000, 0, 0,
                        0, 3600, 0, 3600, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        3, 3, 0, 3600, 0, 3600, 0, 3600,
                        -30000, 30000, 0, 25, 0, 25,
                        10, 100, 10, 40,
                        false, false, true, true, false, false,
                        null, false, false
                ),
                bhModelCapabilities
        ));

        // BH14 Configuration
        Map<HardwareModelEnum, HardwareCapabilityVO> bh14ModelCapabilities = new HashMap<>();
        bh14ModelCapabilities.put(HardwareModelEnum.SB7200, new HardwareCapabilityVO(
                0, 3600, 3600, 3600, 0, 10000, 0, 0,
                0, 3600, 0, 3600, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                3, 3, 0, 3600, 0, 3600, 0, 3600,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, false, false,
                7200, false, false
        ));
        bh14ModelCapabilities.put(HardwareModelEnum.SB9600, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, 0, 10000, 0, 0,
                0, 4600, 0, 4600, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                4, 4, 0, 3600, 0, 5000, 0, 5000,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, false, false,
                9600, false, false
        ));
        bh14ModelCapabilities.put(HardwareModelEnum.SB14200, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, 0, 10000, 0, 0,
                0, 4600, 0, 4600, BigDecimal.ZERO, BigDecimal.ZERO,
                "", "", "", "", "", "",
                4, 4, 0, 3600, 0, 5000, 0, 5000,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, false, false,
                14200, false, false
        ));

        builder.put("BH14", new HardwareSettingsVO(
                "BH14",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_BH,
                bhModels,
                true,
                14,
                18,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 3600, 3600, 3600, 0, 10000, 0, 0,
                        0, 3600, 0, 3600, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        3, 3, 0, 3600, 0, 3600, 0, 3600,
                        -30000, 30000, 0, 25, 0, 25,
                        10, 100, 10, 40,
                        false, false, true, true, false, false,
                        null, false, false
                ),
                bh14ModelCapabilities
        ));

        // BH19 Configuration
        Map<HardwareModelEnum, HardwareCapabilityVO> bh19ModelCapabilities = new HashMap<>();
        bh19ModelCapabilities.put(HardwareModelEnum.SB7200, new HardwareCapabilityVO(
                0, 3600, 3600, 3600, -3600, 30000, -3600, 30000,
                0, 3600, 0, 3600, new BigDecimal("0.8"), new BigDecimal("0.8"),
                "", "", "", "", "", "",
                3, 3, 0, 3600, 0, 3600, 0, 3600,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, true, true,
                7200, false, false
        ));
        bh19ModelCapabilities.put(HardwareModelEnum.SB9600, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, -5000, 30000, -5000, 30000,
                0, 4600, 0, 4600, new BigDecimal("0.8"), new BigDecimal("0.8"),
                "", "", "", "", "", "",
                4, 4, 0, 3600, 0, 5000, 0, 5000,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, true, true,
                9600, false, false
        ));
        bh19ModelCapabilities.put(HardwareModelEnum.SB14200, new HardwareCapabilityVO(
                0, 5000, 5000, 5000, -5000, 30000, -5000, 30000,
                0, 4600, 0, 4600, new BigDecimal("0.8"), new BigDecimal("0.8"),
                "", "", "", "", "", "",
                4, 4, 0, 3600, 0, 5000, 0, 5000,
                -30000, 30000, 0, 25, 0, 25,
                10, 100, 10, 40,
                false, false, true, true, true, true,
                14200, false, false
        ));

        builder.put("BH19", new HardwareSettingsVO(
                "BH19",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Inverter_Goodwe_BH,
                bhModels,
                true,
                19,
                65535,
                0xF7,
                "0002",
                new HardwareCapabilityVO(
                        0, 3600, 3600, 3600, -3600, 30000, -3600, 30000,
                        0, 3600, 0, 3600, new BigDecimal("0.8"), new BigDecimal("0.8"),
                        "", "", "", "", "", "",
                        3, 3, 0, 3600, 0, 3600, 0, 3600,
                        -30000, 30000, 0, 25, 0, 25,
                        10, 100, 10, 40,
                        false, false, true, true, true, true,
                        14200, false, false
                ),
                bh19ModelCapabilities
        ));

        // PylonHV Configuration
        builder.put("PylonHV", new HardwareSettingsVO(
                "PylonHV",
                HardwareTypeEnum.Batteries,
                HardwareFamilyEnum.Battery_Pylon_HV,
                new HardwareModelEnum[] { HardwareModelEnum.PylonHV },
                true,
                0,
                65535,
                0x01,
                null,
                new HardwareCapabilityVO(
                        0, 4950, 0, 0, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        0, 0, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, false
                ),
                new HashMap<>()
        ));

        // PylonUS Configuration
        builder.put("PylonUS", new HardwareSettingsVO(
                "PylonUS",
                HardwareTypeEnum.Batteries,
                HardwareFamilyEnum.Battery_Pylon_LV,
                new HardwareModelEnum[] {
                        HardwareModelEnum.PylonUS,
                        HardwareModelEnum.PylonUS5000,
                        HardwareModelEnum.US5000,
                        HardwareModelEnum.US3000C
                },
                true,
                0,
                65535,
                0x01,
                null,
                new HardwareCapabilityVO(
                        0, 4950, 0, 0, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        0, 0, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, false
                ),
                new HashMap<>()
        ));

        // Pylon Configuration
        builder.put("Pylon", new HardwareSettingsVO(
                "Pylon",
                HardwareTypeEnum.Batteries,
                HardwareFamilyEnum.Battery_Pylon_LV,
                new HardwareModelEnum[] {
                        HardwareModelEnum.Pylon2000,
                        HardwareModelEnum.Pylon2000A,
                        HardwareModelEnum.Pylon2000B,
                        HardwareModelEnum.Pylon3000B,
                        HardwareModelEnum.Pylon3000C,
                        HardwareModelEnum.Pylon5000,
                        HardwareModelEnum.PylonLV
                },
                true,
                0,
                65535,
                0x00,
                null,
                new HardwareCapabilityVO(
                        0, 4950, 0, 0, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        0, 0, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, false
                ),
                new HashMap<>()
        ));

        // LG Configuration
        builder.put("LG", new HardwareSettingsVO(
                "LG",
                HardwareTypeEnum.Batteries,
                HardwareFamilyEnum.Battery_LG,
                new HardwareModelEnum[] {
                        HardwareModelEnum.LG_RESU_3_3,
                        HardwareModelEnum.LG_RESU_6_5,
                        HardwareModelEnum.LG_RESU_10,
                        HardwareModelEnum.LGM48_3_3
                },
                false,
                0,
                65535,
                0x00,
                null,
                new HardwareCapabilityVO(
                        0, 4950, 0, 0, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        0, 0, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, false
                ),
                new HashMap<>()
        ));

        // RedbackLV Configuration
        builder.put("RedbackLV", new HardwareSettingsVO(
                "RedbackLV",
                HardwareTypeEnum.Batteries,
                HardwareFamilyEnum.Battery_Redback,
                new HardwareModelEnum[] { HardwareModelEnum.RED_R1_5000LV },
                true,
                0,
                65535,
                0x01,
                null,
                new HardwareCapabilityVO(
                        0, 4950, 0, 0, 0, 4950, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 4600, 0, 4600, 0, 4600,
                        0, 0, 0, 100, 0, 100,
                        10, 100, 10, 40,
                        false, true, true, false, false, false,
                        null, true, false
                ),
                new HashMap<>()
        ));

        current = Collections.unmodifiableMap(builder);
    }

    public static Map<String, HardwareSettingsVO> getCurrent() {
        return current;
    }

    public static HardwareSettingsVO getUnsupportedInverterConfiguration() {
        return new HardwareSettingsVO(
                "Unsupported",
                HardwareTypeEnum.Inverter,
                HardwareFamilyEnum.Unknown,
                new HardwareModelEnum[] { HardwareModelEnum.Unknown },
                true,
                0,
                Short.MAX_VALUE,
                0,
                null,
                new HardwareCapabilityVO(
                        0, 2000, 2000, 2000, 0, 2000, 0, 0,
                        0, 0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO,
                        "", "", "", "", "", "",
                        0, 8, 0, 2000, 0, 2000, 0, 2000,
                        0, 0, 0, 15, 0, 15,
                        10, 100, 10, 40,
                        false, true, false, false, false, false,
                        null, true, false
                ),
                new HashMap<>()
        );
    }
}