package com.ebon.energy.fms.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.parser.ParserConfig;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class CustomDoubleDeserializer implements ObjectDeserializer {

    @Override
    public Double deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Object value = parser.parse();
        if (value == null) {
            return null;
        }

        // 处理不同数值类型
        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString()).setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
        } else {
            throw new IllegalArgumentException("Cannot convert " + value.getClass() + " to Double");
        }
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}