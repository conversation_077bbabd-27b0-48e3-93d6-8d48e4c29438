package com.ebon.energy.fms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.tablestore")
public class AliTableStoreConfig {

    private String endPoint;

    private String accessKey;

    private String secretKey;

    private String instanceName;

    private String telemetryStorageFromDateyyyyMMdd;

    private String telemetryOldStoragePriorToDateyyyyMMdd;

    private Long useOnlyOldStoragePriorToEpoch;

    private Long useOnlyNewTelemetryFromEpoch;

    private static final String FarFutureDate = "30240901";

    @PostConstruct
    public void initialiseStorageRepositoryDates() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate farFutureDate;
        try {
            farFutureDate = LocalDate.parse(FarFutureDate, formatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Failed to parse FarFutureDate", e);
        }

        LocalDate useOnlyNewTelemetryFromDate;
        try {
            useOnlyNewTelemetryFromDate = LocalDate.parse(getTelemetryStorageFromDateyyyyMMdd(), formatter);
        } catch (DateTimeParseException e) {
            useOnlyNewTelemetryFromDate = farFutureDate;
        }

        LocalDate useOnlyOldStoragePriorToDate;
        try {
            useOnlyOldStoragePriorToDate = LocalDate.parse(getTelemetryOldStoragePriorToDateyyyyMMdd(), formatter);
        } catch (DateTimeParseException e) {
            useOnlyOldStoragePriorToDate = farFutureDate;
        }

        ZonedDateTime nemZonedOldDateTime = useOnlyOldStoragePriorToDate.atStartOfDay(ZoneOffset.ofHours(10));
        ZonedDateTime nemZonedNewDateTime = useOnlyNewTelemetryFromDate.atStartOfDay(ZoneOffset.ofHours(10));

        // Check dates don't have an invalid setup.
        if (useOnlyNewTelemetryFromDate.isBefore(useOnlyOldStoragePriorToDate)) {
            /*log.info(String.format("StorageRepository unable to comply with setting to only look in the new Telemetry Storage account after %s. As it is less than the provided old Stoarge account only date of %s.",
                    useOnlyNewTelemetryFromDate.format(formatter),
                    useOnlyOldStoragePriorToDate.format(formatter)));*/

            ZonedDateTime nemZonedFutureDateTime = farFutureDate.atStartOfDay(ZoneOffset.ofHours(10));
            nemZonedNewDateTime = nemZonedFutureDateTime;
        }

        this.useOnlyOldStoragePriorToEpoch = nemZonedOldDateTime.toInstant().getEpochSecond();
        this.useOnlyNewTelemetryFromEpoch = nemZonedNewDateTime.toInstant().getEpochSecond();
    }

}