package com.ebon.energy.fms.config;

import com.ebon.energy.fms.common.json.*;
import com.ebon.energy.fms.common.json.CustomZonedDateTimeDeserializer;
import com.ebon.energy.fms.common.json.CustomZonedDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Configuration
public class JacksonConfig {

    @Autowired
    public void configureMapper(ObjectMapper objectMapper) {
        var simpleModule = new SimpleModule();

        simpleModule.addSerializer(OffsetDateTime.class, new JsonSerializer<OffsetDateTime>() {
            @Override
            public void serialize(OffsetDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                // 例如只输出日期和时间，不带时区
                gen.writeString(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(value));
            }
        });

        // 添加 Duration 的序列化和反序列化器
        simpleModule.addSerializer(Duration.class, new DurationToTimeSerializer());
        simpleModule.addDeserializer(Duration.class, new DurationFromTimeDeserializer());

        // 添加 ZonedDateTime 的序列化和反序列化器
//        simpleModule.addSerializer(ZonedDateTime.class, new CustomZonedDateTimeSerializer());
//        simpleModule.addDeserializer(ZonedDateTime.class, new CustomZonedDateTimeDeserializer());

        objectMapper.registerModule(simpleModule);

        // Configure ObjectMapper to ignore null values during serialization
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // Configure ObjectMapper to ignore unknown properties during deserialization
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
}
