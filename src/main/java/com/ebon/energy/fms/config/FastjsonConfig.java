package com.ebon.energy.fms.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.ebon.energy.fms.domain.vo.*;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;

@Configuration
public class FastjsonConfig {

    @PostConstruct
    public void initFastjsonConfig() {
        ParserConfig parserConfig = ParserConfig.getGlobalInstance();
        parserConfig.putDeserializer(Watt.class, new WattDeserializer());
        parserConfig.putDeserializer(WattHour.class, new WattHourDeserializer());
        parserConfig.putDeserializer(Ampere.class, new AmpereDeserializer());
        parserConfig.putDeserializer(Volt.class, new VoltDeserializer());
        parserConfig.putDeserializer(VoltAmps.class, new VoltAmpsDeserializer());
        parserConfig.putDeserializer(Capacity.class, new CapacityDeserializer());
        parserConfig.putDeserializer(Celsius.class, new CelsiusDeserializer());
        parserConfig.putDeserializer(Frequency.class, new FrequencyDeserializer());
        parserConfig.putDeserializer(Kelvin.class, new KelvinDeserializer());
        parserConfig.putDeserializer(Ohm.class, new OhmDeserializer());
        parserConfig.putDeserializer(VoltAmpsReactive.class, new VoltAmpsReactiveDeserializer());
        parserConfig.putDeserializer(Instant.class, new InstantTimeDeserializer());
        parserConfig.putDeserializer(ZonedDateTime.class, new CustomZonedDateTimeDeserializer());
    }

    class WattDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Watt va = new Watt((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Watt va = new Watt((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Watt va = new Watt((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class WattHourDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                WattHour va = new WattHour((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                WattHour va = new WattHour((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                WattHour va = new WattHour((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class AmpereDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Ampere va = new Ampere((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Ampere va = new Ampere((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Ampere va = new Ampere((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class VoltAmpsDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                VoltAmps va = new VoltAmps((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                VoltAmps va = new VoltAmps((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                VoltAmps va = new VoltAmps((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class VoltDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Volt va = new Volt((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Volt va = new Volt((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Volt va = new Volt((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class CapacityDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Capacity va = new Capacity((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Capacity va = new Capacity((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Capacity va = new Capacity((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class CelsiusDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Celsius va = new Celsius((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Celsius va = new Celsius((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Celsius va = new Celsius((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class FrequencyDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Frequency va = new Frequency((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Frequency va = new Frequency((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Frequency va = new Frequency((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class OhmDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Ohm va = new Ohm((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Ohm va = new Ohm((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Ohm va = new Ohm((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class KelvinDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                Kelvin va = new Kelvin((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                Kelvin va = new Kelvin((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                Kelvin va = new Kelvin((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

    class VoltAmpsReactiveDeserializer implements ObjectDeserializer {
        @Override
        public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
            Object value = parser.parse();
            if (value == null) {
                return null;
            }

            if (value instanceof Integer) {
                VoltAmpsReactive va = new VoltAmpsReactive((Integer) value);
                return (T) va;
            } else if (value instanceof Double) {
                VoltAmpsReactive va = new VoltAmpsReactive((Double) value);
                return (T) va;
            } else if (value instanceof BigDecimal) {
                VoltAmpsReactive va = new VoltAmpsReactive((BigDecimal) value);
                return (T) va;
            }
            return null;
        }

        @Override
        public int getFastMatchToken() {
            return 0;
        }
    }

}