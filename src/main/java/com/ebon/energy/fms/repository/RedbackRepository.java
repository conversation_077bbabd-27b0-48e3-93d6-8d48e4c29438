package com.ebon.energy.fms.repository;


import com.ebon.energy.fms.domain.vo.product.control.DeviceControlDTO;
import com.ebon.energy.fms.domain.vo.product.control.ProductDTO;
import com.ebon.energy.fms.domain.vo.product.control.UpdateDeviceControlDTO;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;

/**
 * Repository interface for Redback product data access
 */
public interface RedbackRepository {

    /**
     * Get product information as a ProductDTO
     * 
     * @param serialNumber Product serial number
     * @return ProductDTO containing product information
     */
    ProductDTO getProductDTO( String serialNumber);


    DeviceControlDTO getDeviceControl(String serialNumber);


    void updateDeviceControlRoss1(UpdateDeviceControlDTO updateDeviceControlDTO);


    SystemStatus getLatestSystemStatusAsync(String serialNumber);
}
