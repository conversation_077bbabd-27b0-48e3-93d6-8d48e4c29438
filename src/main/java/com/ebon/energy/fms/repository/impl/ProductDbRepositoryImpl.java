package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.domain.entity.InverterScheduleDO;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleAuditDto;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleInfoDto;
import com.ebon.energy.fms.mapper.third.ConfigurationsMapper;
import com.ebon.energy.fms.mapper.third.InverterScheduleMapper;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.sql.Time;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for product database operations
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ProductDbRepositoryImpl implements ProductDbRepository {

    private final ConfigurationsMapper configurationsMapper;
    private final InverterScheduleMapper inverterScheduleMapper;

    @Override
    public List<ScheduleAuditDto> getActiveScheduleCreateAuditsTrustedAsync(String serialNumber) {
        return configurationsMapper.getActiveScheduleCreateAudits(serialNumber);
    }

    @Override
    public void deleteScheduleTrustedAsync(String scheduleId, String deletedById, String serialNumber, String channel) {
        // Implement the C# functionality in Java
        Date whenUtc = new Date(); // Current UTC time

        int rowsAffected = inverterScheduleMapper.deleteScheduleTrustedAsync(
                deletedById,
                whenUtc,
                channel,
                serialNumber,
                scheduleId
        );

        if (rowsAffected > 1) {
            String msg = String.format("Unexpected number of rows when updating schedule: %s/%s/%s: %d",
                    scheduleId, deletedById, serialNumber, rowsAffected);
            log.warn(msg);
            // Don't log an issue if it's zero as there is a large number of existing schedules that are not in this table.
        }
    }

    @Override
    public void saveScheduleTrustedAsync(String userId, String serialNumber, String scheduleId, Object schedule,
                                         String ianaTimeZoneId, String channel, String userNotes) {
        if (!(schedule instanceof ScheduleInfoDto)) {
            log.error("Schedule object is not of type ScheduleInfoDto");
            return;
        }

        ScheduleInfoDto scheduleInfo = (ScheduleInfoDto) schedule;
        Date createdOnUtc = new Date(); // Current UTC time

        // Convert ZonedDateTime to java.sql.Date
        Date startAtUtc = scheduleInfo.getStartAtUtc() != null ?
                Date.from(scheduleInfo.getStartAtUtc().toInstant()) : null;
        Date endAtUtc = scheduleInfo.getEndAtUtc() != null ?
                Date.from(scheduleInfo.getEndAtUtc().toInstant()) : null;

        // Convert Duration to java.sql.Time
        Time dailyStartTime = null;
        if (scheduleInfo.getDailyStartTime() != null) {
            LocalTime localTime = LocalTime.MIDNIGHT.plus(scheduleInfo.getDailyStartTime());
            dailyStartTime = Time.valueOf(localTime);
        }

        Time duration = null;
        if (scheduleInfo.getScheduleDuration() != null) {
            LocalTime localTime = LocalTime.MIDNIGHT.plus(scheduleInfo.getScheduleDuration());
            duration = Time.valueOf(localTime);
        }

        // Convert ScheduleDays to Integer
        Integer daysOfWeek = scheduleInfo.getDaysOfWeekActive() != null ?
                (int) scheduleInfo.getDaysOfWeekActive().getValue() : 0;

        // Convert action parameter from long to int
        Integer actionParameter = (int) scheduleInfo.getActionParameter();

        // Create InverterScheduleDO object
        InverterScheduleDO inverterSchedule = new InverterScheduleDO();
        inverterSchedule.setSerialNumber(serialNumber);
        inverterSchedule.setScheduleId(scheduleId);
        inverterSchedule.setUserNotes(userNotes);
        inverterSchedule.setCreatedById(RequestUtil.getPortolUserId());
        inverterSchedule.setCreatedOnUtc(createdOnUtc == null ? null : new Timestamp(createdOnUtc.getTime()));
        inverterSchedule.setCreatedChannel(channel);
        inverterSchedule.setStartAtUtc(startAtUtc == null ? null : new Timestamp(startAtUtc.getTime()));
        inverterSchedule.setEndAtUtc(endAtUtc == null ? null : new Timestamp(endAtUtc.getTime()));
        inverterSchedule.setDailyStartTime(dailyStartTime);
        inverterSchedule.setDuration(duration);
        inverterSchedule.setDaysOfWeek(daysOfWeek);
        inverterSchedule.setActionName(scheduleInfo.getActionName());
        inverterSchedule.setActionParameter(actionParameter);
        inverterSchedule.setPriority(scheduleInfo.getPriority());
        inverterSchedule.setIanaTimeZoneId(ianaTimeZoneId);

        // Use the insert method from BaseMapper
        int rowsAffected = inverterScheduleMapper.insert(inverterSchedule);

        if (rowsAffected != 1) {
            log.warn("Unexpected number of rows affected when inserting schedule: {}", rowsAffected);
        }
    }
}
