package com.ebon.energy.fms.repository.converter;

import com.ebon.energy.fms.domain.entity.AppVersionManagementDO;
import com.ebon.energy.fms.domain.po.CreateAppVersionPO;
import com.ebon.energy.fms.domain.vo.AppVersionManagementVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/29
 */
@Mapper
public interface AppVersionManagementConverter {

    AppVersionManagementConverter INSTANCE = Mappers.getMapper(AppVersionManagementConverter.class);


    @Mapping(target = "createdAt", expression = "java(toLong(appVersionManagementDO.getCreatedAt()))")
    @Mapping(target = "updatedAt", expression = "java(toLong(appVersionManagementDO.getUpdatedAt()))")
    AppVersionManagementVO toVo(AppVersionManagementDO appVersionManagementDO);

    List<AppVersionManagementVO> batchToVo(List<AppVersionManagementDO> appVersionManagementDOList);

    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", source = "userId")
    @Mapping(target = "updatedBy", source = "userId")
    AppVersionManagementDO toAppversionManamentDO(CreateAppVersionPO createAppVersionPo, String userId);

    default Long toLong(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
