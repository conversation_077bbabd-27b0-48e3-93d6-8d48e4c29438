package com.ebon.energy.fms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.util.JsonUtils;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.domain.entity.DeviceSettingsDO;
import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.product.DeviceIdInfoVo;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.DeviceMapper;
import com.ebon.energy.fms.mapper.third.DeviceSettingsMapper;
import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.azure.IoTHubUtility;
import com.ebon.energy.fms.util.tuya.TuyaApiUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Locale;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * Java implementation of the C# SettingsService
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SettingsServiceImpl implements com.ebon.energy.fms.repository.ISettingsService {

    private final ObjectMapper objectMapper;

    private final DeviceMapper deviceMapper;

    private final TuyaApiUtil tuyaService;

    private final IoTHubUtility iotHubService;

    private final DeviceSettingsMapper deviceSettingsMapper;


    @Override
    @SneakyThrows
    public DeviceSettingsDto getSettingsDtoTrusted(String serialNumber) {
        var deviceSettingVo = deviceMapper.getDesiredDeviceSettingsBySerialNumber(serialNumber);

        log.info("{}", deviceSettingVo.getDesired());
        var desireSettings = objectMapper.readValue(deviceSettingVo.getDesired(), RossDesiredSettings.class);

        var report = objectMapper.readValue(deviceSettingVo.getReportedDeviceSettings(), RossReportedSettings.class);
        InverterIdentityCard card = new InverterIdentityCard();

        if (deviceSettingVo.getLatestSystemStatus() != null) {
            var latestSystemStatus = JSONObject.parseObject(deviceSettingVo.getLatestSystemStatus(), SystemStatus.class);
            card.setSerialNumber(latestSystemStatus.getInverter().getInverterSN());
            card.setModelName(latestSystemStatus.getInverter().getModelName());
            card.setFirmwareVersion(latestSystemStatus.getInverter().getFirmwareVersion());
            card.setSofterVersion(new RossVersion(latestSystemStatus.getOuijaBoard().getSoftwareVersion()));
            card.setHardwareConfig(deviceSettingVo.getHardwareConfig());
        } else {
            card.setHardwareConfig(deviceSettingVo.getHardwareConfig());
        }

        return new DeviceSettingsDto(report, desireSettings, null, card, null);
    }

    @Override
    @SneakyThrows
    public DeviceSettingsDto getSettingsDtoAsync(String serialNumber) {
        var deviceSettingVo = deviceMapper.getDeviceSettingsDtoQueryNoAuth(serialNumber);
        if (deviceSettingVo == null) {
            return new DeviceSettingsDto(new RossReportedSettings(), new RossDesiredSettings(), new DeviceSettingsIntentVO(), null, null);
        }
        var desireSettings = objectMapper.readValue(deviceSettingVo.getDesiredDeviceSettings(), RossDesiredSettings.class);

        var report = objectMapper.readValue(deviceSettingVo.getReportedDeviceSettings(), RossReportedSettings.class);


        DeviceSettingsIntentVO intent = null;
        if (StringUtils.hasLength(deviceSettingVo.getDeviceSettingsIntent())) {
            intent = JSONObject.parseObject(deviceSettingVo.getDeviceSettingsIntent(), DeviceSettingsIntentVO.class);
        }
        InverterIdentityCard card = new InverterIdentityCard();

        if (deviceSettingVo.getLatestSystemStatus() != null) {
            var latestSystemStatus = JSONObject.parseObject(deviceSettingVo.getLatestSystemStatus(), SystemStatus.class);
            card.setSerialNumber(latestSystemStatus.getInverter().getInverterSN());
            card.setModelName(latestSystemStatus.getInverter().getModelName());
            card.setFirmwareVersion(latestSystemStatus.getInverter().getFirmwareVersion());
            card.setSofterVersion(new RossVersion(latestSystemStatus.getOuijaBoard().getSoftwareVersion()));
            card.setHardwareConfig(deviceSettingVo.getHardwareConfig());
        } else {
            card.setHardwareConfig(deviceSettingVo.getHardwareConfig());
        }

        var explicitSettings = new ExplicitSettings(
                deviceSettingVo.getMinOnGridSoC0to100(),
                deviceSettingVo.getMaxOnGridSoC0to100(),
                deviceSettingVo.getMinOffGridSoC0to100(),
                deviceSettingVo.getMaxOffGridSoC0to100());

        return new DeviceSettingsDto(report, desireSettings, intent, card, explicitSettings);

    }

    @Override
    public void updateDesiredDeviceSettingsFromJsonPatch(String loggedInUserId, String serialNumber, String jsonPatch, DeviceSettingsIntentVO intent, Boolean updateDeviceSettings) {
        updateDeviceControl(loggedInUserId, serialNumber, jsonPatch, intent, updateDeviceSettings);
    }


    /**
     * Updates device control settings
     *
     * @param loggedInUserId       User ID of the logged in user
     * @param serialNumber         Serial number of the device
     * @param jsonPatch            JSON patch to apply to device settings
     * @param intent               Device settings intent
     * @param updateDeviceSettings Whether to update device settings
     * @return the new desired version number
     */
    private void updateDeviceControl(String loggedInUserId, String serialNumber, String jsonPatch, DeviceSettingsIntentVO intent, Boolean updateDeviceSettings) {
        try {
            // Get device ID information
            var deviceInfo = getDeviceIdInformation(serialNumber);
            String idOfDeviceTable = deviceInfo.getId();
            String deviceId = deviceInfo.getDeviceId();
            String cloudPlatformName = deviceInfo.getCloudPlatformName();

            // Convert intent to JSON if not null
            String intentAsJson = intent == null ? null : JsonUtils.toJson(intent);

            // Update device settings if required
            if (updateDeviceSettings) {
                updateDeviceSettingsInternal(
                        loggedInUserId,
                        deviceId,
                        deviceSettings -> {
                            deviceSettings.setDesiredDeviceSettingsPatch(jsonPatch);
                            if (intentAsJson != null) {
                                deviceSettings.setDeviceSettingsIntent(intentAsJson);
                            }
                        },
                        idOfDeviceTable);
            }

            Long lastUpdatedVersion = null;

            // Handle different cloud platforms
            if (cloudPlatformName != null && cloudPlatformName.equals(CloudPlatformName.Tuya.toString())) {
                // For Tuya platform
                lastUpdatedVersion = tuyaService.updateTuyaDeviceShadowDesired(deviceId, tuyaConvertToFullPatch(jsonPatch));
            } else {
                // For other platforms, update device twin
                updateDesiredSettingsOnDeviceTwinInternalAsync(deviceId, jsonPatch);
            }

        } catch (Exception e) {
            log.error("Error updating device control", e);
        }
    }

    /**
     * Gets the ID of the device table for a given device ID
     *
     * @param deviceId Device ID
     * @return ID of the device table
     * @throws IllegalArgumentException if the device is not set up to use new device settings
     */
    private String getIdOfDeviceTable(String deviceId) {
        String deviceTableId = deviceMapper.getIdOfDeviceTableByDeviceId(deviceId);

        if (deviceTableId == null) {
            throw new IllegalArgumentException("Device is not set up to use new device settings for device Id " + deviceId);
        }

        return deviceTableId;
    }

    /**
     * Updates device settings internally
     *
     * @param loggedInUserId  User ID of the logged in user
     * @param deviceId        Device ID
     * @param updateFunc      Function to update device settings
     * @param idOfDeviceTable ID of device table (optional)
     */
    private void updateDeviceSettingsInternal(
            String loggedInUserId,
            String deviceId,
            Consumer<DeviceSettingsDO> updateFunc,
            String idOfDeviceTable) {

        if (idOfDeviceTable == null) {
            idOfDeviceTable = getIdOfDeviceTable(deviceId);
        }

        if (idOfDeviceTable == null) {
            throw new IllegalArgumentException("Device '" + deviceId + "' does not have Device record and likely it is not provisioned or data corruption and so cannot save device settings.");
        }

        // Query existing device settings
        DeviceSettingsDO deviceSetting = deviceSettingsMapper.selectWithProductByDeviceId(idOfDeviceTable);

        if (deviceSetting != null) {
            // Update existing device settings
            updateFunc.accept(deviceSetting);
            deviceSetting.setLastModifiedById(RequestUtil.getPortolUserId());
            deviceSetting.setLastModifiedOnUtc(Timestamp.from(Instant.now()));
            deviceSettingsMapper.updateById(deviceSetting);
        } else {
            // Create new device settings
            deviceSetting = new DeviceSettingsDO();
            deviceSetting.setId(UUID.randomUUID().toString()); // Generate a UUID string
            deviceSetting.setDeviceId(idOfDeviceTable);
            deviceSetting.setReportedDeviceSettings("{}");
            deviceSetting.setDesiredDeviceSettings("{}");
            deviceSetting.setDeviceSettingsIntent("{}");
            // No direct equivalent to HasBeenApplied in the Java entity, might need to be added if required
            deviceSetting.setHasBeenApplied(true);
            deviceSetting.setLastModifiedById(loggedInUserId);
            deviceSetting.setLastModifiedOnUtc(Timestamp.from(Instant.now()));

            // Apply the update function
            updateFunc.accept(deviceSetting);

            // Insert the new record
            deviceSettingsMapper.insert(deviceSetting);
        }
    }

    private DeviceIdInfoVo getDeviceIdInformation(String serialNumber) {
        return deviceMapper.getDeviceIdInformationBySerialNumber(serialNumber);
    }


    @SneakyThrows
    private void updateDesiredSettingsOnDeviceTwinInternalAsync(String deviceId, String desiredSettingPatch) {
        iotHubService.updateDeviceTwin(deviceId, ConvertToFullPatch(desiredSettingPatch));
    }


    private static String tuyaConvertToFullPatch(String justDesired) {
        return String.format(Locale.ROOT, "{\"settings\": %s }", justDesired);
    }


    private static String ConvertToFullPatch(String justDesired) {
        return String.format(Locale.ROOT, "{\"properties\": {\"desired\": {\"settings\": %s }}}}", justDesired);
    }
}
