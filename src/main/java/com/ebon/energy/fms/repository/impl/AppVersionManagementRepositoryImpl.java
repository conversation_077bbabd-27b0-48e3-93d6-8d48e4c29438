package com.ebon.energy.fms.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.AppVersionManagementDO;
import com.ebon.energy.fms.domain.po.AppVersionManagePagePO;
import com.ebon.energy.fms.domain.po.AppVersionQuery;
import com.ebon.energy.fms.domain.po.ModifyAppVersionPO;
import com.ebon.energy.fms.domain.vo.AppVersionManagementVO;
import com.ebon.energy.fms.mapper.primary.AppVersionManagementMapper;
import com.ebon.energy.fms.repository.AppVersionManagementRepository;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

import static com.ebon.energy.fms.common.enums.CommonErrorCodeEnum.APP_VERSION_EXIST;
import static com.ebon.energy.fms.common.enums.CommonErrorCodeEnum.APP_VERSION_NOT_EXIST;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Repository
@RequiredArgsConstructor
public class AppVersionManagementRepositoryImpl implements AppVersionManagementRepository {

    private final AppVersionManagementMapper appVersionManagementMapper;

    @Override
    public IPage<AppVersionManagementDO> selectAppVersionPage(AppVersionManagePagePO appVersionManagePagePo) {
        Page<AppVersionManagementDO> page = new Page<>(appVersionManagePagePo.getCurrent(), appVersionManagePagePo.getPageSize());
        LambdaQueryWrapper<AppVersionManagementDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNoneBlank(appVersionManagePagePo.getKeyword()), AppVersionManagementDO::getProduct, appVersionManagePagePo.getKeyword());
        queryWrapper.orderByDesc(AppVersionManagementDO::getUpdatedAt);
        IPage<AppVersionManagementDO> appVersionManagementPage = appVersionManagementMapper.selectPage(page, queryWrapper);
        return appVersionManagementPage;
    }

    @Override
    public Optional<AppVersionManagementDO> selectByProductAndOsType(AppVersionQuery appVersionQuery) {
        LambdaQueryWrapper<AppVersionManagementDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppVersionManagementDO::getProduct, appVersionQuery.getProduct());
        queryWrapper.eq(AppVersionManagementDO::getOsType, appVersionQuery.getOsType());
        queryWrapper.eq(AppVersionManagementDO::getVersion, appVersionQuery.getVersion());
        queryWrapper.eq(AppVersionManagementDO::getStatus, appVersionQuery.getStatus());
        return Optional.ofNullable(appVersionManagementMapper.selectOne(queryWrapper));
    }

    @Override
    public void save(AppVersionManagementDO appversionManamentDO) {
        try {
            appVersionManagementMapper.insert(appversionManamentDO);
        }catch (DuplicateKeyException e) {
            throw new BizException(APP_VERSION_EXIST);
        }
    }

    @Override
    public Optional<AppVersionManagementDO> findById(Long id) {
        LambdaQueryWrapper<AppVersionManagementDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppVersionManagementDO::getId, id);
        return Optional.ofNullable(appVersionManagementMapper.selectOne(queryWrapper));
    }

    @Override
    public void modify(ModifyAppVersionPO modifyAppVersionPo) {
        Optional<AppVersionManagementDO> appVersionManagementOptional = findById(modifyAppVersionPo.getId());
        if (appVersionManagementOptional.isEmpty()) {
            throw new BizException(APP_VERSION_NOT_EXIST);
        }
        String userId = RequestUtil.getLoginUserEmail();
        LambdaUpdateWrapper<AppVersionManagementDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(StringUtils.isNoneBlank(modifyAppVersionPo.getReleaseNotes()), AppVersionManagementDO::getReleaseNotes, modifyAppVersionPo.getReleaseNotes());
        updateWrapper.set(StringUtils.isNoneBlank(modifyAppVersionPo.getDownloadUrl()), AppVersionManagementDO::getDownloadUrl, modifyAppVersionPo.getDownloadUrl());
        updateWrapper.set(StringUtils.isNoneBlank(modifyAppVersionPo.getUpgradeMethod()), AppVersionManagementDO::getUpgradeMethod, modifyAppVersionPo.getUpgradeMethod());
        updateWrapper.set(StringUtils.isNoneBlank(modifyAppVersionPo.getStatus()), AppVersionManagementDO::getStatus, modifyAppVersionPo.getStatus());
        updateWrapper.set(StringUtils.isNoneBlank(userId), AppVersionManagementDO::getUpdatedBy, userId);
        updateWrapper.set(AppVersionManagementDO::getUpdatedAt, LocalDateTime.now());
        updateWrapper.eq(AppVersionManagementDO::getId, modifyAppVersionPo.getId());
        appVersionManagementMapper.update(null, updateWrapper);
    }


}
