package com.ebon.energy.fms.repository;


import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.product.control.DeviceSettingsDto;

/**
 * Java equivalent of the C# ISettingsService interface
 */
public interface ISettingsService {

    /**
     * Gets device settings DTO without authentication check
     *
     * @param serialNumber Serial number of the device
     * @return DeviceSettingsDto containing device settings
     */
    DeviceSettingsDto getSettingsDtoTrusted(String serialNumber);


    DeviceSettingsDto getSettingsDtoAsync(String serialNumber);

    void updateDesiredDeviceSettingsFromJsonPatch(String loggedInUserId, String serialNumber, String jsonPatch, DeviceSettingsIntentVO intent, Boolean updateDeviceSettings);
}
