package com.ebon.energy.fms.repository;

import com.ebon.energy.fms.domain.vo.product.control.ScheduleAuditDto;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for product database operations
 */
public interface ProductDbRepository {

    /**
     * Get active schedule create audits for a product
     *
     * @param serialNumber Product serial number
     * @return List of schedule audit DTOs
     */
    List<ScheduleAuditDto> getActiveScheduleCreateAuditsTrustedAsync(String serialNumber);

    /**
     * Delete a schedule
     *
     * @param scheduleId   Schedule ID
     * @param deletedById  User ID who deleted the schedule
     * @param serialNumber Product serial number
     * @param channel      Channel through which the schedule was deleted
     * @return CompletableFuture for async operation
     */
    void deleteScheduleTrustedAsync(
            String scheduleId,
            String deletedById,
            String serialNumber,
            String channel);

    /**
     * Save a schedule
     *
     * @param userId         User ID
     * @param serialNumber   Product serial number
     * @param scheduleId     Schedule ID
     * @param schedule       Schedule information
     * @param ianaTimeZoneId Time zone ID
     * @param channel        Channel through which the schedule was saved
     * @param userNotes      User notes
     * @return CompletableFuture for async operation
     */
    void saveScheduleTrustedAsync(
            String userId,
            String serialNumber,
            String scheduleId,
            Object schedule,
            String ianaTimeZoneId,
            String channel,
            String userNotes);

}
