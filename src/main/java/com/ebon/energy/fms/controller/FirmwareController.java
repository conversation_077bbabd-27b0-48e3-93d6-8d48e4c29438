package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.FirmwareUpdatePO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.service.FirmwareService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 固件管理
 */
@RestController
@RequestMapping("/api/firmware")
public class FirmwareController {

    @Resource
    private FirmwareService firmwareService;

    /**
     * 版本查询
     *
     * @return List<FirmwareVersionVO>
     */
    @GetMapping("/get-versions")
    public JsonResult<List<FirmwareVersionSimpleVO>> getVersions() {
        return JsonResult.buildSuccess(firmwareService.getFirmwareVersion());
    }

    /**
     * 版本详情查询
     *
     * @return FirmwareVersionVO
     */
    @GetMapping("/get-versions-detail")
    public JsonResult<FirmwareVersionDetailVO> getVersionsDetail(@RequestParam("id") Integer id) {
        return JsonResult.buildSuccess(firmwareService.getFirmwareVersionDetail(id));
    }

    /**
     * 更新固件
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Upgrade Firmware")
    @PostMapping("/upgrade")
    public JsonResult<List<FirmwareUpdateErrorVO>> upgrade(@RequestBody FirmwareUpdatePO po) {
        return JsonResult.buildSuccess(firmwareService.upgrade(po));
    }

}
