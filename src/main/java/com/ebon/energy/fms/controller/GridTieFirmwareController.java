package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.NoLoginRequired;
import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.common.enums.ModelTypeEnum;
import com.ebon.energy.fms.domain.po.GridTieFirmwareVersionsUpdatePO;
import com.ebon.energy.fms.domain.vo.GridTieFirmwareVersionsUpdateErrorVO;
import com.ebon.energy.fms.domain.vo.GridTieFirmwareVersionsVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.service.GridTieFirmwareVersionsService;
import com.ebon.energy.fms.service.ModelVersionRelationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api/grid-tie-firmware")
public class GridTieFirmwareController {

    @Resource
    private GridTieFirmwareVersionsService gridTieFirmwareVersionsService;

    @Resource
    private ModelVersionRelationService modelVersionRelationService;

    /**
     * 版本查询
     *
     * @return List<GridTieFirmwareVersionsVO>
     */
    @GetMapping("/get-versions")
    public JsonResult<List<GridTieFirmwareVersionsVO>> getVersions() {
        List<GridTieFirmwareVersionsVO> list = gridTieFirmwareVersionsService.getAllList();
        return JsonResult.buildSuccess(list);
    }

    /**
     * 版本详情查询
     *
     * @return
     */
    @GetMapping("/get-version-detail")
    public JsonResult<Map<String, Object>> getVersionDetail(@RequestParam("id") String id) {
        GridTieFirmwareVersionsVO detail = gridTieFirmwareVersionsService.getVersionDetail(id);
        Map<String, Object> map = new HashMap<>();
        map.put("officialVersions", detail);
        map.put("storageContainerName", gridTieFirmwareVersionsService.getStorageContainerURL());
        return JsonResult.buildSuccess(map);
    }

    /**
     * 更新(批量)
     *
     * @param po po
     * @return
     */
    @OperateLogAnnotation(name="Update EMS Firmware")
    @PostMapping("/update")
    public JsonResult<List<GridTieFirmwareVersionsUpdateErrorVO>> update(@Valid @RequestBody GridTieFirmwareVersionsUpdatePO po) throws Exception {
        return JsonResult.buildSuccess(gridTieFirmwareVersionsService.update(po));
    }


    /**
     * 根据模型ID获取精简版本号列表
     * @param modelId 模型ID
     * @return 版本号列表
     */
    @GetMapping("/get-model-versions")
    public JsonResult<List<String>> getVersionStringsByModelId(
            @RequestParam("modelId") Integer modelId) {
        List<String> versions = modelVersionRelationService.listVersionsByModelId(modelId, ModelTypeEnum.EMSFirmware);
        return JsonResult.buildSuccess(versions);
    }


    /**
     * 根据模型Name获取精简版本号列表
     * @param modelName 模型Name
     * @param type 类型 默认0 0EMSFirmware 1Firmware
     * @return 版本号列表
     */
    @GetMapping("/get-versions-by-model-name")
    @NoLoginRequired
    public JsonResult<Map<String, List<String>>> getVersionStringsByModelName(
            @RequestParam("modelNames") List<String> modelNames,
            @RequestParam(value = "type", defaultValue = "0") Integer modelType) {
        Map<String, List<String>> versions = modelVersionRelationService.listVersionsByModelName(modelNames, ModelTypeEnum.fromValue(modelType));
        return JsonResult.buildSuccess(versions);
    }
}
