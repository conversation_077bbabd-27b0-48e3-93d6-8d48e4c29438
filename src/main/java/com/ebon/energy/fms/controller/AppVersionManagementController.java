package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.NoLoginRequired;
import com.ebon.energy.fms.domain.po.AppVersionManagePagePO;
import com.ebon.energy.fms.domain.po.CreateAppVersionPO;
import com.ebon.energy.fms.domain.po.ModifyAppVersionPO;
import com.ebon.energy.fms.domain.vo.AppVersionManagementVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.service.AppVersionManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@RestController
@RequestMapping("/api/app-version")
@RequiredArgsConstructor
@Validated
public class AppVersionManagementController {

    private final AppVersionManagementService appVersionManagementService;

    @GetMapping("page")
    public JsonResult<PageResult<AppVersionManagementVO>> getAppVersionPage(
            @RequestParam(name = "current", defaultValue = "0") Integer current,
            @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize,
            @RequestParam(name = "keyword", required = false) String keyword) {
        return JsonResult.buildSuccess(appVersionManagementService.getAppVersionPage(AppVersionManagePagePO.builder()
                .current(current)
                .pageSize(pageSize)
                .keyword(keyword)
                .build()));
    }

    @PostMapping("create")
    public JsonResult<Void> create(@Valid @RequestBody CreateAppVersionPO createAppVersion) {
        appVersionManagementService.createAppVersion(createAppVersion);
        return JsonResult.buildSuccess();
    }

    @PostMapping("modify")
    public JsonResult<Void> modify(@Valid @RequestBody ModifyAppVersionPO modifyAppVersion) {
        appVersionManagementService.modifyAppVersion(modifyAppVersion);
        return JsonResult.buildSuccess();
    }

}
