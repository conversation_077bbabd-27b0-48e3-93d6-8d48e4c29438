package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.InvertersListPO;
import com.ebon.energy.fms.domain.po.UpdateErrorProcessedPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.ProductControlViewModel;
import com.ebon.energy.fms.domain.vo.telemetry.DataWithLinks;
import com.ebon.energy.fms.domain.vo.telemetry.InverterInfo;
import com.ebon.energy.fms.service.InverterAttentionService;
import com.ebon.energy.fms.service.InverterService;
import com.ebon.energy.fms.service.TelemetryService;
import com.ebon.energy.fms.service.product.control.ProductControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 逆变器管理
 */
@Slf4j
@RestController
@RequestMapping("/api/inverters")
public class InverterController {

    @Resource
    private InverterService inverterService;

    @Resource
    private TelemetryService telemetryService;

    @Resource
    private ProductControlService productControlService;
    
    @Resource
    private InverterAttentionService inverterAttentionService;

    /**
     * 逆变器查询
     *
     * @param po
     * @return
     */
    @GetMapping("/index")
    public JsonResult<InvertersIndexVO> index(InvertersListPO po) {
        InvertersIndexVO indexVO = inverterService.getInverterList(po);
        return JsonResult.buildSuccess(indexVO);
    }

    /**
     * 逆变器信息查询
     *
     * @param sn 序列号
     * @return
     */
    @GetMapping("/info")
    public JsonResult<InverterInfoVO> info(@RequestParam String sn) {
        InverterInfoVO infoVO = inverterService.getInverterInfo(sn);
        return JsonResult.buildSuccess(infoVO);
    }

    /**
     * 标记警告已处理
     * 
     * @param request 
     * @return
     */
    @OperateLogAnnotation(name="Update Alarms Processed")
    @PostMapping("/update-error-processed")
    public JsonResult<Void> updateErrorProcessed(@RequestBody UpdateErrorProcessedPO request) {
        inverterAttentionService.updateErrorProcessed(request.getSerialNumber());
        return JsonResult.buildSuccess();
    }

    /**
     * 逆变器最新遥测数据查询
     *
     * @param sn 逆变器序列号
     * @return
     */
    @GetMapping("/telemetry/latest")
    public JsonResult<DataWithLinks<InverterInfo>> telemetry(@RequestParam String sn) {
        DataWithLinks<InverterInfo> telemetryData = telemetryService.getLastTelemetry(sn);
        return JsonResult.buildSuccess(telemetryData);
    }

    /**
     * 逆变器最新遥测数据状态查询
     *
     * @param sn 逆变器序列号
     * @return
     */
    @GetMapping("/status/latest")
    public JsonResult<DataWithLinks<InverterInfo>> systemStatus(@RequestParam String sn,
                                                                @RequestParam(required = false, defaultValue = "0") Integer round) {
        DataWithLinks<InverterInfo> statusData = telemetryService.getLastStatus(sn, round != null && round == 1);
        return JsonResult.buildSuccess(statusData);
    }

    /**
     * 逆变器最新能源流查询
     *
     * @param sn 逆变器序列号
     * @return
     */
    @GetMapping("/energyflow/latest")
    public JsonResult<DataWithPermalinkVO<EnergyFlowExtendedVO>> energyflow(@RequestParam String sn) {
        DataWithPermalinkVO<EnergyFlowExtendedVO> data = telemetryService.getLastEnergyflow(sn);
        return JsonResult.buildSuccess(data);
    }

    @GetMapping("/battery/work-mode")
    public JsonResult<ProductControlViewModel> batteryWorkMode(@RequestParam String sn) {
      return   JsonResult.buildSuccess(productControlService.queryControl(sn));
    }

    @OperateLogAnnotation(name="Update Work Mode")
    @PostMapping("/battery/work-mode")
    public JsonResult<Void> changeBatteryWorkMode(@RequestBody ProductControlViewModel request) {
        log.info("changeBatteryWorkMode:{}",request);
        productControlService.changeWorkMode(request);
        return JsonResult.buildSuccess();
    }
}