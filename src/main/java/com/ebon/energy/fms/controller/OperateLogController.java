package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.po.OperateLogListPO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.OperateLogVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.service.OperateLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 操作日志
 */
@RestController
@RequestMapping("/api/operate-log")
public class OperateLogController {

    @Resource
    private OperateLogService operateLogService;

    /**
     * 操作日志查询
     *
     * @param po
     * @return
     */
    @PostMapping("/list")
    public JsonResult<PageResult<OperateLogVO>> list(@RequestBody @Valid OperateLogListPO po) {
        return JsonResult.buildSuccess(operateLogService.getOperateLogList(po));
    }

}
