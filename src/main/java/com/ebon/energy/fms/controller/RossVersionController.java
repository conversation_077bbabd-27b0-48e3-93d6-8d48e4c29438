package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.CheckRossVersionPO;
import com.ebon.energy.fms.domain.po.UpdateRossVersionPO;
import com.ebon.energy.fms.domain.vo.CheckRoss2ReadyVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.RossVersionsUpdateErrorVO;
import com.ebon.energy.fms.service.RossVersionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * Ross版本管理
 */
@RestController
@RequestMapping("/api/ross-version")
public class RossVersionController {

    @Resource
    private RossVersionService rossVersionService;

    /**
     * 检查升级
     *
     * @return List<DirectMethodsVO>
     */
    @PostMapping("/check-ready")
    public JsonResult<List<CheckRoss2ReadyVO>> checkRoss2Ready(@RequestBody CheckRossVersionPO po) throws IOException, InterruptedException {
        return JsonResult.buildSuccess(rossVersionService.checkRoss2Ready(po.getSerialNumbers()));
    }

    /**
     * 获取所有ross版本
     * @param
     * @return
     */
    @GetMapping("/get-versions")
    public JsonResult<List<String>> getVersions() throws IOException, InterruptedException {
        return JsonResult.buildSuccess(rossVersionService.getVersions());
    }

    /**
     * 升级
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Update Ross Version")
    @PostMapping("/upgrade")
    public JsonResult<List<RossVersionsUpdateErrorVO>> upgrade(@RequestBody UpdateRossVersionPO po) throws IOException, InterruptedException {
        return JsonResult.buildSuccess(rossVersionService.upgrade(po));
    }
}
