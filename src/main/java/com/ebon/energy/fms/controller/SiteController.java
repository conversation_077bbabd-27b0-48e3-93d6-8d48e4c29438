package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.service.SiteDashboardHistoryService;
import com.ebon.energy.fms.service.SiteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 站点管理
 */
@Slf4j
@RestController
@RequestMapping("/api/site")
public class SiteController {

    @Resource
    private SiteService siteService;

    @Resource
    private SiteDashboardHistoryService siteDashboardHistoryService;

    /**
     * 站点列表查询
     *
     * @param po
     * @return
     */
    @GetMapping("/list")
    public JsonResult<PageResult<SiteVO>> list(SiteListPO po) {
        return JsonResult.buildSuccess(siteService.getSiteList(po));
    }

    /**
     * 站点逆变器列表
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/inverter-list")
    public JsonResult<List<SiteInverterInfoVO>> siteInverterList(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteService.getSiteInverterList(siteId));
    }

    /**
     * 站点历史电量统计
     *
     * @param siteId 站点ID
     * @return
     */
    @GetMapping("/history")
    public JsonResult<SiteHistoryVO> siteHistory(@RequestParam("siteId") String siteId) {
        return JsonResult.buildSuccess(siteDashboardHistoryService.getSiteHistory(siteId, 7));
    }

}
