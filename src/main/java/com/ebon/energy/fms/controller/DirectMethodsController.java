package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.DirectMethodSendPO;
import com.ebon.energy.fms.domain.po.FirmwareUpdatePO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.service.DirectMethodsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 直接方法
 */
@RestController
@RequestMapping("/api/direct-method")
public class DirectMethodsController {

    @Resource
    private DirectMethodsService directMethodsService;

    /**
     * 获取全部直接方法
     *
     * @return List<DirectMethodsVO>
     */
    @GetMapping("/all-direct-methods")
    public JsonResult<List<DirectMethodsVO>> getAllDirectMethods() {
        return JsonResult.buildSuccess(directMethodsService.getAllDirectMethods());
    }

    /**
     * 调用直接方法
     * @param po
     * @return
     */
    @OperateLogAnnotation(name="Send Direct Method")
    @PostMapping("/send-direct-method")
    public JsonResult<List<SendDirectMethodErrorVO>> sendDirectMethod(@RequestBody DirectMethodSendPO po) {
        return JsonResult.buildSuccess(directMethodsService.sendDirectMethod(po));
    }
}
