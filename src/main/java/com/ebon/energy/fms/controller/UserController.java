package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.NoLoginRequired;
import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.LoginPO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.service.UserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 用户管理
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Resource
    private UserService userService;

    /**
     * 登录
     *
     * @param response
     * @param loginPO
     * @return
     */
    @NoLoginRequired
    @OperateLogAnnotation(name="Login")
    @PostMapping("/login")
    public JsonResult login(HttpServletResponse response,
                            @RequestBody @Valid LoginPO loginPO) {
        userService.login(loginPO.getEmail(), loginPO.getPassword(), response);
        return JsonResult.buildSuccess();
    }

    /**
     * 用户登出
     *
     * @param response
     * @return
     */
    @NoLoginRequired
    @RequestMapping(value = "/login-out", method = RequestMethod.GET)
    public JsonResult loginOut(HttpServletResponse response) {
        userService.loginOut(response);
        return JsonResult.buildSuccess();
    }

}
