package com.ebon.energy.fms.controller;


import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.product.control.invert.GenericScheduleItemViewModel;
import com.ebon.energy.fms.domain.vo.product.control.shcedule.ValidateScheduleRequest;
import com.ebon.energy.fms.service.ScheduleValidatorApiService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Validated
@RestController
@RequestMapping("/api/schedule")
@RequiredArgsConstructor
public class ScheduleValidatorApiController {

    private final ScheduleValidatorApiService scheduleValidatorApiService;

    @PostMapping("/validate")
    public JsonResult<Void> validate(@RequestBody @Valid ValidateScheduleRequest request) {
        request.validate();
        return JsonResult.buildSuccess();
    }
}
