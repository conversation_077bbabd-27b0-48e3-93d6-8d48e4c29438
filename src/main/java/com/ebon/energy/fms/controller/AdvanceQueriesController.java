package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.common.annotation.OperateLogAnnotation;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQueryCreateVO;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQueryFavouritesPO;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQuerySearchPO;
import com.ebon.energy.fms.domain.po.advanceQueries.AdvanceQueryUpdateVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.advanceQueries.*;
import com.ebon.energy.fms.service.AdvanceQueriesService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 高级查询
 */
@RestController
@RequestMapping("/api/advance-queries")
public class AdvanceQueriesController {

    @Resource
    private AdvanceQueriesService advanceQueriesService;


    /**
     * 创建Query
     * @param createVO
     * @return
     */
    @OperateLogAnnotation(name="Create Query")
    @PostMapping("/create")
    public JsonResult<Boolean> create(@Valid @RequestBody AdvanceQueryCreateVO createVO) {
        return JsonResult.buildSuccess(advanceQueriesService.create(createVO));
    }

    /**
     * 更新Query
     * @param updateVO
     * @return
     */
    @OperateLogAnnotation(name="Update Query")
    @PostMapping("/update")
    public JsonResult<Boolean> update(@Valid @RequestBody AdvanceQueryUpdateVO updateVO) {
        return JsonResult.buildSuccess(advanceQueriesService.update(updateVO));
    }

    /**
     * 删除Query
     * @param queryId
     * @return
     */
    @OperateLogAnnotation(name="Delete Query")
    @PostMapping("/delete")
    public JsonResult<Boolean> delete(@RequestParam("queryId") Integer queryId) {
        return JsonResult.buildSuccess(advanceQueriesService.delete(queryId));
    }

    /**
     * 详情
     * @param queryId
     * @return
     */
    @GetMapping("/detail")
    public JsonResult<AdvanceQueryFullVO> detail(@RequestParam("queryId") Integer queryId) {
        return JsonResult.buildSuccess(advanceQueriesService.detail(queryId));
    }

    /**
     * 收藏的查询
     *
     * @param po
     * @return
     */
    @GetMapping("/favourites")
    public JsonResult<PageResult<AdvanceQueryVO>> listByCreator(AdvanceQueryFavouritesPO po) {
        return JsonResult.buildSuccess(advanceQueriesService.favourites(po));
    }

    /**
     * 高级查询
     *
     * @param po
     * @return
     */
    @GetMapping("/search")
    public JsonResult<PageResult<AdvanceQueryVO>> search(AdvanceQuerySearchPO po) {
        return JsonResult.buildSuccess(advanceQueriesService.search(po));
    }

    /**
     * 获取tags列表
     * @param queryId
     * @return List<TagsVO>
     */
    @GetMapping("/tags")
    public JsonResult<List<TagsVO>> tags(@RequestParam(value = "queryId", required = false) Integer queryId) {
        return JsonResult.buildSuccess(advanceQueriesService.tagsList(queryId));
    }


    /**
     * 获取columns列表
     * @param queryId
     * @return List<QueryResultColumnsVO>
     */
    @GetMapping("/columns")
    public JsonResult<List<QueryResultColumnsVO>> columns(@RequestParam(value = "queryId", required = false) Integer queryId) {
        return JsonResult.buildSuccess(advanceQueriesService.columnsList(queryId));
    }


    /**
     * 获取Result
     * @param queryId
     * @return QueryResultVO
     */
    @GetMapping("/result")
    public JsonResult<QueryResultVO> result(@RequestParam(value = "queryId") Integer queryId) {
        return JsonResult.buildSuccess(advanceQueriesService.result(queryId));
    }

}