package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.po.AuditsListPO;
import com.ebon.energy.fms.domain.vo.AuditsVO;
import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.service.AuditService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 管理员
 */
@RestController
@RequestMapping("/api/audit")
public class AuditController {

    @Resource
    private AuditService auditService;

    /**
     * 审计查询
     *
     * @param po
     * @return
     */
    @PostMapping("/list")
    public JsonResult<PageResult<AuditsVO>> list(@RequestBody @Valid AuditsListPO po) {
        return JsonResult.buildSuccess(auditService.getAuditList(po));
    }

}
