package com.ebon.energy.fms.mapper.third;

import com.ebon.energy.fms.domain.vo.product.control.InternalDeviceInfoAndSettings;
import org.apache.ibatis.annotations.*;
import java.time.LocalDateTime;

@Mapper
public interface DeviceInfoAndSettingsMapper {

    @Select({
        "<script>",
        "SELECT ",
        "  product.RedbackProductSn,",
        "  JSON_VALUE(product.LatestSystemStatus, '$.Inverter.ModelName') as modelName,",
        "  JSON_VALUE(product.LatestSystemStatus, '$.Inverter.FirmwareVersion') as firmwareVersion,",
        "  REPLACE(REPLACE(JSON_VALUE(product.LatestSystemStatus, '$.OuijaBoard.SoftwareVersion'), 'ROSS v', ''), ', ', '.') as softwareVersion,",
        "  product.HardwareConfig as hardwareConfig,",
        "  ds.DesiredDeviceSettings as desired,",
        "  ds.ReportedDeviceSettings as reported,",
        "  ds.DeviceSettingsIntent as intent,",
        "  explicits.MinOnGridSoC0to100 as minOnGridSoC0to100,",
        "  explicits.MaxOnGridSoC0to100 as maxOnGridSoC0to100,",
        "  explicits.MinOffGridSoC0to100 as minOffGridSoC0to100,",
        "  explicits.MaxOffGridSoC0to100 as maxOffGridSoC0to100",
        "FROM RedbackProducts product",
        "LEFT JOIN Device device ON device.SerialNumber = product.RedbackProductSn AND device.ApplicationName = 'Ross'",
        "LEFT JOIN ExplicitSettings explicits ON product.RedbackProductSn = explicits.SerialNumber",
        "<choose>",
        "  <when test='asOf != null'>",
        "    LEFT JOIN DeviceSettings FOR SYSTEM_TIME AS OF #{asOf} ds ON device.Id = ds.DeviceId",
        "  </when>",
        "  <otherwise>",
        "    LEFT JOIN DeviceSettings ds ON ds.DeviceId = device.Id",
        "  </otherwise>",
        "</choose>",
        "<if test='userId != null'>",
        "  INNER JOIN dbo.AllowedSerialNumbers(#{userId}) asn ON product.RedbackProductSn = asn.SerialNumber",
        "</if>",
        "WHERE RedbackProductSn = #{serialNumber}",
        "</script>"
    })
    InternalDeviceInfoAndSettings getDeviceInfoAndSettings(
        @Param("userId") String userId,
        @Param("serialNumber") String serialNumber,
        @Param("asOf") LocalDateTime asOf
    );
}
