package com.ebon.energy.fms.mapper.second;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InvertersMapper extends BaseMapper<InvertersDO> {

    @Select("select * from Inverters where ${query}")
    List<InvertersDO> selectInvertersByQuery(@Param("query") String query);
}
