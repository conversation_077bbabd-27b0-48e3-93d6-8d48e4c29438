package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InverterScheduleDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.sql.Time;
import java.util.Date;

@Mapper
public interface InverterScheduleMapper extends BaseMapper<InverterScheduleDO> {
    
    /**
     * Delete a schedule by marking it as deleted (soft delete)
     *
     * @param deletedById User ID who deleted the schedule
     * @param whenUtc Current UTC time
     * @param channel Channel through which the schedule was deleted
     * @param serialNumber Product serial number
     * @param scheduleId Schedule ID
     * @return The number of rows affected
     */
    @Update("UPDATE InverterSchedule SET " +
            "DeletedById = #{deletedById}, " +
            "DeletedOnUtc = #{whenUtc}, " +
            "DeletedChannel = #{channel} " +
            "WHERE ScheduleId = #{scheduleId} AND SerialNumber = #{serialNumber}")
    int deleteScheduleTrustedAsync(
            @Param("deletedById") String deletedById,
            @Param("whenUtc") Date whenUtc,
            @Param("channel") String channel,
            @Param("serialNumber") String serialNumber,
            @Param("scheduleId") String scheduleId);
            
    /**
     * Save a new schedule
     *
     * @param serialNumber Product serial number
     * @param scheduleId Schedule ID
     * @param userNotes User notes
     * @param createdById User ID who created the schedule
     * @param createdOnUtc Creation time (UTC)
     * @param createdChannel Channel through which the schedule was created
     * @param startAtUtc Start time (UTC)
     * @param endAtUtc End time (UTC)
     * @param dailyStartTime Daily start time
     * @param duration Duration
     * @param daysOfWeek Days of week
     * @param actionName Action name
     * @param actionParameter Action parameter
     * @param priority Priority
     * @param ianaTimeZoneId IANA time zone ID
     * @return The number of rows affected
     */
    @Insert("INSERT INTO InverterSchedule " +
            "(SerialNumber, ScheduleId, UserNotes, CreatedById, CreatedOnUtc, CreatedChannel, " +
            "StartAtUtc, EndAtUtc, DailyStartTime, Duration, DaysOfWeek, ActionName, ActionParameter, " +
            "Priority, IanaTimeZoneId) " +
            "VALUES " +
            "(#{serialNumber}, #{scheduleId}, #{userNotes}, #{createdById}, #{createdOnUtc}, #{createdChannel}, " +
            "#{startAtUtc}, #{endAtUtc}, #{dailyStartTime}, #{duration}, #{daysOfWeek}, #{actionName}, " +
            "#{actionParameter}, #{priority}, #{ianaTimeZoneId})")
    int saveScheduleTrustedAsync(
            @Param("serialNumber") String serialNumber,
            @Param("scheduleId") String scheduleId,
            @Param("userNotes") String userNotes,
            @Param("createdById") String createdById,
            @Param("createdOnUtc") Date createdOnUtc,
            @Param("createdChannel") String createdChannel,
            @Param("startAtUtc") Date startAtUtc,
            @Param("endAtUtc") Date endAtUtc,
            @Param("dailyStartTime") Time dailyStartTime,
            @Param("duration") Time duration,
            @Param("daysOfWeek") Integer daysOfWeek,
            @Param("actionName") String actionName,
            @Param("actionParameter") Integer actionParameter,
            @Param("priority") Integer priority,
            @Param("ianaTimeZoneId") String ianaTimeZoneId);
}
