package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.ProductDailyCachesDO;
import com.ebon.energy.fms.domain.entity.ProductDailyHistoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface ProductDailyCachesMapper extends BaseMapper<ProductDailyCachesDO> {

    @Select("SELECT SUM(TotalGeneration) AS TotalGeneration, SUM(TotalImport) AS TotalImport, SUM(TotalExport) AS TotalExport FROM ProductDailyCaches WHERE " +
            " RedbackProductSn=#{serialNumber}")
    ProductDailyCachesDO selectTotal(@Param("serialNumber") String serialNumber);

    @Select("SELECT SUM(TotalGeneration) AS yearTotal FROM ProductDailyCaches WHERE " +
            " RedbackProductSn=#{serialNumber}" +
            " AND TotalGeneration IS NOT NULL" +
            " AND DATEPART(YEAR, CONVERT(DATE, DATE, 103)) = #{year} ")
    BigDecimal selectYearTotalGeneration(@Param("serialNumber") String serialNumber, @Param("year") Integer year);

    @Select("SELECT SUM(TotalGeneration) AS monthTotal FROM [dbo].[ProductDailyCaches] WHERE " +
            " RedbackProductSn=#{serialNumber} and TotalGeneration IS NOT NULL" +
            " AND DATEPART(YEAR, CONVERT(DATE, DATE, 103)) = #{year} and DATEPART(MONTH, CONVERT(DATE, DATE, 103)) = #{month}")
    BigDecimal selectMonthTotalGeneration(@Param("serialNumber") String serialNumber, @Param("year") Integer year, @Param("month") Integer month);

    @Select("SELECT SUM(TotalGeneration) AS dayTotal FROM [dbo].[ProductDailyCaches] WHERE " +
            " RedbackProductSn=#{serialNumber} and TotalGeneration IS NOT NULL" +
            " AND Date = #{date}")
    BigDecimal selectDayTotalGeneration(@Param("serialNumber") String serialNumber, @Param("date") String date);

    @Select("DECLARE @EndTimeIsToday BIT = 1;\n" +
            "DECLARE @LocalStartTime datetime = #{startTime};\n" +
            "DECLARE @LocalEndTime datetime = #{endTime};\n" +
            "DECLARE @SerialNumber VARCHAR(50) = #{serialNumber, jdbcType=VARCHAR};\n" +
            "DECLARE @PublicSiteId VARCHAR(50) = '';\n" +
            "\n" +
            "WITH Parameters AS (\n" +
            " SELECT \n" +
            "      CASE WHEN @PublicSiteId IS NULL OR @PublicSiteId = '' THEN NULL ELSE @PublicSiteId END AS PublicSiteId\n" +
            " , CASE WHEN @SerialNumber IS NULL OR @SerialNumber = '' THEN NULL ELSE @SerialNumber END AS SerialNumber\n" +
            " , @EndTimeIsToday AS EndTimeIsToday\n" +
            " , @LocalStartTime AS LocalStartTime\n" +
            " , Convert(DATE, @LocalStartTime) AS LocalStartDate\n" +
            " , DateFromParts(Year(@LocalStartTime), Month(@LocalStartTime), 1) AS LocalStartMonth\n" +
            " , @LocalEndTime AS LocalEndTime\n" +
            " , Convert(DATE, @LocalEndTime) AS LocalEndDate\n" +
            " , DateFromParts(Year(@LocalEndTime), Month(@LocalEndTime), 1) AS LocalEndMonth\n" +
            //" -- Make sure someone hasn't tried to supply both a PublicSiteId and SerialNumber. --\n" +
            " WHERE (CASE WHEN @PublicSiteId IS NULL OR @PublicSiteId = '' THEN NULL ELSE @PublicSiteId END) IS NULL\n" +
            " OR (CASE WHEN @SerialNumber IS NULL OR @SerialNumber = '' THEN NULL ELSE @SerialNumber END) IS NULL\n" +
            ")\n" +
            //"-- CTE by SAM from https://stackoverflow.com/questions/17529860/how-to-list-all-dates-between-two-dates\n" +
            ", AllDaysLocal AS (  -- plus 'OPTION (MAXRECURSION 0)' at the end of the query\n" +
            "    SELECT @LocalStartTime AS [Date], 1 AS [level]\n" +
            "    UNION ALL\n" +
            "    SELECT   DATEADD(DAY, 1, [Date]), [level] + 1\n" +
            "    FROM     AllDaysLocal\n" +
            " CROSS APPLY Parameters\n" +
            "    WHERE    [Date] < Parameters.LocalEndTime\n" +
            ") \n" +
            ", LocalDaysWithCache AS ( \n" +
            "    SELECT \n" +
            " sit.SystemId AS PublicSiteId\n" +
            " , ins.RedbackProductSn AS SerialNumber\n" +
            " , ins.Id As InstallationId\n" +
            " , DateFromParts(Year(AllDaysLocal.[Date]), Month(AllDaysLocal.[Date]), 1) AS 'Month'\n" +
            "    , Convert(DATE, AllDaysLocal.[Date]) AS 'Day'\n" +
            "    , AllDaysLocal.[Date] AS 'Date'\n" +
            /*"  -- https://rbtech.visualstudio.com/Redback/_workitems/edit/52869\n" +
            "    -- If any day in the past is outside of the safe bounds, specified in AC, or is null \n" +
            "    -- then it is given the average for that month \n" +
            "    -- and the monthly data is marked isAccurate: false\n" +
            "    --\n" +
            "    -- Maxes for consumption, export, import and generation are in stories #53060, #53075, #53070; \n" +
            "    -- Null out ProductDailyCaches values that exceed expected maximums, are below zero, or are zero but after system went offline. For now (see averages below). --\n" +*/
            "    , CASE WHEN pdc.TotalConsumption > 240 THEN NULL\n" +
            "           ELSE CASE WHEN pdc.TotalConsumption < 0 THEN NULL\n" +
            "               ELSE CASE WHEN pdc.TotalConsumption = 0\n" +
            "                                    AND (pro.LastSystemStatusReceived IS NULL\n" +
            "                                         OR (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (DateAdd(MINUTE, 10, pro.LastSystemStatusReceived) AT TIME ZONE 'UTC'))\n" +
            "                               THEN NULL\n" +
            "                         ELSE pdc.TotalConsumption\n" +
            "        END\n" +
            "    END\n" +
            "      END AS ValidatedConsumptionkWh\n" +
            "    , CASE WHEN pdc.TotalExport > 144 THEN NULL\n" +
            "        ELSE CASE WHEN pdc.TotalExport < 0 THEN NULL\n" +
            "               ELSE CASE WHEN pdc.TotalExport = 0\n" +
            "                                    AND (pro.LastSystemStatusReceived IS NULL\n" +
            "                                         OR (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (DateAdd(MINUTE, 10, pro.LastSystemStatusReceived) AT TIME ZONE 'UTC'))\n" +
            "                               THEN NULL\n" +
            "                         ELSE pdc.TotalExport\n" +
            "        END\n" +
            "    END\n" +
            "      END AS ValidatedExportkWh\n" +
            "    , CASE WHEN pdc.TotalImport > 240 THEN NULL\n" +
            "        ELSE CASE WHEN pdc.TotalImport < 0 THEN NULL\n" +
            "               ELSE CASE WHEN pdc.TotalImport = 0\n" +
            "                                    AND (pro.LastSystemStatusReceived IS NULL\n" +
            "                                         OR (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (DateAdd(MINUTE, 10, pro.LastSystemStatusReceived) AT TIME ZONE 'UTC'))\n" +
            "                               THEN NULL\n" +
            "                         ELSE pdc.TotalImport\n" +
            "        END\n" +
            "    END\n" +
            "      END AS ValidatedImportkWh\n" +
            "    , CASE WHEN pdc.TotalGeneration > 144 THEN NULL\n" +
            "        ELSE CASE WHEN pdc.TotalGeneration < 0 THEN NULL\n" +
            "               ELSE CASE WHEN pdc.TotalGeneration = 0\n" +
            "                                    AND (pro.LastSystemStatusReceived IS NULL\n" +
            "                                         OR (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (DateAdd(MINUTE, 10, pro.LastSystemStatusReceived) AT TIME ZONE 'UTC'))\n" +
            "                               THEN NULL\n" +
            "                         ELSE pdc.TotalGeneration\n" +
            "        END\n" +
            "    END\n" +
            "      END AS ValidatedGenerationkWh\n" +
            "    , CASE WHEN pdc.BatteryChargedkWh > 100 THEN NULL\n" +
            "        ELSE CASE WHEN pdc.BatteryChargedkWh < 0 THEN NULL\n" +
            "               ELSE CASE WHEN pdc.BatteryChargedkWh = 0\n" +
            "                                    AND (pro.LastSystemStatusReceived IS NULL\n" +
            "                                         OR (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (DateAdd(MINUTE, 10, pro.LastSystemStatusReceived) AT TIME ZONE 'UTC'))\n" +
            "                               THEN NULL\n" +
            "                         ELSE pdc.BatteryChargedkWh\n" +
            "        END\n" +
            "    END\n" +
            "      END AS ValidatedBatteryChargedkWh\n" +
            "    , CASE WHEN pdc.BatteryDischargedkWh > 100 THEN NULL\n" +
            "        ELSE CASE WHEN pdc.BatteryDischargedkWh < 0 THEN NULL\n" +
            "               ELSE CASE WHEN pdc.BatteryDischargedkWh = 0\n" +
            "                                    AND (pro.LastSystemStatusReceived IS NULL\n" +
            "                                         OR (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (DateAdd(MINUTE, 10, pro.LastSystemStatusReceived) AT TIME ZONE 'UTC'))\n" +
            "                               THEN NULL\n" +
            "                         ELSE pdc.BatteryDischargedkWh\n" +
            "        END\n" +
            "    END\n" +
            "      END AS ValidatedBatteryDischargedkWh\n" +
            "\n" +
            "    , pdc.TotalConsumption AS OriginallConsumptionkWh\n" +
            "    , pdc.TotalExport AS OriginalExportkWh\n" +
            "    , pdc.TotalImport AS OriginalImportkWh\n" +
            "    , pdc.TotalGeneration AS OriginalGenerationkWh\n" +
            " , pdc.BatteryChargedkWh AS OriginalBatteryChargedkWh\n" +
            " , pdc.BatteryDischargedkWh AS OriginalBatteryDischargedkWh\n" +
            "\n" +
            "    FROM AllDaysLocal\n" +
            " CROSS APPLY Parameters\n" +
            "    JOIN dbo.RedbackProductInstallations ins WITH(NOLOCK) ON ins.InstallationEndDate IS NULL\n" +
            "    JOIN dbo.Site sit ON ins.SiteId = sit.Id\n" +
            "    JOIN dbo.RedbackProducts pro WITH(NOLOCK) on pro.RedbackProductSn = ins.RedbackProductSn  \n" +
            "    -- security check removed in 2.18.2 + 2.19 because it has a significant performance impact \n" +
            "    -- and this query is already protected by the security checks done by methods called before calling this query\n" +
            "    JOIN dbo.Addresses adr ON adr.Id = Coalesce(sit.AddressId, ins.AddressId)\n" +
            "    LEFT JOIN dbo.ProductDailyCaches pdc WITH(NOLOCK)\n" +
            "        ON pdc.RedbackProductSn = ins.RedbackProductSn\n" +
            "        -- Convert to DATETIMEOFFSET for date comparisons. --\n" +
            "        AND (pdc.[Time] AT TIME ZONE adr.TimeZoneId) > (ins.InstallationStartDate AT TIME ZONE 'UTC')\n" +
            "        AND pdc.[Time] >= Parameters.LocalStartMonth\n" +
            "        AND pdc.[Time] < Parameters.LocalEndTime\n" +
            "        AND AllDaysLocal.Date = CONVERT(DATE, pdc.[Time])\n" +
            "\n" +
            "\n" +
            " WHERE ins.RedbackProductSn = Parameters.SerialNumber\n" +
            "\n" +
            //"    -- Switch installation date to local time, before converting to a date (without time), and then comparing it to our date range. --\n" +
            "    AND AllDaysLocal.Date >= Convert(date, SwitchOffset((ins.InstallationStartDate AT TIME ZONE 'UTC'), DateName(TZOFFSET, (AllDaysLocal.Date AT TIME ZONE adr.TimeZoneId))))\n" +
            "    AND AllDaysLocal.Date < Parameters.LocalEndTime\n" +
            ")\n" +
            ", Averages AS ( \n" +
            "    SELECT \n" +
            " c.PublicSiteId\n" +
            " , c.SerialNumber\n" +
            " , c.[Month]\n" +
            " , Round(Avg(c.ValidatedConsumptionkWh), 3) as AverageConsumptionkWh\n" +
            " , Round(Avg(c.ValidatedExportkWh), 3) as AverageExportkWh\n" +
            " , Round(Avg(c.ValidatedImportkWh), 3) as AverageImportkWh\n" +
            " , Round(Avg(c.ValidatedGenerationkWh), 3) as AverageGenerationkWh\n" +
            " , Round(Avg(c.ValidatedBatteryChargedkWh), 3) as AverageBatteryChargedkWh\n" +
            " , Round(Avg(c.ValidatedBatteryDischargedkWh), 3) as AverageBatteryDischargedkWh\n" +
            "    FROM LocalDaysWithCache c\n" +
            " CROSS APPLY Parameters\n" +
            "    -- Sometimes we do not want to include end date (today's partial results) in averages. --\n" +
            "    WHERE (Parameters.EndTimeIsToday = 0 OR c.[Date] < Parameters.LocalEndDate)\n" +
            "    GROUP BY c.PublicSiteId, c.SerialNumber, c.[Month]\n" +
            ")\n" +
            ", AdjustedDailies AS (\n" +
            "    SELECT ldc.*\n" +
            "    , CASE WHEN ldc.ValidatedConsumptionkWh IS NOT NULL THEN ldc.ValidatedConsumptionkWh ELSE Averages.AverageConsumptionkWh END as ConsumptionkWh\n" +
            "    , CASE WHEN ldc.ValidatedConsumptionkWh IS NOT NULL THEN 0 ELSE 1 END as ConsumptionAdjusted\n" +
            "    , CASE WHEN ldc.ValidatedExportkWh IS NOT NULL THEN ldc.ValidatedExportkWh ELSE Averages.AverageExportkWh END as ExportkWh\n" +
            "    , CASE WHEN ldc.ValidatedExportkWh IS NOT NULL THEN 0 ELSE 1 END as ExportAdjusted\n" +
            "    , CASE WHEN ldc.ValidatedImportkWh IS NOT NULL THEN ldc.ValidatedImportkWh ELSE Averages.AverageImportkWh END as ImportkWh\n" +
            "    , CASE WHEN ldc.ValidatedImportkWh IS NOT NULL THEN 0 ELSE 1 END as ImportAdjusted\n" +
            "    , CASE WHEN ldc.ValidatedGenerationkWh IS NOT NULL THEN ldc.ValidatedGenerationkWh ELSE Averages.AverageGenerationkWh END as GenerationkWh\n" +
            "    , CASE WHEN ldc.ValidatedGenerationkWh IS NOT NULL THEN 0 ELSE 1 END as GenerationAdjusted\n" +
            " , CASE WHEN ldc.ValidatedBatteryChargedkWh IS NOT NULL THEN ldc.ValidatedBatteryChargedkWh ELSE Averages.AverageBatteryChargedkWh END as BatteryChargedkWh\n" +
            "    , CASE WHEN ldc.ValidatedBatteryChargedkWh IS NOT NULL THEN 0 ELSE 1 END as BatteryChargedAdjusted\n" +
            " , CASE WHEN ldc.ValidatedBatteryDischargedkWh IS NOT NULL THEN ldc.ValidatedBatteryDischargedkWh ELSE Averages.AverageBatteryDischargedkWh END as BatteryDischargedkWh\n" +
            "    , CASE WHEN ldc.ValidatedBatteryDischargedkWh IS NOT NULL THEN 0 ELSE 1 END as BatteryDischargedAdjusted\n" +
            " , Averages.AverageConsumptionkWh\n" +
            " , Averages.AverageExportkWh\n" +
            " , Averages.AverageImportkWh\n" +
            " , Averages.AverageGenerationkWh\n" +
            " , Averages.AverageBatteryChargedkWh\n" +
            " , Averages.AverageBatteryDischargedkWh\n" +
            "    FROM LocalDaysWithCache  ldc\n" +
            "    JOIN Averages ON ldc.[Month] = Averages.Month\n" +
            " CROSS APPLY Parameters\n" +
            "    WHERE ldc.[Date] < Parameters.LocalEndDate\n" +
            "    UNION -- Running as two separate parts to allow for bulk optimisation through INNER JOIN, with only today running with LEFT OUTER JOIN. --\n" +
            "    SELECT ldc.*\n" +
            "    , CASE WHEN ldc.ValidatedConsumptionkWh IS NOT NULL THEN ldc.ValidatedConsumptionkWh ELSE Averages.AverageConsumptionkWh END as ConsumptionkWh\n" +
            "    , CASE WHEN ldc.ValidatedConsumptionkWh IS NOT NULL THEN 0 ELSE 1 END as ConsumptionAdjusted\n" +
            "    , CASE WHEN ldc.ValidatedExportkWh IS NOT NULL THEN ldc.ValidatedExportkWh ELSE Averages.AverageExportkWh END as ExportkWh\n" +
            "    , CASE WHEN ldc.ValidatedExportkWh IS NOT NULL THEN 0 ELSE 1 END as ExportAdjusted\n" +
            "    , CASE WHEN ldc.ValidatedImportkWh IS NOT NULL THEN ldc.ValidatedImportkWh ELSE Averages.AverageImportkWh END as ImportkWh\n" +
            "    , CASE WHEN ldc.ValidatedImportkWh IS NOT NULL THEN 0 ELSE 1 END as ImportAdjusted\n" +
            "    , CASE WHEN ldc.ValidatedGenerationkWh IS NOT NULL THEN ldc.ValidatedGenerationkWh ELSE Averages.AverageGenerationkWh END as GenerationkWh\n" +
            "    , CASE WHEN ldc.ValidatedGenerationkWh IS NOT NULL THEN 0 ELSE 1 END as GenerationAdjusted\n" +
            " , CASE WHEN ldc.ValidatedBatteryChargedkWh IS NOT NULL THEN ldc.ValidatedBatteryChargedkWh ELSE Averages.AverageBatteryChargedkWh END as BatteryChargedkWh\n" +
            "    , CASE WHEN ldc.ValidatedBatteryChargedkWh IS NOT NULL THEN 0 ELSE 1 END as BatteryChargedAdjusted\n" +
            " , CASE WHEN ldc.ValidatedBatteryDischargedkWh IS NOT NULL THEN ldc.ValidatedBatteryDischargedkWh ELSE Averages.AverageBatteryDischargedkWh END as BatteryDischargedkWh\n" +
            "    , CASE WHEN ldc.ValidatedBatteryDischargedkWh IS NOT NULL THEN 0 ELSE 1 END as BatteryDischargedAdjusted\n" +
            " , Averages.AverageConsumptionkWh\n" +
            " , Averages.AverageExportkWh\n" +
            " , Averages.AverageImportkWh\n" +
            " , Averages.AverageGenerationkWh\n" +
            " , Averages.AverageBatteryChargedkWh\n" +
            " , Averages.AverageBatteryDischargedkWh\n" +
            "    FROM LocalDaysWithCache  ldc\n" +
            "    LEFT JOIN Averages ON ldc.[Month] = Averages.Month\n" +
            " CROSS APPLY Parameters\n" +
            "    WHERE ldc.[Date] >= Parameters.LocalEndDate\n" +
            ")\n" +
            ", GroupedDailies AS (\n" +
            " SELECT\n" +
            " b.PublicSiteId\n" +
            " , Max(InstallationId) AS MaxInstallationId\n" +
            "    , b.[Month]\n" +
            "    , b.[Day]\n" +
            "    , b.[Date]\n" +
            "\n" +
            " , Round(Sum(b.ConsumptionkWh), 3) as ConsumptionkWh\n" +
            " , Round(Sum(b.ExportkWh), 3) as ExportkWh\n" +
            " , Round(Sum(b.ImportkWh), 3) as ImportkWh\n" +
            " , Round(Sum(b.GenerationkWh), 3) as GenerationkWh\n" +
            " , Round(Sum(b.BatteryChargedkWh), 3) as BatteryChargedkWh\n" +
            " , Round(Sum(b.BatteryDischargedkWh), 3) as BatteryDischargedkWh\n" +
            " , Max(b.ConsumptionAdjusted) as ConsumptionAdjusted\n" +
            " , Max(b.ExportAdjusted) as ExportAdjusted\n" +
            " , Max(b.ImportAdjusted) as ImportAdjusted\n" +
            " , Max(b.GenerationAdjusted) as GenerationAdjusted\n" +
            " , Max(b.BatteryChargedAdjusted) as BatteryChargedAdjusted\n" +
            " , Max(b.BatteryDischargedAdjusted) as BatteryDischargedAdjusted\n" +
            "\n" +
            " , Round(Sum(b.ValidatedConsumptionkWh), 3) as ValidatedConsumptionkWh\n" +
            " , Round(Sum(b.ValidatedExportkWh), 3) as ValidatedExportkWh\n" +
            " , Round(Sum(b.ValidatedImportkWh), 3) as ValidatedImportkWh\n" +
            " , Round(Sum(b.ValidatedGenerationkWh), 3) as ValidatedGenerationkWh\n" +
            " , Round(Sum(b.ValidatedBatteryChargedkWh), 3) as ValidatedBatteryChargedkWh\n" +
            " , Round(Sum(b.ValidatedBatteryDischargedkWh), 3) as ValidatedBatteryDischargedkWh\n" +
            "\n" +
            " , Round(Sum(b.OriginallConsumptionkWh), 3) as OriginallConsumptionkWh\n" +
            " , Round(Sum(b.OriginalExportkWh), 3) as OriginalExportkWh\n" +
            " , Round(Sum(b.OriginalImportkWh), 3) as OriginalImportkWh\n" +
            " , Round(Sum(b.OriginalGenerationkWh), 3) as OriginalGenerationkWh\n" +
            " , Round(Sum(b.OriginalBatteryChargedkWh), 3) as OriginalBatteryChargedkWh\n" +
            " , Round(Sum(b.OriginalBatteryDischargedkWh), 3) as OriginalBatteryDischargedkWh\n" +
            "\n" +
            " , Round(Sum(b.AverageConsumptionkWh), 3) as AverageConsumptionkWh\n" +
            " , Round(Sum(b.AverageExportkWh), 3) as AverageExportkWh\n" +
            " , Round(Sum(b.AverageImportkWh), 3) as AverageImportkWh\n" +
            " , Round(Sum(b.AverageGenerationkWh), 3) as AverageGenerationkWh\n" +
            " , Round(Sum(b.AverageBatteryChargedkWh), 3) as AverageBatteryChargedkWh\n" +
            " , Round(Sum(b.AverageBatteryDischargedkWh), 3) as AverageBatteryDischargedkWh\n" +
            "\n" +
            " FROM AdjustedDailies b\n" +
            " GROUP BY b.PublicSiteId, b.[Month], b.[Day], b.[Date]\n" +
            ")\n" +
            ", DailiesMarkedAge AS (\n" +
            "    SELECT\n" +
            " g.PublicSiteId\n" +
            " , g.MaxInstallationId\n" +
            "    , g.Day\n" +
            "    , Coalesce(g.GenerationkWh, 0) AS TotalGeneration\n" +
            "    , Coalesce(g.ImportkWh, 0) AS TotalImport\n" +
            "    , DateDiff(d, g.Day, Parameters.LocalEndDate) DaysOld\n" +
            "    FROM GroupedDailies g\n" +
            " CROSS APPLY Parameters\n" +
            ")\n" +
            ", MarkedTimeFrames AS (\n" +
            "    SELECT\n" +
            " g.PublicSiteId\n" +
            " , g.MaxInstallationId\n" +
            "    , g.Day\n" +
            "    , g.TotalGeneration\n" +
            "    , g.TotalImport\n" +
            "    , (CASE WHEN Parameters.EndTimeIsToday = 1 AND g.DaysOld = 0 THEN 1 ELSE 0 END) AS IsToday\n" +
            "    , (CASE WHEN (Parameters.EndTimeIsToday = 1 AND g.DaysOld <= 7 AND g.DaysOld != 0)\n" +
            "     OR (Parameters.EndTimeIsToday = 0 AND g.DaysOld < 7)\n" +
            "         THEN 1 ELSE 0 END) AS Is7LastDays\n" +
            "    , (CASE WHEN (Parameters.EndTimeIsToday = 1 AND g.DaysOld <= 30 AND g.DaysOld != 0)\n" +
            "     OR (Parameters.EndTimeIsToday = 0 AND g.DaysOld < 30)\n" +
            "         THEN 1 ELSE 0 END) AS Is30LastDays\n" +
            "    , (CASE WHEN (Parameters.EndTimeIsToday = 1 AND g.DaysOld <= 90 AND g.DaysOld != 0)\n" +
            "     OR (Parameters.EndTimeIsToday = 0 AND g.DaysOld < 90)\n" +
            "         THEN 1 ELSE 0 END) AS Is90LastDays\n" +
            "    FROM DailiesMarkedAge g\n" +
            " CROSS APPLY Parameters\n" +
            ")\n" +
            ", TotalsByTimeFrame AS (\n" +
            " SELECT\n" +
            "   Round(Sum(z.TotalGeneration * z.Is7LastDays), 3) AS TotalGenerationLast7days\n" +
            " , Round(Sum(z.TotalImport * z.Is7LastDays), 3) AS TotalImportLast7days\n" +
            " , Round(Sum(z.TotalGeneration * z.Is30LastDays), 3) AS TotalGenerationLast30days\n" +
            " , Round(Sum(z.TotalImport * z.Is30LastDays), 3) AS TotalImportLast30days\n" +
            " , Round(Sum(z.TotalGeneration * z.Is90LastDays), 3) AS TotalGenerationLast90days\n" +
            " , Round(Sum(z.TotalImport * z.Is90LastDays), 3) AS TotalImportLast90days\n" +
            " , Round(Sum(z.TotalGeneration * z.IsToday), 3) AS TotalGenerationToday\n" +
            " , Round(Sum(z.TotalImport * z.IsToday), 3) AS TotalImportToday\n" +
            " , Count(z.[Day]) AS CacheRows\n" +
            " , Max(z.MaxInstallationId) AS MaxInstallationId\n" +
            " FROM MarkedTimeFrames z\n" +
            ")\n" +
            "\n" +
            "\n" +
            "SELECT\n" +
            "  CONVERT(nvarchar, Finals.[Day]) as Date\n" +
            ", CONVERT(int, ROUND(ConsumptionkWh * 1000, 0)) as DailyUsageWh\n" +
            ", CONVERT(int, ROUND(ExportkWh * 1000, 0)) as DailySoldWh\n" +
            ", CONVERT(int, ROUND(ImportkWh * 1000, 0)) as DailyBoughtWh\n" +
            ", CONVERT(int, ROUND(GenerationkWh * 1000, 0)) as DailyGenerationWh\n" +
            ", CONVERT(int, ROUND(BatteryChargedkWh * 1000, 0)) as DailyBatteryChargedWh\n" +
            ", CONVERT(int, ROUND(BatteryDischargedkWh * 1000, 0)) as DailyBatteryDischargedWh\n" +
            "\n" +
            ", ConsumptionAdjusted as DailyUsageAdjusted\n" +
            ", ExportAdjusted as DailySoldAdjusted\n" +
            ", ImportAdjusted as DailyBoughtAdjusted\n" +
            ", GenerationAdjusted as DailyGenerationAdjusted\n" +
            ", BatteryChargedAdjusted as DailyBatteryChargedAdjusted\n" +
            ", BatteryDischargedAdjusted as DailyBatteryDischargedAdjusted\n" +
            ", Finals.Day AS DateDate\n" +
            "\n" +
            "FROM GroupedDailies Finals\n" +
            "ORDER BY Finals.[Date] DESC OPTION (MAXRECURSION 0);")
    List<ProductDailyHistoryDO> selectEnergyDailyHistory(@Param("serialNumber") String serialNumber, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
