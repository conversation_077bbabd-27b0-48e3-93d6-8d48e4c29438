package com.ebon.energy.fms.mapper.second;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.QueryResultColumnsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface QueryResultColumnsMapper extends BaseMapper<QueryResultColumnsDO> {

    @Select("SELECT  [Column]  FROM QueryResultColumns")
    List<QueryResultColumnsDO> allColumns();
}
