package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.DeviceDO;
import com.ebon.energy.fms.domain.entity.DeviceSettingsDO;
import com.ebon.energy.fms.domain.entity.ProductDeviceSettingsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DeviceSettingsMapper extends BaseMapper<DeviceSettingsDO> {

    @Select("SELECT " +
            "d.ModifiedDateUtc, " +
            "ds.ReportedDeviceSettings, " +
            "ds.DesiredDeviceSettings, " +
            "ds.DeviceSettingsIntent, " +
            "ds.ReportedLastUpdated, " +
            "ds.DesiredDeviceSettingsPatch, " +
            "ds.DesiredVersion, " +
            "product.LatestSystemStatus, " +
            "product.HardwareConfig, " +
            "product.RedbackProductSn, " +
            "o.MinOnGridSoC0to100, " +
            "o.MaxOnGridSoC0to100, " +
            "o.MinOffGridSoC0to100, " +
            "o.MaxOffGridSoC0to100 " +
            "FROM Device d " +
            "JOIN DeviceSettings ds ON d.Id = ds.DeviceId " +
            "JOIN RedbackProducts product ON d.SerialNumber = product.RedbackProductSn " +
            "LEFT JOIN ExplicitSettings o ON product.RedbackProductSn = o.SerialNumber " +
            "WHERE d.SerialNumber = #{serialNumber} AND d.ApplicationName = #{ApplicationName} " +
            "ORDER BY d.ModifiedDateUtc DESC")
    List<ProductDeviceSettingsDO> selectWithProduct(@Param("serialNumber") String serialNumber, @Param("ApplicationName") String ApplicationName);
    
    @Select("SELECT * " +
            "FROM DeviceSettings ds " +
            "WHERE ds.DeviceId = #{deviceId} "
           )
    DeviceSettingsDO selectWithProductByDeviceId(@Param("deviceId") String deviceId);
}
