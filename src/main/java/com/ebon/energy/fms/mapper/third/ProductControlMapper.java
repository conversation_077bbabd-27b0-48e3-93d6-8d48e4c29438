package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.RedbackProductsDO;
import com.ebon.energy.fms.domain.vo.product.control.UpdateProductControlInfo;
import org.apache.ibatis.annotations.*;

@Mapper
public interface ProductControlMapper extends BaseMapper<RedbackProductsDO> {
    @Select("SELECT\n" +
            "    p.ProductOwnerId as ownerId,\n" +
            "    (SELECT top 1 Configurations FROM Configurations c WHERE c.RedbackProductSn = p.RedbackProductSn AND ConfigurationType = 2) as configuration,\n" +
            "    p.IsOptInForOptimization as isOptimised,\n" +
            "    (SELECT top 1 Value FROM RedbackProductDetails rpd WHERE rpd.RedbackProductSn = p.RedbackProductSn AND Name = 'IsAllowedToOptimise') as canBeOptimised\n" +
            "FROM\n" +
            "    RedbackProducts p\n" +
            "WHERE\n" +
            "    RedbackProductSn = #{serial}")
    UpdateProductControlInfo getProductBySerial(@Param("serial") String serial);

    /**
     * Update the IsOptInForOptimization value for a product by its serial number
     *
     * @param serial The product serial number
     * @param isOptInForOptimization The new optimization status value
     * @return The number of rows affected
     */
    @Update("UPDATE RedbackProducts SET IsOptInForOptimization = #{isOptInForOptimization} WHERE RedbackProductSn = #{serial}")
    int updateProductOptimizationStatus(@Param("serial") String serial, @Param("isOptInForOptimization") boolean isOptInForOptimization);
}
