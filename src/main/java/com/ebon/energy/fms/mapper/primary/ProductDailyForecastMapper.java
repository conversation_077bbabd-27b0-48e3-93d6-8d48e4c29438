package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.ProductDailyForecastDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductDailyForecastMapper extends BaseMapper<ProductDailyForecastDO> {
    
    @Insert("<script>" +
            "INSERT INTO ProductDailyForecast (RedbackProductSn, Date, Generation) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.redbackProductSn}, #{item.date}, #{item.generation})" +
            "</foreach>" +
            "</script>")
    int insertBatch(@Param("list") List<ProductDailyForecastDO> list);
}
