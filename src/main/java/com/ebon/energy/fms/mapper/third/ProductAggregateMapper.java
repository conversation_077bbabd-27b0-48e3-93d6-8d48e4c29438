package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.ProductAggregateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface ProductAggregateMapper extends BaseMapper<ProductAggregateDO> {

    @Select("SELECT SUM(EPvTotalForToday) AS YearTotal FROM  ProductAggregate WHERE SerialNumber=#{serialNumber}" +
            " AND EPvTotalForToday IS NOT NULL" +
            " AND YEAR(DateLocal) = #{year} ")
    BigDecimal selectYearPvTotal(@Param("serialNumber") String serialNumber, @Param("year") Integer year);

    @Select("SELECT SUM(EPvTotalForToday) AS monthTotal FROM  ProductAggregate WHERE SerialNumber=#{serialNumber}" +
            " AND EPvTotalForToday IS NOT NULL" +
            " AND YEAR(DateLocal) = #{year} AND MONTH(DateLocal) = #{month}")
    BigDecimal selectMonthPvTotal(@Param("serialNumber") String serialNumber, @Param("year") Integer year, @Param("month") Integer month);
}
